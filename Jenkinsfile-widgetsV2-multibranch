pipeline {
    agent {
        node {
            label 'qa-win'
        }
    }
    parameters {
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: false,
                description: 'Should execute tests, or just build the source.'
        )

        // Environment Settings
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: [
                        'integration',
                        'staging'
                ],
                description: 'Environment to execute tests. Requires EXECUTE_TESTS set to TRUE.'
        )

        choice(
                name: 'DEFAULT_BROWSER',
                choices: [
                        'chrome_headless',
                        'firefox_headless',
                        'edge_headless',
                        'chrome',
                        'firefox',
                        'edge'
                ],
                description: 'Browser type to use for tests execution.'
        )

        // Notifications
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
               description: '''Slack channel for test results:
                 - #platform-reports: Platform test reports
                 - #qa-automation: Automation framework updates
                 - #test-executions: General test execution updates'''
        )
    }

    environment {
        WIDGETS_API_URL = 'https://widgets-integration.sportal365.com/metadata.json'
    }

    stages {
        stage('Fetch Widget Version') {
            steps {
                script {
                    try {
                        // Using curl to fetch version and storing it in environment variable
                        def response = sh(script: """
                            curl -s ${WIDGETS_API_URL}
                        """, returnStdout: true).trim()

                        // Parse JSON response and set environment variable
                        def metadata = readJSON text: response
                        def widgetVersion = metadata.widgets.latest_version ?: '8.2.0'    // Fallback version if API fails

                        // Set the widget version environment variable
                        env.WIDGETS_VERSION = widgetVersion

                        echo "Fetched widget version: ${env.WIDGETS_VERSION}"
                    } catch (Exception e) {
                        echo "Failed to fetch widget version: ${e.getMessage()}"
                        echo "Using fallback version 8.2.0"
                        env.WIDGETS_VERSION = '8.2.0'
                    }
                }
            }
        }

        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl ui-tests-widgets-v2 -am'
            }
        }

        stage('Test') {
            when {
                expression {
                    return params.EXECUTE_TESTS
                }
            }
            steps {
                script {
                    def parallelTests = [
                            'footballWidgets': {
                                executeTestWithRetry([
                                        groups: 'widgetsV2&football&!visual',
                                        environment: EXECUTION_ENVIRONMENT,
                                        widgetsVersion: env.WIDGETS_VERSION
                                ])
                            },
                            'tennisWidgets': {
                                executeTestWithRetry([
                                        groups: 'widgetsV2&tennis&!visual',
                                        environment: EXECUTION_ENVIRONMENT,
                                        widgetsVersion: env.WIDGETS_VERSION,
                                        delay: 180
                                ])
                            },
                            'iceHockeyWidgets': {
                                executeTestWithRetry([
                                        groups: 'widgetsV2&iceHockey&!visual',
                                        environment: EXECUTION_ENVIRONMENT,
                                        widgetsVersion: env.WIDGETS_VERSION,
                                        delay: 220
                                ])
                            },
                            'basketballWidgets': {
                                executeTestWithRetry([
                                        groups: 'widgetsV2&basketball&!visual',
                                        environment: EXECUTION_ENVIRONMENT,
                                        widgetsVersion: env.WIDGETS_VERSION,
                                        delay: 260
                                ])
                            }
                    ]

                    parallel parallelTests
                }
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        env.TEST_SUMMARY = processTestResults()
                    }
                    allure([
                            includeProperties: false,
                            jdk: '',
                            properties: [],
                            reportBuildPolicy: 'ALWAYS',
                            results: [[path: '**/allure-results']]
                    ])
                    zip zipFile: './ui-tests-widgets-v2/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./ui-tests-widgets-v2/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${env.TEST_SUMMARY}\n More info at: ${env.BUILD_URL}")
                }
                failure {
                    script {
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${env.TEST_SUMMARY}\nMore info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}

def executeTestWithRetry(Map config) {
    def startTime = System.currentTimeMillis()

    // Apply delay if specified
    if (config.delay) {
        sleep(config.delay)
    }

    def command = """mvn test -pl ui-tests-widgets-v2 -am -Dsurefire.failIfNoSpecifiedTests=false -Dgroups="${config.groups}" -Denvironment=${config.environment} -DfootballWidgetsVersion=${config.widgetsVersion} -DbasketballWidgetsVersion=${config.widgetsVersion} -DtennisWidgetsVersion=${config.widgetsVersion} -DiceHockeyWidgetsVersion=${config.widgetsVersion}"""

    try {
        bat command
        def duration = (System.currentTimeMillis() - startTime) / 1000 / 60 // Convert to minutes

        if (duration < 10) {
            echo "Test execution for ${config.groups} completed too quickly (${duration} minutes). Retrying..."
            sleep(30) // Wait 30 seconds before retry
            bat command
        }
    } catch (Exception e) {
        echo "Failed to execute test for ${config.groups}: ${e.message}"
        throw e
    }
}

def processTestResults() {
    def testResults = junit '**/target/surefire-reports/*.xml'
    def testSummary = "Test Results:\n"
    testResults?.each { suite ->
        testSummary += "${suite.totalCount} tests were run\n"
        testSummary += "  Passed: ${suite.passCount}\n"
        testSummary += "  Failed: ${suite.failCount}\n"
        testSummary += "  Skipped: ${suite.skipCount}\n"
    }
    return testSummary
}
