<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="textArea.css">
</head>
<body>
    <label for="myTextArea">Placeholders</label>
<textarea id="myTextArea" name="myTextArea" placeholder="your Text term goes here" class="myTestClass"></textarea>
    <br>
    <br>
    <label for="myTextArea1">Physical input element size</label>
<textarea id="myTextArea1" name="myTextArea" size="30"></textarea>
    <br>
    <br>
    <label for="myTextArea2">Element value length</label>
<textarea id="myTextArea2" name="myTextArea" size="30" minlength="10" maxlength="80"></textarea>
    <br>
    <br>
    <label for="myTextArea3">Providing default options</label>
<textarea id="myTextArea3" name="myTextArea"><EMAIL></textarea>
    <br>
    <br>
    <label for="myTextArea4">Autocomplete= ON</label>
<textarea id="myTextArea4" name="myTextArea" autocomplete="on"></textarea>
    <br>
    <br>
    <label for="myTextArea5">Autocomplete= OFF</label>
<textarea id="myTextArea5" name="myTextArea" autocomplete="off"></textarea>
    <br>
    <br>
    <label for="myTextArea6">Readonly Set</label>
<textarea id="myTextArea6" name="myTextArea" readonly></textarea>
    <br>
    <br>
    <label for="myTextArea7">Required Set</label>
<textarea id="myTextArea7" name="myTextArea" required></textarea>
    <br>
    <br>
    <label for="myTextArea8">Test Hover</label>
<textarea id="myTextArea8" name="myTextArea" onmouseover="this.style.color='red';"></textarea>
    <br>
    <br>
    <label for="myTextArea9">Test Focus</label>
<textarea id="myTextArea9" name="myTextArea" onfocus="this.style.color='blue';"></textarea>
    <br>
    <br>
    <label for="myTextArea10">Test Disabled</label>
<textarea id="myTextArea10" name="myTextArea" disabled></textarea>
    <br>
    <br>
    <label for="myTextArea11">Rows and Cols Set</label>
<textarea id="myTextArea11" name="myTextArea" rows="5" cols="50"></textarea>
    <br>
    <br>
    <label for="myTextArea12">SpellCheck</label>
<textarea id="myTextArea12" name="myTextArea" spellcheck></textarea>
    <br>
    <br>
    <label for="myTextArea13">Wrap</label>
<textarea id="myTextArea13" name="myTextArea" wrap="hard"></textarea>

    <script type="text/javascript" src="textArea.js"></script>
</body>
</html>