<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="checkBox.css">
</head>
<body>
    <br>
    <br>
    <label for="myCheckbox">Providing default options</label>
    <input type="checkbox" id="myCheckbox" name="subscribe" value="newsletter" checked>

    <br>
    <br>
    <label for="myCheckbox1">Test Hover</label>
    <input type="checkbox" id="myCheckbox1" checked onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myCheckbox2">Test Focus</label>
    <input type="checkbox" id="myCheckbox2" name="subscribe" value="newsletter" checked onfocus="this.style.color='blue';">

    <br>
    <br>
    <label for="myCheckbox3">Test IsDisabled</label>
    <input type="checkbox" id="myCheckbox3" name="subscribe" value="newsletter" disabled>

    <script type="text/javascript" src="checkBox.js"></script>
</body>
</html>