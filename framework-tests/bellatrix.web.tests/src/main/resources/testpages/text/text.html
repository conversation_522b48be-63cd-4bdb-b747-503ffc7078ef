<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="text.css">
</head>
<body>
    <label for="myText">Placeholders</label>
    <input id="myText" name="myText" type="Text" placeholder="your Text term goes here" class="myTestClass">

    <br>
    <br>
    <label for="myText1">Physical input element size</label>
    <input id="myText1" name="myText" type="Text" size="30">

    <br>
    <br>
    <label for="myText2">Element value length</label>
    <input id="myText2" name="myText" type="Text" size="30" minlength="10" maxlength="80">

    <br>
    <br>
    <label for="myText4">Autocomplete= ON</label>
    <input id="myText4" name="myText" type="Text" autocomplete="on">

    <br>
    <br>
    <label for="myText5">Autocomplete= OFF</label>
    <input id="myText5" name="myText" type="Text" autocomplete="off">

    <br>
    <br>
    <label for="myText6">Readonly Set</label>
    <input id="myText6" name="myText" type="Text" readonly>

    <br>
    <br>
    <label for="myText7">Required Set</label>
    <input id="myText7" name="myText" type="Text" required>

    <br>
    <br>
    <label for="myText8">Test Hover</label>
    <input id="myText8" name="myText" type="Text" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myText9">Test Focus</label>
    <input id="myText9" name="myText" type="Text" onfocus="this.style.color='blue';">

    <div id="myText10Wrapper">
        <br>
        <br>
        <label for="myText10">Test Disabled</label>
        <input id="myText10" name="myText" type="Text" disabled>
    </div>


    <script type="text/javascript" src="text.js"></script>
</body>
</html>