<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="password.css">
</head>
<body>
    <label for="myPassword">Placeholders</label>
    <input id="myPassword" name="myPassword" type="password" placeholder="your password term goes here" class="myTestClass">

    <br>
    <br>
    <label for="myPassword1">Physical input element size</label>
    <input id="myPassword1" name="myPassword" type="password" size="30">

    <br>
    <br>
    <label for="myPassword2">Element value length</label>
    <input id="myPassword2" name="myPassword" type="password" size="30" minlength="10" maxlength="80">

    <br>
    <br>
    <label for="myPassword3">Providing default options</label>
    <input id="myPassword3" name="myPassword" type="password" value="password for stars">

    <br>
    <br>
    <label for="myPassword4">Autocomplete= ON</label>
    <input id="myPassword4" name="myPassword" type="password" autocomplete="on">

    <br>
    <br>
    <label for="myPassword5">Autocomplete= OFF</label>
    <input id="myPassword5" name="myPassword" type="password" autocomplete="off">

    <br>
    <br>
    <label for="myPassword6">Readonly Set</label>
    <input id="myPassword6" name="myPassword" type="password" readonly>

    <br>
    <br>
    <label for="myPassword7">Required Set</label>
    <input id="myPassword7" name="myPassword" type="password" required>

    <br>
    <br>
    <label for="myPassword8">Test Hover</label>
    <input id="myPassword8" name="myPassword" type="password" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myPassword9">Test Focus</label>
    <input id="myPassword9" name="myPassword" type="password" onfocus="this.style.color='blue';">

    <div id="myPassword10Wrapper">
        <br>
        <br>
        <label for="myPassword10">Test Disabled</label>
        <input id="myPassword10" name="myPassword" type="password" disabled>
    </div>

    <script type="text/javascript" src="password.js"></script>
</body>
</html>