<!DOCTYPE html>
<html>
<head>
    <title>Table HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="table.css">
</head>
<body>
    <div class="example">
        <h3>Data Tables</h3>
        <p>Often times when you see a table it contains data which is sortable -- sometimes with actions that can be taken within each row (e.g. edit, delete). And it can be challenging to automate interaction with sets of data in a table depending on how it is constructed.</p>

        <h4>Example 1</h4>
        <p>No Class or ID attributes to signify groupings of rows and columns</p>
        <table id="table1" class="tablesorter">
            <thead>
                <tr>
                    <th><span>Last Name</span></th>
                    <th><span>First Name</span></th>
                    <th><span>Email</span></th>
                    <th><span>Due</span></th>
                    <th><span>Web Site</span></th>
                    <th><span>Action</span></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Smith</td>
                    <td>John</td>
                    <td><EMAIL></td>
                    <td>$50.00</td>
                    <td>http://www.jsmith.com</td>
                    <td>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td>Bach</td>
                    <td>Frank</td>
                    <td><EMAIL></td>
                    <td>$51.00</td>
                    <td>http://www.frank.com</td>
                    <td>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td>Doe</td>
                    <td>Jason</td>
                    <td><EMAIL></td>
                    <td>$100.00</td>
                    <td>http://www.jdoe.com</td>
                    <td>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td>Conway</td>
                    <td>Tim</td>
                    <td><EMAIL></td>
                    <td>$50.00</td>
                    <td>http://www.timconway.com</td>
                    <td>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
            </tbody>
        </table>

        <h4>Example 2</h4>
        <p>Class and ID attributes to signify groupings of rows and columns</p>
        <table id="table2" class="tablesorter">
            <thead>
                <tr>
                    <th><span class='last-name'>Last Name</span></th>
                    <th><span class='first-name'>First Name</span></th>
                    <th><span class='email'>Email</span></th>
                    <th><span class='dues'>Due</span></th>
                    <th><span class='web-site'>Web Site</span></th>
                    <th><span class='action'>Action</span></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class='last-name'>Smith</td>
                    <td class='first-name'>John</td>
                    <td class='email'><EMAIL></td>
                    <td class='dues'>$50.00</td>
                    <td class='web-site'>http://www.jsmith.com</td>
                    <td class='action'>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td class='last-name'>Bach</td>
                    <td class='first-name'>Frank</td>
                    <td class='email'><EMAIL></td>
                    <td class='dues'>$51.00</td>
                    <td class='web-site'>http://www.frank.com</td>
                    <td class='action'>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td class='last-name'>Doe</td>
                    <td class='first-name'>Jason</td>
                    <td class='email'><EMAIL></td>
                    <td class='dues'>$100.00</td>
                    <td class='web-site'>http://www.jdoe.com</td>
                    <td class='action'>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
                <tr>
                    <td class='last-name'>Conway</td>
                    <td class='first-name'>Tim</td>
                    <td class='email'><EMAIL></td>
                    <td class='dues'>$50.00</td>
                    <td class='web-site'>http://www.timconway.com</td>
                    <td class='action'>
                        <a href='#edit'>edit</a>
                        <a href='#delete'>delete</a>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="example">
        <h3>Basic Tables</h3>
        <p>Some tables dont have the HTML elements thead, tbody or tfoot. While designing locators this should be taken into consideration.</p>

        <h4>Example 1</h4>
        <p>Simple table with *tr* and *td* elements only</p>
        <table id="simpleTable">
            <tr>
                <td>Smith</td>
                <td>John</td>
                <td>$50.00</td>
                <td>http://www.jsmith.com</td>
            </tr>
            <tr>
                <td>Bach</td>
                <td>Frank</td>
                <td>$51.00</td>
                <td>http://www.frank.com</td>
            </tr>
            <tr>
                <td>Doe</td>
                <td>Jason</td>
                <td>$100.00</td>
                <td>http://www.jdoe.com</td>
            </tr>
            <tr>
                <td>Conway</td>
                <td>Tim</td>
                <td>$50.00</td>
                <td>http://www.timconway.com</td>
            </tr>
        </table>
    </div>
    <div class="example">
        <h3>Nested Tables</h3>
        <p>Some tables have nested tables in one or more of the table cells. This causes difficulties when trying to iterate over the table cells of a row, since only the direct children of the row have to be located.</p>

        <h4>Example 1</h4>
        <p>Single Inline table in table cell</p>
        <table id="nestedTable">
            <thead>
                <tr>
                    <th><span>Last Name</span></th>
                    <th><span>First Name</span></th>
                    <th><span>Email</span></th>
                    <th><span>Due</span></th>
                    <th><span>Web Site</span></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Smith</td>
                    <td>John</td>
                    <td>
                        <table>
                            <tr><td><b>Business</b></td><td><EMAIL></td></tr>
                            <tr><td><b>Personal</b></td><td><EMAIL></td></tr>
                        </table>
                    </td>
                    <td>$50.00</td>
                    <td>http://www.jsmith.com</td>
                </tr>
                <tr>
                    <td>Bach</td>
                    <td>Frank</td>
                    <td>
                        <table>
                            <tr><td><b>Business</b></td><td><EMAIL></td></tr>
                            <tr><td><b>Personal</b></td><td><EMAIL></td></tr>
                        </table>
                    </td>
                    <td>$51.00</td>
                    <td>http://www.frank.com</td>
                </tr>
                <tr>
                    <td>Doe</td>
                    <td>Jason</td>
                    <td>
                        <table>
                            <tr><td><b>Business</b></td><td></td></tr>
                            <tr><td><b>Personal</b></td><td><EMAIL></td></tr>
                        </table>
                    </td>
                    <td>$100.00</td>
                    <td>http://www.jdoe.com</td>
                </tr>
                <tr>
                    <td>Conway</td>
                    <td>Tim</td>
                    <td>
                        <table>
                            <tr><td><b>Business</b></td><td><EMAIL></td></tr>
                            <tr><td><b>Personal</b></td><td></td></tr>
                        </table>
                    </td>
                    <td>$50.00</td>
                    <td>http://www.timconway.com</td>
                </tr>
            </tbody>
        </table>
    </div>
    <script type="text/javascript" src="table.js"></script>
</body>
</html>