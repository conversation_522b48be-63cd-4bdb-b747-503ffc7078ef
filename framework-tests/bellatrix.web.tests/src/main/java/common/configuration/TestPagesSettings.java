package common.configuration;

import lombok.Data;

@Data
public class TestPagesSettings {
    private String anchorLocalPage;
    private String buttonLocalPage;
    private String checkBoxLocalPage;
    private String colorLocalPage;
    private String dateLocalPage;
    private String dateTimeLocalLocalPage;
    private String gridLocalPage;
    private String divLocalPage;
    private String elementLocalPage;
    private String emailLocalPage;
    private String headingLocalPage;
    private String imageLocalPage;
    private String labelLocalPage;
    private String monthLocalPage;
    private String numberLocalPage;
    private String optionLocalPage;
    private String outputLocalPage;
    private String passwordLocalPage;
    private String phoneLocalPage;
    private String progressLocalPage;
    private String radioLocalPage;
    private String rangeLocalPage;
    private String resetLocalPage;
    private String searchLocalPage;
    private String selectLocalPage;
    private String spanLocalPage;
    private String textAreaLocalPage;
    private String textFieldLocalPage;
    private String timeLocalPage;
    private String urlLocalPage;
    private String weekLocalPage;
    private String tableLocalPage;
    private String layoutPricingPage;
    private String shadowDomPage;
}