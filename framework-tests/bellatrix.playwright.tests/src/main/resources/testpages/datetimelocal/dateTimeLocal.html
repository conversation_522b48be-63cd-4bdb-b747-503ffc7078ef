<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="dateTimeLocal.css">
</head>
<body>
    <label for="myTime">Default</label>
    <input id="myTime" name="myTime" type="datetime-local" class="myTestClass">

    <br>
    <br>
    <label for="myTime1">Test Max Min Step</label>
    <input id="myTime1" name="myTime1" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30">

    <br>
    <br>
    <label for="myTime2">Default Value</label>
    <input id="myTime2" name="myTime2" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" value="2017-06-01T08:30">

    <br>
    <br>
    <label for="myTime3">Autocomplete= ON</label>
    <input id="myTime3" name="myTime3" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" autocomplete="on">

    <br>
    <br>
    <label for="myTime4">Autocomplete= OFF</label>
    <input id="myTime4" name="myTime4" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" autocomplete="off">

    <br>
    <br>
    <label for="myTime5">Readonly Set</label>
    <input id="myTime5" name="myTime5" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" readonly>

    <br>
    <br>
    <label for="myTime6">Required Set</label>
    <input id="myTime6" name="myTime6" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" required>

    <br>
    <br>
    <label for="myTime7">Test Hover</label>
    <input id="myTime7" name="myTime7" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myTime8">Test Focus</label>
    <input id="myTime8" name="myTime8" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:30" onfocus="this.style.color='blue';">

    <br>
    <br>
    <label for="myTime9">Disabled Set</label>
    <input id="myTime9" name="myTime9" type="datetime-local" step="10" min="2017-06-01T08:30" max="2017-06-30T16:302017-06-01T08:30" disabled>

    <script type="text/javascript" src="dateTimeLocal.js"></script>
</body>
</html>