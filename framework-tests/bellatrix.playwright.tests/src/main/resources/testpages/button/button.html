<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="button.css">
</head>
<body>
    <br>
    <br>
    <label for="myButton">Providing default options Input Button</label>
    <input type="button" id="myButton" name="start" value="Start">

    <br>
    <br>
    <label for="myButton1">Test Hover Input Button</label>
    <input type="button" id="myButton1" name="start" value="Start" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myButton2">Test Focus Input Button</label>
    <input type="button" id="myButton2" name="start" value="Start" onfocus="this.style.color='blue';">

    <br>
    <br>
    <label for="myButton3">Test IsDisabled Input Button</label>
    <input type="button" id="myButton3" name="start" value="Start" disabled>


    <br>
    <br>
    <label for="myButton4">Providing default options Input Submit</label>
    <input type="submit" id="myButton4" name="start" value="Start">

    <br>
    <br>
    <label for="myButton5">Test Hover Input Submit</label>
    <input type="submit" id="myButton5" name="start" value="Start" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myButton6">Test Focus Input Submit</label>
    <input type="submit" id="myButton6" name="start" value="Start" onfocus="this.style.color='blue';">

    <br>
    <br>
    <label for="myButton7">Test IsDisabled Input Submit</label>
    <input type="submit" id="myButton7" name="start" value="Start" disabled>

    <br>
    <br>
    <label for="myButton8">Providing default options Button</label>
    <button type="button" id="myButton8" name="start" value="Start">Start</button>

    <br>
    <br>
    <label for="myButton9">Test Hover Input Submit</label>
    <button type="button" id="myButton9" name="start" value="Start" onmouseover="this.style.color='red';">Start</button>

    <br>
    <br>
    <label for="myButton10">Test Focus Input Submit</label>
    <button type="button" id="myButton10" name="start" value="Start" onfocus="this.style.color='blue';">Start</button>

    <br>
    <br>
    <label for="myButton11">Test IsDisabled Input Submit</label>
    <button type="button" id="myButton11" name="start" value="Start" disabled>Start</button>



    <script type="text/javascript" src="button.js"></script>
</body>
</html>