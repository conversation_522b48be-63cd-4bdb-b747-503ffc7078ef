<!DOCTYPE html>
<html>
<head>
    <title>Url HTML Control Demo</title>
    <link rel="stylesheet" type="text/css" href="number.css">
</head>
<body>
    <label for="myNumber">Test Placeholders</label>
    <input id="myNumber" name="myNumber" type="number" placeholder="Multiple of 10" class="myTestClass">

    <br>
    <br>
    <label for="myNumber1">Test Max Min Step</label>
    <input id="myNumber1" name="myNumber1" type="number" step="2" min="10" max="20">

    <br>
    <br>
    <label for="myNumber2">Default Value</label>
    <input id="myNumber2" name="myNumber2" type="number" step="2" min="10" max="20" value="4">

    <br>
    <br>
    <label for="myNumber3">Autocomplete= ON</label>
    <input id="myNumber3" name="myNumber3" type="number" step="2" min="10" max="20" value="4" autocomplete="on">

    <br>
    <br>
    <label for="myNumber4">Autocomplete= OFF</label>
    <input id="myNumber4" name="myNumber4" type="number" step="2" min="10" max="20" value="4" autocomplete="off">

    <br>
    <br>
    <label for="myNumber5">Readonly Set</label>
    <input id="myNumber5" name="myNumber5" type="number" step="2" min="10" max="20" value="4" readonly>

    <br>
    <br>
    <label for="myNumber6">Required Set</label>
    <input id="myNumber6" name="myNumber6" type="number" step="2" min="10" max="20" value="4" required>

    <br>
    <br>
    <label for="myNumber7">Test Hover</label>
    <input id="myNumber7" name="myNumber7" type="number" step="2" min="10" max="20" value="4" onmouseover="this.style.color='red';">

    <br>
    <br>
    <label for="myNumber8">Test Focus</label>
    <input id="myNumber8" name="myNumber8" type="number" step="2" min="10" max="20" value="4" onfocus="this.style.color='blue';">

    <br>
    <br>
    <label for="myNumber9">Disabled Set</label>
    <input id="myNumber9" name="myNumber6" type="number" step="2" min="10" max="20" value="4" disabled>


    <script type="text/javascript" src="number.js"></script>
</body>
</html>