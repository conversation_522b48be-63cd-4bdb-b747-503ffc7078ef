{"troubleshootingSettings": {"debugInformationEnabled": "true"}, "desktopSettings": {"serviceUrl": "http://127.0.0.1:4722", "executionType": "regular", "defaultLifeCycle": "restart everytime time", "defaultAppPath": "${user.home}/WPFSampleApp.exe", "artificialDelayBeforeAction": "0", "automaticallyScrollToVisible": "false", "waitUntilReadyOnElementFound": "false", "screenshotsOnFailEnabled": "false", "screenshotsSaveLocation": "${user.home}/BELLATRIX/videos", "videosOnFailEnabled": "false", "videosSaveLocation": "${user.home}/BELLATRIX/videos", "timeoutSettings": {"implicitWaitTimeout": "5", "elementWaitTimeout": "30", "sleepInterval": "1", "waitForPartialUrl": "30", "validationsTimeout": "30", "elementToBeVisibleTimeout": "30", "elementToExistTimeout": "30", "elementToNotExistTimeout": "30", "elementToBeClickableTimeout": "30", "elementNotToBeVisibleTimeout": "30", "elementToHaveContentTimeout": "15"}, "gridSettings": [{"providerName": "saucelabs", "url": "http://ondemand.saucelabs.com:80/wd/hub", "arguments": [{"screenResolution": "1280x800", "recordVideo": "true", "recordScreenshots": "true", "username": "myUserName", "accessKey": "myPass", "name": "bellatrix_run"}]}, {"providerName": "grid", "url": "http://127.0.0.1:4444/wd/hub", "arguments": [{"name": "bellatrix_run"}]}]}}