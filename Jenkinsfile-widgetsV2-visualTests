pipeline {
    agent {
        node {
            label 'qa-win'
        }
    }
    environment {
        // Define COMMIT_HASH
        COMMIT_HASH=''
        CRON_SETTING=''
        WIDGETS_API_URL = 'https://widgets-integration.sportal365.com/metadata.json'
    }
    parameters {
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: true,
                description: 'Should execute tests, or just build the source.'
        )

        // Test Configuration
        choice(
                name: 'TESTS_FILTER',
                choices: [
                        '&visual',
                        '&football&visual',
                        '&tennis&visual',
                        '&basketball&visual',
                        '&icehockey&visual',
                        '&smoke'
                ],
                description: 'Select tests to execute'
        )

        // Environment Settings
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: [
                        'integration',
                        'staging'
                ],
                description: 'Environment to execute tests. Requires EXECUTE_TESTS set to TRUE.'
        )

        choice(
                name: 'DEFAULT_BROWSER',
                choices: [
                        'chrome_headless',
                        'firefox_headless',
                        'edge_headless',
                        'chrome',
                        'firefox',
                        'edge'
                ],
                description: 'Browser type to use for tests execution.'
        )

        // Widget Versions - kept for override purposes
        string(
                name: 'WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override the widgets version. Leave empty to use the version from API.'
        )

        // Notifications
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#test-executions',
                        '#platform-reports',
                        '#qa-automation'
                ],
              description: '''Slack channel for test results:
                 - #platform-reports: Platform test reports
                 - #qa-automation: Automation framework updates
                 - #test-executions: General test execution updates'''
        )
        booleanParam(name: 'AI_ANALYSIS', defaultValue: true, description: 'Add AI analysis to the results posting.')
    }
    stages {
        stage('Print Commit Hash') {
            when {
                expression {
                    return params.COMMIT_HASH != 'No Commit Hash Provided'
                }
            }
            steps {
                // Waiting for the CMS to be deployed and display the commit hash
                script {
                    echo "The commit hash is: ${params.COMMIT_HASH}"
                    echo 'Waiting for 1 minutes before triggering the pipeline...'
                    sleep(time: 1, unit: 'MINUTES')
                }
            }
        }

        stage('Set Widget Version') {
            steps {
                script {
                    if (params.WIDGETS_VERSION?.trim()) {
                        // Use parameter version if provided
                        env.FINAL_WIDGETS_VERSION = params.WIDGETS_VERSION
                        echo "Using provided widgets version: ${env.FINAL_WIDGETS_VERSION}"
                    } else {
                        // Fetch version from API
                        try {
                            def response = sh(script: """
                                curl -s ${WIDGETS_API_URL}
                            """, returnStdout: true).trim()

                            def metadata = readJSON text: response
                            env.FINAL_WIDGETS_VERSION = metadata.widgets.latest_version ?: '8.3.0'

                            echo "Fetched widgets version from API: ${env.FINAL_WIDGETS_VERSION}"
                        } catch (Exception e) {
                            echo "Failed to fetch widget version: ${e.getMessage()}"
                            echo "Using fallback version 8.3.0"
                            env.FINAL_WIDGETS_VERSION = '8.3.0'
                        }
                    }
                }
            }
        }

        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl ui-tests-widgets-v2 -am'
            }
        }

        stage('Tests Execution') {
            steps {
                slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#FFA500', message: "Testing has started :rocket:\n '${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                bat """mvn test -pl ui-tests-widgets-v2 -am -Dsurefire.failIfNoSpecifiedTests=false -Dgroups="widgetsV2${TESTS_FILTER}" -DBUILD_TAG="${BUILD_TAG}" -Denvironment=${EXECUTION_ENVIRONMENT} -DfootballWidgetsVersion=${env.FINAL_WIDGETS_VERSION} -DbasketballWidgetsVersion=${env.FINAL_WIDGETS_VERSION} -DtennisWidgetsVersion=${env.FINAL_WIDGETS_VERSION} -DiceHockeyWidgetsVersion=${env.FINAL_WIDGETS_VERSION}"""
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        env.TEST_SUMMARY = processTestResults()
                    }
                    allure([
                            includeProperties: false,
                            jdk: '',
                            properties: [],
                            reportBuildPolicy: 'ALWAYS',
                            results: [[path: '**/allure-results']]
                    ])
                    zip zipFile: './system-tests-cms/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./system-tests-cms/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n  ${env.TEST_SUMMARY}\n More info at: ${env.BUILD_URL}")
                }
                failure {
                    script {
                        env.TEST_SUMMARY_AI = pipelineUtils.analyzeAllureResultsUsingAI(params.AI_ANALYSIS)
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\n ${env.TEST_SUMMARY_AI}\n More info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}

def processTestResults() {
    def testResults = junit '**/target/surefire-reports/*.xml'
    def testSummary = "Test Results:\n"
    testResults?.each { suite ->
        testSummary += "${suite.totalCount} tests were run\n"
        testSummary += "  Passed: ${suite.passCount}\n"
        testSummary += "  Failed: ${suite.failCount}\n"
        testSummary += "  Skipped: ${suite.skipCount}\n"
    }
    return testSummary
}
