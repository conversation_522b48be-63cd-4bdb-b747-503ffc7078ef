pipeline {
    agent  {
    	node {
          label 'qa-win'
	    }
    }
    environment {
            // Define COMMIT_HASH
            COMMIT_HASH=''
    }
    parameters {
        // Define a string parameter for COMMIT_HASH
        string(
                name: 'COMMIT_HASH',
                defaultValue: 'No Commit Hash Provided',
                description: '''Git commit hash for tracking specific build versions.
                Leave as default for regular test runs.
                Example: 2ab342f'''
        )
        string(
                name: 'TESTS_FILTER',
                defaultValue: '',
                description: '''Additional test filters to combine with the default "api" filter.
                Use & for AND, | for OR operations.
                Example: "&schemaTests" or "|regressionTests"'''
        )
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: ['staging', 'integration'],
                description: '''Test execution environment:
                - staging: For pre-production testing
                - integration: For development testing'''
        )
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: false,
                description: '''Controls test execution:
                - true: Run tests after build
                - false: Build only, skip tests
                Note: Tests will still run if COMMIT_HASH is provided'''
        )
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
                description: '''Slack channel for test results:
                - #platform-reports: Platform test reports
                - #qa-automation: Automation framework updates
                - #test-executions: General test execution updates'''
        )
    }
    stages {
        stage('Print Commit Hash') {
            when {
                expression {
                    return params.COMMIT_HASH != 'No Commit Hash Provided'
                }
            }
            steps {
                // Waiting for the CMS to be deployed and display the commit hash
                script {
                    echo "The commit hash is: ${params.COMMIT_HASH}"
                    echo 'Waiting for 25 minutes before triggering the pipeline...'
                    sleep(time: 25, unit: 'MINUTES')
                }
            }
        }

        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl api-tests-backend-sports -am'
            }
        }
        stage('Test') {
            when {
                expression {
                    return params.EXECUTE_TESTS
                }
            }
            steps {
                slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#FFA500', message: "Testing has started :rocket:\n '${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                bat """mvn test -pl api-tests-backend-sports -am -Dsurefire.failIfNoSpecifiedTests=false -Dgroups="api${TESTS_FILTER}" -Denvironment=${EXECUTION_ENVIRONMENT}"""
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        env.TEST_SUMMARY = processTestResults()
                    }
                    allure([
                        includeProperties: false, 
                        jdk: '', 
                        properties: [], 
                        reportBuildPolicy: 'ALWAYS', 
                        results: [[path: '**/target/allure-results']]
                    ])
                    zip zipFile: './system-tests-cms/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./api-tests-backend-sports/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\nMore info at: ${env.BUILD_URL}")
                }
                failure {
                    script {
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\nMore info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}

def processTestResults() {
    def testResults = junit '**/target/surefire-reports/*.xml'
    def testSummary = "Test Results:\n"
    testResults?.each { suite ->
        testSummary += "${suite.totalCount} tests were run\n"
        testSummary += "  Passed: ${suite.passCount}\n"
        testSummary += "  Failed: ${suite.failCount}\n"
        testSummary += "  Skipped: ${suite.skipCount}\n"
    }
    return testSummary
}
