def pipelineUtils

pipeline {
    agent {
        node {
            label 'qa-win'
        }
    }
    parameters {
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: true,
                description: 'Should execute tests, or just build the source.'
        )

        // Environment Settings
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: [
                        'integration',
                        'staging'
                ],
                description: 'Environment to execute tests. Requires EXECUTE_TESTS set to TRUE.'
        )

        choice(
                name: 'DEFAULT_BROWSER',
                choices: [
                        'chrome_headless',
                        'firefox_headless',
                        'edge_headless',
                        'chrome',
                        'firefox',
                        'edge'
                ],
                description: 'Browser type to use for tests execution.'
        )

        // Widget Versions - kept for override purposes
        string(
                name: 'WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override the widgets version. Leave empty to use the version from API.'
        )

        // Notifications
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
               description: '''Slack channel for test results:
                  - #platform-reports: Platform test reports
                  - #qa-automation: Automation framework updates
                  - #test-executions: General test execution updates'''
        )
        booleanParam(name: 'AI_ANALYSIS', defaultValue: true, description: 'Add AI analysis to the results posting.')
    }

    environment {
        WIDGETS_API_URL = 'https://widgets-integration.sportal365.com/metadata.json'
    }

    stages {
        stage('Fetch Widget Version') {
            steps {
                script {
                    try {
                        // Using curl to fetch version and storing it in environment variable
                        def response = sh(script: """
                            curl -s ${WIDGETS_API_URL}
                        """, returnStdout: true).trim()

                        // Parse JSON response and set environment variable
                        def metadata = readJSON text: response
                        def widgetVersion = metadata.widgets.latest_version ?: '8.3.0'    // Fallback version if API fails

                        // Set the widget version environment variable
                        env.WIDGETS_VERSION = widgetVersion

                        echo "Fetched widget version: ${env.WIDGETS_VERSION}"
                    } catch (Exception e) {
                        echo "Failed to fetch widget version: ${e.getMessage()}"
                        echo "Using fallback version 8.3.0"
                        env.WIDGETS_VERSION = '8.3.0'
                    }
                }
            }
        }
        stage('Setup') {
            steps {
                script {
                    // Load the shared functions
                    pipelineUtils = load('jenkins/vars/pipelineUtils.groovy')
                    pipelineUtils.setAIEnvironmentVariables()
                }
            }
        }
        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl ui-tests-widgets-v2 -am'
            }
        }

        stage('Test') {
            when {
                expression {
                    return params.EXECUTE_TESTS
                }
            }
            steps {
                script {
                    def parallelTests = [
                            'storybook': {
                                pipelineUtils.executeWidgetsTestsWithRetry([
                                        groups: 'storybook&visual',
                                        environment: EXECUTION_ENVIRONMENT,
                                        widgetsVersion: env.WIDGETS_VERSION,
                                        project: 'ui-tests-widgets-v2'
                                ])
                            }
                    ]
                    parallel parallelTests
                }
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                    }
                    allure([
                            includeProperties: false,
                            jdk: '',
                            properties: [],
                            reportBuildPolicy: 'ALWAYS',
                            results: [[path: '**/allure-results']]
                    ])
                    zip zipFile: './system-tests-cms/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./system-tests-cms/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    script {
                        def testResults = junit testResults: '**/target/surefire-reports/*.xml'
                        def testSummary = pipelineUtils.formatTestResults(testResults)
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\n More info at: ${env.BUILD_URL}")
                    }
                }
                failure {
                    script {
                        env.TEST_SUMMARY_AI = pipelineUtils.analyzeAllureResultsUsingAI(params.AI_ANALYSIS)
                        def testResults = junit testResults: '**/target/surefire-reports/*.xml'
                        def testSummary = pipelineUtils.formatTestResults(testResults)
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\n ${env.TEST_SUMMARY_AI}\n More info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}
