<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Automate The Planet Ltd.
  ~ Author: <PERSON>
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ You may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>bellatrix</artifactId>
        <groupId>solutions.bellatrix</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>bellatrix.android</artifactId>
    <version>1.0</version>

    <dependencies>
        <dependency>
            <groupId>solutions.bellatrix</groupId>
            <artifactId>bellatrix.core</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>solutions.bellatrix</groupId>
            <artifactId>bellatrix.layout</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>solutions.bellatrix</groupId>
            <artifactId>bellatrix.plugins.video</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>solutions.bellatrix</groupId>
            <artifactId>bellatrix.plugins.screenshots</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>solutions.bellatrix</groupId>
            <artifactId>bellatrix.web</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>