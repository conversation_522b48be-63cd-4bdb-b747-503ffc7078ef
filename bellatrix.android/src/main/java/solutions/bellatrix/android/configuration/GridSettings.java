/*
 * Copyright 2022 Automate The Planet Ltd.
 * Author: <PERSON>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * You may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package solutions.bellatrix.android.configuration;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;

public class GridSettings {
    @Getter @Setter private String providerName;
    @Getter @Setter private String optionsName;
    @Getter @Setter private String url;
    @Getter @Setter private List<HashMap<String, String>> arguments;
}
