/*
 * Copyright 2022 Automate The Planet Ltd.
 * Author: <PERSON>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * You may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package solutions.bellatrix.android.components;

import solutions.bellatrix.android.components.contracts.ComponentDisabled;
import solutions.bellatrix.android.components.contracts.ComponentOn;
import solutions.bellatrix.android.components.contracts.ComponentText;
import solutions.bellatrix.core.plugins.EventListener;

public class Switch extends AndroidComponent implements ComponentDisabled, ComponentOn, ComponentText {
    public final static EventListener<ComponentActionEventArgs> TURNING_ON = new EventListener<>();
    public final static EventListener<ComponentActionEventArgs> TURNED_ON = new EventListener<>();
    public final static EventListener<ComponentActionEventArgs> TURNING_OFF = new EventListener<>();
    public final static EventListener<ComponentActionEventArgs> TURNED_OFF = new EventListener<>();

    @Override
    public Class<?> getComponentClass() {
        return getClass();
    }

    public void turnOn() {
        defaultCheck(TURNING_ON, TURNED_ON);
    }

    public void turnOff() {
        defaultUncheck(TURNING_OFF, TURNED_OFF);
    }

    @Override
    public boolean isOn() {
        return defaultGetCheckedAttribute();
    }

    @Override
    public boolean isDisabled() {
        return false;
    }

    @Override
    public String getText() {
        return defaultGetText();
    }
}
