package facades;

import data.constants.*;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.constants.enums.football.FootballMatchStatus;
import data.models.basketball.competition.CompetitionModel;
import data.models.basketball.game.GameModel;
import data.models.basketball.season.SeasonDetailsModel;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballSingleEventBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballStandingsBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballTeamProgrammeBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballTournamentProgrammeBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.searchapi.ResultModel;
import factories.blockies.basketball.BasketballLivescoreBlockyFieldsFactory;
import factories.blockies.basketball.BasketballSingleEventBlockyFieldsFactory;
import factories.blockies.basketball.BasketballTeamProgrammeBlockyFieldsFactory;
import factories.blockies.basketball.BasketballTournamentProgrammeBlockyFieldsFactory;
import plugins.authentication.Project;
import repositories.basketball.BasketballCompetitionsHttpRepository;
import repositories.basketball.BasketballGamesHttpRepository;
import repositories.basketball.BasketballSeasonsHttpRepository;
import repositories.basketball.BasketballTeamsHttpRepository;
import repositories.core.SportApiHttpRepository;
import repositories.searchapiV2.SearchV2EventsHttpRepository;
import repositories.searchapiV2.SearchV2SuggestHttpRepository;
import services.AuthenticationService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static data.constants.StringConstants.*;

public class BasketballApiFacade {

    private final Map<String, Object> defaultQueryParams;
    private final BasketballTeamsHttpRepository basketballTeamsHttpRepo;
    private final BasketballGamesHttpRepository basketballGamesHttpRepo;
    private final BasketballCompetitionsHttpRepository basketballCompetitionsHttpRepo;
    private final BasketballSeasonsHttpRepository basketballSeasonsRepo;
    private final SearchV2EventsHttpRepository searchEventsHttpRepo;
    private final SearchV2SuggestHttpRepository searchSuggestHttpRepo;
    private final OddsApiFacade oddsApiFacade;

    public BasketballApiFacade() {
        this(AuthenticationService.CurrentProject.INSTANCE.getProject());
    }

    public BasketballApiFacade(Project project) {
        defaultQueryParams = new HashMap<>(Map.of(OFFSET_STRING, 0, LIMIT_STRING, 100));
        basketballTeamsHttpRepo = new BasketballTeamsHttpRepository(project);
        basketballGamesHttpRepo = new BasketballGamesHttpRepository(project);
        basketballCompetitionsHttpRepo = new BasketballCompetitionsHttpRepository(project);
        basketballSeasonsRepo = new BasketballSeasonsHttpRepository(project);
        searchEventsHttpRepo = new SearchV2EventsHttpRepository(project);
        searchSuggestHttpRepo = new SearchV2SuggestHttpRepository(project);
        oddsApiFacade = new OddsApiFacade(project);
    }

    public BasketballSingleEventBlockyFieldsModel getSingleEventBlockySportData(EventStatusType eventStatus, String... teamNames) {
        List<ResultModel> teams = Arrays.stream(teamNames).toList().stream().map(t -> searchSuggestHttpRepo.getTeamsByName(t, SupportedSports.BASKETBALL).get(0)).toList();
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, eventStatus.name());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.BASKETBALL.getValue());

        if (teamNames.length > 0) {
            queryParams.put(StringConstants.PARTICIPANTS_FILTER, String.join(",", teams.stream().map(ResultModel::getId).toList()));
        }

        Bookmaker bookmaker;
        if (eventStatus.equals(EventStatusType.NOT_STARTED)) {
            queryParams.put(StringConstants.SORT_DIRECTION_STRING, ASCENDING_STRING.toUpperCase());
            bookmaker = oddsApiFacade.getOdds(SupportedSports.BASKETBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        } else {
            bookmaker = null;
        }

        List<ResultModel> eventsResult = searchEventsHttpRepo.getAll(queryParams).getResult();

        if (eventsResult.isEmpty()) {
            throw new RuntimeException("No events found in the future.");
        } else {
            ResultModel event =
                    eventsResult.stream().filter(
                                    e -> e.getTranslations() != null
                                            && !e.getTranslations().isEmpty())
                            .findFirst().get();
            var eventTeams =
                    event.getParticipantDetails().stream().map(
                                    x -> searchSuggestHttpRepo.getByIds(x.getParticipant().getId(), SupportedSports.BASKETBALL).stream().findFirst().get())
                            .toList();
            return BasketballSingleEventBlockyFieldsFactory.buildDynamicFieldsOnly(eventTeams.get(0), eventTeams.get(1), event, bookmaker);
        }
    }

    public LivescoreBlockyFieldsModel getLivescoreBlockySportData(LocalDate date) {
        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.BASKETBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return BasketballLivescoreBlockyFieldsFactory.buildDynamicFieldsOnly(date, true, bookmaker);
    }

    public BasketballStandingsBlockyFieldsModel getStandingsSportData(BasketballCompetitionName competition) {
        var blockyData = BasketballStandingsBlockyFieldsModel.builder().build();
        var allCompetitions = basketballCompetitionsHttpRepo.getAll(SportApiHttpRepository.DEFAULT_QUERY_PARAMS).getResult();
        var firstCompetition = allCompetitions.stream().filter(x -> x.getName().equals(competition.getName())).findFirst().get();
        var season = basketballSeasonsRepo.getSeasonsByCompetition(firstCompetition.getId()).getResult().get(0);
        var seasonDetails = basketballSeasonsRepo.getSeasonsDetails(season.getId()).getResult();

        blockyData.setCompetition(firstCompetition);
        blockyData.setSeason(seasonDetails);
        blockyData.setStage(seasonDetails.getStages().get(0));
        return blockyData;
    }

    public BasketballTeamProgrammeBlockyFieldsModel getTeamProgrammeSportData(BasketballTeamEnum basketballTeamEnum, StatusEnum... seasonStatus) {
        ResultModel team = searchSuggestHttpRepo.getTeamsByName(basketballTeamEnum.getName(), SupportedSports.BASKETBALL).get(0);

        SeasonDetailsModel season = basketballSeasonsRepo.getSeasonsByTeam(team.getId()).getResult().stream()
                .filter(s -> seasonStatus.length == 0 || s.getStatus().equals(seasonStatus[0].name()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException(
                        seasonStatus.length > 0
                                ? "No season found with status: " + seasonStatus[0].name()
                                : "No seasons found for team"
                ));

        var seasonDetails = basketballSeasonsRepo.getSeasonsDetails(season.getId()).getResult();
        var stage = seasonDetails.getStages().get(0);
        var rounds = stage.getRounds();

        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.BASKETBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return BasketballTeamProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(team, season, stage, rounds, bookmaker);
    }

    public BasketballTournamentProgrammeBlockyFieldsModel getTournamentProgrammeSportData(BasketballCompetitionName basketballCompetition) {
        var allCompetitions = basketballCompetitionsHttpRepo.getAll().getResult();
        CompetitionModel competition = allCompetitions.stream().filter(x -> x.getName().toLowerCase().contains(basketballCompetition.getName().toLowerCase())).findFirst().get();
        var season = basketballSeasonsRepo.getSeasonsByCompetition(competition.getId()).getResult().get(0);
        var seasonDetails = basketballSeasonsRepo.getSeasonsDetails(season.getId()).getResult();
        var stage = seasonDetails.getStages().get(0);
        var rounds = stage.getRounds();

        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.BASKETBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return BasketballTournamentProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(competition, season, stage, rounds, bookmaker);
    }

    public List<GameModel> getBasketballEventsWithOdds(String... oddClient) {
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.FROM_GAME_TIME_STRING, LocalDateTime.now().format(DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'")));
        queryParams.put(StringConstants.TO_GAME_TIME_STRING, LocalDateTime.now().plusDays(2).format(DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'")));
        queryParams.put(StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED.name());

        if (oddClient.length > 0) {
            queryParams.put(StringConstants.ODD_CLIENT_STRING, oddClient[0]);
        }

        List<GameModel> matchesWithOdds = basketballGamesHttpRepo.getAll(queryParams).getResult()
                .stream()
                .filter(event -> !event.getOdds().isEmpty())
                .collect(Collectors.toList());

        if (matchesWithOdds.isEmpty()) {
            throw new RuntimeException("No basketball matches with odds found.");
        }

        return matchesWithOdds;
    }
}