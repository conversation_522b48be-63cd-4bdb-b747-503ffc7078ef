package facades;

import data.constants.*;
import data.constants.enums.CustomEntityEnum;
import data.constants.enums.football.FootballEntity;
import data.constants.enums.football.FootballTournamentEnum;
import data.constants.video.VideoType;
import data.models.articles.ArticleModel;
import data.models.articles.ArticleResponseModel;
import data.models.articles.MainMediaItem;
import data.models.authors.AuthorModel;
import data.models.banners.BannerModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.galleries.GalleryRequestModel;
import data.models.galleries.GalleryResponseModel;
import data.models.images.ImageModel;
import data.models.images.ImagesModel;
import data.models.lists.ListModel;
import data.models.related.DataListObject;
import data.models.related.RelatedModel;
import data.models.searchapi.ResultModel;
import data.models.tags.TagModel;
import data.models.tags.TagResponseModel;
import data.models.videos.VideoRequestModel;
import data.models.videos.VideoResponseModel;
import data.models.wikipages.WikiModel;
import data.models.wikipages.WikiResponseModel;
import data.utils.DateUtils;
import data.widgets.options.enums.StatusTypeEnum;
import factories.articles.ArticlesHttpFactory;
import factories.articles.ArticlesRelatedHttpFactory;
import factories.authors.AuthorHttpFactory;
import factories.banners.BannerHttpFactory;
import factories.categories.CategoryHttpFactory;
import factories.distribution.DistributionChannelsHttpFactory;
import factories.galleries.GalleryHttpFactory;
import factories.images.ImagesHttpFactory;
import factories.lists.ListHttpFactory;
import factories.tags.TagHttpFactory;
import factories.video.VideoHttpFactory;
import factories.wikipages.WikiPagesHttpFactory;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.content.*;
import repositories.core.ApiResponse;
import repositories.football.v2.FootballCoachesV2HttpRepository;
import repositories.searchapiV2.SearchV2SuggestHttpRepository;
import services.AuthenticationService;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static data.constants.video.VideoType.REGULAR;

public class ContentApiFacade {

    private final Project project;
    private final ArticlesHttpRepository articlesHttpRepo;
    private final BannerHttpRepository bannerHttpRepo;
    private final VideoHttpRepository videoHttpRepo;
    private final WikiHttpRepository wikiHttpRepo;
    private final ArticlesOptionalDataRelatedHttpRepository articlesOptionalDataRelatedHttpRepo;
    private final CategoriesHttpRepository categoriesHttpRepo;
    private final ArticlesV2HttpRepository articlesV2HttpRepo;
    private final TagHttpRepository tagHttpRepo;
    private final ListHttpRepository listHttpRepo;
    private final GalleriesHttpRepository galleriesHttpRepo;
    private final AuthorsHttpRepository authorsHttpRepo;
    private final GalleriesV2HttpRepository galleriesV2HttpRepo;
    private final DistributionRegionsHttpRepository distributionRegionsHttpRepo;
    private final DistributionChannelsHttpRepository distributionChannelsHttpRepo;
    private final ImagesHttpRepository imagesHttpRepo;
    private final FootballApiFacade footballApiFacade;
    private final VideoV2HttpRepository videoV2HttpRepo;
    private final SearchV2SuggestHttpRepository searchV2SuggestHttpRepo;
    protected FootballCoachesV2HttpRepository footballCoachesV2HttpRepo;
    protected AdminsHttpRepository adminsHttpRepo;

    public ContentApiFacade() {
        this(AuthenticationService.CurrentProject.INSTANCE.getProject());
    }

    public ContentApiFacade(Project project) {
        this.project = project;
        articlesHttpRepo = new ArticlesHttpRepository(project);
        bannerHttpRepo = new BannerHttpRepository(project);
        videoHttpRepo = new VideoHttpRepository(project);
        wikiHttpRepo = new WikiHttpRepository(project);
        categoriesHttpRepo = new CategoriesHttpRepository(project);
        articlesV2HttpRepo = new ArticlesV2HttpRepository(project);
        tagHttpRepo = new TagHttpRepository(project);
        listHttpRepo = new ListHttpRepository(project);
        galleriesHttpRepo = new GalleriesHttpRepository(project);
        authorsHttpRepo = new AuthorsHttpRepository(project);
        galleriesV2HttpRepo = new GalleriesV2HttpRepository(project);
        distributionRegionsHttpRepo = new DistributionRegionsHttpRepository(project);
        distributionChannelsHttpRepo = new DistributionChannelsHttpRepository(project);
        imagesHttpRepo = new ImagesHttpRepository(project);
        articlesOptionalDataRelatedHttpRepo = new ArticlesOptionalDataRelatedHttpRepository(project);
        footballApiFacade = new FootballApiFacade(project);
        footballCoachesV2HttpRepo = new FootballCoachesV2HttpRepository(project);
        searchV2SuggestHttpRepo = new SearchV2SuggestHttpRepository(project);
        videoV2HttpRepo = new VideoV2HttpRepository(project);
        adminsHttpRepo = new AdminsHttpRepository(project);
    }

    public void deleteArticle(ArticleResponseModel article) {
        if (article != null && article.getId() != null) {
            articlesHttpRepo.delete(article.getId());
        }
    }

    public void deleteArticle(ArticleModel article) {
        if (article != null) {
            articlesHttpRepo.delete(article.getId());
        }
    }

    public void deleteArticles(List<ArticleResponseModel> articles) {
        if (articles != null) {
            for (ArticleResponseModel article : articles) {
                deleteArticle(article);
            }
        }
    }

    public BannerModel createBanner() {
        BannerModel bannerToBeCreated = new BannerHttpFactory().buildDefaultBanner();
        ApiResponse<BannerModel> createdBannerResponse = bannerHttpRepo.create(bannerToBeCreated);
        createdBannerResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdBannerResponse.getResult();
    }

    public void deleteBanner(BannerModel banner) {
        if (banner != null && banner.getId() != null) {
            bannerHttpRepo.delete(banner.getId()).then().statusCode(HttpStatus.SC_OK);
        }
    }

    public void deleteVideo(VideoResponseModel video) {
        if (video != null && video.getId() != null) {
            videoHttpRepo.delete(video.getId()).then().statusCode(HttpStatus.SC_OK);
        }
    }

    public void deleteCategory(String... categoryId) {
        for (String id : categoryId) {
            if (id != null) {
                categoriesHttpRepo.delete(id).then().statusCode(HttpStatus.SC_OK);
            }
        }
    }

    public void deleteCategories(List<String> categoryNames) {
        if (categoryNames != null && !categoryNames.isEmpty()) {
            Set<String> targetNames = new HashSet<>(categoryNames);

            List<String> categoryIds = categoriesHttpRepo.getAll(Map.of("page", "1", "limit", "200"))
                    .getResult()
                    .stream()
                    .filter(category -> targetNames.stream().anyMatch(name -> category.getTitle().contains(name)))
                    .map(Category::getId)
                    .toList();

            for (String categoryId : categoryIds) {
                articlesV2HttpRepo.getAll(String.format("search?query=*&category=%s&language=en", categoryId));
                Long totalPages = articlesV2HttpRepo.getMetaData().getResult().getPagination().getTotalPages();

                while (totalPages > 0) {
                    articlesV2HttpRepo.getAll(String.format("search?query=*&category=%s&language=en&page=%d", categoryId, totalPages))
                            .getResult()
                            .stream()
                            .map(ArticleResponseModel::getId)
                            .forEach(articlesHttpRepo::delete);

                    totalPages--;
                }

                categoriesHttpRepo.delete(categoryId);
            }
        }
    }

    public TagResponseModel createTag() {
        TagHttpFactory tagHttpFactory = new TagHttpFactory();
        TagModel tagToBeCreated = tagHttpFactory.buildDefaultTag();
        return tagHttpRepo.create(tagToBeCreated).getResult();
    }

    public void deleteTag(String tagId) {
        if (tagId != null && !tagId.isEmpty()) {
            tagHttpRepo.delete(tagId).then().statusCode(HttpStatus.SC_OK);
        }
    }

    public void deleteList(String... listIds) {
        for (var id : listIds) {
            if (id != null) {
                listHttpRepo.delete(id);
            }
        }
    }

    public void deleteLists(List<ListModel> lists) {
        for (ListModel list : lists) {
            if (list != null && list.getId() != null) {
                deleteList(list.getId());
            }
        }
    }

    public GalleryResponseModel createGallery(String status, Instant... publishedAt) {
        String categoryId = getCategory(CategoryEnum.FOOTBALL.getName()).get(0).getId();
        String imageId = getImage(false).getId();
        GalleryRequestModel galleryToBeCreated = GalleryHttpFactory.buildDefaultGallery(imageId, categoryId);
        if (publishedAt.length > 0) {
            galleryToBeCreated.setPublishedAt("%s".formatted(publishedAt[0]));
        }
        galleryToBeCreated.setStatus(status);
        var createdGalleryResponse = galleriesHttpRepo.create(galleryToBeCreated);
        createdGalleryResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdGalleryResponse.getResult();
    }

    public GalleryResponseModel createGallery() {
        return createGallery(StringConstants.INACTIVE);
    }

    public List<GalleryResponseModel> createGalleries(int galleriesCount, String status) {
        List<GalleryResponseModel> galleries = new ArrayList<>();
        for (int i = 0; i < galleriesCount; i++) {
            Instant publishedAt = DateUtils.getCurrentUTC().minus(i, ChronoUnit.MINUTES);
            galleries.add(createGallery(status, publishedAt));
        }
        return galleries;
    }

    public void deleteGallery(GalleryResponseModel gallery) {
        if (gallery != null && gallery.getId() != null) {
            galleriesHttpRepo.delete(gallery.getId()).then().statusCode(HttpStatus.SC_OK);
        }
    }

    public void deleteGalleries(List<GalleryResponseModel> galleries) {
        for (GalleryResponseModel gallery : galleries) {
            deleteGallery(gallery);
        }
    }

    public void deleteAuthor(String authorId) {
        if (authorId != null) {
            authorsHttpRepo.delete(authorId).then().statusCode(HttpStatus.SC_OK);
        }
    }

    public void setProjectDefaultAuthor(String authorName) {
        var createdAuthor = authorsHttpRepo.getAll().getResult()
                .stream()
                .filter(author -> author.getName().equals(authorName))
                .findFirst()
                .orElseThrow();
        createdAuthor.setDefaultBoolean(true);
        authorsHttpRepo.update(createdAuthor);
    }

    public Set<String> getTitlesCategories() {
        List<Category> categories = categoriesHttpRepo.getAll(Map.of("page", "1", "limit", "200")).getResult();
        Set<String> expectedCategories = new LinkedHashSet<>();

        for (Category categoryModel : categories) {
            addCategoryWithStatus(expectedCategories, categoryModel);
            for (Category subCategory : categoryModel.getSubs()) {
                addCategoryWithStatus(expectedCategories, subCategory);
            }
        }

        return expectedCategories;
    }

    public List<Category> getCategories() {
        List<Category> categories = categoriesHttpRepo.getAll(Map.of("page", "1", "limit", "200")).getResult();

        List<Category> expectedCategories = new ArrayList<>();
        categories.stream()
                .filter(Category::getActive)
                .filter(c -> !c.getTitle().contains("E2E Auto Critical Functionality Title") && !c.getTitle().contains("AutoTest"))
                .forEach(c -> {
                    expectedCategories.add(c);
                    expectedCategories.addAll(c.getSubs());
                });

        return expectedCategories;
    }

    public List<Category> getCategoriesAssignedToUser(User user) {
        ApiResponse<List<Category>> categoriesForUserResponse = adminsHttpRepo.getCategoriesForUser(user);
        categoriesForUserResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return categoriesForUserResponse.getResult();
    }

    // Articles
    public ArticleResponseModel getCurrentArticle(String currentUrl) {
        String articleId = currentUrl.substring(currentUrl.lastIndexOf("/") + 1);
        var articleResponse = articlesHttpRepo.getById(articleId);
        return articleResponse.getResult();
    }

    public ArticleResponseModel createArticle(FootballEntity footballEntity, StatusEnum status, Instant... publishedAt) {
        if (status != StatusEnum.ACTIVE && status != StatusEnum.INACTIVE) {
            throw new IllegalArgumentException("Status should be ACTIVE or INACTIVE.");
        }

        var footballCategory = categoriesHttpRepo
                .search(SupportedSports.FOOTBALL.getTitle())
                .get(0);

        var defaultAuthor = authorsHttpRepo.getAll().getResult()
                .stream()
                .filter(AuthorModel::getDefaultBoolean).findFirst().orElseThrow();

        var articleToBeCreated = ArticlesHttpFactory.buildDefaultArticle();
        articleToBeCreated.setStatus(status.name().toLowerCase());
        articleToBeCreated.setCategoryId(footballCategory.getId());
        articleToBeCreated.setAuthors(List.of(defaultAuthor.getId()));
        if (publishedAt.length > 0) {
            articleToBeCreated.setPublishedAt("%s".formatted(publishedAt[0]));
        }

        var createdArticle = articlesHttpRepo.create(articleToBeCreated).getResult();
        createRelatedDataEntity(createdArticle, EntityTypeEnum.ARTICLES.name().toLowerCase(), footballEntity);

        return createdArticle;
    }

    public ArticleResponseModel createArticle(FootballEntity footballEntity) {
        return createArticle(footballEntity, StatusEnum.INACTIVE);
    }

    public ApiResponse<List<DataListObject>> createPlayerRelatedDataEntity(ArticleResponseModel createdArticle, EntityRelatedEnum entity, String... playerNames) {
        RelatedHttpRepository articleRelatedHttpRepo = new RelatedHttpRepository(project, createdArticle.getId(), entity.getValue());
        List<DataListObject> dataListObjects = new ArrayList<>();

        for (String playerName : playerNames) {
            ResultModel player = searchV2SuggestHttpRepo.getEntityByName(playerName, EntityTypeEnum.PLAYER);
            RelatedModel relatedModel = ArticlesRelatedHttpFactory.buildPlayerRelatedModel(player);
            if (relatedModel != null && relatedModel.getData() != null) {
                dataListObjects.addAll(relatedModel.getData());
            }
        }

        return articleRelatedHttpRepo.createAll(dataListObjects);
    }

    public ApiResponse<List<DataListObject>> createRelatedDataEntity(ArticleResponseModel createdArticle, String entityType, FootballEntity... footballEntities) {
        RelatedHttpRepository articleRelatedHttpRepo = new RelatedHttpRepository(project, createdArticle.getId(), entityType);
        ArticlesRelatedHttpFactory articlesRelatedHttpFactory = new ArticlesRelatedHttpFactory();
        ArrayList<DataListObject> dataListObjects = new ArrayList<>();

        for (FootballEntity footballEntity : footballEntities) {
            RelatedModel relatedModel = switch (footballEntity) {
                case PLAYER ->
                        articlesRelatedHttpFactory.buildPlayerRelatedModel(footballApiFacade.getTopScorersBlockySportData(FootballTournamentEnum.LA_LIGA));
                case TEAM -> articlesRelatedHttpFactory.buildTeamRelatedModel(footballApiFacade.getNotStartedMatch());
                case COACH -> articlesRelatedHttpFactory.buildCoachRelatedModel(footballApiFacade.getFootballCoach());
                case TOURNAMENT ->
                        articlesRelatedHttpFactory.buildTournamentRelatedModel(footballApiFacade.getNotStartedMatch());
                case MATCH ->
                        articlesRelatedHttpFactory.buildFootballMatchRelatedModel(footballApiFacade.getFinishedMatchFromTournament(FootballTournamentEnum.LA_LIGA));
                case VENUE -> articlesRelatedHttpFactory.buildVenueRelatedModel();
                case SEASON ->
                        articlesRelatedHttpFactory.buildFootballSeasonRelatedModel(footballApiFacade.getFinishedMatchFromTournament(FootballTournamentEnum.LA_LIGA));
                case ARTICLE -> articlesRelatedHttpFactory.buildArticleRelatedModel(getArticle());
                case VIDEO -> articlesRelatedHttpFactory.buildVideoRelatedModel(getVideo());
                case TAG ->
                        articlesRelatedHttpFactory.buildTagRelatedModel(getTag(StringConstants.DO_NOT_DELETE_STRING));
                case GALLERY -> articlesRelatedHttpFactory.buildGalleryRelatedModel(getGallery());
                case NONE -> articlesRelatedHttpFactory.buildEmptyModel();
                default -> throw new IllegalStateException("Unexpected value: " + footballEntity);
            };

            if (relatedModel != null && relatedModel.getData() != null) {
                dataListObjects.addAll(relatedModel.getData());
            }
        }

        return articleRelatedHttpRepo.createAll(dataListObjects);
    }

    public Category getActiveCategory() {
        return categoriesHttpRepo.getAll().getResult()
                .stream()
                .filter(Category::getActive)
                .findAny()
                .orElseThrow(() -> new RuntimeException("There are no active category in the project."));
    }

    public AuthorModel getDefaultAuthor() {
        return authorsHttpRepo.getAll().getResult()
                .stream()
                .filter(AuthorModel::getDefaultBoolean)
                .findAny()
                .orElseThrow(() -> new RuntimeException("Default author not found for content type." +
                        " Please ensure at least one author is marked as default."));
    }

    public AuthorModel getNonDefaultAuthor() {
        return authorsHttpRepo.getAll().getResult()
                .stream()
                .filter(author -> !author.getDefaultBoolean()).findAny()
                .orElseThrow();
    }

    public ArticleResponseModel createArticle() {
        return createArticle(FootballEntity.NONE);
    }

    public List<ArticleResponseModel> createArticles(int countOfArticles, StatusEnum status) {
        List<ArticleResponseModel> createdArticles = new ArrayList<>();
        for (int i = 0; i < countOfArticles; i++) {
            Instant publishedAt = DateUtils.getCurrentUTC().minus(i, ChronoUnit.MINUTES);
            createdArticles.add(createArticle(FootballEntity.NONE, status, publishedAt));
        }
        return createdArticles;
    }

    public List<ArticleResponseModel> searchArticlesByQuery(String query) {
        return articlesV2HttpRepo.search(query);
    }

    public GalleryResponseModel searchGallery(String query) {
        ApiResponse<List<GalleryResponseModel>> listApiResponse = galleriesV2HttpRepo.searchByString(query);
        listApiResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return listApiResponse.getResult().get(0);
    }

    public List<Category> getCategory(String categoryName) {
        return categoriesHttpRepo.searchByString(categoryName).getResult();
    }

    public AuthorModel getAuthor(String authorName) {
        return authorsHttpRepo.getAll().getResult()
                .stream().filter(e -> e.getName().contains(authorName)).findFirst().orElseThrow();
    }

    public CommonModel getDistributionRegion(String regionName) {
        return distributionRegionsHttpRepo.getAll().getResult()
                .stream().filter(e -> e.getName().contains(regionName)).findFirst().orElseThrow();
    }


    public CommonModel getDistributionRegion() {
        return distributionRegionsHttpRepo.getAll().getResult()
                .stream().findAny().orElseThrow(() -> new RuntimeException("There are no distribution regions in the project."));
    }

    public CommonModel getDistributionChannel(String regionChannel) {
        return distributionChannelsHttpRepo.getAll().getResult()
                .stream().filter(e -> e.getName().contains(regionChannel)).findFirst().orElseThrow();
    }

    public CommonModel getDistributionChannel() {
        return distributionChannelsHttpRepo.getAll().getResult()
                .stream().findAny().orElseThrow(() -> new RuntimeException("There are no distribution channels in the project."));
    }

    public CommonModel getOrigin(String originName, ContentTypeEnum contentType) {
        return new OriginHttpRepository(project, contentType.getValue()).getAll()
                .getResult()
                .stream()
                .filter(e -> e.getName().contains(originName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Origin: %s not found for content type: %s".formatted(originName, contentType)));
    }

    public CommonModel getOrigin(ContentTypeEnum contentType) {
        return new OriginHttpRepository(project, contentType.getValue()).getAll()
                .getResult()
                .stream()
                .findAny()
                .orElseThrow(() -> new RuntimeException("No origins found for content type: %s".formatted(contentType)));
    }

    public List<ArticleResponseModel> getArticlesByOriginName(String originName) {
        CommonModel origin = getOrigin(originName, ContentTypeEnum.ARTICLES);
        return getArticlesByOriginId(origin.getId());
    }

    public List<String> getOriginIds(ContentTypeEnum contentType, String... originNames) {
        List<CommonModel> origins = new OriginHttpRepository(project, contentType.getValue()).getAll().getResult();
        List<String> originIds = new ArrayList<>();
        for (String name : originNames) {
            CommonModel matchingOrigin = null;
            for (CommonModel origin : origins) {
                if (origin.getName().contains(name)) {
                    matchingOrigin = origin;
                    break;
                }
            }
            if (matchingOrigin == null) {
                throw new RuntimeException("Origin: %s not found for content type: %s".formatted(name, contentType));
            }
            originIds.add(matchingOrigin.getId());
        }
        return originIds;
    }

    public CommonModel getComment(String policy, ContentTypeEnum contentType) {
        return new CommentsPoliciesHttpRepository(project, contentType.getValue()).getAll()
                .getResult()
                .stream().
                filter(e -> e.getName().equals(policy))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Comment policy: %s not found for content type: %s".formatted(policy, contentType)));
    }

    public CommonModel getComment(ContentTypeEnum contentType) {
        return new CommentsPoliciesHttpRepository(project, contentType.getValue()).getAll()
                .getResult()
                .stream()
                .findAny()
                .orElseThrow(() -> new RuntimeException("No comment policies found for content type: %s".formatted(contentType)));
    }

    public void getRelatedWithOptionalDataQueryParamArticlesById(ArticleResponseModel createdArticle) {
        articlesOptionalDataRelatedHttpRepo.getById(createdArticle.getId());
    }

    public List<DataListObject> getAllRelatedContent(ArticleResponseModel createdArticle) {
        RelatedHttpRepository articlesRelatedHttpRepository = new RelatedHttpRepository(project, createdArticle.getId(), EntityTypeEnum.ARTICLES.name().toLowerCase());
        return articlesRelatedHttpRepository.getAll().getResult();
    }

    public void getAllV2RelatedContent(ArticleResponseModel createdArticle) {
        ArticleRelatedV2HttpRepository articlesRelatedHttpRepository = new ArticleRelatedV2HttpRepository(project, createdArticle.getId());
        articlesRelatedHttpRepository.get();
    }

    public VideoResponseModel createVideo(String status, Instant... publishedAt) {
        String categoryId = getCategory(CategoryEnum.FOOTBALL.getName()).get(0).getId();
        VideoRequestModel videoToBeCreated = VideoHttpFactory.buildVideo(categoryId);
        videoToBeCreated.setStatus(status);
        if (publishedAt.length > 0) {
            videoToBeCreated.setPublishedAt("%s".formatted(publishedAt[0]));
        }
        var createdVideoResponse = videoHttpRepo.create(videoToBeCreated);
        createdVideoResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdVideoResponse.getResult();
    }

    public VideoResponseModel createVideo() {
        return createVideo(StringConstants.INACTIVE);
    }

    public List<VideoResponseModel> createVideos(int videosCount, String status) {
        List<VideoResponseModel> videos = new ArrayList<>();
        for (int i = 0; i < videosCount; i++) {
            Instant publishedAt = DateUtils.getCurrentUTC().minus(i, ChronoUnit.MINUTES);
            videos.add(createVideo(status, publishedAt));
        }
        return videos;
    }

    public VideoResponseModel createEmbedVideo() {
        String categoryId = getCategory(CategoryEnum.FOOTBALL.getName()).get(0).getId();
        VideoRequestModel videoToBeCreated = VideoHttpFactory.buildDefaultEmbedVideo(categoryId);
        var createdVideoResponse = videoHttpRepo.create(videoToBeCreated);
        createdVideoResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdVideoResponse.getResult();
    }

    public VideoResponseModel createVideo(VideoType type) {
        VideoResponseModel video;
        if (type == REGULAR) {
            video = createVideo();
        } else {
            video = createEmbedVideo();
        }
        return video;
    }

    public void deleteVideo(String... videoId) {
        for (String id : videoId) {
            if (id != null) {
                videoHttpRepo.delete(id).then().statusCode(HttpStatus.SC_OK);
            }
        }
    }

    public void deleteVideos(List<VideoResponseModel> videos) {
        for (VideoResponseModel video : videos) {
            deleteVideo(video);
        }
    }

    public ApiResponse<List<VideoResponseModel>> searchVideo(String query) {
        var foundVideosList = videoHttpRepo.searchByString(query);
        foundVideosList.getResponse().then().statusCode(HttpStatus.SC_OK);
        return foundVideosList;
    }

    public WikiResponseModel createWiki(FootballEntity footballEntity, StatusEnum status, Instant... publishedAt) {
        if (status != StatusEnum.ACTIVE && status != StatusEnum.INACTIVE) {
            throw new IllegalArgumentException("Status should be ACTIVE or INACTIVE.");
        }

        AuthorModel defaultAuthor = authorsHttpRepo.getAll().getResult()
                .stream()
                .filter(AuthorModel::getDefaultBoolean).findFirst().orElseThrow();

        WikiModel wikiToBeCreated = WikiPagesHttpFactory.buildDefaultWiki();
        wikiToBeCreated.setAuthors(List.of(defaultAuthor.getId()));
        if (publishedAt.length > 0) {
            wikiToBeCreated.setPublishedAt("%s".formatted(publishedAt[0]));
        }
        return wikiHttpRepo.create(wikiToBeCreated).getResult();
    }

    public WikiResponseModel createWiki(FootballEntity footballEntity) {
        return createWiki(footballEntity, StatusEnum.INACTIVE);
    }

    public WikiResponseModel createWiki() {
        return createWiki(FootballEntity.NONE);
    }

    public void deleteWikiPage(WikiResponseModel wikiPage) {
        if (wikiPage != null && wikiPage.getId() != null) {
            wikiHttpRepo.delete(wikiPage.getId());
        }
    }

    public void deleteWikiPage(String... wikiId) {
        for (String id : wikiId) {
            if (id != null) {
                wikiHttpRepo.delete(id).then().statusCode(HttpStatus.SC_OK);
            }
        }
    }

    public Category createCategory() {
        Category categoryToBeCreated = CategoryHttpFactory.buildDefaultCategory();
        return categoriesHttpRepo.create(categoryToBeCreated).getResult();
    }

    public ListModel createList(ListContentType listContentType) {
        return createList(listContentType, 1, 10);
    }

    public ListModel createList(ListContentType listContentType, int minItems, int maxItems) {
        ListHttpFactory listHttpFactory = new ListHttpFactory();
        ListModel listToBeCreated = null;

        switch (listContentType) {
            case EDITORIAL:
                listToBeCreated = ListHttpFactory.buildEditorialListWithCategory(getActiveCategory());
                listToBeCreated.getConfiguration().setMinItems("%d".formatted(minItems));
                listToBeCreated.getConfiguration().setMaxItems("%d".formatted(maxItems));
                listToBeCreated.setStatus(StatusTypeEnum.ACTIVE.toString().toLowerCase());
                break;
            case SCHEDULED:
                listToBeCreated = listHttpFactory.buildScheduledListDefault();
                break;
            case TAG_SPORTS_CONNECTIONS:
                listToBeCreated = listHttpFactory.buildTagsAndSportsConnectionsListDefault();
                listToBeCreated.getConfiguration().setMinItems("%d".formatted(minItems));
                listToBeCreated.getConfiguration().setMaxItems("%d".formatted(maxItems));
                break;
        }
        ApiResponse<ListModel> createdListResponse = listHttpRepo.create(listToBeCreated);
        createdListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdListResponse.getResult();
    }

    public ListModel createList(ListModel list) {
        ApiResponse<ListModel> createdListResponse = listHttpRepo.create(list);
        createdListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdListResponse.getResult();
    }

    public AuthorModel createAuthor() {
        AuthorModel authorToBeCreated = AuthorHttpFactory.buildDefaultAuthor();
        var createdAuthorResponse = authorsHttpRepo.create(authorToBeCreated);
        createdAuthorResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createdAuthorResponse.getResult();
    }

    public ImageModel uploadImage() {
        ImagesModel image = ImagesHttpFactory.buildDefaultImage();
        var uploadedImageResponse = imagesHttpRepo.uploadImage(image);
        uploadedImageResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return uploadedImageResponse.getResult();
    }

    public ImageModel uploadImageWithWatermark() {
        ImageModel image = uploadImage();
        ImagesModel imagesModel = ImagesHttpFactory.buildWatermarkImage();
        var imageWithWatermarkResponse = imagesHttpRepo.addWatermarkToImage(image.getId(), imagesModel);
        imageWithWatermarkResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return imageWithWatermarkResponse.getResult();
    }

    public TagResponseModel getTag(String tagTitle) {
        return tagHttpRepo.search(tagTitle)
                .stream()
                .filter(t -> t.getTitle().contains(tagTitle))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No tag found with title %s.".formatted(tagTitle)));
    }

    public ArticleResponseModel getArticle() {
        return articlesV2HttpRepo.search(StringConstants.DO_NOT_DELETE_STRING)
                .stream()
                .filter(a -> a.getTitle().contains(StringConstants.DO_NOT_DELETE_STRING))
                .findAny()
                .orElseThrow(() -> new RuntimeException("No article found with title %s.".formatted(StringConstants.DO_NOT_DELETE_STRING)));
    }

    public List<ArticleResponseModel> getArticlesByOriginId(String originId) {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("originId", originId);
        articlesV2HttpRepo.searchByString("*", queryParams);
        Long totalPages = articlesV2HttpRepo.getMetaData().getResult().getPagination().getTotalPages();
        List<ArticleResponseModel> articles = new ArrayList<>();

        while (totalPages > 0) {
            articles.addAll(
                    articlesV2HttpRepo
                            .getAll(String.format("search?query=*&originId=%s&page=%d", originId, totalPages))
                            .getResult()
                            .stream()
                            .toList());

            totalPages--;
        }

        return articles;
    }

    public VideoResponseModel getVideo() {
        return videoV2HttpRepo.search(StringConstants.DO_NOT_DELETE_STRING).get(0);
    }

    public GalleryResponseModel getGallery() {
        return galleriesHttpRepo.getByTitle(StringConstants.DO_NOT_DELETE_STRING).getResult().get(0);
    }

    public ImageModel getImage(boolean hasWatermark) {
        int page = 1;

        while (true) {
            var listImagesResponse = imagesHttpRepo.getByPage(page);
            listImagesResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
            List<ImageModel> images = listImagesResponse.getResult();

            ImageModel image =
                    images.stream().filter(img -> hasWatermark == (img.getGeneric() != null))
                            .findAny().orElse(null);

            if (image != null) {
                return image;
            }

            page++;
        }
    }

    private void addCategoryWithStatus(Set<String> categorySet, Category category) {
        String title = category.getActive() ? category.getTitle() : category.getTitle() + " (Inactive)";
        categorySet.add(title);
    }

    public void addAllMediaDataToArticle(ArticleModel articleToBeCreated) {
        articleToBeCreated.getMainMedia().add(new MainMediaItem(getImage(false)));
        articleToBeCreated.getMainMedia().add(new MainMediaItem(createVideo()));
        articleToBeCreated.getMainMedia().add(new MainMediaItem(getGallery()));
        articleToBeCreated.getMainMedia().add(new MainMediaItem(createEmbedVideo()));
    }

    public CommonModel createDistributionChannel() {
        CommonModel requestBody = DistributionChannelsHttpFactory.buildDefaultDistributionChannel();
        ApiResponse<CommonModel> distributionChannelResponse = distributionChannelsHttpRepo.create(requestBody);
        distributionChannelResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return distributionChannelResponse.getResult();
    }

    public ApiResponse<List<DataListObject>> createPersonRelatedDataEntities(ArticleResponseModel createdArticle,
                                                                             EntityTypeEnum entityType,
                                                                             String... personNames) {
        return createEntityRelatedDataEntities(createdArticle, entityType, CustomEntityEnum.PERSON, personNames);
    }

    public ApiResponse<List<DataListObject>> createOrganizationRelatedDataEntities(ArticleResponseModel createdArticle,
                                                                                   EntityTypeEnum entityType,
                                                                                   String... organizationNames) {
        return createEntityRelatedDataEntities(createdArticle, entityType, CustomEntityEnum.ORGANIZATION, organizationNames);
    }

    private ApiResponse<List<DataListObject>> createEntityRelatedDataEntities(ArticleResponseModel createdArticle, EntityTypeEnum entityType,
                                                                              CustomEntityEnum customEntityType, String... entityNames) {
        List<DataListObject> dataListObjects = new ArrayList<>();
        for (String entityName : entityNames) {
            ResultModel entity = searchV2SuggestHttpRepo.getEntityByName(entityName, customEntityType);
            RelatedModel relatedModel = null;
            switch (customEntityType) {
                case PERSON -> relatedModel = ArticlesRelatedHttpFactory.buildPersonRelatedModel(entity);
                case ORGANIZATION -> relatedModel = ArticlesRelatedHttpFactory.buildOrganizationRelatedModel(entity);
            }
            if (relatedModel != null && relatedModel.getData() != null) {
                dataListObjects.addAll(relatedModel.getData());
            }
        }
        return new RelatedHttpRepository(project, createdArticle.getId(), entityType.getValue())
                .createAll(dataListObjects);
    }
}