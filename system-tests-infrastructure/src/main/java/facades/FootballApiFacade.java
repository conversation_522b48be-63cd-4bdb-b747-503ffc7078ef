package facades;

import data.constants.*;
import data.constants.api.queryparamenums.StatusType;
import data.constants.enums.football.FootballMatchStatus;
import data.constants.enums.football.FootballTeamEnum;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.articles.NestedMatch;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.blockymodels.football.*;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.common.Stage;
import data.models.footballapi.odd_providers.OddProviderModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.player.PlayerModel;
import data.models.footballapi.player.PlayerStatisticsModel;
import data.models.footballapi.stage.Standing;
import data.models.footballapi.teams.MatchesV2TeamModel;
import data.models.footballapi.teams.PlayersStatisticsSeasonsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.models.footballapi.v2.*;
import data.models.footballapi.v2.seasons.SeasonV2DetailsModel;
import data.models.liveblogapi.SportEvents;
import data.models.liveblogapi.Team;
import data.models.related.Tournament;
import data.models.searchapi.ResultModel;
import data.utils.NumberUtils;
import data.widgets.options.enums.FootballPlayerEnum;
import factories.blockies.football.*;
import factories.footballapi.MatchV2Factory;
import factories.footballapi.TeamsV2Factory;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.football.*;
import repositories.football.v2.*;
import repositories.searchapiV2.SearchV2EventsHttpRepository;
import repositories.searchapiV2.SearchV2SuggestHttpRepository;
import services.AuthenticationService;
import solutions.bellatrix.core.utilities.Log;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

public class FootballApiFacade {

    private final Map<String, Object> defaultQueryParams;
    private final FootballTeamsHttpRepository footballTeamsHttpRepo;
    private final FootballTeamsV2HttpRepository footballTeamsV2HttpRepo;
    private final FootballPlayersHttpRepository footballPlayersHttpRepo;
    private final FootballPlayersV2HttpRepository footballPlayersV2HttpRepo;
    private final FootballMatchesV2HttpRepository footballMatchesV2HttpRepo;
    private final FootballMatchesHttpRepository footballMatchesV1HttpRepo;
    private final SearchV2EventsHttpRepository searchEventsHttpRepo;
    private final SearchV2SuggestHttpRepository searchV2SuggestHttpRepo;
    private final FootballTournamentsHttpRepository footballTournamentsHttpRepo;
    private final FootballSeasonsHttpRepository footballSeasonsHttpRepo;
    private final FootballSeasonsV2HttpRepository footballSeasonsV2HttpRepo;
    private final FootballSeasonTopScorersHttpRepository footballTopScorersHttpRepo;
    private final FootballRoundsV2HttpRepository footballRoundsHttpRepo;
    private final FootballStatisticsPlayersHttpRepository footballStatisticsPlayersHttpRepo;
    private final FootballTournamentSeasonEventsHttpRepository footballTournamentSeasonEventsHttpRepo;
    private final FootballOddsProvidersHttpRepository footballOddsProvidersHttpRepo;
    private final OddsApiFacade oddsApiFacade;
    private final SecureRandom secureRandom;

    public FootballApiFacade() {
        this(AuthenticationService.CurrentProject.INSTANCE.getProject());
    }

    public FootballApiFacade(Project project) {
        defaultQueryParams = new HashMap<>(Map.of(StringConstants.OFFSET_STRING, 0, StringConstants.LIMIT_STRING, 100));
        footballTeamsHttpRepo = new FootballTeamsHttpRepository(project);
        footballPlayersV2HttpRepo = new FootballPlayersV2HttpRepository(project);
        footballTeamsV2HttpRepo = new FootballTeamsV2HttpRepository(project);
        footballPlayersHttpRepo = new FootballPlayersHttpRepository(project);
        footballMatchesV2HttpRepo = new FootballMatchesV2HttpRepository(project);
        footballMatchesV1HttpRepo = new FootballMatchesHttpRepository(project);
        searchEventsHttpRepo = new SearchV2EventsHttpRepository(project);
        searchV2SuggestHttpRepo = new SearchV2SuggestHttpRepository(project);
        footballTournamentsHttpRepo = new FootballTournamentsHttpRepository(project);
        footballSeasonsHttpRepo = new FootballSeasonsHttpRepository(project);
        footballSeasonsV2HttpRepo = new FootballSeasonsV2HttpRepository(project);
        footballTopScorersHttpRepo = new FootballSeasonTopScorersHttpRepository(project);
        footballRoundsHttpRepo = new FootballRoundsV2HttpRepository(project);
        footballStatisticsPlayersHttpRepo = new FootballStatisticsPlayersHttpRepository(project);
        footballTournamentSeasonEventsHttpRepo = new FootballTournamentSeasonEventsHttpRepository(project);
        footballOddsProvidersHttpRepo = new FootballOddsProvidersHttpRepository(project);
        oddsApiFacade = new OddsApiFacade(project);
        secureRandom = new SecureRandom();
    }

    public ApiResponse<MatchV2Model> createFinishedMatch(FootballTeamEnum homeTeamName, FootballTeamEnum awayTeamName) {
        String homeTeamId = footballTeamsHttpRepo.getAllByName(homeTeamName.getName()).get(0).getId();
        String awayTeamId = footballTeamsHttpRepo.getAllByName(awayTeamName.getName()).get(0).getId();
        Season season = getSeasonFrom(FootballTournamentEnum.SERIE_A, "2023/2024");

        CreateMatchV2Model notStartedMatch = MatchV2Factory.buildFinishedMatchRequestBody(homeTeamId, awayTeamId, season.getId());
        return footballMatchesV2HttpRepo.createMatch(notStartedMatch);
    }

    public ApiResponse<MatchV2Model> createNotStartedMatch(FootballTeamEnum homeTeamName, FootballTeamEnum awayTeamName,
                                                           FootballTournamentEnum footballTournament, String seasonName) {
        String homeTeamId = footballTeamsHttpRepo.getAllByName(homeTeamName.getName()).get(0).getId();
        String awayTeamId = footballTeamsHttpRepo.getAllByName(awayTeamName.getName()).get(0).getId();
        Season season = getSeasonFrom(footballTournament, seasonName);

        CreateMatchV2Model notStartedMatch = MatchV2Factory.buildNotStartedMatchRequestBody(homeTeamId, awayTeamId, season.getId());
        return footballMatchesV2HttpRepo.createMatch(notStartedMatch);
    }

    public MatchV2Model getNotStartedMatch() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String formattedDateTime = LocalDateTime.now().format(formatter);
        Map<String, Object> notStartedMatchesQuery = new HashMap<>(Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 100,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED,
                StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode(),
                StringConstants.FROM_KICKOFF_TIME_STRING, formattedDateTime,
                StringConstants.SORT_DIRECTION_STRING, StringConstants.ASCENDING_STRING));

        List<MatchV2Model> matches = footballMatchesV2HttpRepo.getAll(notStartedMatchesQuery).getResult().stream().toList();
        int randomNumberInRange = NumberUtils.getRandomNumberInRange(0, matches.size() - 1);
        Log.info("Random index selected: %s".formatted(randomNumberInRange));
        return matches.get(randomNumberInRange);
    }

    /**
     * This method is used exclusively for STATIC projects.
     */
    public MatchV2Model getNotStartedMatch(FootballTournamentEnum tournament) {
        Map<String, Object> notStartedMatchesQuery = Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 100,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED,
                StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode(),
                StringConstants.FROM_KICKOFF_TIME_STRING, "2024-07-20T11:59:52Z",
                StringConstants.TO_KICKOFF_TIME_STRING, "2024-12-1T11:59:52Z",
                StringConstants.TOURNAMENT_IDS_STRING, tournament.getId()
        );
        List<MatchV2Model> matches = footballMatchesV2HttpRepo.getAll(notStartedMatchesQuery).getResult();
        return matches.get(secureRandom.nextInt(0, matches.size() - 1));
    }

    public MatchV2Model getFinishedMatch() {
        Map<String, Object> finishedMatchesQuery = Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 100,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.FINISHED,
                StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode()
        );
        List<MatchV2Model> matches = footballMatchesV2HttpRepo.getAll(finishedMatchesQuery).getResult();
        return matches.get(secureRandom.nextInt(0, matches.size() - 1));
    }

    public MatchV2Model getFinishedMatchFromTournament(FootballTournamentEnum tournament) {
        Map<String, Object> finishedMatchesQuery = Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 10,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.FINISHED,
                StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode(),
                StringConstants.TOURNAMENT_IDS_STRING, tournament.getId()
        );
        List<MatchV2Model> matches = footballMatchesV2HttpRepo.getAll(finishedMatchesQuery).getResult();
        return matches.get(secureRandom.nextInt(0, matches.size() - 1));
    }

    public List<MatchV2Model> getNotStartedMatches(int matchesCount) {
        List<MatchV2Model> notStartedMatches = new ArrayList<>();
        for (int i = 0; i < matchesCount; i++) {
            notStartedMatches.add(getNotStartedMatch());
        }
        return notStartedMatches;
    }

    /**
     * Get not started matches with specific kickoff time range
     *
     * @param fromKickoffTime format: yyyy-MM-dd'T'HH:mm:ss'Z'
     * @param toKickoffTime   format: yyyy-MM-dd'T'HH:mm:ss'Z'
     * @return List of not started matches
     */
    public List<MatchV2Model> getNotStartedMatches(String fromKickoffTime, String toKickoffTime) {
        Map<String, Object> notStartedMatchesQuery = Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 100,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED,
                StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode(),
                StringConstants.FROM_KICKOFF_TIME_STRING, fromKickoffTime,
                StringConstants.TO_KICKOFF_TIME_STRING, toKickoffTime
        );
        return footballMatchesV2HttpRepo.getAll(notStartedMatchesQuery).getResult();
    }

    //TODO: navramov 02/23/2024 Method not tested, needs to be used in a test
    public MatchV2Model getNotStartedMatchWithOdds(FootballTeamEnum footballTeam) {
        Map<String, Object> notStartedMatchesQuery = Map.of(
                StringConstants.OFFSET_STRING, 0,
                StringConstants.LIMIT_STRING, 100,
                StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED,
                StringConstants.SORT_DIRECTION_STRING, StringConstants.ASCENDING_STRING,
                StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_STRING,
                StringConstants.TEAM_IDS_UNDERSCORED_STRING, footballTeam.getLegacyId()
        );
        List<MatchV2Model> matches = footballMatchesV2HttpRepo.getAll(notStartedMatchesQuery).getResult();
        return matches.stream().filter(match -> !match.getOdds().isEmpty())
                .findFirst().orElseThrow(() -> new RuntimeException("No matches with odds found."));
    }

    public ApiResponse<MatchV2Model> createFinishedMatch() {
        return createFinishedMatch(FootballTeamEnum.AC_MILAN, FootballTeamEnum.JUVENTUS);
    }

    public FootballSingleEventBlockyFieldsModel getSingleEventBlockySportData(String teamName, EventStatusType eventStatus) {
        ResultModel team = searchV2SuggestHttpRepo.getTeamsByName(teamName, SupportedSports.FOOTBALL).get(0);
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, eventStatus.name());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, team.getId());
        if (eventStatus == EventStatusType.NOT_STARTED) {
            queryParams.put(StringConstants.SORT_DIRECTION_STRING, SortDirectionEnum.ASCENDING.getStorybookValue().toUpperCase());
        }
        var eventsListResponse = searchEventsHttpRepo.getAll(queryParams);

        Bookmaker bookmaker;
        if (eventStatus.equals(EventStatusType.NOT_STARTED)) {
            bookmaker = oddsApiFacade.getOdds(SupportedSports.TENNIS, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        } else {
            bookmaker = null;
        }

        return FootballSingleEventBlockyFieldsFactory.buildDynamicFieldsOnly(team, eventsListResponse.getResult().get(0), bookmaker);
    }

    public FootballMatchCenterBlockyFieldsModel getFootballMatchCenterBlockySportData(String teamName, EventStatusType eventStatus) {
        ResultModel team = searchV2SuggestHttpRepo.getTeamsByName(teamName, SupportedSports.FOOTBALL).get(0);
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, eventStatus.name());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, team.getId());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        if (eventStatus == EventStatusType.NOT_STARTED) {
            queryParams.put(StringConstants.SORT_DIRECTION_STRING, SortDirectionEnum.ASCENDING.getStorybookValue().toUpperCase());
        }
        var eventsListResponse = searchEventsHttpRepo.getAll(queryParams);

        Bookmaker bookmaker = null;
        if (eventStatus.equals(EventStatusType.NOT_STARTED)) {
            bookmaker = oddsApiFacade.getOdds(SupportedSports.TENNIS, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        }
        return FootballMatchCenterBlockyFieldsFactory.buildDynamicFieldsOnly(team, eventsListResponse.getResult().get(0), bookmaker);
    }

    public FootballSingleEventBlockyFieldsModel getSingleEventBlockySportData(Team homeTeam, SportEvents event) {
        var match = footballMatchesV2HttpRepo.getById(event.getId()).getResult();

        return FootballSingleEventBlockyFieldsFactory.buildDynamicFieldsOnly(homeTeam, match);
    }

    public LivescoreBlockyFieldsModel getLivescoreBlockySportData(LocalDate date) {
        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return FootballLivescoreBlockyFieldsFactory.buildDynamicFieldsOnly(date, true, bookmaker);
    }

    public FootballKnockoutBlockyFieldsModel getKnockoutBlockySportData(FootballTournamentEnum tournamentName) {
        var allTournamentsTournaments = footballTournamentsHttpRepo.getAllTournaments();
        var tournament = allTournamentsTournaments.stream().filter(t -> t.getName().equalsIgnoreCase(tournamentName.getName())).findFirst().get();
        var tournamentDetails = footballTournamentsHttpRepo.getById(tournament.getId().toString()).getResult();
        var season = tournamentDetails.getSeasons().get(0);
        var stages = footballSeasonsHttpRepo.getById(season.getId()).getResult().getStages();
        var stage = stages.get(0);
        return FootballKnockoutBlockyFieldsFactory.buildDynamicFieldsOnly(tournament, season, stage);
    }

    public FootballLineupsBlockyFieldsModel getLineupsBlockySportData(EventStatusType eventStatus) {
        Map<String, Object> teamQuery = new HashMap<>();
        teamQuery.put(StringConstants.STATUS_TYPE_STRING, eventStatus);
        teamQuery.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        teamQuery.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        List<ResultModel> events = searchEventsHttpRepo.getAll(teamQuery).getResult();
        String teamId = events.stream()
                .filter(event -> event.getTranslations().stream()
                        .anyMatch(translation -> translation.getLanguage().equals(Language.BULGARIAN.getCode())))
                .findFirst()
                .map(event -> event.getParticipantDetails().get(0).getParticipant().getId())
                .orElseThrow(() -> new RuntimeException("Football team does not have bulgarian translation"));

        ResultModel team = searchV2SuggestHttpRepo.getTeamsById(teamId, SupportedSports.FOOTBALL).get(0);
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, eventStatus.name());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, team.getId());
        ApiResponse<List<ResultModel>> eventsListResponse = searchEventsHttpRepo.getAll(queryParams);
        ResultModel event = eventsListResponse.getResult().get(0);
        ColorModel firstTeamShirtColor = addShirtColorsForTeam(event.getParticipantDetails().get(0).getParticipant().getLegacyId()).get(0);
        ColorModel secondTeamShirtColor = addShirtColorsForTeam(event.getParticipantDetails().get(1).getParticipant().getLegacyId()).get(1);
        return FootballLineupsBlockyFieldsFactory.buildDynamicFieldsOnly(team, event, firstTeamShirtColor, secondTeamShirtColor);
    }

    public FootballMatchesH2HBlockyFieldsModel getMatchesH2HBlockySportData(FootballTeamEnum team1, FootballTeamEnum team2) {
        ResultModel team1Model = searchV2SuggestHttpRepo.getTeamsByName(team1.getName(), SupportedSports.FOOTBALL).get(0);
        ResultModel team2Model = searchV2SuggestHttpRepo.getTeamsByName(team2.getName(), SupportedSports.FOOTBALL).get(0);
        return FootballMatchesH2HBlockyFieldsFactory.buildDynamicFieldsOnly(team1Model, team2Model);
    }

    public FootballPlayerProfileBlockyFieldsModel getPlayerProfileBlockySportData(FootballPlayerEnum dataPlayerEnum) {
        PlayerModel player = footballPlayersHttpRepo.getAllByName(dataPlayerEnum.getNameEn()).get(0);
        Map<String, Object> teamIdsQuery = new HashMap<>();
        teamIdsQuery.put(StringConstants.LANGUAGE_CODE_STRING, Language.ENGLISH.getCode());

        ApiResponse<PlayerV2Model> statistics = footballPlayersV2HttpRepo.getById(player.getId().toString(), teamIdsQuery);
        Season season = statistics.getResult().getCurrentSeason();
        Tournament stage = statistics.getResult().getCurrentSeason().getTournament();

        teamIdsQuery.put(StringConstants.TEAM_IDS_UNDERSCORED_STRING, "%s".formatted(statistics.getResult().getTeams().get(0).getTeam().getId()));
        teamIdsQuery.put(StringConstants.TEAM_IDS_OPERATOR_STRING, "OR");
        teamIdsQuery.put(StringConstants.LIMIT_STRING, 200);
        teamIdsQuery.put(StringConstants.OFFSET_STRING, 0);
        teamIdsQuery.put(StringConstants.SORT_DIRECTION_STRING, StringConstants.ASCENDING_STRING);
        teamIdsQuery.put(StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED);
        teamIdsQuery.put(StringConstants.LANGUAGE_CODE_STRING, Language.ENGLISH.getCode());

        NestedMatch match = footballMatchesV2HttpRepo.getMatch(
                        statistics.getResult().getCurrentSeason().getId(),
                        teamIdsQuery)
                .getResult()
                .stream()
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No match found"))
                .toNestedMatch();

        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return FootballPlayerProfileBlockyFieldsFactory.buildDynamicFieldsOnly(player, season, stage, match, bookmaker);
    }

    private Season getSeasonFrom(FootballTournamentEnum footballTournament, String seasonName) {
        return footballTournamentsHttpRepo.getById(footballTournament.getId()).getResult().getSeasons()
                .stream()
                .filter(season -> season.getName().equals(seasonName))
                .findFirst()
                .orElseThrow(() -> new AssertionError("Season not found."));
    }

    public FootballTeamProfileBlockyFieldsModel getTeamProfileBlockySportData() {
        Map<String, Object> teamQuery = new HashMap<>();
        teamQuery.put(StringConstants.STATUS_TYPE_STRING, StatusType.NOT_STARTED);
        List<ResultModel> upcomingMatches = searchEventsHttpRepo.getAll(teamQuery).getResult();

        TeamModel team = footballTeamsHttpRepo.getAllByName(upcomingMatches.get(0).getParticipantDetails().get(0).getParticipant().getName()).get(0);
        ApiResponse<List<MatchV2Model>> seasons = footballMatchesV2HttpRepo.getAllMatchesForTeams(StatusType.NOT_STARTED, team.getId());
        Tournament season = seasons.getResult().get(0).getSeason().getTournament();
        MatchV2Model match = seasons.getResult().stream()
                .filter(m -> m.getStatus().getName().equals(EventStatusType.NOT_STARTED.getName()) && m.getHomeTeam().getId().equals(team.getId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No match status 'NOT STARTED' was found for the home team"));
        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);

        return FootballTeamProfileBlockyFieldsFactory.buildDynamicFieldsOnly(team, season, match, bookmaker);
    }

    public FootballPlayerProfileBlockyFieldsModel getPlayerH2HDataFromStatistics(FootballPlayerEnum dataPlayerEnum) {
        PlayerModel player = footballPlayersHttpRepo.getAllByName(dataPlayerEnum.getNameEn()).get(0);
        ApiResponse<List<PlayerStatisticsModel>> statistics = footballStatisticsPlayersHttpRepo.getAllByPlayerIds(player.getId().toString());
        Season season = statistics.getResult().get(0).getSeason();
        Tournament stage = statistics.getResult().get(0).getTournament();
        Map<String, Object> teamIdsQuery = new HashMap<>();
        String teamId = statistics.getResult().get(0).getTeam().getId();
        teamIdsQuery.put(StringConstants.TEAM_IDS_UNDERSCORED_STRING, "%s".formatted(teamId));
        teamIdsQuery.put(StringConstants.TEAM_IDS_OPERATOR_STRING, "OR");
        teamIdsQuery.put(StringConstants.LIMIT_STRING, 200);
        teamIdsQuery.put(StringConstants.OFFSET_STRING, 0);
        teamIdsQuery.put(StringConstants.LANGUAGE_CODE_STRING, Language.ENGLISH.getCode());

        NestedMatch match = footballMatchesV2HttpRepo.getMatch(teamId, teamIdsQuery).getResult()
                .stream()
                .filter(m -> m.getHomeTeam().getId().equals(teamId) || m.getAwayTeam().getId().equals(teamId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No match found"))
                .toNestedMatch();

        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);
        return FootballPlayerProfileBlockyFieldsFactory.buildDynamicFieldsOnly(player, season, stage, match, bookmaker);
    }

    public FootballPlayerH2HBlockyFieldsModel getPlayerH2HBlockySportData(FootballPlayerEnum playerOne, FootballPlayerEnum playerTwo) {
        FootballPlayerProfileBlockyFieldsModel playerOneBlockyModel = getPlayerH2HDataFromStatistics(playerOne);
        FootballPlayerProfileBlockyFieldsModel playerTwoBlockyModel = getPlayerH2HDataFromStatistics(playerTwo);

        return FootballPlayerH2HBlockyFieldsFactory.buildDynamicFieldsOnly(
                playerOneBlockyModel.getPlayer(), playerOneBlockyModel.getSeason(), playerOneBlockyModel.getStage(),
                playerTwoBlockyModel.getPlayer(), playerTwoBlockyModel.getSeason(), playerTwoBlockyModel.getStage());
    }

    public FootballTeamH2HBlockyFieldsModel getTeamH2HBlockySportData(FootballTeamEnum teamOne, FootballTeamEnum teamTwo) {
        TeamModel teamOneModel = footballTeamsHttpRepo.getTeamByExactName(teamOne.getName());
        ApiResponse<List<PlayersStatisticsSeasonsModel>> seasonTeamOneResponse =
                footballTeamsHttpRepo.getSeasonForPlayerStatistics(teamOneModel.getId());
        PlayersStatisticsSeasonsModel teamOneSeason = seasonTeamOneResponse.getResult().get(0);

        TeamModel teamTwoModel = footballTeamsHttpRepo.getTeamByExactName(teamTwo.getName());
        ApiResponse<List<PlayersStatisticsSeasonsModel>> seasonTeamTwoResponse =
                footballTeamsHttpRepo.getSeasonForPlayerStatistics(teamTwoModel.getId());
        PlayersStatisticsSeasonsModel teamTwoSeason = seasonTeamTwoResponse.getResult().get(0);

        return FootballTeamH2HBlockyFieldsFactory.buildDynamicFieldsOnly(
                teamOneModel,
                teamOneSeason,
                teamTwoModel,
                teamTwoSeason);
    }

    public FootballTeamH2HMatchesBlockyFieldsModel getTeamH2HMatchesBlockySportData(StatusType statusType) {
        MatchesV2TeamModel teamOneStatistics = getTeamProfileBlockySportData().getMatch().getHomeTeam();
        MatchesV2TeamModel teamTwoStatistics = getTeamProfileBlockySportData().getMatch().getAwayTeam();
        MatchV2Model match_v2 = footballMatchesV2HttpRepo
                .getAllMatchesForTeams(statusType, teamOneStatistics.getId(), teamTwoStatistics.getId())
                .getResult().stream()
                .findFirst()
                .orElse(null);

        return FootballTeamH2HMatchesBlockyFieldsFactory.buildDynamicFieldsOnly(teamOneStatistics, teamTwoStatistics, match_v2, NestedMatch.builder().build());
    }

    public FootballTeamProgrammeBlockyFieldsModel getTeamProgrammeBlockySportData(FootballTeamEnum footballTeamEnum) {
        TeamModel team = footballTeamsHttpRepo.getAllByName(footballTeamEnum.getName()).get(0);
        ApiResponse<List<TournamentSeasonModel>> teamSeasons = footballSeasonsHttpRepo.getAllByTeam(team.getId());
        var season = footballSeasonsHttpRepo.getById(String.valueOf(teamSeasons.getResult().get(0).getId())).getResult();
        Tournament tournament = season.getTournament();
        Stage stage = season.getStages().get(0);
        RoundV2Model round = footballRoundsHttpRepo.getRoundsByStageId(stage.getId()).getResult().get(0);

        return FootballTeamProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(
                team,
                season,
                tournament,
                stage,
                List.of(round),
                oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0));
    }

    public List<OddProviderModel> getOddProviders() {
        var oddsProviderResponse = footballOddsProvidersHttpRepo.getAll();
        oddsProviderResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return oddsProviderResponse.getResult();
    }

    public FootballTeamSquadBlockyFieldsModel getTeamSquadBlockySportData(FootballTeamEnum footballTeamEnum) {
        TeamModel team = footballTeamsHttpRepo.getAllByName(footballTeamEnum.getName()).stream().findFirst().orElseThrow();
        ApiResponse<List<TournamentSeasonModel>> teamSeasons = footballSeasonsHttpRepo.getAllByTeam(team.getId());
        var season = footballSeasonsHttpRepo.getById(String.valueOf(teamSeasons.getResult().get(0).getId())).getResult();
        Tournament tournament = season.getTournament();

        return FootballTeamSquadBlockyFieldsFactory.buildDynamicFieldsOnly(
                team,
                season,
                tournament);
    }

    public FootballTopScorersBlockyFieldsModel getTopScorersBlockySportData(FootballTournamentEnum footballTournament) {
        var tournament = footballTournamentsHttpRepo.getById(footballTournament.getId()).getResult();
        var season = footballSeasonsHttpRepo.getById(tournament.getSeasons().get(0).getId()).getResult();
        var topScorers = footballTopScorersHttpRepo.getSeasonTopScorers(String.valueOf(season.getId())).getResult();
        var playerToHighlight = topScorers.stream().findFirst().orElseThrow();

        return FootballTopScorersBlockyFieldsFactory.buildDynamicFieldsOnly(
                playerToHighlight,
                season,
                tournament);
    }

    public FootballTournamentProgrammeBlockyFieldsModel getTournamentProgrammeBlockySportData(FootballTournamentEnum footballTournamentEnum) {
        CommonResultModel tournament = footballTournamentsHttpRepo.getById(footballTournamentEnum.getId()).getResult();
        TournamentSeasonModel season = footballSeasonsHttpRepo.getById(tournament.getSeasons().get(0).getId()).getResult();
        Stage stage = season.getStages().get(0);
        List<RoundV2Model> rounds = footballRoundsHttpRepo.getRoundsByStageId(stage.getId()).getResult();

        return FootballTournamentProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(
                season,
                tournament,
                stage,
                rounds,
                oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0));
    }

    public FootballMostDecoratedPlayersBlockyFieldsModel getMostDecoratedPlayersBlockySportData(FootballTournamentEnum footballTournament) {
        var tournament = footballTournamentsHttpRepo.getById(footballTournament.getId()).getResult();
        var season = footballSeasonsHttpRepo.getById(tournament.getSeasons().get(1).getId()).getResult();
        var topScorers = footballTopScorersHttpRepo.getSeasonTopScorers(String.valueOf(season.getId())).getResult();
        var playerToHighlight = topScorers.stream().findFirst().orElseThrow();

        return FootballMostDecoratedPlayersBlockyFieldsFactory.buildDynamicFieldsOnly(playerToHighlight, season, tournament);
    }

    public FootballStandingsBlockyFieldsModel getStandingsBlockySportData(FootballTournamentEnum footballTournamentEnum) {
        CommonResultModel tournament = footballTournamentsHttpRepo.getById(footballTournamentEnum.getId()).getResult();
        TournamentSeasonModel season = footballSeasonsHttpRepo.getById(tournament.getSeasons().get(0).getId()).getResult();
        Stage stage = season.getStages().get(0);
        List<Standing> teams = footballTournamentsHttpRepo.getStandingForStage(stage.getId()).getResult().getStanding();

        return FootballStandingsBlockyFieldsFactory.buildDynamicFieldsOnly(
                season,
                tournament,
                stage,
                teams);
    }

    public FootballFormBlockyFieldsModel getTeamFormBlockySportData(FootballTeamEnum footballTeamEnum) {
        TeamModel team = footballTeamsHttpRepo.getAllByName(footballTeamEnum.getName()).stream().findFirst().orElseThrow();
        ApiResponse<List<TournamentSeasonModel>> teamSeasons = footballSeasonsHttpRepo.getAllByTeam(team.getId());
        var season = footballSeasonsHttpRepo.getById(String.valueOf(teamSeasons.getResult().get(0).getId())).getResult();
        Tournament tournament = season.getTournament();

        return FootballTeamFormBlockyFieldsFactory.buildDynamicFieldsOnly(
                team,
                season,
                tournament);
    }

    public FootballOddsBlockyFieldsModel getOddsBlockySportData() {
        MatchV2Model match = getFootballEventWithOdds();

        return FootballOddsBlockyFieldsFactory.buildDynamicFieldsOnly(
                match.getHomeTeam(),
                match,
                match.getOdds().get(0).getBookmaker());
    }

    public List<MatchV2Model> getFootballEventsWithOdds(String... oddClient) {
        Map<String, Object> queryParams = new HashMap<>(defaultQueryParams);
        queryParams.put(StringConstants.SORT_DIRECTION_STRING, StringConstants.ASCENDING_STRING);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.FROM_KICKOFF_TIME_STRING, LocalDateTime.now().format(DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'")));
        queryParams.put(StringConstants.STATUS_TYPES_STRING, FootballMatchStatus.NOT_STARTED.name());

        if (oddClient.length > 0) {
            queryParams.put(StringConstants.ODD_CLIENT_STRING, oddClient[0]);
        }
        String todayDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<MatchV2Model> matchesWithOdds = footballMatchesV2HttpRepo.getAll(queryParams).getResult()
                .stream()
                /*
                 * This filtration ensures we only get events from the same day.
                 * It's critical for multi-competition tests because when querying events from
                 * different competitions on different days, the API response will only include
                 * events from one of the dates, causing tests to fail when trying to verify
                 * multiple competitions in a single response.
                 */
                .filter(match -> match.getKickoffTime() != null)
                .filter(match -> match.getKickoffTime().startsWith(todayDate))
                .filter(event -> !event.getOdds().isEmpty())
                .toList();

        if (matchesWithOdds.isEmpty()) {
            throw new RuntimeException("No football matches with odds found.");
        }
        return matchesWithOdds;
    }

    public MatchV2Model getFootballEventWithOdds(String... oddClient) {
        return getFootballEventsWithOdds(oddClient).get(0);
    }

    public FootballSingleRoundBlockyFieldsModel getSingleRoundBlockySportData() {
        Integer tournamentId = getFootballEventWithOdds().getSeason().getTournament().getId();
        CommonResultModel tournament = footballTournamentsHttpRepo.getById(tournamentId.toString()).getResult();
        String seasonId = tournament.getSeasons().get(0).getId();
        SeasonV2DetailsModel seasonDetails = footballSeasonsV2HttpRepo.getSeasonsDetails(Map.of(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId)).getResult();
        Bookmaker bookmaker = oddsApiFacade.getOdds(SupportedSports.FOOTBALL, StringConstants.SPORTAL_365_STRING).getBookmakers().get(0);

        return FootballSingleRoundBlockyFieldsFactory.buildDynamicFieldsOnly(tournament, seasonDetails, bookmaker);
    }

    public void updateMatchToNotStarted(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildNotStartedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToPenalty(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildPenaltyMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToPostponed(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildPostponedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFinished(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFinishedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToHalfTime(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildHalfTimeMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFinishAfterExtraTime(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFinishedAfterExtraTimeMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToInterrupted(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildInterruptedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFinishedAfterPenalties(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFinishedAfterPenaltiesMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToWaitingForExtraTime(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildWaitingForExtraTimeMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFinishedAGG(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFinishedAGGMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToAbandoned(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildAbandonedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToNoInfoYet(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildNoInfoYetMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToWaitingForPenalty(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildWaitingForPenaltyMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToKickOffDelayed(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildKickOffDelayedMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToPause(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildPauseMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToCancelled(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildCancelledMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToAwaitingInfo(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildAwaitingInfoMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFirstHalf(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFirstHalfMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToSecondHalf(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildSecondHalfMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToExtraTimeFirstHalf(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildExtraTimeFirstHalfMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToExtraTimeSecondHalf(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildExtraTimeSecondHalfMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToToFinish(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildToFinishMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    public void updateMatchToFinishedAW(String matchId, String kickoffDate) {
        updateMatch(matchId, kickoffDate, footballMatch -> MatchV2Factory.buildFinishedAfterAwardedWinMatchRequestBody(
                footballMatch.getHomeTeam().getId(),
                footballMatch.getAwayTeam().getId(),
                footballMatch.getStage().getId()
        ));
    }

    private void updateMatch(String matchId, String kickOffDate, Function<MatchV2Model, CreateMatchV2Model> createRequestBodyFunction) {
        MatchV2Model footballMatch = footballMatchesV2HttpRepo.getById(matchId).getResult();

        CreateMatchV2Model requestBody = createRequestBodyFunction.apply(footballMatch);
        requestBody.setRoundKey(footballMatch.getRound().getKey());
        requestBody.setKickoffTime("%sT16:00:00Z".formatted(kickOffDate));

        var updatedMatch = footballMatchesV2HttpRepo.updateMatch(requestBody, matchId);
        updatedMatch.getResponse().then().statusCode(HttpStatus.SC_OK);
    }

    public List<ColorModel> addShirtColorsForTeam(String teamId) {
        List<ColorModel> shirtColors = footballTeamsV2HttpRepo.getById(teamId).getResult().getShirtColors();
        if (shirtColors == null || shirtColors.isEmpty()) {
            TeamsV2ColorsModel teamsV2ColorsModel = new TeamsV2Factory().buildTeamsV2PostRequestForColors(teamId);
            var teamColorsResponse = footballTeamsV2HttpRepo.createTeamColors(teamsV2ColorsModel);
            teamColorsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
            shirtColors = teamColorsResponse.getResult().getColors();
        }
        return shirtColors;
    }

    public ResultModel getFootballCoach() {
        return searchV2SuggestHttpRepo.getEntitiesByType(EntityTypeEnum.COACH, SupportedSports.FOOTBALL).stream()
                .filter(c -> c.getTeamIds() == null || c.getTeamIds().isEmpty())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No coach without active teams found"));
    }
}