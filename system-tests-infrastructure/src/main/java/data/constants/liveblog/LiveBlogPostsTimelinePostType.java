package data.constants.liveblog;

import lombok.Getter;

@Getter
public enum LiveBlogPostsTimelinePostType {

    PUBLISHED("Published", "PUBLISHED"),
    PINNED("Pinned", "PUBLISHED&is_pinned=true"),
    HIGHLIGHTS("Highlights", "PUBLISHED&is_highlight=true"),
    DRAFTS("Drafts", "DRAFT");

    private final String value;
    private final String publishedType;

    LiveBlogPostsTimelinePostType(String value, String publishedType) {
        this.value = value;
        this.publishedType = publishedType;
    }
}