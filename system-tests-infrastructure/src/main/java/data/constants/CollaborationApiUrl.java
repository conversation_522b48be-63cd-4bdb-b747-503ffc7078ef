package data.constants;

import data.configuration.SportalSettings;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum CollaborationApiUrl {

    DOCUMENTS(ConfigurationService.get(SportalSettings.class).getCollaborationApiUrl() + "documents"),
    SUB_DOCUMENTS(ConfigurationService.get(SportalSettings.class).getCollaborationApiUrl() + "documents/%s/fields"),
    LIVE(ConfigurationService.get(SportalSettings.class).getCollaborationApiUrl() + "healthz/live"),
    WS_443(ConfigurationService.get(SportalSettings.class).getCollaborationApiUrl() + "ws:443");

    private final String url;

    CollaborationApiUrl(String url) {
        this.url = url;
    }
}