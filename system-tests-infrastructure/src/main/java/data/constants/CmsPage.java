package data.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CmsPage {

    EDIT_PAGE_FORMAT("%s/edit/%s"),
    EDIT_CONTENT_PAGE_LIST("%s/edit-content/%s"),
    DASHBOARD("#/dashboard"),
    ARTICLES("#/smp/articles"),
    RSM_ARTICLES("#/smp/rsm-articles"),
    AI_ARTICLES("#/smp/ai-articles"),
    ARTICLE_EDIT("#/smp/articles/edit/%s"),
    ARTICLE_CREATE("#/smp/articles/create"),
    VIDEOS("#/smp/videos"),
    VIDEO_CREATE("#/smp/videos/create"),
    VIDEO_EDIT("#/smp/videos/edit/%s"),
    GALLERIES("#/smp/galleries"),
    GALLERIES_EDIT(EDIT_PAGE_FORMAT.href),
    WIKI_PAGES("#/smp/wiki-pages"),
    WIKI_PAGES_EDIT(WIKI_PAGES.href + "/edit/%s"),
    LIVE_BLOGS_SMP("#/smp/live-blogs"),
    LIVE_BLOGS_CREATE("#/smp/live-blogs/configuration/create"),
    LIVE_BLOGS_EDIT("#/smp/live-blogs/configuration/edit/%s"),
    LIVE_BLOGS_EDITORIAL_ADMIN("#/smp/live-blogs/editorial-admin/%s"),
    LIVE_BLOG_EDIT("#/smp/live-blogs/configuration/edit/%s"),
    CUSTOM_ENTITIES_LISTING("#/smp/custom-entities"),
    CUSTOM_ENTITIES_CREATE(CUSTOM_ENTITIES_LISTING.getHref() + "/create"),
    CUSTOM_ENTITIES_EDIT(CUSTOM_ENTITIES_LISTING.getHref() + "/edit/%s/%s"),
    ALL_IMAGES("#/smp/images"),
    AUTOMATED_LISTS("#/smp/automated-lists"),
    EDIT_CONTENT_LISTS("#/smp/edit-content"),
    LISTS_EDITORIAL("#/smp/lists"),
    LISTS_SCHEDULED("#/smp/scheduled-lists"),
    LISTS_EDIT_CONTENT("#/smp/lists/edit-content/%s"),
    TAGS_AND_SPORTS_CONNECTIONS("#/smp/tags-and-sports-connections-lists"),
    NEWS_TRACKER("#/smp/news-tracker"),
    AUDIT_LOG("#/smp/audit-log"),
    CATEGORIES("#/smp/categories"),
    TAGS("#/smp/tags"),
    AUTHORS("#/smp/authors"),
    AUTHORS_EDIT(AUTHORS.href + "/edit/%s"),
    BANNERS("#/smp/banners"),
    WIDGETS("#/smp/widgets-page"),
    CREATE_PAGE_FORMAT("%s/create"),
    ALL_ITEMS_PAGE_FORMAT("#/smp/%s"),
    NEWS_FORGE("#/smp/news-forge");

    public final String href;
}