package data.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum BlockyTypeEnum {
    MATCH_CENTER(WidgetBlock.FOOTBALL_MATCH_CENTER_WIDGET_V2.getName(), "match-center", "widgetFootballMatchCenter"),
    FOOTBALL_SINGLE_EVENT(WidgetBlock.FOOTBALL_SINGLE_EVENT.getName(), "football-single-event", "widgetFootballSingleEvent"),
    FOOTBALL_LIVESCORE(WidgetBlock.FOOTBALL_LIVESCORE_WIDGET.getName(), "livescore", "widgetFootballLivescore"),
    FOOTBALL_STANDINGS(WidgetBlock.FOOTBALL_STANDINGS_WIDGET.getName(), "standings", "widgetFootballStandings"),
    FOOTBALL_TEAM_PROGRAMME(WidgetBlock.FOOTBALL_TEAM_PROGRAMME_WIDGET.getName(), "team-programme", "widgetFootballTeamProgramme"),
    FOOTBALL_KNOCKOUT(WidgetBlock.FOOTBALL_KNOCKOUT_SCHEME_WIDGET.getName(), "knockout", "widgetFootballKnockout"),
    FOOTBALL_PLAYER(WidgetBlock.FOOTBALL_PLAYER_WIDGET.getName(), "player", "widgetFootballPlayer"),
    FOOTBALL_TEAM_PROFILE(WidgetBlock.FOOTBALL_TEAM_PROFILE_WIDGET.getName(), "team", "widgetFootballTeam"),
    FOOTBALL_TEAM_SQUAD(WidgetBlock.FOOTBALL_TEAM_SQUAD_WIDGET.getName(), "team-squad", "widgetFootballTeamSquad"),
    FOOTBALL_TOURNAMENT_PROGRAMME(WidgetBlock.FOOTBALL_TOURNAMENT_PROGRAMME_WIDGET.getName(), "tournament-programme", "widgetFootballTournamentProgramme"),
    FOOTBALL_PLAYER_H2H(WidgetBlock.PLAYER_H2H.getName(), "player-h2h", "widgetFootballPlayerH2H"),
    FOOTBALL_TEAM_H2H(WidgetBlock.FOOTBALL_TEAM_H2H.getName(), "team-h2h", "widgetFootballTeamH2H"),
    FOOTBALL_MATCHES_H2H(WidgetBlock.FOOTBALL_MATCHES_H2H.getName(), "football-matches-h2h", "widgetFootballMatchesH2H"),
    FOOTBALL_TEAM_H2H_MATCH(WidgetBlock.FOOTBALL_TEAM_H2H_MATCH.getName(), "football-team-h2h-match", "widgetFootballTeamH2H"),
    FOOTBALL_TEAM_FORM(WidgetBlock.TEAM_FORM_WIDGET.getName(), "team-form", "widgetFootballTeamForm"),
    FOOTBALL_TOP_SCORERS(WidgetBlock.FOOTBALL_TOP_SCORERS.getName(), "top-scorers", "widgetFootballTopScorers"),
    FOOTBALL_MOST_DECORATED_PLAYERS(WidgetBlock.FOOTBALL_MOST_DECORATED_PLAYERS.getName(), "most-decorated", "widgetFootballMostDecoratedPlayers"),
    FOOTBALL_LINEUPS(WidgetBlock.FOOTBALL_LINEUPS.getName(), "lineups", "widgetFootballLineups"),
    FOOTBALL_ODDS(WidgetBlock.FOOTBALL_ODDS.getName(), "odds", "widgetFootballOdds"),
    FOOTBALL_SINGLE_ROUND(WidgetBlock.FOOTBALL_SINGLE_ROUND.getName(), "football-single-round", "widgetFootballSingleRound"),

    BASKETBALL_SINGLE_EVENT(WidgetBlock.BASKETBALL_SINGLE_EVENT.getName(), "basketball-single-event", "widgetBasketballSingleEvent"),
    BASKETBALL_LIVESCORE(WidgetBlock.BASKETBALL_LIVESCORE_WIDGET.getName(), "basketball-livescore", "widgetBasketballLivescore"),
    BASKETBALL_STANDINGS(WidgetBlock.BASKETBALL_STANDINGS_WIDGET.getName(), "basketball-standings", "widgetBasketballStandings"),
    BASKETBALL_TOURNAMENT_PROGRAMME(WidgetBlock.BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET.getName(), "basketball-tournament-programme", "widgetBasketballTournamentProgramme"),
    BASKETBALL_TEAM_PROGRAMME(WidgetBlock.BASKETBALL_TEAM_PROGRAMME_WIDGET.getName(), "basketball-team-programme", "widgetBasketballTeamProgramme"),

    TENNIS_SINGLE_EVENT(WidgetBlock.TENNIS_SINGLE_EVENT.getName(), "tennis-single-event", "widgetTennisSingleEvent"),
    TENNIS_LIVESCORE(WidgetBlock.TENNIS_LIVESCORE.getName(), "tennis-livescore", "widgetTennisLivescore"),
    TENNIS_RANKING(WidgetBlock.TENNIS_RANKING.getName(), "tennis-ranking", "widgetTennisRanking"),
    TENNIS_PLAYOFF(WidgetBlock.TENNIS_PLAYOFF.getName(), "tennis-playoff", "widgetTennisPlayoff"),
    TENNIS_ATHLETE_PROGRAMME(WidgetBlock.TENNIS_ATHLETE_PROGRAMME.getName(), "tennis-athlete-programme", "widgetTennisAthleteProgramme"),
    TENNIS_TOURNAMENT_PROGRAMME(WidgetBlock.TENNIS_TOURNAMENT_PROGRAMME.getName(), "tennis-tournament-programme", "widgetTennisTournamentProgramme"),

    ICE_HOCKEY_SINGLE_EVENT(WidgetBlock.ICE_HOCKEY_SINGLE_EVENT.getName(), "ice-hockey-single-event", "widgetIceHockeySingleEvent"),
    ICE_HOCKEY_STANDINGS(WidgetBlock.ICE_HOCKEY_STANDINGS_WIDGET.getName(), "ice-hockey-standings", "widgetIceHockeyStandings"),
    ICE_HOCKEY_LIVESCORE(WidgetBlock.ICE_HOCKEY_LIVESCORE.getName(), "ice-hockey-livescore", "widgetIceHockeyLivescore"),

    PARAGRAPH(WidgetBlock.PARAGRAPH.getName(), "paragraph"),
    HEADING(WidgetBlock.HEADING.getName(), "heading"),
    QUOTE(WidgetBlock.QUOTE.getName(), "quote"),
    LIST(WidgetBlock.LIST.getName(), "list"),
    TABLE(WidgetBlock.TABLE.getName(), "table"),

    ARTICLE(WidgetBlock.ARTICLE_WIDGET.getName(), "article"),
    GALLERY(WidgetBlock.GALLERY_WIDGET.getName(), "gallery"),
    IMAGE(WidgetBlock.IMAGE_WIDGET.getName(), "image"),
    VIDEO(WidgetBlock.VIDEO_WIDGET.getName(), "video"),
    EMBED_CODE(WidgetBlock.EMBED_CODE_WIDGET.getName(), "embed-code"),
    LINK(WidgetBlock.LINK_WIDGET.getName(), "link"),
    BANNER(WidgetBlock.BANNER_WIDGET.getName(), "banner");

    private static final Map<String, String> widgetTypeToCmsBlockyNameMap = new HashMap<>();
    private final String htmlName;
    private final String title;
    private String widgetType;

    BlockyTypeEnum(String title, String htmlName) {
        this.htmlName = htmlName;
        this.title = title;
    }

    BlockyTypeEnum(String title, String htmlName, String widgetType) {
        this(title, htmlName);
        this.widgetType = widgetType;
    }

    static {
        for (BlockyTypeEnum blocky : BlockyTypeEnum.values()) {
            if (blocky.widgetType != null) {
                widgetTypeToCmsBlockyNameMap.put(blocky.getWidgetType(), blocky.getTitle());
            }
        }
    }

    public static String getCmsBlockyNameByWidgetType(String widgetType) {
        return widgetTypeToCmsBlockyNameMap.get(widgetType);
    }
}