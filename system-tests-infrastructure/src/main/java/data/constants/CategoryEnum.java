package data.constants;

import lombok.AccessLevel;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum CategoryEnum {

    FOOTBALL("Football", "2024110409535996667", "2024110219315933243"),
    TENNIS("Tennis", "2024110409540035142", "2024110219320032537"),
    UEFA("UEFA", "2024110409540195896", "2024110219320103158"),
    BASKETBALL("Basketball", "2024110409540154893", "2024110219320126820"),
    ICE_HOCKEY("Ice Hockey", "2024110409540166378", "2024110219320146414");

    private final String name;
    @Getter(AccessLevel.PRIVATE)
    private final String idStaging;
    @Getter(AccessLevel.PRIVATE)
    private final String idIntegration;

    CategoryEnum(String name, String idStaging, String idIntegration) {
        this.name = name;
        this.idStaging = idStaging;
        this.idIntegration = idIntegration;
    }

    public String getId() {
        if (ConfigurationService.getEnvironment().equals("integration")) {
            return idIntegration;
        }
        return idStaging;
    }
}