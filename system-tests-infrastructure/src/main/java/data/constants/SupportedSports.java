package data.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SupportedSports {

    INVALID("invalid", "Invalid"),
    FOOTBALL("football", "Football"),
    BASKETBALL("basketball", "Basketball"),
    TENNIS("tennis", "Tennis"),
    ICE_HOCKEY("ice_hockey", "Ice hockey"),
    HANDBALL("handball", "Handball"),
    CYCLING("cycling", "Cycling"),
    CRICKET("cricket", "Cricket"),
    FUTSAL("futsal", "Futsal"),
    TABLE_TENNIS("table_tennis", "Table tennis"),
    HORSE_RACING("horse_racing", "Horse Racing"),
    ROLLER_HOCKEY("roller_hockey", "Roller Hockey"),
    RUGBY_LEAGUE("rugby_league", "Rugby League"),
    RUGBY_UNION("rugby_union", "Rugby Union"),
    MOTORSPORTS("motorsports", "Motorsports");

    private final String value;
    private final String title;
}