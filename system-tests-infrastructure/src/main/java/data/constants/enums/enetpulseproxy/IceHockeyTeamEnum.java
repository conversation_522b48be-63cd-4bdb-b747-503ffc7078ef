package data.constants.enums.enetpulseproxy;

import lombok.Getter;

@Getter
public enum IceHockeyTeamEnum {

    NEW_YORK_RANGERS("New York Rangers", "41a2c172-b592-4f35-af81-5332e6db0455"),
    DALLAS_STARS("Dallas Stars", "2b5baffc-de85-4b9d-9d84-71aed8d727d2");

    private final String name;
    private final String id;

    IceHockeyTeamEnum(String name, String id) {
        this.name = name;
        this.id = id;
    }
}