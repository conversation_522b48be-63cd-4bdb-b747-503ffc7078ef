package data.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlayerDataElementsStatisticsEnum {

    EMPTY_STATISTIC("", ""),
    MATCHES_PLAYED("Matches played", "played"),
    GOALS("Goals", "goals"),
    ASSISTS("Assists", "assists"),
    SHOTS("Shots", "shots"),
    FOULS_COMMITTED("Fouls committed", "foulsCommitted"),
    RED_CARDS("Red cards", "redCards"),
    SHOTS_ON_TARGET("Shots on target", "shotsOnTarget");

    private final String value;
    private final String displayValue;
}