package data.constants.enums;

import lombok.Getter;

@Getter
public enum PlayerDataElementsInfoParametersEnum {

    COUNTRY("Country", "country"),
    POSITION("Position", "position"),
    DATE_OF_BIRTH("Date of birth", "dateOfBirth"),
    HEIGHT("Height", "height"),
    WEIGHT("Weight", "weight");

    private final String value;
    private final String displayValue;

    PlayerDataElementsInfoParametersEnum(String displayValue, String value) {
        this.displayValue = displayValue;
        this.value = value;
    }
}