package data.constants.enums;

import data.constants.StringConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ToastMessageEnum {

    WIDGET_EMBED_CODE_COPIED("Widget embed code copied!"),
    ARTICLE_SUCCESSFULLY_CREATED("Article successfully created."),
    ARTICLE_SUCCESSFULLY_UPDATED("Article successfully updated."),
    TAG_SUCCESSFULLY_CREATED("Tag successfully created."),
    TAG_UPDATED_SUCCESSFULLY("Tag updated successfully!"),
    TAG_DELETED_SUCCESSFULLY("Tag deleted successfully!"),
    VIDEO_SUCCESSFULLY_CREATED("Video successfully created."),
    VIDEO_DELETED_SUCCESSFULLY("Video deleted successfully!"),
    GALLERY_SUCCESSFULLY_CREATED("Gallery successfully created."),
    GALLERY_SUCCESSFULLY_UPDATED("Gallery successfully updated."),
    GALLERY_CREATE_FAILED("Gallery create failed."),
    GALLERY_DELETED_SUCCESSFULLY("Gallery deleted successfully!"),
    BANNER_SUCCESSFULLY_CREATED("Banner successfully created."),
    LIVE_BLOG_SUCCESSFULLY_CREATED("Live Blog successfully created."),
    IMAGE_SUCCESSFULLY_UPLOADED("Image successfully uploaded!"),
    IMAGE_SUCCESSFULLY_UPDATED("Image successfully updated"),
    PROBLEM_WITH_UPDATING_IMAGE("There was a problem with updating the image."),
    LIST_UPDATED_SUCCESSFULLY("List updated successfully!"),
    LIST_DELETED_SUCCESSFULLY("List deleted successfully!"),
    ARTICLE_UPDATE_FAILED("Article update failed"),
    ARTICLE_CREATE_FAILED("Article create failed."),
    LIST_CREATE_FAILED("List create failed."),
    ERROR_CREATING_POST("Error creating post"),
    AUTHOR_UPDATED_SUCCESSFULLY("Author updated successfully!"),
    CATEGORY_UPDATED_SUCCESSFULLY("Category updated successfully!"),
    LIST_SUCCESSFULLY_CREATED("List successfully created."),
    CUSTOM_ENTITY_SUCCESSFULLY_SAVED("Custom entity successfully saved"),
    CUSTOM_ENTITY_SUCCESSFULLY_DELETED("Custom entity successfully deleted"),
    WIKI_SUCCESSFULLY_UPDATED("Wiki successfully updated."),
    WIKI_SUCCESSFULLY_DELETED("Wiki successfully deleted."),
    WIKI_CREATE_FAILED("Wiki create failed."),
    WIKI_SUCCESSFULLY_CREATED("Wiki successfully created."),
    THE_WATERMARK_WAS_ADDED_SUCCESSFULLY("The watermark was added successfully."),
    THE_WATERMARK_WAS_REMOVED_SUCCESSFULLY("The watermark was removed successfully."),
    YOUR_POST_HAS_BEEN_SUCCESSFULLY_CREATED("Your post has been successfully created!"),
    END_TIME_MUST_BE_AFTER_START_TIME("Configuration End time must be after Start time"),
    LIVEBLOG_ALREADY_EXIST("Liveblog with similar event and language already exists in the current project"),
    PLEASE_FILL_IN_REQUIRED_FIELDS("Please, fill in the required fields"),
    YOU_HAVE_AN_ITEM_LOCK("You have an item lock in a specific position. This blocks 'add to top' functionality. Please remove the position locks."),
    FIELD_NAME_CANNOT_BE_EMPTY("Field `%s` cannot be empty.".formatted(StringConstants.NAME_STRING)),
    FIELD_THREE_LETTER_CODE_INVALID("Field `%s` is invalid.".formatted(CustomEntitiesFieldsEnum.THREE_LETTER_CODE.getPropertyName()));

    private final String value;
}