package data.constants.enums.football;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FootballPlayerProfileStatisticsEnum {

    EMPTY("", ""),
    STARTED("Started", "started"),
    CONCEDED_GOALS("Conceded goals", "conceded_goals"),
    RED_CARDS("Red cards", "red_cards"),
    CLEAN_SHEETS("Clean sheets", "clean_sheets"),
    SUBSTITUTE_IN("Substitute in", "substitute_in"),
    SUBSTITUTE_OUT("Substitute out", "substitute_out"),
    GOALS_AS_SUBSTITUTE("Goals as a substitute", "goals_as_substitute"),
    MINUTES_AS_SUBSTITUTE("Minutes as a substitute", "minutes_as_substitute"),
    PENALTIES_GOALS("Penalties goals", "penalties_goals"),
    OWN_GOALS("Own goals", "own_goals"),
    SHOTS_ON_TARGET("Shots on target", "shots_on_target"),
    OFFSIDES("Offsides", "offsides"),
    FOULS_COMMITTED("Fouls committed", "fouls_committed"),
    PENALTIES_COMMITTED("Penalties committed", "penalties_committed"),
    PENALTIES_SAVED("Penalties saved", "penalties_saved"),
    PENALTIES_MISSED("Penalties missed", "penalties_missed"),
    PENALTIES_RECEIVED("Penalties received", "penalties_received"),
    CAUGHT_BALL("Caught ball", "caught_ball"),
    SAVES("Saves", "saves");

    private final String displayValue;
    private final String value;

    public static FootballPlayerProfileStatisticsEnum[] getValues() {
        return Arrays.stream(FootballPlayerProfileStatisticsEnum.values())
                .filter(value -> value != EMPTY)
                .toArray(FootballPlayerProfileStatisticsEnum[]::new);
    }
}