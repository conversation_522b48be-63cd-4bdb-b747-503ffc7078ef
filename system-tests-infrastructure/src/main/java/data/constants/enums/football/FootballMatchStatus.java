package data.constants.enums.football;

import lombok.Getter;

@Getter
public enum FootballMatchStatus {

    NOT_STARTED("4"),
    FIRST_HALF("5"),
    SECOND_HALF("6"),
    PENALTY("16"),
    POSTPONED("3"),
    FINISHED("1"),
    EXTRA_TIME_FIRST_HALF("18"),
    EXTRA_TIME_SECOND_HALF("20"),
    HALF_TIME("10"),
    FINISHED_AFTER_EXTRA_TIME("9"),
    INTERRUPTED("12"),
    FINISHED_AFTER_PENALTY("11"),
    WAITING_FOR_EXTRA_TIME("21"),
    FINISHED_AGG("26"),
    ABANDONED("14"),
    NO_INFO_YET("22"),
    WAITING_FOR_PENALTY("15"),
    FINISHED_ASG("24"),
    K<PERSON><PERSON>_OFF_DELAYED("13"),
    PAUS<PERSON>("17"),
    TO_FINISH("8"),
    CANCELLED("2"),
    FINISHED_AFTER_AWARDED_WIN("7"),
    <PERSON>XTRA_TIME_END_OF_FIRST_HALF("19"),
    AWAITING_INFO("23");

    private final String id;

    FootballMatchStatus(String id) {
        this.id = id;
    }
}