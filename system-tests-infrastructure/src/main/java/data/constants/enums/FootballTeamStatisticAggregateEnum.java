package data.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FootballTeamStatisticAggregateEnum {

    /*
    These statistics are from the static project for a specific team.
    This is so that we can compare the exact values of the statistics.
    */
    ASSISTS("e666f4a4-a496-4432-b815-2a2c9bacca52", "Assists", "55", "293", "371"),
    ASSISTS_FIRST_HALF("56603d04-7178-467d-85e6-e85aa6432254", "Assists (first half)", "19", "51", "69"),
    ASSISTS_SECOND_HALF("2c024b22-f02f-481c-9702-2159a753eb92", "Assists (second half)", "36", "70", "88"),
    ASSISTS_OVERTIME("82d1b256-6640-4c05-89b0-3da85671c213", "Assists (overtime)", "", "0", "0"),
    ATTACKS("d02ab4f4-6f56-4dc9-9889-61e167b1019c", "Attacks", "4641", "5831", "7323"),
    BLOCKED_SHOTS("d955fd69-05ed-4a53-a4b9-280b8e96afe4", "Blocked shots", "151", "610", "838"),
    BLOCKED_SHOTS_FIRST_HALF("d6c7879b-afe7-47ec-977f-fcd29e5e234b", "Blocked shots (first half)", "62", "151", "199"),
    BLOCKED_SHOTS_SECOND_HALF("1d8972cd-8ca8-4931-88f5-0a4cdf719de3", "Blocked shots (second half)", "89", "173", "223"),
    BLOCKED_SHOTS_OVERTIME("a44a6e93-7969-4e8b-b727-9637a31bef84", "Blocked shots (overtime)", "", "0", "2"),
    CORNERS("78fa36bc-b3ea-4a63-b73e-41344c170c2b", "Corners", "238", "1165", "1553"),
    CORNERS_FIRST_HALF("7b049ac1-6441-4111-8926-ed39b58fe64a", "Corners (first half)", "128", "302", "382"),
    CORNERS_SECOND_HALF("73bed8f6-c16a-4c40-9030-5c11786f5005", "Corners (second half)", "110", "244", "315"),
    CORNERS_OVERTIME("5925873e-5caf-4d88-9b53-12d3acaef193", "Corners (overtime)", "", "0", "0"),
    COUNTERS("82abaf02-fb8b-4878-b1e4-a1041f14fc73", "Counters", "100", "346", "501"),
    COUNTERS_FIRST_HALF("556b88f4-fd7b-4951-ae56-9208a3058dc4", "Counters (first half)", "46", "89", "128"),
    COUNTERS_SECOND_HALF("c4e2e03a-8ee5-408d-80aa-c97ceb135a0f", "Counters (second half)", "54", "103", "152"),
    COUNTERS_OVERTIME("d5b0c72d-acfd-4ed3-806c-958f8c8d8e8a", "Counters (overtime)", "", "0", "0"),
    CROSSES("6981e8bd-2139-4cc9-a375-5b99422f7973", "Crosses", "599", "3697", "4956"),
    CROSSES_FIRST_HALF("765f7980-664c-4992-b040-6666c34103c4", "Crosses (first half)", "311", "838", "1067"),
    CROSSES_SECOND_HALF("dbb3876f-4735-43e7-9758-d6d1772fdab0", "Crosses (second half)", "288", "740", "1010"),
    CROSSES_OVERTIME("e093a69f-4bfb-45ad-afe7-38ee33952e7d", "Crosses (overtime)", "", "0", "9"),
    DANGEROUS_ATTACKS("ad42a86f-72a2-4f33-8296-692c23f7727f", "Dangerous attacks", "2102", "2726", "3534"),
    FOULS_FIRST_HALF("657c6923-2a59-4742-a915-fad0e7a7855f", "Fouls (first half)", "172", "417", "560"),
    FOULS_SECOND_HALF("ef3695bb-abb0-40fd-ad87-4cb6f938a02c", "Fouls (second half)", "239", "550", "695"),
    FOULS_OVERTIME("a98908db-5a75-40e9-b0a1-da9d8bd21342", "Fouls (overtime)", "", "0", "9"),
    FOULS_COMMITED("877e1bbf-c8bf-493a-9a7b-d2adec4406b7", "Fouls committed", "411", "2263", "3035"),
    FREE_KICKS("539bddd7-fca2-4365-972c-4cc01d113e7e", "Free kicks", "609", "3200", "4290"),
    FREE_KICKS_FIRST_HALF("a650026e-5521-4827-88dc-e331d4662069", "Free kicks (first half)", "275", "596", "812"),
    FREE_KICKS_SECOND_HALF("0dfb3f35-f9f8-4274-8а8bc-371445e6dfde", "Free kicks (second half)", "334", "720", "941"),
    FREE_KICKS_OVERTIME("a9442fae-74cf-4ccf-94d8-ef778df5a5ca", "Free kicks (overtime)", "", "0", "11"),
    GOAL_ATTEMPTS("b520e945-b6f2-4baa-a316-b6b8288e5753", "Goal attempts", "587", "1833", "2361"),
    GOAL_ATTEMPTS_FIRST_HALF("234826c3-2cc3-4357-ab8d-0d4353706e6c", "Goal attempts (first half)", "259", "610", "790"),
    GOAL_ATTEMPTS_SECOND_HALF("31d4c752-30ae-4feb-a194-53750415fbb4", "Goal attempts (second half)", "328", "661", "863"),
    GOAL_ATTEMPTS_OVERTIME("1809dc33-cc70-44f2-9336-cf74d76b4d79", "Goal attempts (overtime)", "", "0", "6"),
    GOAL_KICKS("4c08372d-b640-43a3-8607-668c67f72985", "Goal kicks", "250", "1212", "1632"),
    GOAL_KICKS_FIRST_HALF("60914731-4ebc-407d-9844-87c31d474f8e", "Goal kicks (first half)", "117", "227", "289"),
    GOAL_KICKS_SECOND_HALF("34a23df2-0624-46e6-97c8-c06260c8d2a4", "Goal kicks (second half)", "133", "291", "400"),
    GOAL_KICKS_OVERTIME("92daff1c-3af4-4f18-b1f9-ea6ae92e98a8", "Goal kicks (overtime)", "", "0", "6"),
    GOALS("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Goals", "79", "413", "539"),
    GOALS_FIRST_HALF("df6eb80d-b30a-4a81-b9ff-b40aed5ee60e", "Goals (first half)", "26", "64", "90"),
    GOALS_SECOND_HALF("7b6cf908-ac8d-4bce-bfd1-52aee3916364", "Goals (second half)", "53", "97", "120"),
    GOALS_OVERTIME("21996b2c-c2df-4c12-9b75-9a3ad1fbec6c", "Goals (overtime)", "", "0", "1"),
    OFFSIDES("2f080546-30c1-4ce4-a322-c7fc64cee913", "Offsides", "100", "509", "632"),
    OFFSIDES_FIRST_HALF("53abc727-d484-4aa7-85af-5cc98f8f9747", "Offsides (first half)", "45", "109", "130"),
    OFFSIDES_SECOND_HALF("452249a4-1cb9-421c-a4b8-b5312f7eb1a3", "Offsides (second half)", "55", "125", "148"),
    OFFSIDES_OVERTIME("75912956-1ff7-4465-9062-fc6393f2ae21", "Offsides (overtime)", "", "0", "1"),
    PLAYED("9c474c5c-fdbe-47df-b438-8e2d494e8083", "Played", "0", "0", "0"),
    POSSESSION("acf5f0c1-7382-4cfd-b3c1-61aad029e1a8", "Possession", "2467", "13098", "17209"),
    POSSESSION_FIRST_HALF("7c055687-ddab-47f0-9617-5b99c716dc8d", "Possession (first half)", "2474", "5622", "7277"),
    POSSESSION_SECOND_HALF("d9d7a920-edd1-4055-96d0-e6afe9a228c7", "Possession (second half)", "2464", "5564", "7173"),
    POSSESSION_OVERTIME("3539fec8-b9fd-4c13-853b-283866746a05", "Possession (overtime)", "", "0", "125.5"),
    RED_CARDS("5ebb9fd8-fc6c-4f1c-9db2-ef541d1a993c", "Red cards", "2", "21", "29"),
    RED_CARDS_FIRST_HALF("5afc23c3-d762-4bb0-9259-267b6f01d74c", "Red cards (first half)", "1", "3", "4"),
    RED_CARDS_SECOND_HALF("e31d1c8e-d758-4623-b5ea-0bbca6ec43ba", "Red cards (second half)", "1", "5", "8"),
    RED_CARDS_OVERTIME("31298207-c986-4bd0-a528-e94ee21beae9", "Red cards (overtime)", "", "0", "0"),
    SAVES("3be1de5f-1974-4c80-9602-7e6717f82282", "Saves", "96", "498", "692"),
    SAVES_FIRST_HALF("b3c48771-de9a-4fe0-a82a-fa78f784e465", "Saves (first half)", "57", "110", "155"),
    SAVES_SECOND_HALF("db0b16bc-0cd7-4c65-8d7a-7fa19ee05394", "Saves (second half)", "39", "99", "139"),
    SAVES_OVERTIME("073a647d-bc42-40cb-a48e-759d022f93fe", "Saves (overtime)", "", "0", "1"),
    SHOOTS_OFF_TARGET("c490fc2a-8304-45dc-b98f-a4626d84d1fa", "Shots off target", "204", "1126", "1510"),
    SHOOTS_OFF_TARGET_FIRST_HALF("ce093de8-0d59-404f-b6d4-f2e502473a1e", "Shots off target (first half)", "97", "238", "304"),
    SHOOTS_OFF_TARGET_SECOND_HALF("655bbe69-0854-4a9a-84b2-ae1c7073174b", "Shots off target (second half)", "107", "254", "327"),
    SHOOTS_OFF_TARGET_OVERTIME("9eaf714a-4424-45f2-8763-95d37887de8d", "Shots off target (overtime)", "", "0", "0"),
    SHOOTS_ON_TARGET("da11a5b2-7533-40c7-9c91-2e2655f1d9a5", "Shots on target", "232", "1156", "1549"),
    SHOOTS_ON_TARGET_FIRST_HALF("e9630e22-6ab6-47ef-80ae-5ea5d38d8255", "Shots on target (first half)", "100", "221", "287"),
    SHOOTS_ON_TARGET_SECOND_HALF("b1b15923-186d-4e0d-bb00-f55d2320fbc7", "Shots on target (second half)", "132", "246", "325"),
    SHOOTS_ON_TARGET_OVERTIME("ef6f3bba-f618-4396-949d-9d9b773d8cb4", "Shots on target (overtime)", "", "0", "4"),
    SUBSTITUTIONS("214bc58e-81ba-43e8-89b9-ba60007eb28a", "Substitutions", "161", "808", "1087"),
    SUBSTITUTIONS_FIRST_HALF("d747e927-fc0e-499f-97ef-8a15283256c0", "Substitutions (first half)", "9", "17", "23"),
    SUBSTITUTIONS_SECOND_HALF("d38135c0-e999-4e6b-babc-89de324f7686", "Substitutions (second half)", "152", "354", "461"),
    SUBSTITUTIONS_OVERTIME("9f5febf9-7274-4250-9d84-ab1be44fe408", "Substitutions (overtime)", "", "0", "3"),
    THROW_INS("1f6409f8-0c0b-431a-bdb9-835c2ac77626", "Throw-ins", "661", "3401", "4558"),
    THROW_INS_FIRST_HALF("c703a168-a77b-4168-bc01-8df3c06fbf8c", "Throw-ins (first half)", "335", "781", "1034"),
    THROW_INS_SECOND_HALF("0da32b96-4c66-48cd-8c46-f5679d580e73", "Throw-ins (second half)", "326", "737", "958"),
    THROW_INS_OVERTIME("81f326c2-04cd-44d2-940f-b1305b846cbe", "Throw-ins (overtime)", "", "0", "11"),
    TOTAL_PASSES("742ca5bb-be3d-4a8c-a60c-be6bbe041434", "Total passes", "22545", "28174", "35528"),
    TREATMENTS("844d451d-c23b-4aab-a68f-5a5409b1a3f0", "Treatments", "31", "243", "332"),
    TREATMENTS_FIRST_HALF("01e6f6d7-99a0-4e0e-b4fe-e2305473be8f", "Treatments (first half)", "25", "44", "63"),
    TREATMENTS_SECOND_HALF("bd42690c-5175-4f10-b2fc-c9dc09421f77", "Treatments (second half)", "6", "35", "54"),
    TREATMENTS_OVERTIME("f944b009-c93f-4994-aa42-b6c925b17a7c", "Treatments (overtime)", "", "0", "0"),
    YELLOW_CARDS("1b17cadb-d7f4-4587-8605-4d5506e5399b", "Yellow cards", "86", "448", "606"),
    YELLOW_CARDS_FIRST_HALF("ddcc2212-1253-4110-970b-e837d8a5ee30", "Yellow cards (first half)", "33", "67", "92"),
    YELLOW_CARDS_SECOND_HALF("5b4bd6b1-6c61-4897-a57c-ca39dab57de7", "Yellow cards (second half)", "53", "123", "166"),
    YELLOW_CARDS_OVERTIME("51edfa7b-0b51-41cb-8213-7bacf2eeeca6", "Yellow cards (overtime)", "", "0", "3");

    private final String id;
    private final String name;
    // Value for season statistics
    private final String seasonValue;
    // Value for competition statistics
    private final String competitionValue;
    // Value for aggregate statistics
    private final String aggregateValue;
}
