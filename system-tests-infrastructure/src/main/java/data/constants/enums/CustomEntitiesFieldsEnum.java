package data.constants.enums;

import data.constants.StringConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomEntitiesFieldsEnum {

    DOMAIN("Domain", StringConstants.DOMAIN_STRING, StringConstants.DOMAIN_STRING),
    ENTITY_TYPE("Entity type", StringConstants.ENTITY_TYPE_STRING, "entity-type"),
    SLUG("Slug", StringConstants.SLUG_STRING, ""),
    THREE_LETTER_CODE("Three letter code", "three_letter_code", "");

    private final String value;
    private final String propertyName;
    private final String htmlValue;
}