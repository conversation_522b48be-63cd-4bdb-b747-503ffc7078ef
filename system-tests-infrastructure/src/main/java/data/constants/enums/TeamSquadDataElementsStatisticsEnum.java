package data.constants.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TeamSquadDataElementsStatisticsEnum {

    EMPTY("", ""),
    AGE("Age", "age"),
    MATCHES_PLAYED("Matches played", "matches_played"),
    MINUTES("Minutes", "minutes"),
    MINUTES_AS_A_SUBSTITUTE("Minutes as a substitute", "minutes_substitute"),
    GOALS("Goals", "goals"),
    STARTED("Started", "started"),
    ASSISTS("Assists", "assists"),
    YELLOW_CARDS("Yellow cards", "yellow_cards"),
    RED_CARDS("Red cards", "red_cards"),
    CONCEDED_GOALS("Conceded goals", "conceded"),
    CLEAN_SHEETS("Clean sheets", "cleansheets");

    private final String value;
    private final String displayValue;

    TeamSquadDataElementsStatisticsEnum(String displayValue, String value) {
        this.displayValue = displayValue;
        this.value = value;
    }

    public static TeamSquadDataElementsStatisticsEnum[] getValues() {
        return Arrays.stream(TeamSquadDataElementsStatisticsEnum.values())
                .filter(value -> value != EMPTY)
                .toArray(TeamSquadDataElementsStatisticsEnum[]::new);
    }
}