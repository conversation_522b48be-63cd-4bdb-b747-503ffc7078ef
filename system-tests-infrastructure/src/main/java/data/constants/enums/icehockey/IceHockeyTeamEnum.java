package data.constants.enums.icehockey;

import lombok.Getter;

@Getter
public enum IceHockeyTeamEnum {

    NEW_YORK_RANGERS("41a2c172-b592-4f35-af81-5332e6db0455", "new-york-rangers-1zqsbmbWJiUGUHDyxNnOzd", "", "NYR", "New York Rangers"),
    FLORIDA_PANTHERS("22d67d23-446f-4824-8058-1926c46ceec8", "florida-panthers-13jlARe1sCfwWBzrqFjmoa", "", "FLA", "Florida Panthers"),
    EDMONTON_OILERS("9a0ccf3f-aa47-4909-92f3-d4970172b0a4", "edmonton-oilers-4ggfgUJ2NlGKiRkXCYH84q"),
    WINNIPEG_JETS("", "", "", "WPG", "Winnipeg Jets"),
    PHILADELPHIA_FLYERS("db694bf1-b405-4223-b2ab-d161cf04e723", "philadelphia-flyers-6g1RIZoJiATHSk3OmeKjKt", "", "PHI", "Philadelphia Flyers"),
    DETROIT_RED_WINGS("ef0ce835-1525-4fbc-ae6a-72c3c09cef24", "detroit-red-wings-7H540g5D4kBVh4wal4aLIq", "", "DET", "Detroit Red Wings"),
    GLASGOW_CLAN("", "", "", "", "Glasgow Clan");

    private final String id;
    private final String slugEn;
    private final String shortName;
    private final String fullName;
    private final String threeLetterCode;

    IceHockeyTeamEnum(String id) {
        this.id = id;
        this.slugEn = null;
        this.shortName = null;
        this.threeLetterCode = null;
        this.fullName = null;
    }

    IceHockeyTeamEnum(String id, String slugEn) {
        this.id = id;
        this.slugEn = slugEn;
        this.shortName = null;
        this.threeLetterCode = null;
        this.fullName = null;
    }

    IceHockeyTeamEnum(String id, String slugEn, String shortName) {
        this.id = id;
        this.slugEn = slugEn;
        this.shortName = shortName;
        this.threeLetterCode = null;
        this.fullName = null;
    }

    IceHockeyTeamEnum(String id, String slugEn, String shortName, String threeLetterCode) {
        this.id = id;
        this.slugEn = slugEn;
        this.shortName = shortName;
        this.threeLetterCode = threeLetterCode;
        this.fullName = null;
    }

    IceHockeyTeamEnum(String id, String slugEn, String shortName, String threeLetterCode, String fullName) {
        this.id = id;
        this.slugEn = slugEn;
        this.shortName = shortName;
        this.threeLetterCode = threeLetterCode;
        this.fullName = fullName;
    }
}