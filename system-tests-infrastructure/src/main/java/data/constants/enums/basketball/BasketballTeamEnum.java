package data.constants.enums.basketball;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BasketballTeamEnum {

    CHICAGO_BULLS("Chicago Bulls", "Чикаго Булс", "e22cd69c-07cc-4fbf-8623-64cc3032dd42", "chicago-bulls-6smkwARHNXkwXJFaqrBBXW"),
    CHICAGO_SKY("Chicago Sky", "Чикаго Скай", "5af514cf-98be-4178-9f1f-6c50a5f25045", ""),
    SLOVENIA("Slovenia", "Словения", "", ""),
    BULGARIA("Bulgaria", "България", "", ""),
    ITALY("Italy", "Италия", "", ""),
    GREECE("Greece", "Гърция", "", ""),
    WASHINGTON_WIZARDS("Washington Wizards", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н Уизърдс", "5f3ee8f1-1805-4392-aa46-b49930d340c0",
            "washington-wizards-2tj1IeUQPxy7KLBG2hzIEC"),
    MIAMI_HEAT("Miami Heat", "Маями Хийтс", "95a990d9-9a62-4377-83ba-3fe31383734b", ""),
    BOSTON_CELTICS("Boston Celtics", "Бостън Селтикс", "615f1ab2-c3c1-43c3-8203-6cf40588e07c", ""),
    LA_LAKERS("LA Lakers", "Лос Анджелис Лейкърс", "d9a4e2ea-87b8-4fb2-86ae-1d56b2b1faab", ""),
    ANYANG("Anyang", "", "57a787b2-030c-4314-b0ff-03163db60846", ""),
    KCC_EGIS("KCC Egis", "", "d478c63f-c5fc-4d83-b72e-1cbe4bdcf111", "");

    private final String name;
    private final String nameBG;
    private final String id;
    private final String slugEn;
}