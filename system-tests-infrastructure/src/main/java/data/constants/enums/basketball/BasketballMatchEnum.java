package data.constants.enums.basketball;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BasketballMatchEnum {

    ANYANG_VS_KCC_EGIS("c02b49c5-e0bb-449b-8c77-dae1a981a5e8", "anyang-kcc-egis-5qcJBqNW2zxweYHeBUsOsa"),
    CHICAGO_BULLS_WASHINGTON_WIZARDS("0998df83-a908-48eb-8ebb-f7180e5e38a5", "chicago-bulls-washington-wizards-I6nEhFS44YqpO87b69K8j");

    private final String id;
    private final String slugEn;
}