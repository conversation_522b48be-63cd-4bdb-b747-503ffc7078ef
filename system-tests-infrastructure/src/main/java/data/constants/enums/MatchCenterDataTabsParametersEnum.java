package data.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MatchCenterDataTabsParametersEnum {

    INFORMATION("Information", "lineups"),
    TIMELINE("Timeline", "timeline"),
    STATISTICS("Statistics", "team-h2h"),
    STANDINGS("Standings", "standings"),
    PLAYOFF("Playoff", "playoff"),
    ODDS("Odds", "odds"),
    H2H("H2H", "match-h2h");

    private final String value;
    private final String htmlValue;
}