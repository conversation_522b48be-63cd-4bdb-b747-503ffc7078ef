package data.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CountryEnum {

    BULGARIA("14", "Bulgaria", "", "BG", "BGR", "", ""),
    ITALY("30", "Italy", "", "IT", "ITA", "", ""),
    FRANCE("28", "France", "", "FR", "FRA", "", ""),
    SPAIN("17", "Spain", "", "ES", "ESP", "spain-17", "17c48c6c-1466-4a21-916f-c0e09bc4fe6f"),
    USA("", "USA", "САЩ", "", "USA", "", "64248726-914f-4053-bb3a-b4ffead2d506"),
    SOUTH_KOREA("", "South Korea", "Южна Корея", "KR", "KOR", "", "63e2491d-5584-4255-9d46-59ca3985fd13"),
    ENGLAND("15", "England", "Англия", "GB", "GBR", "", "");

    private final String id;
    private final String name;
    private final String nameBg;
    private final String shortName;
    private final String threeLetterCode;
    private final String slug;
    private final String uuid;
}