package data.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class StringConstants {

    public static final String UPCOMING_MATCHES = "Upcoming matches";
    public static final String AUTOMATIC_SLUG_STRING = "automatic_slug";
    public static final String AUTOMATIC_SEO_TITLE_STRING = "automatic_seo_title";
    public static final String GENERATE_AUTOMATICALLY_FROM_LIVEBLOG_TITLE = "Generate automatically from Liveblog Title";
    public static final String COPY_SEO_TITLE_FROM_THE_MAIN_TITLE = "Copy SEO title from the main title";
    public static final String NO_GAMES_TO_SHOW = "No games to show";
    public static final String OPENS_IN_NEW_WINDOW = "Opens in: New window";
    public static final String OPENS_IN_SAME_WINDOW = "Opens in: Same window";
    public static final String DO_NOT_DELETE_STRING = "DO NOT DELETE";
    public static final String SEARCH_QUERY_STRING = "search?query=%s&language=en";
    public static final String TRANSLATION_NOT_FOUND_STRING = "Translation not found";
    public static final String DEFAULT_COMPETITION_LIST = "all";
    public static final String SPORTAL_BG_NEWS_URL_STRING = "https://sportal.bg/news";
    public static final String SPORTAL_BG_CANNONICAL_URL_STRING = "https://sportal.bg/cannonical";
    public static final String ODD_PROVIDER_ID_STRING = "43";
    public static final String[] TEAM_COMPARISON_ELEMENT_STRING = {"played", "win", "draw", "defeats", "goalsScored", "goalsConceded"};
    public static final String SMP_MULTIPLE_PROJECT_STRING = "smp.multiple";
    public static final String UTC_TIME_STRING = "UTC";
    public static final String TEMPORARY_STRING = "temporary";
    public static final String QUERY_STRING = "query";
    public static final String ALL_SPORTS_STRING = "all sports";
    public static final String EXTERNAL_STRING = "external";
    public static final String STATISTICS_STRING = "statistics";
    public static final String ID_OR_SLUG_STRING = "idOrSlug";
    public static final String NEWS_STRING = "news";
    public static final String EMBED_STRING = "embed";
    public static final String MESSAGE_STRING = "message";
    public static final String STRAPLINE_STRING = "strapline";
    public static final String SUMMARY_STRING = "summary";
    public static final String LIMIT_STRING = "limit";
    public static final String PAGE_STRING = "page";
    public static final String ERROR_TITLE_STRING = "errors.title";
    public static final String OFFSET_STRING = "offset";
    public static final String SCOPE_STRING = "scope";
    public static final String ERROR_STRING = "errors";
    public static final String TOURNAMENT_ID_STRING = "tournament_id";
    public static final String TOURNAMENT_IDS_STRING = "tournament_ids";
    public static final String GROUP_ID_STRING = "group_id";
    public static final String SEASON_ID_UNDERSCORED_STRING = "season_id";
    public static final String SEASON_IDS_UNDERSCORED_STRING = SEASON_ID_UNDERSCORED_STRING + "s";
    public static final String TEAM_ID_STRING = "team_id";
    public static final String STAGE_ID_UNDERSCORED_STRING = "stage_id";
    public static final String STAGE_IDS_UNDERSCORED_STRING = STAGE_ID_UNDERSCORED_STRING + "s";
    public static final String COVERAGE_TYPE_STRING = "coverage_type";
    public static final String VALIDATION_REQUIRED_WITHOUT_ALL_STRING = "validation.required_without_all";
    public static final String VALIDATION_REQUIRED_INTEGER_STRING = "validation.integer";
    public static final String STATUS_STRING = "status";
    public static final String STATUS_TYPE_STRING = "status_type";
    public static final String STATUS_TYPES_STRING = "status_types";
    public static final String LANGUAGE_CODE_STRING = "language_code";
    public static final String SORT_DIRECTION_STRING = "sort_direction";
    public static final String EVENT_IDS_STRING = "event_ids";
    public static final String ASCENDING_STRING = "asc";
    public static final String ASC_UPPERCASE_STRING = "ASC";
    public static final String DESC_STRING = "desc";
    public static final String DESC_UPPERCASE_STRING = "DESC";
    public static final String FROM_KICKOFF_TIME_STRING = "from_kickoff_time";
    public static final String FROM_SCHEDULED_TIME_STRING = "from_scheduled_time";
    public static final String TO_KICKOFF_TIME_STRING = "to_kickoff_time";
    public static final String PLAYER_EVENTS_STRING = "player_events";
    public static final String TOURNAMENT_GROUP_STRING = "tournament_group";
    public static final String SCOPE_TYPE_STRING = "scope_type";
    public static final String TEAM_IDS_CAMELCASE_STRING = "teamIds";
    public static final String TEAM_IDS_UNDERSCORED_STRING = "team_ids";
    public static final String TEAM_IDS_OPERATOR_STRING = "team_ids_operator";
    public static final String DETAIL_STRING = "detail";
    public static final String DETAILS_STRING = DETAIL_STRING + "s";
    public static final String SIZE_METHOD_AS_STRING = "size()";
    public static final String PATH_STRING = "path";
    public static final String DATE_STRING = "date";
    public static final String UTC_OFFSET_STRING = "utc_offset";
    public static final String COMPETITION_LIST_STRING = "competition_list";
    public static final String NAME_STRING = "name";
    public static final String BIO_STRING = "bio";
    public static final String ID_STRING = "id";
    public static final String MARKET_TYPES_STRING = "market_types";
    public static final String ODD_FORMAT_STRING = "odd_format";
    public static final String ODD_TYPE_STRING = "odd_type";
    public static final String FROM_GAME_TIME_STRING = "from_game_time";
    public static final String TO_GAME_TIME_STRING = "to_game_time";
    public static final String STANDING_TYPE_STRING = "standing_type";
    public static final String INVALID_DATA = "invalid data";
    public static final String INVALID_STRING = "invalid";
    public static final String INVALID_DATA_SENT_STRING = "Invalid data sent";
    public static final String GREATER_THAN_MAX_255_CHARACTERS = "may_not_be_greater_than_|max_255|_characters";
    public static final String INVALID_OFFSET_OR_LIMIT_ERROR = "Invalid offset (min: 0) or limit (min: 1, max: 1000)";
    public static final String INVALID_LIMIT_ERROR_STRING = "Limit must be an integer between 1 and 200.";
    public static final String INVALID_OFFSET_ERROR_STRING = "Offset must be a positive integer.";
    public static final String INVALID_TRANSLATION_LANGUAGE_MESSAGE_STRING = "%s is not a valid language code.";
    public static final String MUST_BE_A_STRING_ERROR_STRING = "must_be_a_string";
    public static final String COMPETITION_ID_UNDERSCORED_STRING = "competition_id";
    public static final String COMPETITION_ID_STRING = "competitionId";
    public static final String TYPE_STRING = "type";
    public static final String PROVIDER_STRING = "provider";
    public static final String NO_GAMES_SELECTED_STRING = "No games selected";
    public static final String SEASON_STRING = "season";
    public static final String SEASONS_STRING = "seasons";
    public static final String PLAYER_IDS_CAMELCASE_STRING = "playerIds";
    public static final String PLAYER_IDS_UNDERSCORED_STRING = "player_ids";
    public static final String SEASON_YEAR_STRING = "season_year";
    public static final String TITLE_STRING = "title";
    public static final String LINK_STRING = "link";
    public static final String ERRORS_STRING = "errors";
    public static final String VIDEO_FIRST_CASE_UPPERCASED_STRING = "Video";
    public static final String GALLERY_STRING = "gallery";
    public static final String GALLERY_FIRST_CASE_UPPERCASED_STRING = "Gallery";
    public static final String AUTOMATED_LIST_STRING = "automated list";
    public static final String EDITORIAL_LIST_STRING = "editorial list";
    public static final String SCHEDULED_LIST_STRING = "scheduled list";
    public static final String CATEGORY_STRING = "category";
    public static final String AUTHOR_STRING = "author";
    public static final String AUTHOR_IDS_CAMELCASE_STRING = "authorIds";
    public static final String AUTHOR_IDS_UNDERSCORED_STRING = "author_ids";
    public static final String PUBLISHED_CHANNELS_CAMELCASE_STRING = "publishedChannels";
    public static final String PUBLISHED_CHANNELS_UNDERSCORED_STRING = "published_channels";
    public static final String TAG_STRING = "tag";
    public static final String FOOTBALL_CONNECTIONS = "Football connections";
    public static final String SPORT_EVENT_STRING = "sport_event";
    public static final String TAG_TYPE_STRING = "%s_type".formatted(TAG_STRING);
    public static final String TAG_IDS_STRING = "%s_ids".formatted(TAG_STRING);
    public static final String EMPTY_STRING = "";
    public static final String TEAMS_STRING = "teams";
    public static final String PLAYERS_STRING = "players";
    public static final String CUSTOM_BLOCK_STRING = "Custom block";
    public static final String SPORTAL_365_STRING = "sportal365";
    public static final String SPORTAL_STRING = "sportal";
    public static final String SPORTAL_QA_CLIENT_STRING = "sportal-qa";
    public static final String ODD_CLIENT_STRING = "odd_client";
    public static final String SPORTALIOS = "sportalios";
    public static final String ALL_STRING = "all";
    public static final String POPULAR_SPORTS_STRING = "popular-sports";
    public static final String POPULAR_ICE_HOCKEY_STRING = "popular-ice-hockey";
    public static final String INVALID_HTTPS_STRING = "https://invalidsite1$3%2a.com/";
    public static final String SPORTAL_365_ID = "2023050522324763707";
    public static final String SMP_STRING = "smp";
    public static final String ACTIVE_STRING = "active";
    public static final String INSTANCE_STRING = "instance";
    public static final String FILE_STRING = "file";
    public static final String INACTIVE = "inactive";
    public static final String HOT_NEWS_STRING = "Hot News";
    public static final String EAST_EUROPE = "east-europe";
    public static final String RSS = "RSS";
    public static final String SPORTAL_BG_STRING = "Sportal BG";
    public static final String AUTOMATIC_CONTENT_STRING = "automatic_content";
    public static final String AUTOMATIC_CONTENT_MODE_STRING = "automatic_content_mode";
    public static final String AUTOMATIC_CONTENT_THRESHOLD_STRING = "automatic_content_threshold";
    public static final String POPULARITY_SETTINGS_STRING = "popularity_settings";
    public static final String CRITERIA_STRING = "criteria";
    public static final String NOT_CENSURED = "Not Censured";
    public static final String NINE_BY_SIXTEEN = "9x16";
    public static final String ENTITY_TYPE_STRING = "entity_type";
    public static final String ENTITY_TYPES_STRING = ENTITY_TYPE_STRING + "s";
    public static final String ENTITY_ID_STRING = "entity_id";
    public static final String EDITOR_BLOCK_STRING = "editor_block";
    public static final String HTML_BLOCK_STRING = "html_block";
    public static final String WIDGET_SMP_V2_STRING = "widget_smp_V2";
    public static final String DATA_QA_STRING = "data-qa";
    public static final String CONTENT_TYPE_STRING = "content_type";
    public static final String CONFIGURATION_STRING = "configuration";
    public static final String HASH_STRING = "hash";
    public static final String CONTENT_STRING = "content";
    public static final String DATA_STRING = "data";
    public static final String META_STRING = "meta";
    public static final String PAGE_META_STRING = "page_meta";
    public static final String ARTICLE_STRING = "article";
    public static final String CREATE_STRING = "create";
    public static final String EDIT_STRING = "updated";
    public static final String UPDATED_STRING = "[UPDATED]";
    public static final String LISTS_STRING = "lists";
    public static final String BANNER_STRING = "Banner";
    public static final String CODE_STRING = "code";
    public static final String ORDER_TYPE_STRING = "order_type";
    public static final String IS_PREFERRED_STRING = "is_preferred";
    public static final String MAIN_MEDIA_STRING = "main_media";
    public static final String MAIN_IMAGE_STRING = "main_image";
    public static final String LIVE_BLOG_STRING = "Live Blog";
    public static final String LIVE_BLOG_NO_SPACES_STRING = "liveblog";
    public static final String LIVE_BLOG_UNDERSCORED_STRING = "LIVE_BLOG";
    public static final String WIKI_STRING = "Wiki";
    public static final String WIKI_PAGE_STRING = "wikipage";
    public static final String TEXT_STRING = "text";
    public static final String TEST_IMAGE_PNG = "testImage.png";
    public static final String TEST_WATERMARK_IMAGE_PNG = "testWatermarkImage.png";
    public static final String TEST_IMAGE_BIG_SIZE_JPG = "BigFileSizeImage.jpg";
    public static final String TEST_IMAGE_BIG_RESOLUTION_JPG = "BigResolutionImage.jpg";
    public static final String TEST_IMAGE_SVG = "testImage.svg";
    public static final String TEST_IMAGE_PDF = "testPdf.pdf";
    public static final String TEST_FLAG_PNG = "testFlag.png";
    public static final String IMAGE_STRING = "image";
    public static final String VIDEO_STRING = "video";
    public static final String DESCRIPTION_STRING = "description";
    public static final String ALT_STRING = "alt";
    public static final String DEFAULT_STRING = "default";
    public static final String LANGUAGE_STRING = "language";
    public static final String DEFAULT_LANGUAGE_STRING = "default_language";
    public static final String LANGUAGE_CODE = "language_code";
    public static final String SLUG_STRING = "slug";
    public static final String CONTAINED_IN_DOMAIN_STRING = "contained_in_domain";
    public static final String CONTAINED_IN_PLACE_STRING = "contained_in_place";
    public static final String CONTAINED_IN_ORGANIZATION_STRING = "contained_in_organization";
    public static final String NATIONALITY_STRING = "nationality";
    public static final String BIRTH_PLACE_STRING = "birth_place";
    public static final String ROLE_STRING = "role";
    public static final String ROLES_STRING = ROLE_STRING + "s";
    public static final String ORGANIZATION_STRING = "organization";
    public static final String PLACE_STRING = "place";
    public static final String SUCCESS_STRING = "SUCCESS";
    public static final String PROJECT_STRING = "Project";
    public static final String X_PROJECT_STRING = "X-Project";
    public static final String AUTHORIZATION_STRING = "Authorization";
    public static final String DECIMAL_STRING = "DECIMAL";
    public static final String FRACTIONAL_STRING = "FRACTIONAL";
    public static final String MONEYLINE_STRING = "MONEYLINE";
    public static final String NO_OPTIONS_STRING = "No options";
    public static final String DOMAIN_STRING = "domain";
    public static final String PARAGRAPH_STRING = "paragraph";
    public static final String HEADING_STRING = "heading";
    public static final String CLASS_STRING = "class";
    public static final String QUOTE_STRING = "quote";
    public static final String LIST_STRING = "list";
    public static final String TABLE_STRING = "table";
    public static final String ABOUT_BLANK_STRING = "about:blank";
    public static final String FIELD_MUST_NOT_BE_EMPTY_STRING = "Field must not be empty";
    public static final String NO_DATE_SELECTED_STRING = "No date selected";
    public static final String NO_MATCHES_ARE_CURRENTLY_SELECTED = "No matches are currently selected";
    public static final String PLEASE_SELECT_IMAGE = "Please select image!";
    public static final String TEMP_ID_STRING = "temp-id";
    public static final String IDS_STRING = "ids";
    public static final String COACHES_STRING = "coaches";
    public static final String START_TIME_UNDERSCORED_STRING = "start_time";
    public static final String COACH_STRING = "coach";
    public static final String FOOTBALL_API_STRING = "football-api";
    public static final String SPORTS_SEARCH_API_STRING = "sports-search-api";
    public static final String ARENA_STRING = "arena";
    public static final String MATCH_STRING = "match";
    public static final String SPORT_STRING = "sport";
    public static final String ROUND_FILTER_STRING = "round_filter";
    public static final String COMPETITION_IDS_STRING = "competition_ids";
    public static final String EVENT_START_TIME_STRING = "event_start_time";
    public static final String INPUT_LANGUAGE_STRING = "input_language";
    public static final String TRANSLATION_LANGUAGE_STRING = "translation_language";
    public static final String TEAM_STRING = "team";
    public static final String COMPETITION_STRING = "competition";
    public static final String PARTICIPANTS_STRING = "participants";
    public static final String PARTICIPANTS_FILTER = "participants_filter";
    public static final String FROM_START_TIME = "from_start_time";
    public static final String COUNTRY_CODE_STRING = "country_code";
    public static final String ASSET_TYPE_STRING = "asset_type";
    public static final String ASSET_PATH_STRING = "asset_path";
    public static final String NUMBER_STRING = "number";
    public static final String COUNTRY_ISO_CODE_STRING = "country_iso_code";
    public static final String TO_START_TIME = "to_start_time";
    public static final String EUROPE_SOFIA_STRING = "Europe/Sofia";
    public static final String EN_STRING = "en";
    public static final String VALIDATION_ERROR_STRING = "VALIDATION_ERROR";
    public static final String OBJECT_STRING = "object";
    public static final String PLAYER_STRING = "player";
    public static final String PLAYER_ID_UNDERSCORED = "player_id";
    public static final String GAME_STRING = "game";
    public static final String ERROR_CODE_STRING = "error_code";
    public static final String LOG_REFERENCE_STRING = "log_reference";
    public static final String GAMES_STRING = GAME_STRING + "s";
    public static final String ADD_BLOCKY_MODAL_LOCATOR = "//div[contains(@class,'live-blog-editorial-admin-widget-modal')]";
    public static final String EDIT_BLOCKY_MODAL_LOCATOR = "//div[@class='modal fade show']//div[@class='live-blog-editorial-admin-new-post-container']";
    public static final String SPORTAL_URL = "https://sportal.bg/";
    public static final String CAPTION_STRING = "caption";
    public static final String WIDTH_STRING = "width";
    public static final String WEBSITE_STRING = "website";
    public static final String SOCIAL_MEDIA_LINKS_STRING = "social_media_links - %s";
    public static final String DISPLAY_ASSET_STRING = "display_asset";
    public static final String ICON_STRING = "icon";
    public static final String ALIGNMENT_STRING = "alignment";
    public static final String BAD_REQUEST_STRING = "Bad Request";
    public static final String YES_STRING = "Yes";
    public static final String NO_STRING = "No";
    public static final String RESULTS_STRING = "results";
    public static final String SORT_ORDER_STRING = "sort_order";
    public static final String OPTIONAL_DATA_STRING = "optional_data";
    public static final String MINUTE_STRING = "minute";
    public static final String INJURY_MINUTE_STRING = "injury_%s".formatted(MINUTE_STRING);
    public static final String ODD_CLIENT = "oddClient";
    public static final String DISPLAY_ODDS = "displayOdds";
    public static final String ODDS_STRING = "odds";
    public static final String BODY = "body";
    public static final String FORM_STRING = "form";
    public static final String SHOW_STRING = "Show";
    public static final String BODY_DATA = "%s -> data".formatted(BODY);
    public static final String BODY_DATA_CONFIG = "%s -> config".formatted(BODY_DATA);
    public static final String BODY_DATA_CONFIG_OPTIONS = "%s -> options".formatted(BODY_DATA_CONFIG);
    public static final String ROUNDS_FILTER = "roundsFilter";
    public static final String TOURNAMENT_STRING = "tournament";
    public static final String SORT_DIRECTION_RESULTS = "sortDirectionResults";
    public static final String SORT_DIRECTION_FIXTURES = "sortDirectionFixtures";
    public static final String DATE_SELECTION_STRING = "date_selection";
    public static final String SELECTION_DATE_STRING = "selection_date";
    public static final String FILTER_DATE_STRING = "filter_date";
    public static final List<String> SUPPORTED_SPORTS_LIST = new ArrayList<>(Arrays.asList("Football", "Basketball", "Tennis", "Ice hockey", "Handball", "Cycling", "Cricket", "Futsal", "Table tennis", "Horse racing", "Roller hockey", "Rugby league", "Rugby union", "Motorsports"));
    public static final String DARK_THEME_STRING = "Dark theme";
    public static final String DISPLAYED_STRING = "displayed";
    public static final String NOT_DISPLAYED_STRING = "not " + DISPLAYED_STRING;
    public static final String FIXTURES_STRING = "Fixtures";
    public static final String REFEREES_STRING = "referees";
    public static final String ROUNDS_STRING = "rounds";
    public static final String VENUES_STRING = "venues";
    public static final String VENUE_STRING = "venue";
    public static final String MATCHES_STRING = "matches";
    public static final String OPACITY_STRING = "opacity";
    public static final String POSITION_STRING = "position";
    public static final String MULTIPART_FORM_DATA_STRING = "multipart/form-data";
    public static final String DELETE_EXISTING_STRING = "delete_existing";
    public static final String IMAGE_PATH_STRING = "image_path";
    public static final String WATERMARK_ID_STRING = "watermark_id";
    public static final String MULTI_QA = "multi-qa";
    public static final String CATEGORY_IDS_STRING = "categoryIds";
    public static final String AUTOMATED_CONTENT_STRING = "Automated content";
    public static final String EDITORIAL_CONTENT_STRING = "Editorial content";
    public static final String SELECTED_SECOND_TEAM = "Selected Second Team";
    public static final String SELECTED_FIRST_TEAM_STRING = "Selected First Team";
    public static final String PAGE_1_STRING = "&page=1";
    public static final String EXCLUDE_ORIGIN_ID_STRING = "exclude_origin_id=%s";
    public static final String ORIGIN_ID_STRING = "originId=%s";
    public static final String SELECTED_ELEMENTS_DO_NOT_MEET_THE_REQUIREMENTS_STRING = "The selected elements do not meet the requirements of the list. Minimum items 0, maximum items 0";
    public static final String CLUB_STRING = "club";
    public static final String NATIONAL_TEAM_STRING = "national";
    public static final String SUGGEST_STRING = "suggest";
    public static final String FOOTBALL_WIDGETS_VERSION_STRING = "footballWidgetsVersion";
    public static final String TENNIS_WIDGETS_VERSION_STRING = "tennisWidgetsVersion";
    public static final String BASKETBALL_WIDGETS_VERSION_STRING = "basketballWidgetsVersion";
    public static final String ICE_HOCKEY_WIDGETS_VERSION_STRING = "iceHockeyWidgetsVersion";
    public static final String MULTI_SPORT_WIDGETS_VERSION_STRING = "multiSportWidgetsVersion";
    public static final String RSM_ARTICLES_STRING = "RSM Articles";
    public static final String AI_ARTICLES_GENERATION_STRING = "AI Article Generation Origin";
    public static final String INVALID_TYPE = "invalidType";
    public static final String INVALID_SLUG = "invalidSlug";
    public static final String ORIGIN_STRING = "origin";
    public static final String AUTOMATIC_CALCULATION_STRING = "AUTOMATIC_CALCULATION";
    public static final String COMPETITION_FILTER_STRING = "Competition filter";
    public static final String PARTICIPAN_TYPE_STRING = "participant_type";
    public static final String PARTICIPANT_ID_STRING = "participant_id";
    public static final String PARTICIPANT_IDS_STRING = PARTICIPANT_ID_STRING + "s";
    public static final String TEAM_TYPE_STRING = "team_type";
    public static final String GROUP_BY_STRING = "group_by";
    public static final String START_TIME_STRING = "startTime";
    public static final String NEWS_FORGE_STRING = "News Forge";
}