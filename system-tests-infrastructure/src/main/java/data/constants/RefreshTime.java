package data.constants;

import data.utils.EnumUtils;
import data.widgets.options.enums.DataRefreshTimeEnum;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
public enum RefreshTime {

    NEVER("Never"),
    SUPER_SLOW("Super slow"),
    SLOW("Slow"),
    MEDIUM("Medium"),
    FAST("Fast"),
    SUPER_FAST("Super fast");

    private final String value;

    RefreshTime(String value) {
        this.value = value;
    }

    public static List<String> getEnumValues() {
        return Stream.of(RefreshTime.values()).map(e -> e.value).toList();
    }

    public static DataRefreshTimeEnum getRandEnumValue() {
        return EnumUtils.getRandomEnumValue(DataRefreshTimeEnum.class);
    }
}
