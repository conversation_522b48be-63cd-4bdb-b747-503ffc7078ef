package data.constants;

import data.configuration.SportalSettings;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum ImageApiUrl {

    WATERMARKS(ConfigurationService.get(SportalSettings.class).getImageApiUrl() + "/watermarks"),
    WATERMARKS_IMAGE(WATERMARKS.getUrl() + "/image"),
    UPLOAD(ConfigurationService.get(SportalSettings.class).getImageApiUrl() + "/upload"),
    PROCESS(ConfigurationService.get(SportalSettings.class).getImageApiUrl() + "/process"),
    PROCESS_INTEGRATION(PROCESS.getUrl() + "/smp-image-api-shared-region-integration"),
    TEAM_LOGO(PROCESS_INTEGRATION.getUrl() + "/assets/team/logo/%s");

    private final String url;

    ImageApiUrl(String url) {
        this.url = url;
    }
}