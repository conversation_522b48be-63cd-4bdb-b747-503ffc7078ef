package data.constants;

import lombok.Getter;

@Getter
public enum BlockyField {

    TEAM("Team"),
    SEASON("Season"),
    STAGE("Stage"),
    ROUND("Round"),
    ROUNDS("Rounds"),
    MATCH_TYPE("Match type"),
    SORT_DIRECTION_RESULTS("Sort direction Results"),
    SORT_DIRECTION_FIXTURES("Sort direction Fixtures"),
    DISPLAY_ODDS("Display odds"),
    SELECT_BOOKMAKER("Select bookmaker"),
    REFRESH_TIME("Refresh time"),
    CHOOSE_VIDEO("Choose video"),
    START_AT("Start at"),
    COMPETITION("Competition"),
    TOURNAMENT("Tournament"),
    PLAYER("Player"),
    LIMIT("Limit"),
    START_FROM_POSITION("Start from position"),
    GENDER("Gender");

    private final String value;

    BlockyField(String value) {
        this.value = value;
    }
}
