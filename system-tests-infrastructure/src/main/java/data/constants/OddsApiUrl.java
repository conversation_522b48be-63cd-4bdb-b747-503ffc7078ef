package data.constants;

import data.configuration.SportalSettings;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum OddsApiUrl {

    CONFIGURATIONS(ConfigurationService.get(SportalSettings.class).getOddsApiUrl() + "/configurations"),
    FOOTBALL_CONFIGURATION(ConfigurationService.get(SportalSettings.class).getOddsApiUrl() + "/configurations/football/sportal"),
    BASKETBALL_CONFIGURATION(ConfigurationService.get(SportalSettings.class).getOddsApiUrl() + "/configurations/basketball/sportal365"),
    TENNIS_CONFIGURATION(ConfigurationService.get(SportalSettings.class).getOddsApiUrl() + "/configurations/tennis/%s"),
    CONFIGURATIONS_BY_SPORT(CONFIGURATIONS.getUrl() + "/%s/%s"),
    ODDS(ConfigurationService.get(SportalSettings.class).getOddsApiUrl() + "/odds");

    private final String url;

    OddsApiUrl(String url) {
        this.url = url;
    }
}