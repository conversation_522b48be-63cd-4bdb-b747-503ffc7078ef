package data.constants.apiurls;

import data.configuration.SportalSettings;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum SportEntityCustomizationApiUrl {

    TRANSLATIONS(ConfigurationService.get(SportalSettings.class).getSportEntityCustomizationApiUrl() + "/translations"),
    ASSETS_UPLOAD(ConfigurationService.get(SportalSettings.class).getSportEntityCustomizationApiUrl() + "/assets/upload");

    private final String url;

    SportEntityCustomizationApiUrl(String url) {
        this.url = url;
    }
}