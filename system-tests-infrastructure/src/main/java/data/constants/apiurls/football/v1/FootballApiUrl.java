package data.constants.apiurls.football.v1;

import data.configuration.SportalSettings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
@AllArgsConstructor
public enum FootballApiUrl {

    BASE_URL(ConfigurationService.get(SportalSettings.class).getFootballApiUrl()),
    COUNTRIES(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "countries"),
    EVENTS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "events/"),
    EVENTS_BY_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "events/%s"),
    EVENTS_BY_ID_ODDS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "events/%s/odds"),
    TEAMS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "teams"),
    TEAMS_BY_NAME(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "teams?name="),
    MATCHES_BY_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?match_ids=%s"),
    MATCHES(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches"),
    MATCHES_NOT_STARTED_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?team_ids=%s&status_types=notstarted"),
    MATCHES_FINISHED_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?team_ids=%s&status_types=finished"),
    MATCHES_INPROGRESS_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?team_ids=%s&status_types=inprogress"),
    MATCHES_INTERRUPTED_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?team_ids=%s&status_types=interrupted"),
    MATCHES_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "matches?team_ids=%s&status_types=%s"),
    ODDS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "odds"),
    ODD_PROVIDERS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "odd_providers"),
    PLAYERS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "players"),
    TOURNAMENTS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/"),
    TOURNAMENTS_SEASON(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/"),
    TOURNAMENTS_SEASON_CARD_LIST(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/SEASON_ID/cardlist"),
    TOURNAMENTS_SEASON_TEAMS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/%s/teams"),
    TOURNAMENTS_SEASON_TOP_SCORER(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/%s/topscorer"),
    TOURNAMENTS_SEASON_EVENTS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/%s/events"),
    TOURNAMENTS_STAGE_TEAMS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/stages/%s/teams"),
    TOURNAMENTS_SEASONS_STAGE_ACTIVE(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/stages/active"),
    TOURNAMENTS_STAGE(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "tournaments/seasons/stages/%s?expand=standing"),
    PLAYERS_BY_TEAM_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "teams/%s/players"),
    TEAMS_SEASONS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "teams/%s/players/statistics/seasons"),
    PLAYER_SEASONS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "teams/%s/players/statistics"),
    PLAYERS_BY_NAME(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "players?name=%s"),
    TEAM_SEASONS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "v2/seasons?team_id=%s&language_code=en"),
    TEAM_MATCHES(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "v2/matches?team_ids=%s&limit=200&offset=0&sort_direction=asc&status_types=LIVE,NOT_STARTED&language_code=en"),
    LIVESCORE_MATCHES(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "v2/matches/livescore?language_code=en&match_ids=%s&limit=1&offset=0&odd_client=sportal365&odd_type=PRE_EVENT"),
    STATISTICS_BY_PLAYERS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "statistics/players"),
    STATISTICS_BY_PLAYER_ID(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "statistics/players?player_ids=%s&language_code=en"),
    STATISTICS_TEAMS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "statistics/teams?language_code=en&team_ids=%s&season_ids=%s"),
    SEARCH(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "search?query="),
    STAGES(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "stages"),
    TEAMSTATS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "events/%s/teamstats"),
    TEAMS_STATISTICS(ConfigurationService.get(SportalSettings.class).getFootballApiUrl() + "statistics/teams");

    public final String url;
}