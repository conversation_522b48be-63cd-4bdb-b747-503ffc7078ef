package data.constants.apiurls;

import data.configuration.SportalSettings;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum StandingApiUrl {

    TENNIS_RANKINGS(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/tennis/v2/rankings/available?scope=CURRENT&translation_language=en"),
    TENNIS_RANKINGS_BY_ID(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/tennis/v2/rankings/%s?translation_language=en"),
    PARTICIPANT_RANKINGS(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/%s/v2/rankings"),
    AVAILABLE_STANDINGS(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/%s/v2/standings/available"),
    AVAILABLE_SEASONS(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/%s/v2/seasons/available"),
    STANDINGS(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/standings/%s"),
    STANDING_TYPES(ConfigurationService.get(SportalSettings.class).getStandingApiUrl() + "/standing_types");

    private final String url;

    StandingApiUrl(String url) {
        this.url = url;
    }
}