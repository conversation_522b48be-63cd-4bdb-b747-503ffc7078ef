package data.constants;

import data.constants.enums.CustomEntityEnum;
import data.constants.liveblog.LiveBlogPostsTimelinePostType;
import data.constants.liveblog.TagType;

import java.util.Collection;
import java.util.EnumSet;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class AssertMessages {

    public static String slugDoesNotMatch() {
        return "The slug does not match the expected value.";
    }

    public static String seoTitleDoesNotMatch() {
        return "The SEO title does not match the expected value.";
    }

    public static String authorNotPresentInRequest(String expectedAuthor, String expectedAuthorId) {
        return String.format("Expected author '%1$s' with id: '%2$s' is not present in the request!", expectedAuthor, expectedAuthorId);
    }

    public static String authorIdMismatch(String expectedAuthor, String expectedAuthorId) {
        return String.format("Expected author '%1$s' with id: '%2$s' does not match the actual value!", expectedAuth<PERSON>, expectedAuthorId);
    }

    public static String seoTitleDoesNotMatchArticleTitle(String expectedSeoTitle, String actualSeoTitle) {
        return String.format("Expected SEO title to be '%s', but found '%s'. The SEO title does not match the article title.", expectedSeoTitle, actualSeoTitle);
    }

    public static String slugDoesNotMatch(String expectedSlug, String actualSlug) {
        return String.format("Expected slug '%s', but found '%s'. The generated slug does not match the expected value.", expectedSlug, actualSlug);
    }

    public static String noGamesDisplayed(String numberOfMatches, String actualMessage) {
        return String.format("Expected 'No Games to Show' message, but found '%s'. Matches displayed: %s", actualMessage, numberOfMatches);
    }

    public static String expectedEventDate(String expectedDate, String actualDate) {
        return String.format("Expected event date to be '%s', but found '%s'. Please check the date formatting or data mapping.", expectedDate, actualDate);
    }

    public static String entityNotExpected(String entity) {
        return String.format("The actual '%s' is not the expected one!", entity);
    }

    public static String defaultSportNotSelected(String expectedSport) {
        return String.format("The expected default sport '%s' is not selected!", expectedSport);
    }

    public static String notAllExpectedSportsListed(List<String> expectedSports, List<String> actualSports) {
        return String.format("Not all expected sports are listed!\nExpected: %s\nActual: %s", expectedSports, actualSports);
    }

    public static String expectedEntityState(String entityName, String expectedState, String actualState) {
        return String.format("Expected %s to be '%s', but it was '%s'", entityName, expectedState, actualState);
    }

    public static String matchResultsNotExpectedToBeEmpty(String context) {
        return String.format("Match results are empty in the %s context, but they are not expected to be!", context);
    }

    public static String matchResultsExpectedToBeEmpty(String context) {
        return String.format("Match results are not empty in the %s context, but they are expected to be!", context);
    }

    public static String matchResultsNotExpectedToBeSame() {
        return "Match results are still the same after filtering, but they are not expected to be!";
    }

    public static String entityNotExpected(String entity, Object expected, Object actual) {
        return String.format("The actual '%s' is not the expected one! \r\nExpected: %s\r\nActual: %s", entity, expected, actual);
    }

    public static String expectedEntityNotFound(String expectedEntity, String actualEntity) {
        return String.format("Expected entity doesn't exist! Expected entity is: %s but Actual entity is: %s", expectedEntity, actualEntity);
    }

    public static String expectedTagNotFound(String expectedTag, String actualTag) {
        return String.format("Expected tag doesn't exist! Expected tag is: %s but Actual tag is: %s", expectedTag, actualTag);
    }

    public static String quoteBlockTextNotCorrect(String expectedText, String actualText) {
        return String.format("Quote block text is not correct! \r\nExpected: %s\r\nActual: %s", expectedText, actualText);
    }

    public static String sizeOfBodyObjectNotCorrect(int expectedSize, int actualSize) {
        return String.format("Size of body object is not correct! \r\nExpected: %d\r\nActual: %d", expectedSize, actualSize);
    }

    public static String paragraphsNotEmptyAfterSave() {
        return "There is one or more paragraphs on the page after save.";
    }

    public static String secondParagraphNotEmpty() {
        return "Second paragraph is not empty.";
    }

    public static String quoteBlockIsEmpty() {
        return "Quote block is empty.";
    }

    public static String expectedSuggestionNotFound() {
        return "Expected suggestions are not displayed!";
    }

    public static String expectedTypeNotFound(String expectedType, String entityName) {
        return String.format("The expected type '%s' doesn't exist in the '%s' object", expectedType, entityName);
    }

    public static String teamTagLeftBorderColorNotExpected(Object expected, Object actual) {
        return String.format("Team Tag Suggestion doesn't have the proper Left Red Border Color! \r\nExpected: %s\r\nActual: %s", expected, actual);
    }

    public static String footballConnectionsMissingTags(Collection<String> missingTags) {
        return String.format("The " + StringConstants.FOOTBALL_CONNECTIONS + " don't contain all of the suggested tags! Missing: %s", missingTags);
    }

    public static String suggestedTagsNotCleared(Collection<String> remainingTags) {
        return String.format("Not all added tags have disappeared from the suggested list! Remaining: %s", remainingTags);
    }

    public static String teamTagBottomBorderColorNotExpected(Object expected, Object actual) {
        return String.format("Team Tag Suggestion doesn't have the proper Bottom Red Border Color! \r\nExpected: %s\r\nActual: %s", expected, actual);
    }

    public static String playerTagLeftBorderColorNotExpected(Object expected, Object actual) {
        return String.format("Player Tag Suggestion doesn't have the proper Left Green Border Color! \r\nExpected: %s\r\nActual: %s", expected, actual);
    }

    public static String playerTagBottomBorderColorNotExpected(Object expected, Object actual) {
        return String.format("Player Tag Suggestion doesn't have the proper Bottom Green Border Color! \r\nExpected: %s\r\nActual: %s", expected, actual);
    }

    public static String specificSuggestionNotFound(String tag) {
        return String.format("The expected suggestion '%s' is not displayed!", tag);
    }

    public static String expectedResultNotFound(String title) {
        return String.format("The expected result title '%s' is not existing!", title);
    }

    public static String lessExpectedTags() {
        return "There are fewer than expected tags!";
    }

    public static String tagsNotAppliedAsExpected(int expected, int actual) {
        return String.format("Tags Suggestions are not applied as expected! Expected: %d, Actual: %d", expected, actual);
    }

    public static String tagsSuggestionsNotIncludingExpected(Collection expected, Collection actual) {
        return String.format("First Tags Suggestions don't include all of the expected tags! Expected: %s, Actual: %s", expected, actual);
    }

    public static String tagNotRemovedFromSuggestions(int expectedSize, int actualSize) {
        return String.format("The applied tag is not removed from the suggestions list! Expected size: %d, Actual size: %d", expectedSize, actualSize);
    }

    public static String tagStillInSuggestions(String tag, Collection<String> suggestions) {
        return String.format("The applied tag '%s' is still present in the suggestions list! Current suggestions: %s", tag, suggestions);
    }

    public static String expectedResultShouldNotExist(String title) {
        return String.format("The expected result title '%s' exists, but it is not expected!", title);
    }

    public static String tagsNotProperlyDisplayed(Object expected, Object actual) {
        return String.format("Tags from the API AutoTagging call are not properly displayed! Expected: %s, Actual: %s", expected, actual);
    }

    public static String tagNotFound(Object actual) {
        return String.format("Football tag not found in response. %s", actual);
    }

    public static String expectedEntityTypeNotFound(String entity, String entityType, Object actual) {
        return String.format("Tag '%s' is expected to have %s entities in the API Response body, but it is not! %s", entity, entityType, actual);
    }

    public static String expectedSportNotFound(String entity, String sport, Object actual) {
        return String.format("Tag '%s' is expected to have %s entities in the API Response body, but it is not! %s", entity, sport, actual);
    }

    public static String unexpectedNumberOfSuggestions(String entity) {
        return String.format("Tag '%s' has an unexpected number of suggestions!", entity);
    }

    public static String apiTagsDisplayMismatch(Collection expectedTags, Collection actualTags) {
        return String.format("Tags from the API AutoTagging call are not properly displayed! Expected: %s, Actual: %s", expectedTags, actualTags);
    }

    public static String noLinkedTextFound() {
        return "No linked text was found";
    }

    public static String anchorHrefNotExpected(String expected, String actual) {
        return String.format("The anchor doesn't have the expected href! Expected: %s, Actual: %s", expected, actual);
    }

    public static String anchorTextNotExpected(String expected, String actual) {
        return String.format("The anchor doesn't have the expected text! Expected: %s, Actual: %s", expected, actual);
    }

    public static String anchorDataTypeNotExpected(String expected, String actual) {
        return String.format("The anchor doesn't have the expected data type! Expected: %s, Actual: %s", expected, actual);
    }

    public static String anchorIdNotExpected(String expected, String actual) {
        return String.format("The anchor doesn't have the expected id! Expected: %s, Actual: %s", expected, actual);
    }

    public static String anchorClassNotExpected(String expectedClass) {
        return String.format("The anchor doesn't contain the expected class: %s", expectedClass);
    }

    public static String entityShouldNotBeVisible(String entity) {
        return String.format("The '%s' is expected to be not visible, but it is!", entity);
    }

    public static String entityNotVisible(String entity) {
        return String.format("The '%s' is expected to exist and to be visible, but it is not!", entity);
    }

    public static String entityNotVisible(String entity, String expected, String actual) {
        return String.format("The '%s' is expected to exist and to be visible, but it is not! Expected: %s, Actual: %s", entity, expected, actual);
    }

    public static String entityShouldNotExist(String entity) {
        return String.format("The '%s' is expected to not exist, but it exists!", entity);
    }

    public static String collectionsNotEqual(String entity) {
        return String.format("The collections of '%s' are not equal!", entity);
    }

    public static String collectionsNotEqual(String entity, Object expected, Object actual) {
        return String.format("The collections of '%s' are not equal! Expected: %s\r%nActual: %s", entity, expected, actual);
    }

    //API
    public static String statusCodeNotExpected(String statusCode) {
        return String.format("The response status code is not '%s', but it must be!", statusCode);
    }

    public static String statusCodeNotExpected(int statusCode) {
        return String.format("The response status code is not '%d', but it must be!", statusCode);
    }

    public static String requestNotContains(String entity) {
        return String.format("The expected '%s' doesn't exist in the request, or is not the expected one!", entity);
    }

    public static String requestNotContains(String entity, Object actual) {
        return String.format("The expected '%s' doesn't exist in the request, or is not the expected one! Actual request: %s", entity, actual);
    }

    public static String responseNotContains(String entity) {
        return String.format("The expected '%s' doesn't exist in the response, or is not the expected one!", entity);
    }

    public static String responseNotContains(String entity, Object actual) {
        return String.format("The expected '%s' doesn't exist in the response, or is not the expected one! Actual response: %s", entity, actual);
    }

    public static String requestNotCreated(String entity) {
        return String.format("The expected '%s' is not created and is not listed on the Proxy history!", entity);
    }

    public static String responseNotReceived(String entity) {
        return String.format("The expected '%s' is not received and is not listed on the Proxy history!", entity);
    }

    public static String responseNotExpected() {
        return "The response message doesn't exist, or is not the expected one!";
    }

    public static String liveBlogNotReturnedFromApi() {
        return "Created LiveBlog post was not returned from API.";
    }

    public static String failedToParseParameter(String parameter) {
        return String.format("Failed to parse parameter %s", parameter);
    }

    public static String liveBlogPostNotReturnedFromApi() {
        return "Created LiveBlog post was not returned from API.";
    }

    public static String liveBlogPostParagraphIncorrectlyFormatting(String condition) {
        return String.format("Created LiveBlog Post has incorrect paragraph formatting - %s.", condition);
    }

    public static String liveBlogPostIncorrectProperty(String value) {
        return String.format("Created LiveBlog Post has incorrect %s.", value);
    }

    public static String objectValueNotCorrect(String value, String objectName) {
        return String.format("'%s' value inside '%s' object not correct", value, objectName);
    }

    public static String liveBlogTagNotUpdated(TagType tagType) {
        return String.format("Live blog %s not updated", tagType.name());
    }

    public static String liveBlogTagNotDeleted(TagType tagType) {
        return String.format("Live blog %s not deleted", tagType.name());
    }

    public static String responseNull(String endpoint) {
        return "Response for '%s' endpoint is null but it should not be".formatted(endpoint);
    }

    public static String incorrectDefaultStateOfField(String field) {
        return "Default state of '%s' field not correct".formatted(field);
    }

    public static String requiredRequestParameter(String requestParameter, MethodParameterType parameterType) {
        return "Required request parameter '%s' for method parameter type %s is not present".formatted(requestParameter, parameterType.getType());
    }

    public static String missingFilter(String filter) {
        return "Missing %s filter".formatted(filter);
    }

    public static String requiredStageOrCompetitionParameter() {
        return "One of ['stage_id', 'competition_id'] filters must be provided";
    }

    public static String requiredSportOrCompetitionIdsParameter() {
        return "One of sport or competition_ids parameters must be provided";
    }

    public static String requiredSportQueryParamNotCorrect() {
        return "Invalid sport for competition-events search";
    }

    public static String entityNotDeleted(String entity) {
        return "%s entity not deleted, but it should be.".formatted(entity);
    }

    public static String toastMessageNotDisplayedFor(String entity, String action) {
        return "Toast message for %s %s is not displayed".formatted(action, entity);
    }

    public static String responsesAreNotEqual() {
        return responsesAreNotEqual("");
    }

    public static String responsesAreNotEqual(String entity) {
        return "%s Responses are not equal".formatted(entity);
    }

    public static String userNotRedirectedToCreatePageFor(String entity) {
        return "User not redirected to create %s page".formatted(entity);
    }

    public static String editPageNotLoaded(String entity) {
        return "Edit %s page not loaded. ".formatted(entity.toLowerCase());
    }

    public static String paragraphBlockNotAdded() {
        return "Paragraph block is not added";
    }

    public static String headingBlockNotAdded() {
        return "Heading block is not added";
    }

    public static String quoteBlockNotAdded() {
        return "Quote block is not added";
    }

    public static String listBlockNotAdded() {
        return "List block is not added";
    }

    public static String tableBlockNotAdded() {
        return "Table block is not added";
    }

    public static String paragraphTextNotCorrect(int numberOfParagraph) {
        return "%d Paragraph text is not as expected".formatted(numberOfParagraph);
    }

    public static String paragraphCountNotCorrect() {
        return "Paragraphs count is not as expected";
    }

    public static String entityCountNotCorrect(String entity) {
        return "'%s' count is not as expected".formatted(entity);
    }

    public static String incorrectValueOfField(BlockyField field) {
        return "Value of '%s' field is not as expected".formatted(field.getValue());
    }

    public static String multiSportCompetitionListNotDeleted(String code) {
        return String.format("There is no competition list with identifier %s", code);
    }

    public static String invalidSportSelection() {
        String validSports = EnumSet.allOf(MultiSportSportsEnum.class).stream()
                .filter(sport -> sport != MultiSportSportsEnum.NOT_EXISTING && sport != MultiSportSportsEnum.ALL)
                .map(MultiSportSportsEnum::getValue)
                .collect(Collectors.joining(", "));

        return "Invalid sport selection. The sport options are: " + validSports;
    }

    public static String fieldEmpty(BlockyField field) {
        return "'%s' field is empty but should not be".formatted(field.getValue());
    }

    public static String defaultStateOfFieldIncorrect(BlockyField field) {
        return "Default state of '%s' field is incorrect".formatted(field.getValue());
    }

    public static String valueOfPropertyNotCorrect(String property, String propertyPlacedIn) {
        return "Incorrect value of property '%s', placed in: %s".formatted(property, propertyPlacedIn);
    }

    public static String valueOfPropertyNotCorrect(String property) {
        return "Incorrect value of property '%s'".formatted(property);
    }

    public static String valueOfPropertyNotCorrect(String property, Object expectedValue, Object actualValue) {
        return valueOfPropertyNotCorrect(property) + ". Expected: " + expectedValue + ", but was: " + actualValue;
    }

    public static String fieldCannotBeEmpty(String field) {
        return "Field `%s` cannot be empty.".formatted(field);
    }

    public static String fieldContainsInvalidUUID(String field) {
        return "Field `%s` contains an invalid UUID.".formatted(field);
    }

    public static String entityWithIdOrSlugDoesNotExist(String entity, String uuid) {
        return "%s with id or slug %s does not exist.".formatted(entity, uuid);
    }

    public static String domainDoesNotExistFor(CustomEntityEnum customEntityEnum) {
        return "The %s's domain does not exist.".formatted(customEntityEnum.getValue().toLowerCase(Locale.ROOT));
    }

    public static String fieldIsInvalid(String field) {
        return "Field `%s` is invalid.".formatted(field);
    }

    public static String entityNotExistForProject(String uuid) {
        return "Entity with ID %s does not exist for this project.".formatted(uuid);
    }

    public static String tagScoreErrorMessage(String entityName, int expectedScore, Long actualScore) {
        return "Tag score for %s is not updated. It should be %s, but it is %s"
                .formatted(entityName, expectedScore, actualScore);
    }

    public static String invalidStartTimeFormatErrorMessage() {
        return "Invalid start time format. Please provide a valid date time in ISO format.";
    }

    public static String incorrectContentOfLiveBlogPost(LiveBlogPostsTimelinePostType liveBlogPostsTimelinePostType) {
        return "Content of %s post is not correct.".formatted(liveBlogPostsTimelinePostType.getValue());
    }

    public static String entityNotFound(String entityName) {
        return "%s was not found".formatted(entityName);
    }

    public static String requiredPartNotPresent(String value) {
        return "Required part '%s' is not present.".formatted(value);
    }

    public static String errorMessageDoesNotMatch(String actualMessage, String expectedMessage) {
        return String.format("The displayed error message '%s' does not match the expected message '%s'.", actualMessage, expectedMessage);
    }

    public static String incorrectNumberOfSelectedContent() {
        return "The number of selected content items does not match the expected maximum automatic list items.";
    }

    public static String bookmakerSelectVisible() {
        return "Bookmaker select is visible, but should not be";
    }

    public static String displayOddsCheckboxNotChecked() {
        return "Display Odds checkbox should be checked, but it is not";
    }

    public static String tableStructureMismatch(String expected, String actual) {
        return String.format("Table structure mismatch after merge operation!\nExpected: '%1$s'\nActual:   '%2$s'",
                expected, actual);
    }

    public static String valuesInFieldNotCorrect(String field, Object expected, Object actual) {
        return "Value/s in '%s' field is/are not correct. Expected: '%s', but was: '%s'".formatted(field, expected, actual);
    }

    public static String tournamentSeasonNameMismatch(String expected, String actual) {
        return "Tournament season name should be '%s' but was '%s'"
                .formatted(expected, actual);
    }

    public static String responseCurrentSeasonShouldBePresent() {
        return "Current season should not be null";
    }

    public static String responseCurrentSeasonShouldBeNull() {
        return "Current season should be null";
    }
}