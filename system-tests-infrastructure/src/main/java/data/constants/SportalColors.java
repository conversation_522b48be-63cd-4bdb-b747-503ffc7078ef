package data.constants;

import lombok.Getter;
import org.openqa.selenium.support.Color;

@Getter
public enum SportalColors {

    GREY_900(new Color(20, 20, 20, 1.0)),
    GREY_800(new Color(38, 38, 38, 1.0)),
    GREY_600(new Color(67, 67, 67, 1.0)),
    GREY_500(new Color(78, 78, 78, 1.0)),
    GREY_890(new Color(89, 89, 89, 1.0)),
    GREY_400(new Color(140, 140, 140, 1.0)),
    GREY_300(new Color(217, 217, 217, 1.0)),
    GREY_200(new Color(240, 240, 240, 1.0)),
    PRIMARY_GREY(new Color(153, 153, 153, 1.0)),
    ORANGE_500(new Color(255, 144, 26, 1.0)),
    WHITE_250(new Color(250, 250, 250, 1.0)),
    WHITE_255(new Color(255, 255, 255, 1.0)),
    BLACK(new Color(18, 18, 18, 1.0)),
    BLACK_0(new Color(0, 0, 0, 1.0)),
    EERIE_BLACK(new Color(31, 31, 31, 1.0)),
    CLOUD_BURST(new Color(53, 58, 72, 1.0)),
    FRUIT_SALAD(new Color(76, 175, 80, 1.0)),
    RED(new Color(255, 0, 0, 1)),
    GREEN(new Color(13, 186, 25, 1));

    private final Color colorValue;

    SportalColors(Color colorValue) {
        this.colorValue = colorValue;
    }
}