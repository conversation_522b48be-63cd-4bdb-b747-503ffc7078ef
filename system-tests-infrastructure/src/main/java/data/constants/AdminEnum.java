package data.constants;

import lombok.AccessLevel;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum AdminEnum {

    ADMIN("<EMAIL>", "2023042708272414494", "2020022010025370305");

    private final String name;
    @Getter(AccessLevel.PRIVATE)
    private final String idStaging;
    @Getter(AccessLevel.PRIVATE)
    private final String idIntegration;

    AdminEnum(String name, String idStaging, String idIntegration) {
        this.name = name;
        this.idStaging = idStaging;
        this.idIntegration = idIntegration;
    }

    public String getId() {
        if (ConfigurationService.getEnvironment().equals("integration")) {
            return idIntegration;
        }
        return idStaging;
    }
}
