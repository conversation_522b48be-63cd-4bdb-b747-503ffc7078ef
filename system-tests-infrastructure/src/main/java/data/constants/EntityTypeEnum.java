package data.constants;

import data.constants.enums.EntityEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntityTypeEnum implements EntityEnum {

    TEAM("team"),
    TOURNAMENT("tournament"),
    SEASON("season"),
    GROUP("group"),
    STAGE("stage"),
    COUNTRY("country"),
    PLAYER("player"),
    COACH("coach"),
    LINEUP_PLAYER_TYPE("lineup_player_type"),
    MATCH_STATUS("match_status"),
    VENUE("venue"),
    REFEREE("referee"),
    CITY("city"),
    PRESIDENT("president"),
    LINEUP("lineup"),
    MATCH("match"),
    ASSET("asset"),
    STANDING_RULE("standing_rule"),
    ROUND_TYPE("round_type"),
    GAME("game"),
    ARTICLES("articles"),
    WIKI_PAGES("wiki-pages"),
    VIDEOS("videos"),
    IMAGES("images"),
    GALLERIES("galleries"),
    ARENA("arena"),
    DOMAIN("domain"),
    ORGANIZATION("organization"),
    PERSON("person"),
    ROLE("role"),
    PLACE("place"),
    COMPETITION("competition");

    private final String value;
}