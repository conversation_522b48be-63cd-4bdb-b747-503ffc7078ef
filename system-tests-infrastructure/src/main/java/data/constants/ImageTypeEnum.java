package data.constants;

import lombok.Getter;

import java.util.Random;

@Getter
public enum ImageTypeEnum {

    CONTENT("Content"),
    PRESS("Press"),
    AVATAR("Avatar"),
    THUMBNAIL("Thumbnail");

    private final String name;

    ImageTypeEnum(String name) {
        this.name = name;
    }

    public static ImageTypeEnum getRandom() {
        return values()[(new Random().nextInt(values().length))];
    }
}