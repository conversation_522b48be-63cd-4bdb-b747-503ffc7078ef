package data.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Language {

    BULGARIAN("bg", "Bulgarian"),
    ROMANIAN("ro", "Romanian"),
    HUNGARIAN("hu", "Hungarian"),
    ENGLISH("en", "English"),
    TURKISH("tr", "Turkish"),
    GERMAN("de", "German"),
    PORTUGUESE("pt", "Portuguese"),
    ITALIAN("it", "Italian"),
    GREEK("el", "Greek"),
    SERBIAN("sr", "Serbian"),
    SWEDISH("sv", "Swedish"),
    SPANISH("es", "Spanish"),
    FRENCH("fr", "French"),
    DUTCH("nl", "Dutch"),
    SLOVAK("sk", "Slovak"),
    DANISH("da", "Danish"),
    ARABIC("ar", "Arabic");

    private final String code;
    private final String value;

}