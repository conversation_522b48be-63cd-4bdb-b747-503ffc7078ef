package data.widgets.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.lang.reflect.Type;
import java.util.LinkedHashMap;

public class ObjectSerializer {
    public Gson getScriptSerializer(boolean disableInnerClassSerialization){
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder = registerGsonTypeAdapter(gsonBuilder);
        gsonBuilder.disableHtmlEscaping();
        if (disableInnerClassSerialization){
            gsonBuilder.disableInnerClassSerialization();
        }
        gsonBuilder.setLenient();
        return gsonBuilder.create();
    }

    public Gson getAttributeSerializer() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder = registerGsonTypeAdapter(gsonBuilder);
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gsonBuilder.setPrettyPrinting();
        return gsonBuilder.create();
    }

    protected GsonBuilder registerGsonTypeAdapter(GsonBuilder gsonBuilder) {
        for (var typeAdapter : getTypeAdapters().entrySet()) {
            gsonBuilder.registerTypeAdapter(typeAdapter.getKey(), typeAdapter.getValue());
        }

        return gsonBuilder;
    }

    protected LinkedHashMap<Type, Object> getTypeAdapters() {
        LinkedHashMap<Type, Object> typeAdapters = new LinkedHashMap<>();
//        typeAdapters.put(DataLabels.class, new DataLabelsTypeAdapter());
        return typeAdapters;
    }
}
