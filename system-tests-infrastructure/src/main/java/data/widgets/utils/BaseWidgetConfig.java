package data.widgets.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.juneau.marshaller.Json5;

public abstract class BaseWidgetConfig {

    public String toScript() {
        ObjectSerializer serializer = new ObjectSerializer();
        var gson = serializer.getScriptSerializer(false);
        return gson.toJson(this);
    }

    public String toAttribute() {
        ObjectSerializer serializer = new ObjectSerializer();
        var gson = serializer.getScriptSerializer(false);
        return gson.toJson(this);
    }

    public String toJson5() {
        return Json5.of(this);
    }

    protected String toJsonCustomQuotes() {
        final ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);

        try {
            // Serialize this object to a JSON string, including only non-null fields
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }
}
