package data.widgets.attributes;

import data.widgets.options.enums.BooleanEnum;
import j2html.attributes.Attribute;

public class DataBettingLogoDisplayAttribute extends Attribute {

    public DataBettingLogoDisplayAttribute(BooleanEnum value) {
        super("data-betting-logo-display", value.getValue());
    }

    public DataBettingLogoDisplayAttribute(boolean value) {
        this(value ? BooleanEnum.TRUE : BooleanEnum.FALSE);
    }
}