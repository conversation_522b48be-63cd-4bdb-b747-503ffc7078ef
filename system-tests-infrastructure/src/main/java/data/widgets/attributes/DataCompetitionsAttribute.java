package data.widgets.attributes;

import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.utils.ObjectSerializer;
import j2html.attributes.Attribute;

import java.util.List;

public class DataCompetitionsAttribute extends Attribute {

    private ObjectSerializer serializer = new ObjectSerializer();

    public DataCompetitionsAttribute(DataCompetitionEnum... value) {
        this(List.of(value).toString());
    }

    public DataCompetitionsAttribute(String value) {
        super("data-competitions", value);
    }

    public DataCompetitionsAttribute(Integer value) {
        super("data-competitions", value.toString());
    }
}