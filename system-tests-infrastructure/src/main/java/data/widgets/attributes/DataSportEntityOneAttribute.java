package data.widgets.attributes;

import j2html.attributes.Attribute;
import data.widgets.options.models.DataSportEntity;

public class DataSportEntityOneAttribute extends Attribute {
    public DataSportEntityOneAttribute(DataSportEntity value) {
        super("data-sport-entity-one", value.toAttribute());
    }

    public DataSportEntityOneAttribute(String value) {
        super("data-sport-entity-one", value);
    }
}
