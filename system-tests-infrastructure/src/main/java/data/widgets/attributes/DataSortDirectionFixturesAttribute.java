package data.widgets.attributes;

import j2html.attributes.Attribute;
import data.widgets.options.enums.DataSortDirectionEnum;

public class DataSortDirectionFixturesAttribute extends Attribute {
    public DataSortDirectionFixturesAttribute(DataSortDirectionEnum value) {
        super("data-sort-direction-fixtures", value.getValue());
    }

    public DataSortDirectionFixturesAttribute(String value) {
        super("data-sort-direction-fixtures", value);
    }
}
