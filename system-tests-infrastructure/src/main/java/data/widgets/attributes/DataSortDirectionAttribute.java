package data.widgets.attributes;

import data.widgets.options.enums.DataSortDirectionEnum;
import j2html.attributes.Attribute;

public class DataSortDirectionAttribute extends Attribute {

    public DataSortDirectionAttribute(DataSortDirectionEnum value) {
        super("data-sort-direction", value == null ? null : value.getStorybookValue().toUpperCase());
    }

    public DataSortDirectionAttribute(String value) {
        super("data-sort-direction", value);
    }
}