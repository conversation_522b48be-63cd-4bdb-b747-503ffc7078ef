package data.widgets.attributes;

import data.constants.StringConstants;
import data.models.tennisapi.RoundModel;
import data.utils.StringUtils;
import data.widgets.options.models.DataRoundsFilter;
import j2html.attributes.Attribute;

import java.util.List;

public class DataRoundsFilterAttribute extends Attribute {

    public DataRoundsFilterAttribute(List<DataRoundsFilter> value) {
        super("data-rounds-filter", value == null ? null :
                StringUtils.replaceSpacesWithSymbol(value.toString(), StringConstants.EMPTY_STRING));
    }
}
