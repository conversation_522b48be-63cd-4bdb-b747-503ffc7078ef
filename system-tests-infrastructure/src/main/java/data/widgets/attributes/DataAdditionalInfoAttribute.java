package data.widgets.attributes;

import data.widgets.options.enums.BooleanEnum;
import j2html.attributes.Attribute;

public class DataAdditionalInfoAttribute extends Attribute {

    public DataAdditionalInfoAttribute(BooleanEnum value) {
        super("data-additional-info", value.getValue());
    }

    public DataAdditionalInfoAttribute(boolean value) {
        super("data-additional-info", value ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
    }
}