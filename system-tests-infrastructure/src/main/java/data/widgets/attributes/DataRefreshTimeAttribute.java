package data.widgets.attributes;

import data.widgets.options.enums.DataRefreshTimeEnum;
import j2html.attributes.Attribute;

public class DataRefreshTimeAttribute extends Attribute {

    public DataRefreshTimeAttribute(DataRefreshTimeEnum value) {
        super("data-refresh-time", value == null ? null : value.name().toLowerCase());
    }

    public DataRefreshTimeAttribute(String value) {
        super("data-refresh-time", value == null ? null : value.toLowerCase());
    }
}