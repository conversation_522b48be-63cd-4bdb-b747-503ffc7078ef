package data.widgets.attributes;

import data.widgets.options.enums.DataTournamentEnum;
import j2html.attributes.Attribute;

public class DataTournamentAttribute extends Attribute {

    public DataTournamentAttribute(DataTournamentEnum dataTournamentEnum) {
        this(dataTournamentEnum.getId());
    }

    public DataTournamentAttribute(String value) {
        super("data-tournament", value);
    }
}
