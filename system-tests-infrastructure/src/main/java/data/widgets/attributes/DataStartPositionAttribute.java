package data.widgets.attributes;

import j2html.attributes.Attribute;
import data.widgets.options.enums.DataStartPositionEnum;

public class DataStartPositionAttribute extends Attribute {
    public DataStartPositionAttribute(DataStartPositionEnum value) {
        super("data-start-position", value.getValue());
    }

    public DataStartPositionAttribute(String value) {
        super("data-start-position", value);
    }
}
