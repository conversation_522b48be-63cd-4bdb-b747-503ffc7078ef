package data.widgets.attributes;

import data.widgets.options.enums.BooleanEnum;
import j2html.attributes.Attribute;

public class DataOddsClickableAttribute extends Attribute {
    public DataOddsClickableAttribute(BooleanEnum value) {
        super("data-odds-clickable", value.getValue());
    }

    public DataOddsClickableAttribute(Boolean value) {
        super("data-odds-clickable", value.toString());
    }
}
