package data.widgets.attributes;

import data.widgets.options.enums.BooleanEnum;
import j2html.attributes.Attribute;

public class DataBettingLogoClickAction extends Attribute {

    public DataBettingLogoClickAction(BooleanEnum value) {
        super("data-betting-logo-click-action", value.getValue());
    }

    public DataBettingLogoClickAction(boolean value) {
        this(value ? BooleanEnum.TRUE : BooleanEnum.FALSE);
    }
}