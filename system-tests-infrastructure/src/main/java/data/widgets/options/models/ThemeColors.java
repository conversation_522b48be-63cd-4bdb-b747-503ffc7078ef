package data.widgets.options.models;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import data.widgets.utils.BaseWidgetConfig;

@Builder
@Getter
@Setter
public class ThemeColors extends BaseWidgetConfig {
    String hiContrast;
    String loContrast;
    String progressBarPrimaryBgColor;
    String highLighted;
    String diffContrastColor;
    String diffContrastBgColor;
    String oddsContainerPrimaryBgColor;
    String oddItemPrimaryBgColor;
    String oddItemLabelColor;
    String oddItemCoefficientColor;
    String dividerBorderColor;
    String sportEntityContainerPrimaryBgColor;
    String fansUnitedContainerPrimaryBgColor;
    String fansUnitedContentPrimaryBgColor;
    String fansUnitedPillHoverColor;
    String tabRowBackgroundColor;
    String primaryBackgroundColor;
    String matchCenterPrimaryBackgroundColor;
    String borderColor;
    String filtersContainerPrimaryBackgroundColor;
    String liveMatchStatusColor;
    String fullTimeMatchStatusColor;
    String pillBackgroundColor;
    String pillActiveBackgroundColor;
    String pillBorderColor;
    String pillActiveTextColor;
    String pillTextColor;
    String disabledPillTextColor;
    String pillColor;
    String rowBackgroundColor;
    String tableRightIndicatorColor;
    String surfaceBackgroundColor;
    String sportEntityOneColor;
    String sportEntityTwoColor;
    String sportEntityGreaterStatBgColor;
    String sportEntityLesserStatBgColor;
    String tooltipBackgroundColor;
    String tooltipColor;
    String oddsSelectionBackgroundColor;
    String oddItemHoverColor;
    String oddsSelectionBorderColor;
    String roundColor;
    String statusBadgeBgColor;
    String statusBadgeTextColor;
    String footballFieldTone;
    String collapsibleBgColor;
    String footballFieldLineColor;
    String lineupsHeaderContainerBgColor;
    String lineupsRowEvenBgColor;
    String lineupsRowOddBgColor;
    String lineupsSecondaryTextColor;
    String mobileTabsSelectedBgColor;
    String lineupsPrimaryBackgroundColor;
    String progressOneBgColor;
    String progressTwoBgColor;
    String subHeaderBgColor;
    String relegationColor;
    String relegationPlayoff;
    String promotion;
    String promotionPlayoff;
    String championshipPlayoff;
    String tierTwo;
    String tierTwoPlayoff;
    String topPlayoff;
    String top;
    String knockoutBgColor;
    String knockoutHeaderBgColor;
    String knockoutHeaderBorderColor;
    String knockoutGameBgColor;
    String knockoutGameBorderColor;
    String teamLostColor;
    String timelineBackgroundColor;
    String secondaryHeaderTextColor;
    String timelineTabBackgroundColor;
    String tabTextColor;
    String tabBackgroundColor;
    String tabActiveTextColor;
    String tabActiveBackgroundColor;
    String tabMenuBorderColor;
    String stakesBgColor;
    String stakesSelectionBgColor;
    String stakesSelectedBgColor;
    String selectedStakeColor;
    String accentPrimaryBgColor;
    String accentContrastColor;
    String stakeInputBorderColor;
    String borderMultiSportColor;
    String multiSportCommonColor;
    String gamePartColor;
    String multiSportWinnerColor;
    String multiSportTextColor;
    String multisportHighlightColor;
    String multiSportBgColor;
    String multiSportHoverColor;
    String dropdownBgColor;
    String liveIndicatorColor;
    String postponedMatchColor;
    String progressContainerBgColor;
    String ageRestrictTextColor;
    String multiSportDropdownButtonBgColor;
    String dropdownActiveDateBgColor;
    String teamPrimaryBackgroundColor;
    String secondaryMainEventParticipantNameColor;
    String playerPrimaryBackgroundColor;
    String statsContainerBackgroundColor;
    String playerSecondaryTextColor;
    String playerBioBackgroundColor;
    String playerBioTextColor;
    String playerProfileBorderColor;
    String matchRowBgColor;
    String tournamentHeaderBgColor;
    String teamSecondaryTextColor;
    String emptyTeamImage;
}
