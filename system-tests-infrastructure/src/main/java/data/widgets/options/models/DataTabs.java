package data.widgets.options.models;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import data.widgets.utils.BaseWidgetConfig;

@Builder
@Getter
@Setter
public class DataTabs extends BaseWidgetConfig {
    public String info;
    public String stats;
    public String matches;
    public String predictions;

    @Override
    public String toAttribute() {
        return toJson5();
    }
}
