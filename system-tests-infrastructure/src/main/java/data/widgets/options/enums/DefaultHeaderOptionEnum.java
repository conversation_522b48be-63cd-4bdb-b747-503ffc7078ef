package data.widgets.options.enums;

import data.constants.SortDirectionEnum;
import data.utils.EnumUtils;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
public enum DefaultHeaderOptionEnum {

    DIVISION("Division"),
    OVERALL("Overall"),
    CONFERENCE("Conference");

    private final String value;

    DefaultHeaderOptionEnum(String value) {
        this.value = value;
    }

    public static List<String> getEnumValues() {
        return Stream.of(SortDirectionEnum.values()).map(SortDirectionEnum::getValue).toList();
    }

    public static DefaultHeaderOptionEnum getRandomValue() {
        return EnumUtils.getRandomEnumValue(DefaultHeaderOptionEnum.class);
    }
}
