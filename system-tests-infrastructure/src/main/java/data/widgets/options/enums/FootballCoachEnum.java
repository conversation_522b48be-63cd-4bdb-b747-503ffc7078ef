package data.widgets.options.enums;

import lombok.Getter;

@Getter
public enum FootballCoachEnum {

    JUERGEN_KLOPP("155", "juergen-klopp-155", "<PERSON><PERSON><PERSON>lopp", "Юрге<PERSON> клопп"),
    PEP_GUARDIOLA("8492", "pep-guardiola-8492", "Pep Guardiola", "Пеп Гвардиола"),
    HANSI_FLICK("2314", "hans-dieter-flick-2314", "Hans-<PERSON><PERSON>lick", "Ханси Флик");

    private final String id;
    private final String slugEn;
    private final String nameEn;
    private final String nameBg;

    FootballCoachEnum(String id, String slugEn, String nameEn, String nameBg) {
        this.id = id;
        this.slugEn = slugEn;
        this.nameEn = nameEn;
        this.nameBg = nameBg;
    }
}