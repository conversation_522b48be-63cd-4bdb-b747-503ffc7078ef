package data.widgets.options.enums;

import lombok.Getter;

@Getter
public enum DataStageEnum {

    EMPTY(""),
    PREMIER_LEAGUE_2021_2022("16524"),
    PREMIER_LEAGUE_2022_2023("18118"),
    CHAMPIONS_LEAGUE_GROUPS("18148"),
    CHAMPIONS_LEAGUE_FINAL("17341"),
    EUROPEAN_CHAMPIONSHIP_2024_FINAL_STAGE("18574"),
    KNOCKOUT_STAGE("15645"),
    KNOCKOUT_SMALL_FINAL_STAGE("15206"),
    NBA_STANDINGS_OVERALL("b23a8447-8cb2-4ac9-9f29-38ead9e613d7"),
    NHL_PLAYOFFS_2023_2024("7b63885e-bd68-430d-b790-2994f4dfc6e1"),
    NHL_STANDINGS_FORM_2023_2024("65159820-58ea-449b-9f73-716433d60231"),
    US_OPEN_JUNIORS_GIRLS("78e4d8f2-c16f-48ba-a37d-78bd0f060578"),
    TENNIS_NO_MATCHES("7c833688-6410-490b-9b7a-5c526f2e9e31");

    private final String value;

    DataStageEnum(String id) {
        this.value = id;
    }
}