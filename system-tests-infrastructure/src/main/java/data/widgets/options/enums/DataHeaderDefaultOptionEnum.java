package data.widgets.options.enums;

import data.utils.NumberUtils;
import lombok.Getter;

import java.util.List;

@Getter
public enum DataHeaderDefaultOptionEnum {

    ALL("ALL"),
    LIVE("LIVE"),
    FINISHED("FINISHED"),
    FIXTURES("FIXTURES"),
    UPCOMING("UPCOMING"),
    ODDS("ODDS"),
    LIST("list"),
    GRID("grid"),
    RESULTS("RESULTS"),
    POPULAR("POPULAR"),
    H2H("H2H"),
    FIRST_TEAM("FIRST_TEAM"),
    SECOND_TEAM("SECOND_TEAM");

    private final String value;
    private static final List<DataHeaderDefaultOptionEnum> LIVESCORE_BLOCKY_OPTIONS = List.of(ALL, POPULAR, UPCOMING, LIVE, FINISHED, ODDS);

    DataHeaderDefaultOptionEnum(String id) {
        this.value = id;
    }

    public static List<DataHeaderDefaultOptionEnum> getOptionsForLivescoreBlocky() {
        return LIVESCORE_BLOCKY_OPTIONS;
    }

    public static DataHeaderDefaultOptionEnum getRandomOptionForLivescoreBlocky() {
        int randomIndex = NumberUtils.getRandomNumberInRange(0, LIVESCORE_BLOCKY_OPTIONS.size() - 1);
        return LIVESCORE_BLOCKY_OPTIONS.get(randomIndex);
    }
}