package data.widgets.options.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DataPopularListEnum {

    SPORTAL_POPULAR("sportal_popular"),
    SPORTALIOS("sportalios"),
    ALL("all"),
    MULTI_QA("multi-qa"),
    DEFAULT("default"),
    POPULAR_BASKETBALL("auto-qa"),
    POPULAR_ICE_HOCKEY("popular-ice-hockey"),
    POPULAR_SPORTS("popular-sports"),
    BASKET_ICE_HOCKEY_SPORTS("basket-ice-hockey-sports"),
    MULTI_FE_STATIC_PROJECT("multi-fe"),
    BASKET_TENNIS_SPORTS("basket-tennis-sports"),
    TENNIS_BASKET_SPORTS("tennis-basket-sports");

    private final String value;
}