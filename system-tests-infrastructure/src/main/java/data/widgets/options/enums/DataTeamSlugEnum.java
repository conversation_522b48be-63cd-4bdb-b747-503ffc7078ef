package data.widgets.options.enums;

public enum DataTeamSlugEnum {
    BOCA_JUNIORS_SLUG_EN("boca-juniors-5U6fp57BnK35z1DWGhJxRM"),
    DENVER_NUGGETS_SLUG_EN("denver-nuggets-5EdvyE6dXB7wkWH7IWJprt"),
    LACIO_SLUG_EN("lazio-244"),
    ARSENAL_SLUG_EN("arsenal-97"),
    PIRIN_BLAGOEVGRAD_SLUG_EN("pirin-blagoevgrad-1"),
    EVERTON_SLUG_EN("everton-96"),
    HEBAR_SLUG_EN("hebar-18599"),
    LYON_SLUG_EN("sc-lyon-4129"),
    ANESY_SLUG_EN("annecy-fc-6047"),
    OLIMPICO_SLUG_EN("olimpico-4jCel4gKX4UAzYFJ3pgroh"),
    SSC_NAPOLI_SLUG_EN("ssc-napoli-262"),
    MANCHESTER_CITY_SLUG_EN("manchester-city-104"),
    AJAX_SLUG_EN("ajax-390"),
    USA_SLUG_EN("usa-1874"),
    BENFICA_SLUG_EN("benfica-450"),
    NETHERLANDS_SLUG_EN("netherlands-1562"),
    PFC_LOKOMOTIV_SOFIA_SLUG_EN("pfc-lokomotiv-sofia-1929-8"),
    CHICAGO_BULLS_SLUG_EN("chicago-bulls-4XhH2hs6HikLGdnOiGd9LR"),
    QUEIMADENSE("queimadense-12451"),
    MANCHESTER_UNITED_SLUG_EN("manchester-united-102"),
    BOTEV_VRATSA_SLUG_EN("botev-vratsa-31"),
    LEVSKI_SLUG_EN("levski-sofia-2"),
    SLAVIA_SOFIA_SLUG_EN("slavia-sofia-13"),
    PORTUGAL_SLUG_EN("portugal-1651"),
    BAYERN_MUNICH_SLUG_EN("bayern-munich-233"),
    CHICAGO_BULLS_SLUG_BG(""),
    MILWAUKEE_BUCKS_SLUG_EN("milwaukee-bucks-63nWpWuMbVWN77ok9kAfSI"),
    CENTRO_SPORTIVO_PARAIBANO("centro-sportivo-paraibano-12563"),
    MILWAUKEE_BUCKS_SLUG_BG("miluoki-baks-63nWpWuMbVWN77ok9kAfSI"),
    EMPTY("");

    private final String value;

    DataTeamSlugEnum(String slug) {
        this.value = slug;
    }

    public String getValue() {
        return this.value;
    }
}
