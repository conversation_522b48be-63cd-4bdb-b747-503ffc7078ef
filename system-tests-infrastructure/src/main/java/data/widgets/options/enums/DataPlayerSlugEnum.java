package data.widgets.options.enums;

public enum DataPlayerSlugEnum {
    CRISTIANO_RONALDO_SLUG_EN("xx"),
    MARIO_KIREV_SLUG_EN("mario-kirev-148"),
    MAKSIM_KOVALYOV_SLUG_EN("maksym-k<PERSON><PERSON>ov-258643"),
    YOA<PERSON>_BAURENSKI_SLUG_EN("yoan-b<PERSON><PERSON><PERSON>-204530"),
    RADOSLAV_KIRILOV_SLUG_EN("radoslav-kirilov-931"),
    ANDREA_VAVASSORI_SLUG_EN("andrea-vavassori-2Vs5Vfb5YZHaWMM5TnRygy"),
    CARLOS_ALCARAZ_SLUG_EN("carlos-alcaraz-29LVEVh1KCf4GEMh8z8wBd"),
    CONSTANT_LESTIENNE_SLUG_EN("constant-lestienne-4TIo3zh2TpfetbGjsleGTd"),
    BJORN_FRATANGELO_SLUG_EN("bjorn-fratangelo-1ewprgVuslsSEVHSNjMaYR"),
    ULISES_BLANCH_SLUG_EN("ulises-blanch-2r4Twy7z7nB1Ul4tf57pIp"),
    ERLING_HOLLAND_SLUG_EN("x");

    private final String value;

    DataPlayerSlugEnum(String id) {
        this.value = id;
    }

    public String getValue() {
        return this.value;
    }
}
