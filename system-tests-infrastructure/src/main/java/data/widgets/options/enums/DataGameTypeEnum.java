package data.widgets.options.enums;

import data.utils.EnumUtils;
import lombok.Getter;

@Getter
public enum DataGameTypeEnum {

    SINGLE("Single"),
    DOUBLE("Double"),
    TEAM("Team");

    private final String value;

    DataGameTypeEnum(String value) {
        this.value = value;
    }

    public static DataGameTypeEnum getRandomValue() {
        return EnumUtils.getRandomEnumValue(DataGameTypeEnum.class);
    }
}