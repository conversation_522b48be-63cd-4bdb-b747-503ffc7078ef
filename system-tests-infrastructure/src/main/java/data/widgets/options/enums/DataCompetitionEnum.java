package data.widgets.options.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DataCompetitionEnum {

    EMPTY("", "", "", ""),
    BNP_PARIBAS_OPEN_2023("d55047e6-09de-4322-b4e9-de04b6114353", "bnp-paribas-open-6UW3LxlK2PbWVMPQhkYWgt",
            "BNP Paribas Open", "ATP Индиън Уелс"),
    CHAMPIONS_LEAGUE("27", "champions-league-27",
            "Champions League", "Шампионска лига"),
    SERIE_B_ITALY("7b7d496f-f02a-415c-be72-f47bf835a4a4", "", "Serie B", "Серия Б"),
    CLUB_FRIENDLIES("224", "club-friendlies-224",
            "Club Friendlies", "Контроли"),
    FRIENDLIES("29", "friendlies-29",
            "Friendlies", "Приятелски мачове"),
    EUROPEAN_CHAMPIONSHIP("37", "european-championship-37",
            "European Championship", "Европейско първенство"),
    FIRST_PROFESSIONAL_LEAGUE("1", "first-professional-league-1",
            "First Professional League", "Първа професионална лига"),
    NBA("0f81a7b1-17e1-443e-850a-343c69831245", "nba-TG8WGYx8qzE73JR2IN9mP",
            "NBA", "НБА"),
    NBA_SUMMER_LEAGUE("e1f78fc8-1fdd-4c3a-a499-c93d0b6bdc29", "nba-summer-league-6sOPOLs2rKZgMWyt6UHPMP",
            "NBA Summer League", "НБА Лятна лига"),
    KBL("821b9beb-1689-426d-bfc4-f0ddb9615c38", "kbl-3xVcQpFvUjqwPnf75hWQIa",
            "KBL", "КБЛ"),
    SPAIN_CUP("4332b327-9852-4185-a53f-6befab9ef625", "cup-22neera3mtiaFBj2atbhJN",
            "Copa del Rey", "Купа на Краля"),
    PREMIER_LEAGUE("3", "premier-league-3",
            "Premier League", "Висша лига"),
    LIGA_ENDESA("95b31132-9d1a-44e7-bd1a-c7284966f571", "liga-endesa-4YTgorJOGDxB255ASHzPxR",
            "Liga Endesa", "Лига Ендеса"),
    UEFA_NATIONS_LEAGUE("553", "uefa-nations-league-a-553",
            "UEFA Nations League", "Лига на нациите на УЕФА"),
    US_OPEN("e236507b-e7af-48a7-bd59-44da079707fe", "us-open-6sr5QuEHBrLGa0RND4Vwg2",
            "US Open", "Ю Ес Оупън"),
    WORLD_CHAMPIONSHIP("30", "world-cup-30",
            "World Championship", "Световно първенство"),
    SHANGHAI_ROLEX_MASTERS("2ad5c871-fd93-458c-9092-790374ccda2c", "shanghai-rolex-masters-4dh9b698lFPHJ4sdAn22d",
            "Shanghai Rolex Masters", "Шанхай Ролекс Мастърс"),
    AUSTRALIA_OPEN("f3a8d7df-2173-477a-860a-3124a7a2b569", "",
            "Australian Open", "Аустралиън Оупън"),
    HOPMAN_CUP("602fbed2-b3f5-4d1c-b68d-6c3f1c332f8d", "",
            "Hopman Cup", "Купа Хопман"),
    ROLEX_MONTE_CARLO_MASTERS("602fbed2-b3f5-4d1c-b68d-6c3f1c332f8d", "",
            "Rolex Monte-Carlo Masters", "Ролекс Монте Карло Мастърс"),
    ICE_HOCKEY_CLUB_FRIENDLIES("c5ce1c98-b55d-4e9f-b91d-21e016fd6d3", "club-friendlies-5moBmqlQOuQSQTwilG9t9r",
            "Ice Hockey Club Friendlies", "Приятелски клубни мачове по хокей на лед"),
    ICE_HOCKEY_NHL("c5ce1c98-b55d-4e9f-b91d-21e016fd6d3d", "nhl-61FgGZ59BSoaHTBB6WBEab",
            "NHL", "НХЛ"),
    FIRST_PROFESSIONAL_LEAGUE_ECL_PLAYOFF("22829", "first-professional-league-ecl-playoff-22829",
            "First Professional League ECL Playoff", "");

    private final String id;
    private final String slugEn;
    private final String name;
    private final String nameBg;
}