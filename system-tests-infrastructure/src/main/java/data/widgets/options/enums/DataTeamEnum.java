package data.widgets.options.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DataTeamEnum {

    // Football
    FOOTBALL_SPAIN("1770", "spain-1770", "", "", "Spain", "Испания"),
    FOOTBALL_SWITZERLAND("1807", "switzerland-1807", "", "", "Switzerland", "Швейцария"),
    BSC_YOUNG_BOYS("486", "bsc-young-boys-486", "", "YB", "BSC Young Boys", "Йънг Бойс"),
    FOOTBALL_CSKA_SOFIA("17", "cska-sofia-17", "", "CSS", "CSKA Sofia", "ЦСКА София"),
    FOOTBALL_CSKA_1948("20147", "cska-1948-20147", "", "C48", "CSKA 1948", "ЦСКА 1948"),
    AJAX("390", "ajax-390", "", "", "", "Аякс"),
    ANNECY_FC("6047", "annecy-fc-6047", "AnnFC", "ANN", "Annecy FC", "Анси"),
    ARSENAL("97", "arsenal-97", "Arsenal", null, "Arsenal", "Арсенал"),
    ASTON_VILLA("101", "aston-villa-101", "", "AVL", "Aston Villa", "Астън Вила"),
    TOTTENHAM("89", "tottenham-hotspur-89", "", "", "Tottenham Hotspur", "Тотнъм"),
    BAYERN_MUNICH("233", "bayern-munich-233", "Bayern", "FCB", "Bayern Munich", "Байерн Мюнхен"),
    BENFICA("450", "benfica-450", "", "", "", "Бенфика"),
    BOTEV_VRATSA("31", "botev-vratsa-31", "", "BOV", "Botev Vratsa", "Ботев Враца"),
    CENTRO_SPORTIVO_PARAIBANO("xx", "centro-sportivo-paraibano-12563", "Centro SP", "CSP", "", "Сентро Спортиво"),
    IBEROSTAR_TENERIFE("7db43e9e-99e8-46cd-9c06-b8c4f130a11f", "iberostar-tenerife-3pCPb93lXnw11M5kwr7PWZ", "", "", "", "Иберостар Тенерифе"),
    PHILLY_76ERS("458ce0c2-a936-4177-ae49-cd6cf571b19b", "philadelphia-76ers-27EqozMfV1HhUmCippXCuJ", "", "", "", "Филаделфия 76ърс"),
    DENVER_NUGGETS("122ef315-b906-4736-a878-fee90309281d", "denver-nuggets-34WuExYXNx2VuLhkr0mgs3", "", "", "", "Денвър Нъгетс"),
    EVERTON("96", "everton-96", "", "", "", "Евертън"),
    BRENTFORD("136", "brentford-136", "B", "BRE", "Brentford", "Брентфорд"),
    HEBAR_PAZARDZHIK("18599", "hebar-18599", "", "", "", "Хебър Пазарджик"),
    LAZIO("244", "lazio-244", "", "", "Lazio", "Лацио"),
    LEVSKI_SOFIA("2", "levski-sofia-2", "", "", "", "Левски София"),
    SC_LYON("4129", "lyon-la-duchere-4129", "LyonDuch", "LLD", "Lyon La Duchere", "Лион Ла Дюшер"),
    LIVERPOOL("92", "liverpool-92", "Liver", "LIV", "Liverpool", "Ливърпул"),
    LOKOMOTIV_SOFIA("8", "pfc-lokomotiv-sofia-1929-8", "", "", "", "Локомотив София"),
    MANCHESTER_CITY("104", "manchester-city-104", "Man C", "MNC", "Manchester City", "Манчестър Сити"),
    MANCHESTER_UNITED("102", "manchester-united-102", "Man U", "MUN", "Manchester United", "Манчестър Юнайтед"),
    GRAN_CANARIA("aa0c65cd-7616-4409-99b8-e92c919be1f8", "gran-canaria-5AsMOykCj0IgVS170poFei", "", "", "", "Гран Канария"),
    SSC_NAPOLI("262", "ssc-napoli-262", "", "", "SSC Napoli", "Наполи"),
    NEDERLAND("1562", "netherlands-1562", "", "", "", "Нидерландия"),
    MACHITES_BC("c5348619-9efa-45b8-80b6-72711242d1d6", "machites-bc-607UUl7dBXScHWJ8KglXXi", "", "", "", "Мачитес"),
    PIRIN_BLAGOEVGRAD("1", "pirin-blagoevgrad-1", "", "", "Pirin Blagoevgrad", "Пирин Благоевград"),
    PORTUGAL("1651", "portugal-1651", "Portgal", "POR", "Portugal", "Португалия"),
    NORWAY("", "", "", "", "Norway", "Норвегия"),
    FRANCE("2434", "france-2434", "France", "FRA", "France", "Франция"),
    ARGENTINA("2153", "argentina-2153", "", "ARG", "Argentina", "Аржентина"),
    CRYSTAL_PALACE("110", "crystal-palace-110", "Crystal Pal", "CRY", "Crystal Palace", "Кристъл Палас"),
    RANGERS("819", "", "", "", "", "Рейнджърс"),
    SLAVIA_SOFIA("13", "slavia-sofia-13", "", "SLA", "Slavia Sofia", "Славия София"),
    USA("1874", "usa-1874", "", "", "", "САЩ"),
    WOLVERHAMPTON("109", "wolverhampton-109", "Wolves", "WOL", "Wolverhampton", "Уулвърхемптън"),
    BARCELONA("205", "", "barcelona-205", "BAR", "Barcelona", "Барселона"),
    AL_NASSR_FC("1681", "al-nassr-fc-1681", "", "ALN", "Al Nassr FC", "Ал Насър"),
    REAL_MADRID("204", "real-madrid-204", "", "RMA", "Real Madrid", "Реал Мадрид"),

    // Basketball
    CHICAGO_BULLS("e22cd69c-07cc-4fbf-8623-64cc3032dd42", "chicago-bulls-6smkwARHNXkwXJFaqrBBXW", "", "", "", "Чикаго Булс"),
    NEW_ORLEANS_PELICANS("d19632d9-c0fb-4fc7-8df9-91cf1b15cdaf", "", "", "", "", "Ню Орлиънс Пеликанс"),
    OKLAHOMA_CITY_THUNDER("e05d3876-f7c1-4bb0-9cd1-a800ac963ada", "", "", "", "", "Оклахома Сити Тъндър"),
    BOSTON_CELTICS("615f1ab2-c3c1-43c3-8203-6cf40588e07c", "boston-celtics-2xjiXuck5HN4sgzCCwOiWi", "", "BOS", "Boston Celtics", "Бостън Селтикс"),
    MIAMI_HEAT("95a990d9-9a62-4377-83ba-3fe31383734b", "miami-heat-4YPLbhTj4HhSSZ4uoJ3JD1", "", "MIA", "Miami Heat", "Маями Хийт"),
    WASHINGTON_WIZARDS("5f3ee8f1-1805-4392-aa46-b49930d340c0", "washington-wizards-2tj1IeUQPxy7KLBG2hzIEC", "", "", "", "Вашингтон Уизардс"),
    CHARLOTTE_HORNETS("89301f8d-1061-4ec5-8071-e27ae6625628", "charlotte-hornets-4ArwLu3rIHqbhwQk3M8UYq", "", "", "", "Шарлот Хорнетс"),
    NEW_YORK_KNICKS("969a0ab8-ff42-4685-955a-dd97aeeb3377", "new-york-knicks-4aBFH6lXsuJHPjrkVRFjct", "", "", "", "Ню Йорк Никс"),
    PORTLAND_TRAIL_BLAZERS("8b57fa6a-019c-4b85-8e4e-9fcfb4a2cbe6", "portland-trail-blazers-4Ew8eadFBsvRbjDKLspfAk", "", "", "Portland Trail Blazers", "Портланд Трейл Блейзърс"),
    MILWAUKEE_BUCKS("ff67b612-f0a0-4607-b027-aaf7a7448d71", "milwaukee-bucks-7lwRFbmPxoryEqh9ZLBI3N", "", "", "", "Милуоки Бъкс"),
    ATLANTA_HAWKS("93a30b1f-8dd7-4b44-9fc1-ceec532a6e7c", "", "", "", "Atlanta Hawks", "Атланта Хоукс"),
    ANYANG("57a787b2-030c-4314-b0ff-03163db60846", "anyang-2fOtQ9DZHUaZEr6bkO7pRe", "", "ANY", "Anyang", ""),
    KCC_EGIS("d478c63f-c5fc-4d83-b72e-1cbe4bdcf111", "kcc-egis-6SvZB0nXniprkx1I2t5Cmv", "", "KCC", "KCC Egis", ""),
    BARCELONA_BASKETBALL("7872cad8-6397-4655-9ad9-40c321c7d6fc", "barcelona-basketball-7K4OTZgLRGMXxdwY9FRTJQ", "", "FCB", "Barcelona", "Барселона"),
    REAL_MADRID_BASKETBALL("f0a27137-091d-4029-ade3-32eb5ae7b4a0", "real-madrid-7K4OTZgLRGMXxdwY9FRTJQ", "", "", "Real Madrid", "Реал Мадрид"),
    CAJA_LABORAL_BASKONIA("14318e01-d4f6-46da-9c82-bf6a8922b669", "caja-laboral-baskonia-c6UZVRMDharEj61Nf1uJ7", "", "", "Caja Laboral Baskonia", "Кая Лаборал Баскония"),
    BASQUET_GIRONA("c77ed0d0-8079-44af-aecc-d1b29a825c70", "basquet-girona-64RQXE5aUnGWWU8dgJVyme", "", "", "Basquet Girona", "Баскет Жирона"),
    BASKET_ZARAGOZA("14b4b98c-9901-4e0f-91a0-0559b13f6b29", "basket-zaragoza-d4R9gpcEWdlexpXcIDpJB", "", "", "Basket Zaragoza", ""),

    // Tennis
    SWITZERLAND_TENNIS("", "", "", "", "Switzerland", "Швейцария"),
    CROATIA_TENNIS("", "", "", "", "Croatia", "Хърватия"),

    // Ice Hockey
    ICE_HOCKEY_NEW_YORK_RANGERS("41a2c172-b592-4f35-af81-5332e6db0455", "new-york-rangers-1zqsbmbWJiUGUHDyxNnOzd", "", "NYR", "New York Rangers", "Ню Йорк Рейнджърс"),
    ICE_HOCKEY_OTTAWA_SENATORS("7ed48eed-43fa-4e22-b842-3be81b4f7224", "ottawa-senators-3rKAhXpI7KM2vm69jmOPrA", "", "OTT", "Ottawa Senators", "Отава Сенатърс"),
    ICE_HOCKEY_NEW_YORK_ISLANDERS("32a9fd0e-9962-43a9-81e6-8d43c2cdc57f", "new-york-islanders-1XbIgFH4nc6nlOr67l2rZn", "", "NYI", "New York Islanders", "Ню Йорк Айлендърс"),
    ICE_HOCKEY_PHILADELPHIA_FLYERS("db694bf1-b405-4223-b2ab-d161cf04e723", "philadelphia-flyers-6g1RIZoJiATHSk3OmeKjKt", "", "PHI", "Philadelphia Flyers", "Филаделфия Флайърс"),
    ICE_HOCKEY_DETROIT_RED_WINGS("ef0ce835-1525-4fbc-ae6a-72c3c09cef24", "detroit-red-wings-7H540g5D4kBVh4wal4aLIq", "", "", "Detroit Red Wings", "Детройт Ред Уингс"),
    ICE_HOCKEY_MONTREAL_CANADIENS("103822cf-4fe9-4c06-967a-f7e4667d6062", "montreal-canadiens-UbWws5ffsQdcqqMcptCjK", "", "MTL", "Montreal Canadiens", "Монреал Канейдиънс"),
    ICE_HOCKEY_WASHINGTON_CAPITALS("7a58f883-27aa-43cd-a1bc-37bd2690c826", "washington-capitals-3irirfW5KskReOWltshCnm", "", "WSH", "Washington Capitals", "Вашингтон Кепитълс"),

    // Other
    EMPTY("", "", "", "", "", "");

    private final String id;
    private final String slugEn;
    private final String shortName;
    private final String threeLetterCode;
    private final String fullName;
    private final String nameBg;
}