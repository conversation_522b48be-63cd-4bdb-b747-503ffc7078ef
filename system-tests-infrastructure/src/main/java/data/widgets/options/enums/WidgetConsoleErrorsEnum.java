package data.widgets.options.enums;

import lombok.Getter;

@Getter
public enum WidgetConsoleErrorsEnum {
    INVALID_LANGUAGE("A locale object was not found for the provided string"),
    INVALID_SPORT("The value of the data-widget-sport property is incorrect. Allowed values are: football, basketball, tennis"),
    INVALID_WIDGET_TYPE("The value of the data-widget-type property is incorrect. Allowed values are: tournament, team, player, event, h2h"),
    INVALID_WIDGET_ID("The value of the data-widget-type property is incorrect. Allowed values are: tournament, team, player, event, h2h"),
    INVALID_MATCH_ID("Odds module initialization failed. The odds array is empty.");

    private final String value;

    WidgetConsoleErrorsEnum(String id) {
        this.value = id;
    }
}
