package data.models.standings;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.standingapi.GroupModel;
import data.models.standingapi.StandingModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StandingsModel {

    private GroupModel group;
    private String type;
    private String filter;
    @SerializedName("ranking_type")
    @JsonProperty("ranking_type")
    private String rankingType;
    private List<StandingModel> standing;
}