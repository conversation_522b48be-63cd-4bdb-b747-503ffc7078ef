package data.models.sportentitycustomization;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AssetsUpload extends ApiEntity {

    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
    @JsonProperty("entity_id")
    @SerializedName("entity_id")
    private String entityId;
    @JsonProperty("asset_type")
    @SerializedName("asset_type")
    private String assetType;
    @JsonProperty("asset_path")
    @SerializedName("asset_path")
    private String assetPath;
    private String file;
    private String sport;
}