
package data.models.standingapi.tennisrankings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TennisRankingsModel extends ApiEntity {

    private List<RankingOption> data;

    @Override
    public String getEntityId() {
        return null;
    }
}
