
package data.models.standingapi.tennisrankings;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Country {

    private Assets assets;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String id;
    private String name;
    private String slug;
}
