
package data.models.standingapi.tennisrankings;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.standingapi.DisplayEntity;
import data.models.standingapi.Row;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import repositories.core.ApiEntity;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@Data
@Builder
public class RankingOption extends ApiEntity {

    @SerializedName("display_entity")
    @JsonProperty("display_entity")
    private DisplayEntity displayEntity;
    private Group group;
    private String id;
    @SerializedName("standing_type")
    @JsonProperty("standing_type")
    private StandingType standingType;
    @SerializedName("page_meta")
    @JsonProperty("page_meta")
    private PageMeta pageMeta;
    private List<Row> rows;

    @Override
    public String getEntityId() {
        return getId();
    }
}
