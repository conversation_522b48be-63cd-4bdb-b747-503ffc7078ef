
package data.models.standingapi.tennisrankings;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StandingType {

    private Assets assets;
    private String category;
    @SerializedName(value = "entity_type", alternate = "entityType")
    private String entityType;
    private String id;
    private String name;
    private String subcategory;
}
