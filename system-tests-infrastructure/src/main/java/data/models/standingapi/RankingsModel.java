package data.models.standingapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import data.models.standingapi.tennisrankings.PageMeta;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RankingsModel extends ApiEntity {

    private String id;
    private String filter;
    private GroupModel group;
    private List<Row> rows;
    @SerializedName("standing_type")
    @JsonProperty("standing_type")
    private StandingTypeModel standingType;
    @SerializedName("display_entity")
    @JsonProperty("display_entity")
    private DisplayEntity displayEntity;
    @SerializedName("page_meta")
    @JsonProperty("page_meta")
    private PageMeta pageMeta;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}