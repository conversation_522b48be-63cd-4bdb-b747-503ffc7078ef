package data.models.standingapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StageModel {

    private String id;
    private String name;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private String slug;
    private Assets assets;
    @SerializedName("available_playoffs")
    @JsonProperty("available_playoffs")
    private Object availablePlayoffs;
    @SerializedName("available_standings")
    @JsonProperty("available_standings")
    private Object availableStandings;
}