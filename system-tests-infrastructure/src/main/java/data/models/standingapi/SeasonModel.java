package data.models.standingapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeasonModel {

    private String id;
    private String name;
    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
    private Object assets;
    private String slug;
}