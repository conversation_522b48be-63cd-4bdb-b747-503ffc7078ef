package data.models.standingapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.multisportapi.DisplayAssetModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParticipantsModel {

    private String id;
    private String name;
    private String slug;
    private String position;
    @JsonProperty("three_letter_code")
    @SerializedName("three_letter_code")
    private String threeLetterCode;
    @JsonProperty("short_name")
    @SerializedName("short_name")
    private String shortName;
    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
    @JsonProperty("display_asset")
    @SerializedName("display_asset")
    private DisplayAssetModel displayAsset;
    @JsonProperty("legacy_id")
    @SerializedName("legacy_id")
    private String legacyId;
    private Object assets;
}