package data.models.idmapping;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.Date;

public class MappingEntityModel {
    public String sport;
    @SerializedName("legacy_id")
    public String legacyId;
    @SerializedName("start_date")
    public String startDate;
    @SerializedName("start_time")
    public Date startTime;
    @SerializedName("entity_type")
    public String entityType;
    public ArrayList<String> participants;
    @SerializedName("competition_id")
    public String competitionId;
}
