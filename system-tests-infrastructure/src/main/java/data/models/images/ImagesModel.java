package data.models.images;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.images.watermarks.UrlsModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ImagesModel extends ApiEntity {

    private String description;
    @SerializedName("origin_id")
    @JsonProperty("origin_id")
    private String originId;
    private String type;
    private String alt;
    private String caption;
    private List<String> authors;

    //upload image response
    private String originalFilename;
    private String path;
    private String storageType;

    //add watermark to image
    private GenericModel generic;
    private UrlsModel urls;

    @Override
    public String getEntityId() {
        return null;
    }
}
