package data.models.images.watermarks;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WatermarksImageModel {

    @JsonProperty("delete_existing ")
    @SerializedName("delete_existing ")
    private String deleteExisting;
    @JsonProperty("image_path")
    @SerializedName("image_path")
    private String imagePath;
    @JsonProperty("watermark_id")
    @SerializedName("watermark_id")
    private String watermarkId;
}