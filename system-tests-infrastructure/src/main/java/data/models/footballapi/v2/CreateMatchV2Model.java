package data.models.footballapi.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.v2.scoremodels.ScoreV2Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateMatchV2Model {

    @SerializedName("status_id")
    @JsonProperty("status_id")
    private String statusId;
    @SerializedName("stage_id")
    @JsonProperty("stage_id")
    private String stageId;
    @SerializedName("home_team_id")
    @JsonProperty("home_team_id")
    private Integer homeTeamId;
    @SerializedName("away_team_id")
    @JsonProperty("away_team_id")
    private Integer awayTeamId;
    @SerializedName("round_key")
    @JsonProperty("round_key")
    private String roundKey;
    @SerializedName("score")
    @JsonProperty("score")
    private ScoreV2Model score;
    @SerializedName("coverage")
    @JsonProperty("coverage")
    private String coverage;
    @SerializedName("kickoff_time")
    @JsonProperty("kickoff_time")
    private String kickoffTime;
    @SerializedName("finished_at")
    @JsonProperty("finished_at")
    private String finishedAt;
    @SerializedName("phase_started_at")
    @JsonProperty("phase_started_at")
    private String phaseStartedAt;
}