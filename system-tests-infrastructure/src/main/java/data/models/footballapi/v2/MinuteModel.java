package data.models.footballapi.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MinuteModel {

    @SerializedName("regular_time")
    @JsonProperty("regular_time")
    private long regularTime;
    @SerializedName("injury_time")
    @JsonProperty("injury_time")
    private Long injuryTime;
}