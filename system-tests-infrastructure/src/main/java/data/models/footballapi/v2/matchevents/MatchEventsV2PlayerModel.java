package data.models.footballapi.v2.matchevents;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.v2.AssetsPlayer;
import data.models.footballapi.v2.CountryModel;
import data.models.footballapi.v2.PlayerProfileModel;
import data.models.footballapi.v2.SocialModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MatchEventsV2PlayerModel {

    private CountryModel country;
    private Boolean active;
    private String birthdate;
    @SerializedName("birth_city")
    @JsonProperty("birth_city")
    private String birthCity;
    private PlayerProfileModel profile;
    private List<SocialModel> social;
    private String uuid;
    private String id;
    private String name;
    private String slug;
    private String position;
    private String gender;
    private AssetsPlayer assets;
}