package data.models.footballapi.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RoundV2Model extends ApiEntity {

    @SerializedName("start_date")
    @JsonProperty("start_date")
    private String startDate;
    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    private String status;
    private String id;
    private String key;
    private String name;
    private String type;
    private String uuid;

    @Override
    public String getEntityId() {
        return getId();
    }
}