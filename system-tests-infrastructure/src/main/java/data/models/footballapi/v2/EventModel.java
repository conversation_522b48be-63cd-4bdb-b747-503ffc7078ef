package data.models.footballapi.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.tournamentseason.Score;
import data.models.footballapi.v2.scoremodels.ScoreV2Model;
import data.models.footballapi.v2.scoremodels.TotalScoreModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventModel {

    private String id;
    @SerializedName("match_id")
    @JsonProperty("match_id")
    private String matchId;
    @SerializedName("type_code")
    @JsonProperty("type_code")
    private String typeCode;
    @SerializedName("team_position")
    @JsonProperty("team_position")
    private String teamPosition;
    private int minute;
    @SerializedName("injury_minute")
    @JsonProperty("injury_minute")
    private int injuryMinute;
    @SerializedName("injury_time_minutes")
    @JsonProperty("injury_time_minutes")
    private Integer injuryTimeMinutes;
    @SerializedName("team_id")
    @JsonProperty("team_id")
    private String teamId;
    @SerializedName("primary_player")
    @JsonProperty("primary_player")
    private PlayerV2Model primaryPlayer;
    @SerializedName("secondary_player")
    @JsonProperty("secondary_player")
    private PlayerV2Model secondaryPlayer;
    private TotalScoreModel score;
}