package data.models.footballapi.v2.knockoutschemes;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GroupModel extends ApiEntity {

    private String id;
    private Integer order;
    private List<TeamModel> teams;
    //matches
    private List<MatchModel> matches;
    @SerializedName("child_object_id")
    @JsonProperty("child_object_id")
    private String childObjectId;

    @Override
    public String getEntityId() {
        return getId();
    }
}