package data.models.footballapi.v2.seasons;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateSeasonV2Model {

    @SerializedName("tournament_id")
    @JsonProperty("tournament_id")
    private String tournamentId;
    private String name;
}