package data.models.footballapi.v2.knockoutschemes;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TeamModel extends ApiEntity {

    private String name;
    private String id;
    private String slug;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private String gender;
    private String type;
    private AssetsModel assets;
    private WinnerModel winner;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;

    @Override
    public String getEntityId() {
        return getId();
    }
}