package data.models.footballapi.v2.referees;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.v2.CountryModel;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RefereeV2Model extends ApiEntity {

    private String id;
    private String name;
    private String slug;
    private CountryModel country;
    private String birthdate;
    private Boolean active;
    private String gender;
    private AssetsReferee assets;
    private String uuid;
    // Used for create/update referee requests
    @SerializedName("country_id")
    @JsonProperty("country_id")
    private String countryId;

    @Override
    public String getEntityId() {
        return getId();
    }
}