package data.models.footballapi.v2.matchevents;

import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class MatchEventsV2RequestBodyModel extends ApiEntity {

    private List<MatchEventsV2Model> events;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}