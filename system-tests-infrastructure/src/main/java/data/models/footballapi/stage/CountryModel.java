package data.models.footballapi.stage;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CountryModel extends ApiEntity {

    private Long id;
    private String name;
    private String slug;
    @SerializedName("url_flag")
    @JsonProperty("url_flag")
    private String urlFlag;

    @Override
    public String getEntityId() {
        return String.valueOf(getId());
    }
}