package data.models.footballapi.stage;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.related.Country;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class StagesActiveModel extends ApiEntity {

    private Long id;
    private String name;
    private String slug;
    private Boolean cup;
    @SerializedName("tournament_season_id")
    @JsonProperty("tournament_season_id")
    private Long tournamentSeasonId;
    @SerializedName("tournament_id")
    @JsonProperty("tournament_id")
    private int tournamentId;
    private Country country;
    private String uuid;
    @SerializedName("start_date")
    @JsonProperty("start_date")
    private String startDate;
    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    private Boolean live;

    @Override
    public String getEntityId() {
        return String.valueOf(getId());
    }
}