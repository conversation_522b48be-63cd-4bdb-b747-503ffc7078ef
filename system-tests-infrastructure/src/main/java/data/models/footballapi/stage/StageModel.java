
package data.models.footballapi.stage;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.common.Country;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class StageModel extends ApiEntity {

    private Country country;
    private Boolean cup;
    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    private List<Group> groups;
    private Long id;
    private String uuid;
    private Boolean live;
    private String name;
    private String slug;
    @SerializedName("stage_groups")
    @JsonProperty("stage_groups")
    private Long stageGroups;
    @SerializedName("start_date")
    @JsonProperty("start_date")
    private String startDate;
    @SerializedName("tournament_id")
    @JsonProperty("tournament_id")
    private Long tournamentId;
    @SerializedName("tournament_season_id")
    @JsonProperty("tournament_season_id")
    private Long tournamentSeasonId;
    private List<Standing> standing;

    @Override
    public String getEntityId() {
        return getId().toString();
    }
}