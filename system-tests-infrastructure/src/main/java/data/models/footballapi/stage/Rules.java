package data.models.footballapi.stage;

import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Rules extends ApiEntity {
    private int id;
    private String code;
    private String name;
    private String type;

    @Override
    public String getEntityId() {
        return String.valueOf(getId());
    }
}
