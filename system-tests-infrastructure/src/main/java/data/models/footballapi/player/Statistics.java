
package data.models.footballapi.player;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;

@lombok.Data
@Builder
public class Statistics {
    private Long assists;
    private Long cleansheets;
    private <PERSON> conceded;
    private <PERSON> goals;
    @SerializedName("goals_substitute")
    @JsonProperty("goals_substitute")
    private Long goalsSubstitute;
    private Long minutes;
    @SerializedName("minutes_substitute")
    @JsonProperty("minutes_substitute")
    private Long minutesSubstitute;
    private Long played;
    @SerializedName("red_cards")
    @JsonProperty("red_cards")
    private Long redCards;
    private <PERSON> started;
    @SerializedName("substitute_in")
    @JsonProperty("substitute_in")
    private Long substituteIn;
    @SerializedName("substitute_out")
    @JsonProperty("substitute_out")
    private Long substituteOut;
    @SerializedName("yellow_cards")
    @JsonProperty("yellow_cards")
    private Long yellowCards;
}