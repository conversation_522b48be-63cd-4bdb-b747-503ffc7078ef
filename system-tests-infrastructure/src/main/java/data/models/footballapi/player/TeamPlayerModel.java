
package data.models.footballapi.player;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TeamPlayerModel extends ApiEntity {
    private Boolean active;
    private Boolean loan;
    @SerializedName("shirt_number")
    @JsonProperty("shirt_number")
    private Long shirtNumber;
    private PlayerModel player;

    @Override
    public String getEntityId() {
        return getShirtNumber().toString();
    }
}
