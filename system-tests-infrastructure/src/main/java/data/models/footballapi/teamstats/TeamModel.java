package data.models.footballapi.teamstats;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TeamModel {

    private Boolean national;
    private Long id;
    private String name;
    private String slug;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private Boolean undecided;
    private String gender;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
    private String type;
    private String uuid;
}