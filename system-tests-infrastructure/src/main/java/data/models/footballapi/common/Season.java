package data.models.footballapi.common;

import data.models.related.Tournament;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Season {

    private String id;
    private String name;
    private String slug;
    private Tournament tournament;
    private Boolean active;
    private String status;
    private String uuid;
}