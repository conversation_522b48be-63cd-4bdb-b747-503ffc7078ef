package data.models.footballapi.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Status {

    private String id;
    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private String type;
    private String code;
    private String uuid;
}