package data.models.footballapi.tournamentseason;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Venue {

    private Long id;
    private String name;
    private String slug;
    @SerializedName("url_image")
    @JsonProperty("url_image")
    private String urlImage;
    private Assets assets;
    private String uuid;
}