
package data.models.footballapi.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;

import java.util.List;

@lombok.Data
@Builder
public class OddProvider {
    private Object country;
    private Long id;
    private List<Link> links;
    private String name;
    private String url;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
}
