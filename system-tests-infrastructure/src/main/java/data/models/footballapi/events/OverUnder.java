
package data.models.footballapi.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;

@lombok.Data
@Builder
public class OverUnder {
    @SerializedName("0.5")
    @JsonProperty("0.5")
    private CommonEventObject object0_5;
    @SerializedName("1.5")
    @JsonProperty("1.5")
    private CommonEventObject object1_5;
    @SerializedName("2")
    @JsonProperty("2")
    private CommonEventObject object2;
    @SerializedName("2.5")
    @JsonProperty("2.5")
    private CommonEventObject object2_5;
    @SerializedName("3")
    @JsonProperty("3")
    private CommonEventObject object3;
    @SerializedName("3.5")
    @JsonProperty("3.5")
    private CommonEventObject object3_5;
    @SerializedName("4")
    @JsonProperty("4")
    private CommonEventObject object4;
    @SerializedName("object4.5")
    @JsonProperty("object4.5")
    private CommonEventObject object4_5;
    @SerializedName("5")
    @JsonProperty("5")
    private CommonEventObject object5;
    @SerializedName("object5.5")
    @JsonProperty("object5.5")
    private CommonEventObject object5_5;
    @SerializedName("6")
    @JsonProperty("6")
    private CommonEventObject object6;
}
