
package data.models.footballapi.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.common.Country;
import data.models.footballapi.common.Social;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FootballSearchResultModel extends ApiEntity {
    private Country country;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private Long founded;
    private String gender;
    private Long id;
    private String name;
    private Boolean national;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private Object shortName;
    private Social social;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private String type;
    private Boolean undecided;
    @SerializedName("url_away_kit")
    @JsonProperty("url_away_kit")
    private String urlAwayKit;
    @SerializedName("url_home_kit")
    @JsonProperty("url_home_kit")
    private String urlHomeKit;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
    @SerializedName("url_squad_image")
    @JsonProperty("url_squad_image")
    private String urlSquadImage;
    private String region;
    private Boolean regional_league;
    private String slug;

    @Override
    public String getEntityId() {
        return getId().toString();
    }
}
