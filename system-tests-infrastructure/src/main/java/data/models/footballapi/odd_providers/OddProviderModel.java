
package data.models.footballapi.odd_providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OddProviderModel extends ApiEntity {

    private Object country;
    private String id;
    private Object links; //List<Link>
    private String name;
    private String url;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;

    @Override
    public String getEntityId() {
        return getId();
    }
}