package data.models.footballapi.topscorer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.player.PlayerModel;
import data.models.footballapi.teams.TeamModel;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TopScorerModel extends ApiEntity {

    private Long rank;
    private TeamModel team;
    private PlayerModel player;
    private Long goals;
    @SerializedName("scored_first")
    @JsonProperty("scored_first")
    private Long scoredFirst;
    private Long penalties;
    @SerializedName("missed_penalties")
    @JsonProperty("missed_penalties")
    private Long missedPenalties;
    @SerializedName("yellow_cards")
    @JsonProperty("yellow_cards")
    private Long yellowCards;
    @SerializedName("red_cards")
    @JsonProperty("red_cards")
    private Long redCards;
    private Long minutes;
    private Long played;
    private Long assists;

    @Override
    public String getEntityId() {
        return getRank().toString();
    }
}