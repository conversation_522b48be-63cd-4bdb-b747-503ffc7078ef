package data.models.footballapi.teams;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class MatchesV2TeamModel extends ApiEntity {

    private String id; //Long ? in Article Request -> Body -> Preview
    private String name;
    private String slug;
    @SerializedName(value = "threeLetterCode", alternate = {"three_letter_code"})
    @JsonProperty(defaultValue = "threeLetterCode", value = "three_letter_code")
    private String threeLetterCode;
    private String gender;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private String type;
    @SerializedName("shirt_color")
    @JsonProperty("shirt_color")
    private String shirtColor;
    private String uuid;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private Assets assets;

    @Override
    public String getEntityId() {
        return null;
    }
}