package data.models.footballapi.teamstatistics;

import data.constants.StringConstants;
import data.models.footballapi.common.Season;
import data.models.related.Tournament;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TeamsStatisticsModel extends ApiEntity {

    private TeamModel team;
    private Season season;
    private Tournament tournament;
    private List<StagesModel> stages;
    private TeamStatisticsModel statistics;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}