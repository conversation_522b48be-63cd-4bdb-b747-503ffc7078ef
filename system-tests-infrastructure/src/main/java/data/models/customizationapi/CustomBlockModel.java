package data.models.customizationapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CustomBlockModel extends ApiEntity {

    private String id;
    private String name;
    private IconModel icon;
    private String status;
    @SerializedName("application_url")
    @JsonProperty("application_url")
    private String applicationUrl;

    @Override
    public String getEntityId() {
        return getId();
    }
}