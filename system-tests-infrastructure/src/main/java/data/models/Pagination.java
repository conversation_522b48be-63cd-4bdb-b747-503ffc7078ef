
package data.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;

@lombok.Data
@Builder
public class Pagination {
    private Long count;
    @SerializedName("current_page")
    @JsonProperty("current_page")
    private Long currentPage;
    private Links links;
    @SerializedName("per_page")
    @JsonProperty("per_page")
    private Long perPage;
    private Long total;
    @SerializedName("total_pages")
    @JsonProperty("total_pages")
    private Long totalPages;
}