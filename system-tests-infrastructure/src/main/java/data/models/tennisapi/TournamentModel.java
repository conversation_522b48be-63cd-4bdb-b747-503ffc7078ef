package data.models.tennisapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TournamentModel extends ApiEntity {

    private String id;
    private String name;
    private String slug;
    private CompetitionModel competition;
    private CountryModel country;
    private CategoryModel category;
    private String surface;
    private String gender;
    @SerializedName("season_year")
    @JsonProperty("season_year")
    private String seasonYear;
    @SerializedName("start_date")
    @JsonProperty("start_date")
    private String startDate;
    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("indoor_outdoor")
    @JsonProperty("indoor_outdoor")
    private String indoorOutdoor;
    private Assets assets;
    private List<RoundModel> rounds;

    @Override
    public String getEntityId() {
        return getId();
    }
}
