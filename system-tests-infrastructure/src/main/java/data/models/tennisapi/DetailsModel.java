package data.models.tennisapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DetailsModel extends ApiEntity {

    private String id;
    private String name;
    private CountryModel country;
    private String gender;
    private String birthdate;
    private String weight;
    private String height;
    private String slug;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private Assets assets;

    @Override
    public String getEntityId() {
        return getId();
    }
}
