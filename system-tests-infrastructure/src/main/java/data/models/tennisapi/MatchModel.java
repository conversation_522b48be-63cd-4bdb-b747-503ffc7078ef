package data.models.tennisapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class MatchModel extends ApiEntity {

    private String id;
    private String slug;
    private StatusModel status;
    private RoundModel round;
    private TournamentModel tournament;
    private List<ParticipantModel> participants;
    private List<ResultModel> results;
    private CoverageModel coverage;
    private String gender;
    private String type;
    private List<Object> odds;
    @SerializedName("scheduled_start_time")
    @JsonProperty("scheduled_start_time")
    private String scheduledStartTime;
    @SerializedName("started_at")
    @JsonProperty("started_at")
    private String startedAt;
    @SerializedName("finished_at")
    @JsonProperty("finished_at")
    private String finishedAt;
    @SerializedName("best_of_sets")
    @JsonProperty("best_of_sets")
    private int bestOfSets;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private Assets assets;

    @Override
    public String getEntityId() {
        return getId();
    }
}