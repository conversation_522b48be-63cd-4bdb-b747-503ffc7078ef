package data.models.tennisapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResultModel {

    private TypeModel type;
    private List<ValueModel> values;
    @SerializedName("tiebreak_values")
    @JsonProperty("tiebreak_values")
    private List<ValueModel> tiebreakValues;
}
