package data.models.sportsstatistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class StatisticsAvailableResponseModel extends ApiEntity  {

    private String id;
    private String name;
    @JsonProperty("short_name")
    @SerializedName("short_name")
    private String shortName;
    @JsonProperty("three_letter_code")
    @SerializedName("three_letter_code")
    private String threeLetterCode;
    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
    private CompetitionModel competition;

    @Override
    public String getEntityId() {
        return getId();
    }
}
