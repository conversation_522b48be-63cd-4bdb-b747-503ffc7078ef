package data.models.searchapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TelephoneModel {

    @SerializedName("country_code")
    @JsonProperty("country_code")
    private String countryCode;
    private String number;
    @SerializedName("country_iso_code")
    @JsonProperty("country_iso_code")
    private String countryIsoCode;
}
