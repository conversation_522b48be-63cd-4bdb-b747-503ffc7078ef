package data.models.searchapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.Language;
import data.models.multisportapi.TranslationModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventResultModel {

    private String id;
    private String name;
    private String value;
    private String position;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private List<TranslationModel> translations;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;

    public TranslationModel getTranslation(Language language) {
        return translations.stream()
                .filter(translation -> translation.getLanguage().equals(language.getCode()))
                .findFirst()
                .orElseThrow();
    }
}
