package data.models.searchapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContainedInPlaceModel {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<Translation> translations;
    private String address;
    private String id;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private GeoModel geo;
    @SerializedName("display_asset")
    @JsonProperty("display_asset")
    private DisplayAsset displayAsset;
    private IconModel icon;
    private String website;
    private String telephone;
    @SerializedName("default_language")
    @JsonProperty("default_language")
    private String defaultLanguage;
    private String slug;
    @SerializedName("social_media_links")
    @JsonProperty("social_media_links")
    private List<SocialMediaLinksModel> socialMediaLinks;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    @SerializedName("contained_in_domain")
    @JsonProperty("contained_in_domain")
    private String containedInDomain;
    @SerializedName("contained_in_place")
    @JsonProperty("contained_in_place")
    private Object containedInPlace;
}
