package data.models.searchapi.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.searchapi.Translation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeasonModel {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<Translation> translations;
    private String id;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
}