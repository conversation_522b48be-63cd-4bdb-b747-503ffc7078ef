
package data.models.searchapi.events;

import data.models.searchapi.Meta;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EventsModel extends ApiEntity {

    private Meta meta;
    private List<Result> results;

    @Override
    public String getEntityId() {
        return null;
    }
}