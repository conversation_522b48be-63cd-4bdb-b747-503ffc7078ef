
package data.models.searchapi.events;

import com.google.gson.annotations.SerializedName;
import data.models.searchapi.DisplayAsset;
import data.models.searchapi.Translation;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class Result {

    private Competition competition;
    @SerializedName(value = "competitionId", alternate = "competition_id")
    private String competitionId;
    @SerializedName(value = "display_asset", alternate = "displayAsset")
    private DisplayAsset displayAsset;
    @SerializedName(value = "entity_type", alternate = "entityType")
    private String entityType;
    private String id;
    private String name;
    private List<String> participants;
    private String position;
    private List<Result> results;
    private String slug;
    private String sport;
    @SerializedName(value = "start_time", alternate = "startTime")
    private String startTime;
    @SerializedName(value = "status_type", alternate = "statusType")
    private String statusType;
    private List<Translation> translations;
    private String value;
    private Boolean isSelected;
}