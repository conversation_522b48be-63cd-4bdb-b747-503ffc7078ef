package data.models.searchapi;

import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DomainsOrderModel extends ApiEntity {

    private List<String> order;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}