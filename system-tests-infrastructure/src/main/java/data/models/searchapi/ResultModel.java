package data.models.searchapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import data.models.footballapi.venues.VenuesModel;
import data.models.multisportapi.RoundModel;
import data.models.multisportapi.SeriesModel;
import data.models.searchapi.events.Competition;
import data.models.searchapi.events.SeasonModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ResultModel extends ApiEntity {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<Translation> translations;
    private String id;
    @SerializedName("default_language")
    @JsonProperty("default_language")
    private String defaultLanguage;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
    @SerializedName("display_asset")
    @JsonProperty("display_asset")
    private DisplayAsset displayAsset;
    private String sport;
    private CountryModel country;
    @SerializedName("country_id")
    @JsonProperty("country_id")
    private String countryId;
    private String city;
    @SerializedName("team_ids")
    @JsonProperty("team_ids")
    private List<String> teamIds;
    @SerializedName("competition_ids")
    @JsonProperty("competition_ids")
    private List<String> competitionIds;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private Object containedInDomain;
    private String slug;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    private String gender;
    private String type;
    private String birthdate;
    private String position;
    @SerializedName("competition_id")
    @JsonProperty("competition_id")
    private String competitionId;
    private StatusModel status;
    @SerializedName("status_type")
    @JsonProperty("status_type")
    private String statusType;
    private List<String> participants;
    private List<EventResultModel> results;
    @SerializedName("start_time")
    @JsonProperty("start_time")
    private String startTime;
    @SerializedName("end_time")
    @JsonProperty("end_time")
    private String endTime;
    private EventStageModel stage;
    @SerializedName("participant_details")
    @JsonProperty("participant_details")
    private List<ParticipantDetailsModel> participantDetails;
    private Competition competition;
    private SeasonModel season;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
    @SerializedName("tag_score")
    @JsonProperty("tag_score")
    private Long tagScore;
    private NationalityModel nationality;
    @SerializedName("contained_in_place")
    @JsonProperty("contained_in_place")
    private ContainedInPlaceModel containedInPlace;
    @SerializedName("contained_in_organization")
    @JsonProperty("contained_in_organization")
    private ContainedInOrganizationModel containedInOrganization;
    @SerializedName("custom_order")
    @JsonProperty("custom_order")
    private Integer customOrder;
    private IconModel icon;
    private String uuid;
    private VenuesModel venue;
    private Boolean national;
    private Boolean undecided;
    private Object region;
    @SerializedName("url_thumb")
    @JsonProperty("url_thumb")
    private String urlThumb;
    @SerializedName("url_image")
    @JsonProperty("url_image")
    private String urlImage;
    private RoundModel round;
    private SeriesModel series;

    @Override
    public String getEntityId() {
        return getId();
    }

    public String getNameTranslation(String language) {
        if (language.isEmpty()) {
            return this.getTranslations().get(0).getName();
        }

        return this.getTranslations().stream()
                .filter(t -> t.getLanguage().equals(language)).findFirst()
                .orElseThrow(() -> new AssertionError(StringConstants.TRANSLATION_NOT_FOUND_STRING)).getName();
    }
}