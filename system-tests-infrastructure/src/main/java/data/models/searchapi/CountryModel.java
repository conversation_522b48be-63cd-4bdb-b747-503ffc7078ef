package data.models.searchapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CountryModel {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<Translation> translations;
    private String id;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
    @SerializedName("display_asset")
    @JsonProperty("display_asset")
    private DisplayAsset displayAsset;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String slug;
    private String uuid;
    @SerializedName("url_flag")
    @JsonProperty("url_flag")
    private String urlFlag;
}