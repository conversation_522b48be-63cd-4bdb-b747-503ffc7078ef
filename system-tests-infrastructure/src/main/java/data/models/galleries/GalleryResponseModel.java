package data.models.galleries;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.*;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.contentapi.latest.LatestItemModel;
import data.models.images.GenericModel;
import lombok.Data;
import lombok.*;
import repositories.core.ApiEntity;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GalleryResponseModel extends ApiEntity implements LatestItemModel {

    private String id;
    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
    private String title;
    private String subtitle;
    private String strapline;
    private String footer;
    private List<BodyItem> body;
    @JsonProperty("published_at")
    @SerializedName("published_at")
    private String publishedAt;
    @JsonProperty("published_until")
    @SerializedName("published_until")
    private String publishedUntil;
    private String status;
    private String type;
    @JsonProperty("custom_author")
    @SerializedName("custom_author")
    private Object customAuthor;
    private Image image;
    private Comments comments;
    private List<Item> items;
    private CommonModel origin;
    private GenericModel generic;
    private Urls urls;
    private Seo seo;
    @JsonProperty("created_at")
    @SerializedName("created_at")
    private OffsetDateTime createdAt;
    @JsonProperty("updated_at")
    @SerializedName("updated_at")
    private OffsetDateTime updatedAt;
    @JsonProperty("content_updated_at")
    @SerializedName("content_updated_at")
    private String contentUpdatedAt;
    private String language;
    private List<String> versions;
    @JsonProperty("run_ads")
    @SerializedName("run_ads")
    private Boolean runAds;
    @JsonProperty("betting_content")
    @SerializedName("betting_content")
    private Boolean bettingContent;
    @JsonProperty("is_adult_content")
    @SerializedName("is_adult_content")
    private Boolean isAdultContent;
    @JsonProperty("adult_content")
    @SerializedName("adult_content")
    private Boolean adultContent;
    @JsonProperty("is_sensitive_content")
    @SerializedName("is_sensitive_content")
    private Boolean isSensitiveContent;
    @JsonProperty("sensitive_content")
    @SerializedName("sensitive_content")
    private Boolean sensitiveContent;
    private Category category;
    private String message;
    @JsonProperty("additional_categories")
    @SerializedName("additional_categories")
    private List<Category> additionalCategories;
    private List<AuthorModel> authors;
    @JsonProperty("main_media")
    @SerializedName("main_media")
    private List<MainMediaItem> mainMedia;
    @JsonProperty("created_by")
    @SerializedName("created_by")
    private CreatedBy createdBy;
    @JsonProperty("published_regions")
    @SerializedName("published_regions")
    private List<CommonModel> publishedRegions;
    @JsonProperty("published_channels")
    @SerializedName("published_channels")
    private List<CommonModel> publishedChannels;
    @JsonProperty("is_content_updated_at_set_automatically")
    @SerializedName("is_content_updated_at_set_automatically")
    private Boolean isContentUpdatedAtSetAutomatically;

    @Override
    public String getEntityId() {
        return getId();
    }
}