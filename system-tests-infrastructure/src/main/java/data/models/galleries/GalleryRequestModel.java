package data.models.galleries;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.BodyItem;
import data.models.articles.MainMediaItem;
import data.models.articles.Seo;
import data.models.contentapi.latest.LatestItemModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GalleryRequestModel extends ApiEntity implements LatestItemModel {

    private Seo seo;
    private String id;
    private String title;
    private String subtitle;
    private String strapline;
    private List<BodyItem> body;
    @JsonProperty("category_id")
    @SerializedName("category_id")
    private String categoryId;
    @JsonProperty("published_at")
    @SerializedName("published_at")
    private String publishedAt;
    @JsonProperty("published_until")
    @SerializedName("published_until")
    private String publishedUntil;
    private String status;
    @JsonProperty("comment_collection_id")
    @SerializedName("comment_collection_id")
    private String commentCollectionId;
    private List<String> authors;
    @JsonProperty("custom_author")
    @SerializedName("custom_author")
    private String customAuthor;
    private String language;
    @JsonProperty("image_id")
    @SerializedName("image_id")
    private String imageId;
    @JsonProperty("image_description")
    @SerializedName("image_description")
    private String imageDescription;
    @JsonProperty("additional_categories")
    @SerializedName("additional_categories")
    private List<String> additionalCategories;
    private String footer;
    @JsonProperty("published_regions")
    @SerializedName("published_regions")
    private List<String> publishedRegions;
    @JsonProperty("published_channels")
    @SerializedName("published_channels")
    private List<String> publishedChannels;
    @JsonProperty("origin_id")
    @SerializedName("origin_id")
    private String originId;
    @JsonProperty("external_url")
    @SerializedName("external_url")
    private String externalUrl;
    @JsonProperty("canonical_url")
    @SerializedName("canonical_url")
    private String canonicalUrl;
    @JsonProperty("allow_comments")
    @SerializedName("allow_comments")
    private boolean allowComments;
    private String type;
    @JsonProperty("comment_policy_id")
    @SerializedName("comment_policy_id")
    private String commentPolicyId;
    @JsonProperty("main_media")
    @SerializedName("main_media")
    private List<MainMediaItem> mainMedia;
    private List<Item> items;
    @JsonProperty("translation_group_id")
    @SerializedName("translation_group_id")
    private String translationGroupId;
    @JsonProperty("run_ads")
    @SerializedName("run_ads")
    private boolean runAds;
    @JsonProperty("is_adult_content")
    @SerializedName("is_adult_content")
    private boolean isAdultContent;
    @JsonProperty("is_sensitive_content")
    @SerializedName("is_sensitive_content")
    private boolean isSensitiveContent;
    @JsonProperty("betting_content")
    @SerializedName("betting_content")
    private boolean bettingContent;
    @JsonProperty("content_updated_at")
    @SerializedName("content_updated_at")
    private String contentUpdatedAt;

    @Override
    public String getEntityId() {
        return getId();
    }
}