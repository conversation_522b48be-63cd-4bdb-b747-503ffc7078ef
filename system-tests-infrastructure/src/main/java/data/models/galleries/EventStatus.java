
package data.models.galleries;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;

@lombok.Data
@Builder
public class EventStatus {
    private String code;
    private Long id;
    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private String type;
}
