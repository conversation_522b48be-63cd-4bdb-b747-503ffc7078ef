package data.models.clientapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

import static data.constants.StringConstants.EMPTY_STRING;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SportsProjectModel extends ApiEntity {

    private String project;
    private List<SportModel> sports;
    @SerializedName("deployment_status")
    @JsonProperty("deployment_status")
    private String deploymentStatus;

    @Override
    public String getEntityId() {
        return EMPTY_STRING;
    }
}
