package data.models.clientapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeatureModel extends ApiEntity {

    private String id;
    private List<FeaturesModel> features;

    @Override
    public String getEntityId() {
        return getId();
    }
}