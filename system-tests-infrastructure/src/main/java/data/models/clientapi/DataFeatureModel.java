package data.models.clientapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataFeatureModel {

    private String competitionList;
    private String website;
    private Object gallery;
    private Object configuration;
    private Object managers;
    private String url;
    private Object article;
    private Long maximumSteps;
    private Object authentication;
    @SerializedName("odds_authentication")
    @JsonProperty("odds_authentication")
    private Object oddsAuthentication;
    private Object wikipage;
    @SerializedName("quick_watermark")
    @JsonProperty("quick_watermark")
    private Object quickWatermark;
    @SerializedName("static_template")
    @JsonProperty("static_template")
    private String staticTemplate;
    private String oddClient;
    private String id;
    private String company;
    private String token;
    private String key;
    private Object delivery;
    private Object segments;
    private List<String> sports;
    private List<String> admins;
    private List<Object> affiliates;
    private List<Object> pages;
    @SerializedName("available_footers")
    @JsonProperty("available_footers")
    private List<Object> availableFooters;
    private List<Object> mappings;
    @SerializedName("request_headers")
    @JsonProperty("request_headers")
    private List<Object> requestHeaders;
    private Boolean isManual;
    @SerializedName("autotagging_enabled")
    @JsonProperty("autotagging_enabled")
    private Boolean autotaggingEnabled;
    private String onlyFirstOccurrence;
    private Object linkOptions;
    private List<String> types;
    @SerializedName("populate_url")
    @JsonProperty("populate_url")
    private PopulateUrl populateUrl;
    private Authorization authorization;
}