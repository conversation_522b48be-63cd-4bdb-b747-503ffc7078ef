package data.models.authors;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Seo {

    private Object description;
    private Boolean follow;
    private Boolean index;
    private Object jsonld;
    private List<Object> keywords;
    @SerializedName("redirect_type")
    @JsonProperty("redirect_type")
    private Object redirectType;
    private String slug;
    private String title;
    @SerializedName("automatic_seo_title")
    @JsonProperty("automatic_seo_title")
    private Boolean automaticSeoTitle;
    @SerializedName("automatic_slug")
    @JsonProperty("automatic_slug")
    private Boolean automaticSlug;
    @SerializedName("automatic_title")
    @JsonProperty("automatic_title")
    private Boolean automaticTitle;
}
