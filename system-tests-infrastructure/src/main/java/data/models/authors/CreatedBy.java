
package data.models.authors;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreatedBy {

    @SerializedName("full_name")
    @JsonProperty("full_name")
    private String fullName;
    @Expose
    private String id;
}
