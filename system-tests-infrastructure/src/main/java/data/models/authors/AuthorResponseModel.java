
package data.models.authors;

import data.models.MetaModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

import java.util.List;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthorResponseModel extends ApiEntity {
    private List<AuthorModel> data;
    private MetaModel meta;
    private Urls urls;

    @Override
    public String getEntityId() {
        return data.get(0).getId();
    }
}
