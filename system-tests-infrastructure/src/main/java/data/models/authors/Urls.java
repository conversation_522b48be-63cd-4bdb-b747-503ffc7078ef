package data.models.authors;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Urls {

    @SerializedName("canonical_url")
    @JsonProperty("canonical_url")
    private String canonicalUrl;
    @SerializedName("external_url")
    @JsonProperty("external_url")
    private String externalUrl;
    @SerializedName("original_source_url")
    @JsonProperty("original_source_url")
    private String originalSourceUrl;
    @SerializedName("skip_url_generation")
    @JsonProperty("skip_url_generation")
    private boolean skipUrlGeneration;
}
