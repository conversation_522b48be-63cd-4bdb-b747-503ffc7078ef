package data.models.translation;

import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TranslationModel extends ApiEntity {

    private ResponseDataModel responseData;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}