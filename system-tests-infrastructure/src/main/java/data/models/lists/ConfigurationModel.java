package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.categories.Category;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigurationModel {

    private List<SportModel> sports;
    private Category category;
    @SerializedName("max_items")
    @JsonProperty("max_items")
    private String maxItems;
    @SerializedName("min_items")
    @JsonProperty("min_items")
    private String minItems;
    private SportModel sport;
}