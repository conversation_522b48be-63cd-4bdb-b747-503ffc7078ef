package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataModel {

    private Object id; // It can be a string or an integer
    private String uuid;
    private String name;
    @JsonProperty("url_logo")
    @SerializedName("url_logo")
    private String urlLogo;
    @JsonProperty("entity_type")
    @SerializedName("entity_type")
    private String entityType;
}