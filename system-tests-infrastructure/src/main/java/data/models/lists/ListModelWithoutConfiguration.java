package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.common.CreatedBy;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class ListModelWithoutConfiguration extends ApiEntity {

    // TODO: Zdravko 22.04.2024 Remove duplicate model once backend ticket SBE-3018 is done;
    private String id;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String title;
    private String slug;
    private String type;
    private String status;
    private Object image;
    private int weight;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    private String language;
    @SerializedName("content_type")
    @JsonProperty("content_type")
    private String contentType;
    private ConfigurationModelWithoutCategory configuration;
    private Object generic;
    private String hash;
    private List<ListItemModel> items;

    //items
    @SerializedName("locked_positions")
    @JsonProperty("locked_positions")
    private Object lockedPositions;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;


    @Override
    public String getEntityId() {
        return getId();
    }
}