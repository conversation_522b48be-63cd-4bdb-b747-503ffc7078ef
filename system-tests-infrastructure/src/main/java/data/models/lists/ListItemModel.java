package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.searchapi.Meta;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ListItemModel extends ApiEntity {

    public String id;
    public ListItemDataModel data;
    public String type;
    public int weight;
    public Meta meta;
    @SerializedName("is_automatic_content")
    @JsonProperty("is_automatic_content")
    public Boolean isAutomaticContent;

    @Override
    public String getEntityId() {
        return getId();
    }
}