package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PopularitySettingsModel {

    @SerializedName("data_source")
    @JsonProperty("data_source")
    private String dataSource;
    @SerializedName("update_frequency")
    @JsonProperty("update_frequency")
    private String updateFrequency;
    @SerializedName("time_range")
    @JsonProperty("time_range")
    private String timeRange;
}