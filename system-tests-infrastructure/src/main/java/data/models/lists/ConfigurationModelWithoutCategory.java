package data.models.lists;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ConfigurationModelWithoutCategory {

    private List<SportModel> sports;
    // TODO: Zdravko 22.04.2024 Remove duplicate model once backend ticket SBE-3018 is done;
    //private CategoryModel category;
    @SerializedName("max_items")
    @JsonProperty("max_items")
    private String maxItems;
    @SerializedName("min_items")
    @JsonProperty("min_items")
    private String minItems;
}