
package data.models.basketball.competition;

import data.models.basketball.season.Stage;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.ArrayList;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompetitionDetailsModel extends ApiEntity {

    public String id;
    public String name;
    public String slug;
    public CompetitionModel competition;
    public String status;
    public ArrayList<Stage> stages;
    public String entity_type;
    public Object assets;

    @Override
    public String getEntityId() {
        return getId();
    }
}
