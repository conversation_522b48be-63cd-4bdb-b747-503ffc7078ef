
package data.models.basketball.player;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Position {
    private Assets assets;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String id;
    private String name;
    private String type;
}
