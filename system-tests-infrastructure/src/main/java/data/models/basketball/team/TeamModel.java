package data.models.basketball.team;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import data.models.basketball.CountryModel;
import data.models.basketball.season.SeasonModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TeamModel extends ApiEntity {

    private String id;
    private String name;
    private String slug;
    private CountryModel country;
    private String type;
    private String gender;
    private Social social;
    @Builder.Default
    private List<CoachesModel> coaches = new ArrayList<>();
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @JsonProperty("active_seasons")
    @SerializedName("active_seasons")
    @Builder.Default
    private List<SeasonModel> activeSeasons = new ArrayList<>();
    private Assets assets;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;

    @Override
    public String getEntityId() {
        return getId();
    }
}