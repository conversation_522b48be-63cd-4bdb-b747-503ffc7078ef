package data.models.basketball.season;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import data.models.basketball.competition.CompetitionModel;
import lombok.*;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeasonModel extends ApiEntity {

    private Assets assets;
    private CompetitionModel competition;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String id;
    private String name;
    private String slug;
    private String status;

    @Override
    public String getEntityId() {
        return getId();
    }
}