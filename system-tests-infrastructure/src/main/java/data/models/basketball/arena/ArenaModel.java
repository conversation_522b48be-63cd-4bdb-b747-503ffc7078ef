package data.models.basketball.arena;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import data.models.basketball.CountryModel;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ArenaModel extends ApiEntity {

    private Assets assets;
    private Object capacity;
    private City city;
    private Coordinates coordinates;
    private CountryModel country;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String id;
    private String name;
    private String slug;

    @Override
    public String getEntityId() {
        return getId();
    }
}