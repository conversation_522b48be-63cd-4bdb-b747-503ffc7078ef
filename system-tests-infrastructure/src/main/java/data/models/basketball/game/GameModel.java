package data.models.basketball.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.basketball.season.Round;
import data.models.basketball.season.SeasonModel;
import data.models.basketball.season.Stage;
import data.models.basketball.team.TeamModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GameModel extends ApiEntity {

    private String id;
    private String slug;
    private Stage stage;
    private SeasonModel season;
    private Status status;
    private Round round;
    private List<Score> score;
    private Winner winner;
    private Object arena;
    private Object series;
    private String coverage;
    private Integer minute;
    private List<Object> odds;
    @SerializedName("game_time")
    @JsonProperty("game_time")
    private String gameTime;
    @SerializedName("home_team")
    @JsonProperty("home_team")
    private TeamModel homeTeam;
    @SerializedName("away_team")
    @JsonProperty("away_team")
    private TeamModel awayTeam;

    @Override
    public String getEntityId() {
        return getId();
    }

    public TeamModel getTeam(String teamId) {
        return homeTeam.getId().equals(teamId) ? homeTeam : awayTeam;
    }
}