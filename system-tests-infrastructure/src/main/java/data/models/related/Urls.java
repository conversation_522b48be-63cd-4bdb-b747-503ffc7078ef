package data.models.related;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Urls {

    @SerializedName("external_url")
    @JsonProperty("external_url")
    private Object externalUrl;
    @SerializedName("canonical_url")
    @JsonProperty("canonical_url")
    private Object canonicalUrl;
    @SerializedName("public_url_desktop")
    @JsonProperty("public_url_desktop")
    private Object publicUrlDesktop;
    @SerializedName("public_url_mobile")
    @JsonProperty("public_url_mobile")
    private Object publicUrlMobile;
    @SerializedName("public_url_amp")
    @JsonProperty("public_url_amp")
    private Object publicUrlAmp;
    @SerializedName("audio_url")
    @JsonProperty("audio_url")
    private Object audioUrl;
    @SerializedName("original_source_url")
    @JsonProperty("original_source_url")
    private Object originalSourceUrl;
    @SerializedName("skip_url_generation")
    @JsonProperty("skip_url_generation")
    private boolean skipUrlGeneration;
}