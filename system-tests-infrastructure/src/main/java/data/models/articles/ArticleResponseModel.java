package data.models.articles;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.contentapi.latest.LatestItemModel;
import lombok.Data;
import lombok.*;
import lombok.experimental.SuperBuilder;
import repositories.core.ApiEntity;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ArticleResponseModel extends ApiEntity implements LatestItemModel {

    private Object footer;
    private CommonModel origin;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private OffsetDateTime createdAt;
    private String language;
    private List<String> versions;
    private String title;
    private List<BodyItem> body;
    private String type;
    @SerializedName("published_channels")
    @JsonProperty("published_channels")
    private List<CommonModel> publishedChannels;
    @SerializedName("custom_author")
    @JsonProperty("custom_author")
    private Object customAuthor;
    private Urls urls;
    @SerializedName("run_ads")
    @JsonProperty("run_ads")
    private Boolean runAds;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private OffsetDateTime updatedAt;
    @SerializedName("published_until")
    @JsonProperty("published_until")
    private String publishedUntil;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaItem> mainMedia;
    private String id;
    @SerializedName("published_at")
    @JsonProperty("published_at")
    private String publishedAt;
    private Seo seo;
    private Boolean live;
    @SerializedName("adult_content")
    @JsonProperty("adult_content")
    private Boolean adultContent;
    @SerializedName("is_adult_content")
    @JsonProperty("is_adult_content")
    private Boolean isAdultContent;
    @JsonProperty("is_sensitive_content")
    private Boolean isSensitiveContent;
    @SerializedName("sensitive_content")
    @JsonProperty("sensitive_content")
    private Boolean sensitiveContent;
    private Image image;
    private Comments comments;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;
    private Object generic;
    @SerializedName("published_regions")
    @JsonProperty("published_regions")
    private List<CommonModel> publishedRegions;
    private Boolean important;
    @SerializedName("betting_content")
    @JsonProperty("betting_content")
    private Boolean bettingContent;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String subtitle;
    private String strapline;
    private Category category;
    @SerializedName("category_id")
    @JsonProperty("category_id")
    private String categoryId;
    @SerializedName("additional_categories")
    @JsonProperty("additional_categories")
    private List<Category> additionalCategories;
    private String status;
    private List<AuthorModel> authors;
    private Owner owner;
    private String alt;
    private String description;
    private String caption;
    private String path;
    @SerializedName("main_crop")
    @JsonProperty("main_crop")
    private Object mainCrop;
    @SerializedName(value = "embed_code", alternate = {"embedCode"})
    @JsonProperty(defaultValue = "embed_code", value = "embedCode")
    private String embedCode;
    @SerializedName("comment_collection_id")
    @JsonProperty("comment_collection_id")
    private String commentCollectionId;
    @SerializedName("canonical_url")
    @JsonProperty("canonical_url")
    private String canonicalUrl;
    @SerializedName("external_url")
    @JsonProperty("external_url")
    private String externalUrl;
    @SerializedName("allow_comments")
    @JsonProperty("allow_comments")
    private boolean allowComments;
    private PropertiesModel properties;
    private String message;
    private String provider;
    @SerializedName("entity_scope")
    @JsonProperty("entity_scope")
    private Object entityScope;
    @SerializedName("audio_text")
    @JsonProperty("audio_text")
    private Object audioText;
    private String source;
    @SerializedName("relevancy_rating")
    @JsonProperty("relevancy_rating")
    private Object relevancyRating;
    @SerializedName("content_updated_at")
    @JsonProperty("content_updated_at")
    private String contentUpdatedAt;
    @SerializedName("is_content_updated_at_set_automatically")
    @JsonProperty("is_content_updated_at_set_automatically")
    private Boolean isContentUpdatedAtSetAutomatically;
    private String copyright;
    @SerializedName("referral_url")
    @JsonProperty("referral_url")
    private String referralUrl;

    @Override
    public String getEntityId() {
        return getId();
    }
}