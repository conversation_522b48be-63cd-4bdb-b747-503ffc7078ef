package data.models.articles;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.liveblogapi.Team;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Options {

    @SerializedName(value = "api_key", alternate = "apiKey")
    private String apiKey;
    @SerializedName(value = "api_u_r_l", alternate = "apiURL")
    private String apiURL;
    @SerializedName(value = "default_competition_list", alternate = "defaultCompetitionList")
    private String defaultCompetitionList;
    @SerializedName(value = "display_odds", alternate = "displayOdds")
    private boolean displayOdds;
    private Boolean fansUnitedEnabled;
    private Boolean fansUnitedExpanded;
    private String lang;
    private Object market; //Market
    @SerializedName("market_value_type")
    @JsonProperty("market_value_type")
    private String marketValueType;
    @SerializedName("match_id")
    @JsonProperty("match_id")
    private String matchId;
    @SerializedName(value = "odd_client", alternate = "oddClient")
    private Object oddClient;
    @SerializedName(value = "odd_provider_ids", alternate = "oddProviderIds")
    private String oddProviderIds;
    private Boolean preEventOnly;
    private String project;
    private String timezone;
    private String season;
    @SerializedName(value = "season_id", alternate = "seasonId")
    private String seasonId;
    @SerializedName(value = "stage_id", alternate = "stageId")
    private String stageId;
    private List<Long> groups;
    private String teamId;
    private String sortDirectionResults;
    private String sortDirectionFixtures;
    private List<String> rounds;
    private List<RoundFilterModel> roundsFilter;
    private String matchType;
    private String playerId;
    @SerializedName(value = "competition_id", alternate = "competitionId")
    private String competitionId;
    private Boolean displayTabs;
    private String defaultTab;
    private Object elements;
    @SerializedName(value = "player1_id", alternate = "player1Id")
    private Long player1Id;
    @SerializedName(value = "player1_tournament_season_id", alternate = "player1TournamentSeasonId")
    private String player1TournamentSeasonId;
    @SerializedName(value = "player2_id", alternate = "player2Id")
    private Long player2Id;
    @SerializedName(value = "player2_tournament_season_id", alternate = "player2TournamentSeasonId")
    private String player2TournamentSeasonId;
    @SerializedName(value = "team1_id", alternate = "team1Id")
    private String team1Id;
    @SerializedName(value = "team1_tournament_season_id", alternate = "team1TournamentSeasonId")
    private String team1TournamentSeasonId;
    @SerializedName(value = "team2_id", alternate = "team2Id")
    private String team2Id;
    @SerializedName(value = "team2_tournament_season_id", alternate = "team2TournamentSeasonId")
    private String team2TournamentSeasonId;
    @SerializedName(value = "can_select_markets", alternate = "canSelectMarkets")
    private Boolean canSelectMarkets;
    @SerializedName(value = "display_match_header", alternate = "displayMatchHeader")
    private Boolean displayMatchHeader;
    @SerializedName(value = "display_team_short_names_on_desktop", alternate = "displayTeamShortNamesOnDesktop")
    private Boolean displayTeamShortNamesOnDesktop;
    @SerializedName(value = "display_team_short_names_on_mobile", alternate = "displayTeamShortNamesOnMobile")
    private Boolean displayTeamShortNamesOnMobile;
    @SerializedName(value = "event_id", alternate = "eventId")
    private Long eventId;
    private Object mainOddProvider;
    @SerializedName(value = "stat_fields", alternate = "statFields")
    private List<String> statFields;
    private Team team1;
    private Team team2;
    private String refreshTime;
    private String date;
    private List<String> highlightPlayers;
    private String limit;
    private String offset;
    private String standingType;
    private String tournamentId;
    private List<Long> highlightTeams;
    private boolean displayHeader;
    @SerializedName("data_header_default_option")
    public String dataHeaderDefaultOption;
    @SerializedName("header_default_option")
    public String headerDefaultOption;
}