package data.models.articles;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Data {

    private String changeId;
    @SerializedName("placeholder_name")
    @JsonProperty("placeholder_name")
    private String placeholderName;
    private Config config;
    private String type;
    private String alt;
    private String caption;
    private String description;
    private ArrayList<String> imageRatio;
    private String id;
    @SerializedName("main_image_id")
    @JsonProperty("main_image_id")
    private String mainImageId;
    private String content;
    private Preview preview;
    private String sport;
    @SerializedName("widget_type")
    @JsonProperty("widget_type")
    private String widgetType;
    private Object tags;
    private String startAt;
}