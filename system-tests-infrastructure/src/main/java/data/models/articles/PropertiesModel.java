package data.models.articles;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PropertiesModel {
    @JsonProperty("run_ads")
    @SerializedName("run_ads")
    private Boolean runAds;
    @JsonProperty("is_adult_content")
    @SerializedName("is_adult_content")
    private Boolean isAdultContent;
    @JsonProperty("is_sensitive_content")
    @SerializedName("is_sensitive_content")
    private Boolean isSensitiveContent;
    private Boolean important;
    @JsonProperty("betting_content")
    @SerializedName("betting_content")
    private Boolean bettingContent;
}