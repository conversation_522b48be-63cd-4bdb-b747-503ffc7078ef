package data.models.articles;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PublishedChannelsItem {
	@SerializedName("default")
	private boolean jsonMemberDefault;
	@SerializedName("updated_at")
	private String updatedAt;
	private String name;
	private String description;
	@SerializedName("created_at")
	private String createdAt;
	private String id;
	@SerializedName("created_by")
	private CreatedBy createdBy;
	private String slug;
}