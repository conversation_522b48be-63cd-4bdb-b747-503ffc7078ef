
package data.models.articles.tennisblocky;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Result {

    private String id;
    private String name;
    private String position;
    private List<Translation> translations;
    private String value;

}
