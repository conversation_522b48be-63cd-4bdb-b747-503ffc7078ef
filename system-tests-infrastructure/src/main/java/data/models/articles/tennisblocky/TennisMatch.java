
package data.models.articles.tennisblocky;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TennisMatch {
    private data.models.searchapi.events.Result match;
    private List<data.models.searchapi.suggest.Result> players;
    private Boolean displayOdds;
    private String refreshTime;
}
