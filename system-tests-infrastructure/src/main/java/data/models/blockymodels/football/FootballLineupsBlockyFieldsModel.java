package data.models.blockymodels.football;

import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.v2.ColorModel;
import data.models.searchapi.ResultModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballLineupsBlockyFieldsModel extends SportBlockyFieldsModel {

    private ResultModel team;
    private ResultModel event;
    private ColorModel firstTeamShirtColor;
    private ColorModel secondTeamShirtColor;
    private boolean showCoach;
    private boolean showFirstTeam;
    private boolean showSubstitution;
    private boolean toggleFirstTeamOpen;
    private boolean toggleSubstituteOpen;
}