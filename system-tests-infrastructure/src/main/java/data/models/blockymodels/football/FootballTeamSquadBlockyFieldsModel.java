package data.models.blockymodels.football;

import data.constants.enums.TeamSquadDataElementsStatisticsEnum;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.models.related.Tournament;
import data.widgets.options.enums.TeamSquadViewEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballTeamSquadBlockyFieldsModel extends SportBlockyFieldsModel {

    private TeamModel team;
    private Tournament tournament;
    private TournamentSeasonModel season;
    private TeamSquadViewEnum view;
    private List<TeamSquadDataElementsStatisticsEnum> statistics;
}