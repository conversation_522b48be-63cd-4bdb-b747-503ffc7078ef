package data.models.blockymodels.football;

import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.models.related.Tournament;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballFormBlockyFieldsModel extends SportBlockyFieldsModel {
    private TeamModel team;
    private String limit;
    private String title;
    private TournamentSeasonModel season;
    private Tournament stage;
}