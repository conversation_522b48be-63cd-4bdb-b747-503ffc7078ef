package data.models.blockymodels.football;

import data.models.blockymodels.SingleEventBlockyFieldsModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballSingleEventBlockyFieldsModel extends SingleEventBlockyFieldsModel {

    private boolean displayMainEvent;
    private boolean fansUnitedExpanded;
    private boolean fansUnitedEnabled;
}