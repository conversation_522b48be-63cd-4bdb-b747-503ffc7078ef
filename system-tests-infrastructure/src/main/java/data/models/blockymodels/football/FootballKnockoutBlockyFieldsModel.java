package data.models.blockymodels.football;

import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.common.Stage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballKnockoutBlockyFieldsModel extends SportBlockyFieldsModel {

    private CommonResultModel tournament;
    private Season season;
    private Stage stage;
}