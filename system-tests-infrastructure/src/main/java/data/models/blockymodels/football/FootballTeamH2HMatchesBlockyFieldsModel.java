package data.models.blockymodels.football;

import data.constants.enums.MatchDataElementsStatisticsEnum;
import data.models.blockymodels.SportBlockyWithOddsFieldsModel;
import data.models.footballapi.teams.MatchesV2TeamModel;
import data.models.footballapi.v2.MatchV2Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballTeamH2HMatchesBlockyFieldsModel extends SportBlockyWithOddsFieldsModel {
    private MatchesV2TeamModel teamOne;
    private MatchesV2TeamModel teamTwo;
    private MatchV2Model match;
    private ArrayList<MatchDataElementsStatisticsEnum> statistics;
    private boolean displayHeader;
    private boolean dataHeaderDisplay;
}
