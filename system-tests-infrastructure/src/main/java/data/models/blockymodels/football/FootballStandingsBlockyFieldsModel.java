package data.models.blockymodels.football;

import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Stage;
import data.models.footballapi.stage.Standing;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballStandingsBlockyFieldsModel extends SportBlockyFieldsModel {

    private CommonResultModel tournament;
    private TournamentSeasonModel season;
    private Stage stage;
    private List<Standing> allTeams;
    private List<Standing> teamsForHighlight;
    private long show;
    private String limit;
    private String startFromPosition;
}