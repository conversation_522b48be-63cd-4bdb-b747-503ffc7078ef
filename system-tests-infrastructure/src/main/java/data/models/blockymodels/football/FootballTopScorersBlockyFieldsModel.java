package data.models.blockymodels.football;

import data.constants.enums.PlayerDataElementsStatisticsEnum;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.topscorer.TopScorerModel;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballTopScorersBlockyFieldsModel extends SportBlockyFieldsModel {

    private TopScorerModel playerToHighLight;
    private CommonResultModel tournament;
    private TournamentSeasonModel season;
    private ArrayList<PlayerDataElementsStatisticsEnum> statistics;
    private Boolean filterStatisticsByTeam;
    private TeamModel team;
    private long startFromPosition;
    private long show;
}