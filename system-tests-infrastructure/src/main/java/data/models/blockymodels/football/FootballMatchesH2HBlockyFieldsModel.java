package data.models.blockymodels.football;

import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.searchapi.ResultModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballMatchesH2HBlockyFieldsModel extends SportBlockyFieldsModel {

    private ResultModel firstFootballTeam;
    private ResultModel secondFootballTeam;
    private String limit;
}
