package data.models.blockymodels.football;

import data.constants.MatchType;
import data.models.blockymodels.SportBlockyWithOddsFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Stage;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.models.footballapi.v2.RoundV2Model;
import data.widgets.options.enums.DataSortDirectionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FootballTournamentProgrammeBlockyFieldsModel extends SportBlockyWithOddsFieldsModel {

    private CommonResultModel tournament;
    private TournamentSeasonModel season;
    private Stage stage;
    private List<RoundV2Model> rounds;
    private DataSortDirectionEnum sortDirection;
    private String limit;
    private MatchType matchType;
}