package data.models.blockymodels.content;

import data.constants.ImageAlignmentEnum;
import data.constants.ImageWidthEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImageBlockyFieldsModel {

    private boolean hasWatermark;
    private String imageDescription;
    private List<String> authors;
    private String imageRatios;
    private ImageWidthEnum imageWidth;
    private ImageAlignmentEnum imageAlignment;
    private String imageAlt;
    private String imageCaption;
    private String link;
    private String text;
    private boolean openInNewWindow;
    private boolean openInTheSameWindow;
}
