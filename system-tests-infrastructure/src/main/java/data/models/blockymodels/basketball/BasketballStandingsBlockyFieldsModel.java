package data.models.blockymodels.basketball;

import com.google.gson.annotations.SerializedName;
import data.models.basketball.competition.CompetitionModel;
import data.models.basketball.season.SeasonDetailsModel;
import data.models.basketball.season.Stage;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.widgets.options.enums.DefaultHeaderOptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class BasketballStandingsBlockyFieldsModel extends SportBlockyFieldsModel {
    private CompetitionModel competition;
    private SeasonDetailsModel season;
    private Stage stage;
    private DefaultHeaderOptionEnum defaultHeaderOption;
    private boolean displayOdds;
    @SerializedName("season_id")
    private String seasonId;
    @SerializedName("stage_id")
    private String stageId;
    @SerializedName("competition_id")
    private String competitionId;
}