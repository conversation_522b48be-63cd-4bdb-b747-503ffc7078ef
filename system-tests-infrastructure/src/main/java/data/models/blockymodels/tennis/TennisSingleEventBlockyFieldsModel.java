package data.models.blockymodels.tennis;

import data.models.blockymodels.SingleEventBlockyFieldsModel;
import data.models.tennisapi.PlayerModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class TennisSingleEventBlockyFieldsModel extends SingleEventBlockyFieldsModel {

    private PlayerModel player;
    private PlayerModel playerTwo;
}