package data.models.blockymodels.tennis;

import data.models.blockymodels.SportBlockyWithOddsFieldsModel;
import data.models.tennisapi.CompetitionModel;
import data.models.tennisapi.RoundModel;
import data.models.tennisapi.TournamentModel;
import data.widgets.options.enums.DataGameTypeEnum;
import data.widgets.options.enums.DataGenderEnum;
import data.widgets.options.enums.DataSortDirectionEnum;
import data.widgets.options.models.DataDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class TennisTournamentProgrammeBlockyFieldsModel extends SportBlockyWithOddsFieldsModel {

    private CompetitionModel competition;
    private String season;
    private TournamentModel tournament;
    private String limit;
    private DataDate dateFrom;
    private DataDate dateTo;
    private List<RoundModel> rounds;
    private DataSortDirectionEnum sortDirection;
    private DataGameTypeEnum matchType;
    private DataGenderEnum gender;
}