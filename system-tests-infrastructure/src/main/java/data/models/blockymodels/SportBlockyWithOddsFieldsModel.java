package data.models.blockymodels;

import data.models.footballapi.odds.Bookmaker;
import data.widgets.options.models.DataDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class SportBlockyWithOddsFieldsModel extends SportBlockyFieldsModel {

    private boolean displayOdds;
    private Bookmaker bookmaker;
    private DataDate dateFrom;
    private DataDate dateTo;
}