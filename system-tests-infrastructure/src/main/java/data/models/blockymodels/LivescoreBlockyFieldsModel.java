package data.models.blockymodels;

import data.models.multisportapi.CompetitionModel;
import data.widgets.options.enums.DataHeaderDefaultOptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class LivescoreBlockyFieldsModel extends SportBlockyWithOddsFieldsModel {

    private LocalDate date;
    private List<CompetitionModel> competitionFilter;
    private DataHeaderDefaultOptionEnum defaultHeaderOption;
    private boolean displayHeader;
}