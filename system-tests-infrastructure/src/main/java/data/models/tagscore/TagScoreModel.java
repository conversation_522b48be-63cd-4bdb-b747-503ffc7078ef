package data.models.tagscore;

import lombok.*;
import repositories.core.ApiEntity;

import static data.constants.StringConstants.EMPTY_STRING;

@Data
@Builder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TagScoreModel extends ApiEntity {

    // There are no fields here because Tag Score API does not return anything when request is successful.

    @Override
    public String getEntityId() {
        return EMPTY_STRING;
    }
}