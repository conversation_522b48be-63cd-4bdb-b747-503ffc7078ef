package data.models.collaborationapi.subdocument;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubDocumentModel extends ApiEntity {
    private String id;
    private Meta meta;
    @SerializedName("admin_id")
    @JsonProperty("admin_id")
    private String adminId;
    private String name;

    @Override
    public String getEntityId() {
        return getId();
    }
}
