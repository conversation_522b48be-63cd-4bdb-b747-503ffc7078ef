
package data.models.collaborationapi.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentResponseModel extends ApiEntity {
    private String id;
    private String message;

    @Override
    public String getEntityId() {
        return getId();
    }
}
