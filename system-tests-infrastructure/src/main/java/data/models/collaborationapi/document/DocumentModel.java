package data.models.collaborationapi.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentModel extends ApiEntity {
    private String id;
    @SerializedName("admin_id")
    @JsonProperty("admin_id")
    private String adminId;
    @SerializedName("check_active_connections")
    @JsonProperty("check_active_connections")
    private Boolean checkActiveConnections;

    @Override
    public String getEntityId() {
        return getId();
    }
}
