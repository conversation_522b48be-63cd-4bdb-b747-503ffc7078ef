package data.models.tags;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.common.CreatedBy;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TagModel extends ApiEntity {

    private String id;
    private String title;
    private String description;
    private String type;
    private SeoModel seo;
    private UrlsModel urls;
    private int weight;
    @SerializedName("order_type")
    @JsonProperty("order_type")
    private String orderType;
    private Object generic;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    private String language;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaModel> mainMedia;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;
    @JsonProperty("external_url")
    @SerializedName("external_url")
    private String externalUrl;
    @JsonProperty("canonical_url")
    @SerializedName("canonical_url")
    private String canonicalUrl;

    @Override
    public String getEntityId() {
        return getId();
    }
}