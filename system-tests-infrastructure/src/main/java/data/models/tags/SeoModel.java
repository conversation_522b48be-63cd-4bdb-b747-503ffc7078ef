package data.models.tags;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeoModel {

    private String slug;
    private Boolean index;
    private String title;
    private Boolean follow;
    private String jsonld;
    private List<Object> keywords;
    private String description;
    @SerializedName("redirect_type")
    @JsonProperty("redirect_type")
    private String redirectType;
    @SerializedName("automatic_seo_title")
    @JsonProperty("automatic_seo_title")
    private Boolean automaticSeoTitle;
    @SerializedName("automatic_slug")
    @JsonProperty("automatic_slug")
    private Boolean automaticSlug;
}