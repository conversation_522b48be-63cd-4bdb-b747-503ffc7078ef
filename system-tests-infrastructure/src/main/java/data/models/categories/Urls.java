
package data.models.categories;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Urls {
    @SerializedName("canonical_url")
    @JsonProperty("canonical_url")
    private Object canonicalUrl;
    @SerializedName("external_url")
    @JsonProperty("external_url")
    private Object externalUrl;
    @SerializedName("original_source_url")
    @JsonProperty("original_source_url")
    private String originalSourceUrl;
    @SerializedName("skip_url_generation")
    @JsonProperty("skip_url_generation")
    private boolean skipUrlGeneration;
}
