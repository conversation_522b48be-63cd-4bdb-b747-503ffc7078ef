package data.models.categories;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.PropertiesModel;
import data.models.common.CommonModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Category extends ApiEntity {

    private String id;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String title;
    private Object description;
    private Boolean active;
    @SerializedName("parent_id")
    @JsonProperty("parent_id")
    private Object parentId;
    private Long weight;
    private Object generic;
    private Seo seo; //check
    private Urls urls; //check
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    private String language;
    private Boolean live;
    private Boolean evergreen;
    private Boolean important;
    @SerializedName("ads-enabled")
    @JsonProperty("ads-enabled")
    private Boolean adsEnabled;
    @SerializedName("adult-content")
    @JsonProperty("adult-content")
    private Boolean adultContent;
    @SerializedName("breaking-news")
    @JsonProperty("breaking-news")
    private Boolean breakingNews;
    @SerializedName("with-match-ads")
    @JsonProperty("with-match-ads")
    private Boolean withMatchAds;
    @SerializedName("betting-content")
    @JsonProperty("betting-content")
    private Boolean bettingContent;
    @SerializedName("for-translation")
    @JsonProperty("for-translation")
    private Boolean forTranslation;
    @SerializedName("sensitive-content")
    @JsonProperty("sensitive-content")
    private Boolean sensitiveContent;
    private List<Category> subs;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<Object> mainMedia;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;
    @SerializedName("custom_author")
    @JsonProperty("custom_author")
    private Object customAuthor;
    private CommonModel origin;
    private PropertiesModel properties;
    @SerializedName("published_at")
    @JsonProperty("published_at")
    private String publishedAt;
    @SerializedName("published_until")
    @JsonProperty("published_until")
    private String publishedUntil;
    @SerializedName("description_html")
    @JsonProperty("description_html")
    private String descriptionHtml;
    @SerializedName("subtitle")
    @JsonProperty("subtitle")
    private String subtitle;

    @Override
    public String getEntityId() {
        return getId();
    }
}