
package data.models.contentapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseModel extends ApiEntity {

    private String message;

    @Override
    public String getEntityId() {
        return null;
    }
}
