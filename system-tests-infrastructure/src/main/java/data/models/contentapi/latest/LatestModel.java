package data.models.contentapi.latest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.MainMediaItem;
import data.models.articles.Seo;
import data.models.categories.Category;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LatestModel extends ApiEntity {

    private String id;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String title;
    private String slug;
    private String subtitle;
    private String strapline;
    private String status;
    private Seo seo;
    private Object type;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("published_at")
    @JsonProperty("published_at")
    private String publishedAt;
    @SerializedName("sport_events")
    @JsonProperty("sport_events")
    private List<SportEvent> sportEvents;
    private List<Object> versions;
    private Category category;
    @SerializedName("additional_categories")
    @JsonProperty("additional_categories")
    private List<Category> additionalCategories;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaItem> mainMedia;

    @Override
    public String getEntityId() {
        return getId();
    }
}