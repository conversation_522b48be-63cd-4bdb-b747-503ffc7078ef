package data.models.contentapi.latest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.Venue;
import data.models.galleries.EventStatus;
import data.models.related.TournamentSeasonStage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SportEvent {

    private String id;
    private String sport;
    private String round;
    private String slug;
    private int incidents;
    private Venue venue;
    private Object referee;
    private int spectators;
    private String minute;
    private String uuid;
    @SerializedName("event_status")
    @JsonProperty("event_status")
    private EventStatus eventStatus;
    @SerializedName("start_time")
    @JsonProperty("start_time")
    private String startTime;
    @SerializedName("home_team")
    @JsonProperty("home_team")
    private TeamModel homeTeam;
    @SerializedName("away_team")
    @JsonProperty("away_team")
    private TeamModel awayTeam;
    @SerializedName("goal_home")
    @JsonProperty("goal_home")
    private int goalHome;
    @SerializedName("goal_away")
    @JsonProperty("goal_away")
    private int goalAway;
    @SerializedName("tournament_season_stage")
    @JsonProperty("tournament_season_stage")
    private TournamentSeasonStage tournamentSeasonStage;
    @SerializedName("home_score")
    @JsonProperty("home_score")
    private Integer homeScore;
    @SerializedName("away_score")
    @JsonProperty("away_score")
    private Integer awayScore;
    @SerializedName("lineup_available")
    @JsonProperty("lineup_available")
    private boolean lineupAvailable;
    @SerializedName("live_updates")
    @JsonProperty("live_updates")
    private boolean liveUpdates;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    @SerializedName("teamstats_available")
    @JsonProperty("teamstats_available")
    private boolean teamstatsAvailable;
    @SerializedName("finished_at")
    @JsonProperty("finished_at")
    private String finishedAt;
    @SerializedName("started_at")
    @JsonProperty("started_at")
    private String startedAt;
    @SerializedName("first_half_started_at")
    @JsonProperty("first_half_started_at")
    private String firstHalfStartedAt;
    @SerializedName("second_half_started_at")
    @JsonProperty("second_half_started_at")
    private String secondHalfStartedAt;
    @SerializedName("extra_time_first_half_started_at")
    @JsonProperty("extra_time_first_half_started_at")
    private String extraTimeFirstHalfStartedAt;
    @SerializedName("extra_time_second_half_started_at")
    @JsonProperty("extra_time_second_half_started_at")
    private String extraTimeSecondHalfStartedAt;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
}