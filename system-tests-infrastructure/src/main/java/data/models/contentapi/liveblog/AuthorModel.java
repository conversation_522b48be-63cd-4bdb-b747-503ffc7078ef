package data.models.contentapi.liveblog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AuthorModel extends ApiEntity {
    private String id;
    private String name;

    @Override
    public String getEntityId() {
        return getId();
    }
}
