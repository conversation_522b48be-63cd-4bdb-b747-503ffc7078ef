package data.models.articlessearch;

import data.models.MetaModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ArticleSearchModel extends ApiEntity {

    private List<Datum> data;
    private MetaModel meta;

    @Override
    public String getEntityId() {
        return null;
    }
}
