package data.models.articlesscheduler;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MatchesModel {

    @JsonProperty("match_id")
    @SerializedName("match_id")
    private String matchId;
    @JsonProperty("competition_id")
    @SerializedName("competition_id")
    private String competitionId;
    @JsonProperty("competition_name")
    @SerializedName("competition_name")
    private String competitionName;
    @JsonProperty("match_name")
    @SerializedName("match_name")
    private String matchName;
    @JsonProperty("match_date")
    @SerializedName("match_date")
    private String matchDate;
}