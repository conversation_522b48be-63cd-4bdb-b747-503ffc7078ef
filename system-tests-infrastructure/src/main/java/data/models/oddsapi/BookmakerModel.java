package data.models.oddsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookmakerModel {

    private String id;
    private String name;
    private String url;
    private List<AssetModel> assets;
    private List<LinkModel> links;
    private BrandingModel branding;
    @JsonProperty("event_urls")
    @SerializedName("event_urls")
    private List<EventUrlModel> eventUrls;
    @JsonProperty("stakes_disclaimer")
    @SerializedName("stakes_disclaimer")
    private String stakesDisclaimer;
    @JsonProperty("domain_name")
    @SerializedName("domain_name")
    private String domainName;
}