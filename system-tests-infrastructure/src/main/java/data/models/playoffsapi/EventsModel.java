package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EventsModel extends ApiEntity {
    private String id;
    private String slug;
    private List<ResultModel> results;
    private StatusModel status;
    private List<ParticipantsModel> participants;
    @SerializedName("scheduled_start_time")
    @JsonProperty("scheduled_start_time")
    private String scheduledStartTime;

    @Override
    public String getEntityId() {
        return getId();
    }
}
