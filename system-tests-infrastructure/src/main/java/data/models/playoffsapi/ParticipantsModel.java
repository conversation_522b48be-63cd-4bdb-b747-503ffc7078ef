package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ParticipantsModel extends ApiEntity {

    private String id;
    private String name;
    private String slug;
    private String gender;
    private String type;
    private List<DetailModel> details;
    private AssetsModel assets;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private SeedModel seed;
    private WinnerModel winner;
    @SerializedName("draw_order")
    @JsonProperty("draw_order")
    private int drawOrder;
    @SerializedName("event_order")
    @JsonProperty("event_order")
    private int eventOrder;

    @Override
    public String getEntityId() {
        return getId();
    }
}
