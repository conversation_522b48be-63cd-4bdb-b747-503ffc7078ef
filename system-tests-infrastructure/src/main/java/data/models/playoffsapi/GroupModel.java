package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GroupModel extends ApiEntity {

    private String id;
    private int order;
    private List<ResultModel> results;
    private List<ParticipantsModel> participants;
    private List<EventsModel> events;
    @SerializedName("child_object_id")
    @JsonProperty("child_object_id")
    private String childObjectId;

    @Override
    public String getEntityId() {
        return getId();
    }
}
