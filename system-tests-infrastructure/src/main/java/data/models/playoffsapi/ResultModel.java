package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResultModel {
    private List<EventResultParticipantModel> participants;
    @SerializedName("result_type")
    @JsonProperty("result_type")
    private ResultTypeModel resultType;
}
