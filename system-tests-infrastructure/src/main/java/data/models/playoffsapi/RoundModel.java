package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RoundModel extends ApiEntity {

    private String id;
    private String name;
    private List<GroupModel> groups;
    private String status;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("start_date")
    @JsonProperty("start_date")
    private String startDate;
    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    private Assets assets;

    @Override
    public String getEntityId() {
        return getId();
    }
}
