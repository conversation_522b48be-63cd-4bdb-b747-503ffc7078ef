package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.Assets;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ResultTypeModel extends ApiEntity {
    private String id;
    private String name;
    private String type;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    private Assets assets;

    @Override
    public String getEntityId() {
        return getId();
    }
}
