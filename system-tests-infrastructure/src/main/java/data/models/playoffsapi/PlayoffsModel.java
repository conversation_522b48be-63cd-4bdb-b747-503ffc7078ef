package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class PlayoffsModel extends ApiEntity {

    private String status;
    private DataModel data;
    @SerializedName("available_playoff_types")
    @JsonProperty("available_playoff_types")
    private List<AvailablePlayoffTypeModel> availablePlayoffTypes;

    @Override
    public String getEntityId() {
        return "";
    }
}
