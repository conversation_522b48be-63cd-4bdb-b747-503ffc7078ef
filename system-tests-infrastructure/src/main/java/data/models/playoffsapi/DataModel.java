package data.models.playoffsapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataModel {

    private StageModel stage;
    private List<RoundModel> rounds;
    private Object type;
    @SerializedName("start_round")
    @JsonProperty("start_round")
    private StartRoundModel startRound;
    @SerializedName("end_round")
    @JsonProperty("end_round")
    private EndRoundModel endRound;
    @SerializedName("small_final")
    @JsonProperty("small_final")
    private boolean smallFinal;
}
