
package data.models.liveblogapi.post;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

@lombok.Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Block extends ApiEntity {
    private Data data;
    private String id;
    private String type;

    @Override
    public String getEntityId() {
        return getId();
    }
}