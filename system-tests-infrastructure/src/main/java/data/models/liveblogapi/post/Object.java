
package data.models.liveblogapi.post;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import data.models.articles.CreatedBy;
import data.models.articles.MainMediaItem;
import data.models.articles.Seo;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.liveblogapi.AdditionalCategories;
import lombok.Builder;

import java.util.List;

@lombok.Data
@Builder
public class Object {
    @SerializedName("additional_categories")
    @JsonProperty("additional_categories")
    private List<AdditionalCategories> additionalCategories;
    @SerializedName("adult_content")
    @JsonProperty("adult_content")
    private Boolean adultContent;
    private List<AuthorModel> authors;
    @SerializedName("betting_content")
    @JsonProperty("betting_content")
    private Boolean bettingContent;
    private Category category;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createdAt;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;
    @SerializedName("custom_author")
    @JsonProperty("custom_author")
    private java.lang.Object customAuthor;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private java.lang.Object footer;
    private String id;
    private Boolean important;
    @SerializedName("is_adult_content")
    @JsonProperty("is_adult_content")
    private Boolean isAdultContent;
    @SerializedName("is_sensitive_content")
    @JsonProperty("is_sensitive_content")
    private Boolean isSensitiveContent;
    private String language;
    private Boolean live;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaItem> mainMedia;
    @SerializedName("published_at")
    @JsonProperty("published_at")
    private String publishedAt;
    @SerializedName("published_until")
    @JsonProperty("published_until")
    private java.lang.Object publishedUntil;
    @SerializedName("run_ads")
    @JsonProperty("run_ads")
    private Boolean runAds;
    @SerializedName("sensitive_content")
    @JsonProperty("sensitive_content")
    private Boolean sensitiveContent;
    private Seo seo;
    private String status;
    private String strapline;
    private java.lang.Object subtitle;
    private String title;
    private String type;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updatedAt;
    @SerializedName("text_align")
    @JsonProperty("text_align")
    private String textAlign;
    private double start;
    private String content;
    @SerializedName("embed_type")
    @JsonProperty("embed_type")
    private String embedType;
    @SerializedName("validation_type")
    @JsonProperty("validation_type")
    private ValidationType validationType;
    private String link;
    @SerializedName("link_type")
    @JsonProperty("link_type")
    private String linkType;
    @SerializedName("open_type")
    @JsonProperty("open_type")
    private String openType;
    private String text;
    private String code;
    @SerializedName(StringConstants.IS_PREFERRED_STRING)
    private Boolean isPreferred;
}
