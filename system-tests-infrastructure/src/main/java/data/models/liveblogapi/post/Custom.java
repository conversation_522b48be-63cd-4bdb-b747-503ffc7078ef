
package data.models.liveblogapi.post;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.ArticleResponseModel;
import data.models.authors.AuthorModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;

import java.util.List;

@lombok.Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Custom extends ApiEntity {
    @SerializedName("main_image")
    @JsonProperty("main_image")
    private ArticleResponseModel mainImage;
    private Position position;
    private String alignment;
    private String alt;
    private List<AuthorModel> authors;
    private String caption;
    private String description;
    private String width;
    @SerializedName("starts_at")
    @JsonProperty("starts_at")
    private String startsAt;

    @Override
    public String getEntityId() {
        return "";
    }
}
