package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeoModel {

    @JsonProperty("automatic_slug")
    @SerializedName("automatic_slug")
    private Boolean automaticSlug;
    @JsonProperty("automatic_seo_title")
    @SerializedName("automatic_seo_title")
    private Boolean automaticSeoTitle;
    private String title;
}