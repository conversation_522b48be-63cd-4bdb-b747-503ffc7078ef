
package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Sponsor { //TODO Can be replaced with OddProviderModel.class
    private String country;
    private String id;
    private Object links; //List<Object>
    private String name;
    private String url;
    @SerializedName("url_logo")
    @JsonProperty("url_logo")
    private String urlLogo;
}