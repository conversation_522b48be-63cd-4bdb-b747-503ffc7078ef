package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.authors.AuthorModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Collaborators {
    @SerializedName("additional_info")
    @JsonProperty("additional_info")
    private AdditionalInfo additionalInfo;
    private AuthorModel author;
}
