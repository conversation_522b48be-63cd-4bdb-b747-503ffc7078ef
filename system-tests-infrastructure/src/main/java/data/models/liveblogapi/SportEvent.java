package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.footballapi.stage.StageModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.TournamentSeasonEventModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SportEvent {

    private String id;
    private String uuid;
    @SerializedName("tournament_season_stage")
    @JsonProperty("tournament_season_stage")
    private StageModel tournamentSeasonStage;
    @SerializedName("home_team")
    @JsonProperty("home_team")
    private TeamModel homeTeam;
    @JsonProperty("away_team")
    @SerializedName("away_team")
    private TeamModel awayTeam;
    @SerializedName("start_time")
    @JsonProperty("start_time")
    private String startTime;
}