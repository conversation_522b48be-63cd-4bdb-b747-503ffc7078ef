package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import repositories.core.ApiEntity;


@EqualsAndHashCode(callSuper = false)
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveBlogDeleteModel extends ApiEntity {
    @SerializedName("check_active_connections")
    @JsonProperty("check_active_connections")
    private Boolean checkActiveConnections;
    @SerializedName("modified_by")
    @JsonProperty("modified_by")
    private CreatedBy modifiedBy;
    @Override
    public String getEntityId() {
        return null;
    }
}
