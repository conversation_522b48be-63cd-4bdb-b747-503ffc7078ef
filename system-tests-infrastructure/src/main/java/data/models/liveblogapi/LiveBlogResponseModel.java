package data.models.liveblogapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.MainMediaItem;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.contentapi.latest.LatestItemModel;
import data.models.footballapi.odd_providers.OddProviderModel;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LiveBlogResponseModel extends ApiEntity implements LatestItemModel {

    private String message;
    private String id;
    private String title;
    private String description;
    private String status;
    private String type;
    private String slug;
    private String sport;
    private String language;
    private Category category;
    private List<Collaborators> collaborators;
    private List<AuthorModel> authors;
    private List<Competitions> competitions;
    private List<Team> teams;
    private int pagination;
    private List<OddProviderModel> sponsors;
    @SerializedName("start_time")
    @JsonProperty("start_time")
    private String startTime;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("end_time")
    @JsonProperty("end_time")
    private String endTime;
    @SerializedName("created_at")
    @JsonProperty("created_at")
    private String createAt;
    @SerializedName("updated_at")
    @JsonProperty("updated_at")
    private String updateAt;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaItem> mainMedia;
    @SerializedName("additional_categories")
    @JsonProperty("additional_categories")
    private List<AdditionalCategories> additionalCategories;
    @SerializedName("created_by")
    @JsonProperty("created_by")
    private CreatedBy createdBy;
    @SerializedName("sport_events")
    @JsonProperty("sport_events")
    private List<SportEvents> sportEvents;
    @SerializedName("match_header")
    @JsonProperty("match_header")
    private Boolean matchHeader;
    private List<String> versions;
    @SerializedName("adult_content")
    @JsonProperty("adult_content")
    private Boolean adultContent;
    @SerializedName("is_adult_content")
    @JsonProperty("is_adult_content")
    private Boolean isAdultContent;
    @JsonProperty("sensitive_content")
    private Boolean sensitiveContent;
    @JsonProperty("is_sensitive_content")
    private Boolean isSensitiveContent;
    @SerializedName("content_updated_at")
    @JsonProperty("content_updated_at")
    private String contentUpdatedAt;

    @Override
    public String getEntityId() {
        return getId();
    }
}