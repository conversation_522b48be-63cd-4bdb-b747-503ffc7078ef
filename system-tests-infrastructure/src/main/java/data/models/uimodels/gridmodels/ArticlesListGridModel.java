package data.models.uimodels.gridmodels;

import lombok.*;
import solutions.bellatrix.web.components.advanced.TableHeader;

import java.time.LocalDateTime;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ArticlesListGridModel {

    @TableHeader(name = "Title")
    private String title;
    @TableHeader(name = "Active")
    private String active;
    @TableHeader(name = "Created by")
    private String createdBy;
    @TableHeader(name = "Published at")
    private LocalDateTime publishedAt;
    @TableHeader(name = "Category")
    private String category;
    @TableHeader(name = "Views")
    private String views;
    @TableHeader(name = "Comments")
    private String comments;
    @TableHeader(name = "Actions")
    private String actions;
}