package data.models.uimodels.gridmodels;

import data.customelements.CustomEntitiesGridActions;
import lombok.*;
import solutions.bellatrix.web.components.advanced.TableHeader;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomEntitiesListGridModel {

    @TableHeader(name = "Name")
    private String name;

    @TableHeader(name = "Domain")
    private String domain;

    @TableHeader(name = "Entity Type")
    private String entityType;

    @TableHeader(name = "Actions")
    private String actions;
}