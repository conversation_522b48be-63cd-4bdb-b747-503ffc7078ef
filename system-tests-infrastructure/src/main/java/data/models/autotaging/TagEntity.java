package data.models.autotaging;

import com.google.gson.annotations.SerializedName;
import data.models.searchapi.DisplayAsset;
import data.models.searchapi.Translation;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;

@Data
@Builder
public class TagEntity {

    public String name;
    @SerializedName("short_name")
    public Object shortName;
    @SerializedName("three_letter_code")
    public Object threeLetterCode;
    public ArrayList<Translation> translations;
    public String id;
    public String slug;
    @SerializedName("entity_type")
    public String entityType;
    public String sport;
    @SerializedName("display_asset")
    public DisplayAsset displayAsset;
    @SerializedName("legacy_id")
    public String legacyId;
    @SerializedName("country_id")
    public String countryId;
    public TagsCountry country;
    public String gender;
    public String birthdate;
    @SerializedName("competition_ids")
    public Object competitionIds;
    @SerializedName("team_ids")
    public Object teamIds;
}
