
package data.models.autotaging;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Venue {

    private Long capacity;
    private String city;
    private Country country;
    private Long id;
    private Double lat;
    private Double lng;
    private String name;
    @SerializedName("url_image")
    @JsonProperty("url_image")
    private Object urlImage;
}
