package data.models.autotaging;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AutoTaggingRequestBodyModel extends ApiEntity {

    private Set<String> sports;
    private String text;
    @SerializedName("input_language")
    @JsonProperty("input_language")
    private String inputLanguage;
    @SerializedName("competition_ids")
    @JsonProperty("competition_ids")
    private List<String> competitionIds;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}
