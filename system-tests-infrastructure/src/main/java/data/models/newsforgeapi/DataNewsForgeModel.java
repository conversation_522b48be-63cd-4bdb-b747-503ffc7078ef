package data.models.newsforgeapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataNewsForgeModel {

    @JsonProperty("original_content")
    @SerializedName("original_content")
    private OriginalContentModel originalContent;
    @JsonProperty("translated_content")
    @SerializedName("translated_content")
    private TranslatedContentModel translatedContent;
}