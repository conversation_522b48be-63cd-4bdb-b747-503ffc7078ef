package data.models.newsforgeapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GeneratePostModel extends ApiEntity {

    @JsonProperty("target_url")
    @SerializedName("target_url")
    private String targetUrl;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}