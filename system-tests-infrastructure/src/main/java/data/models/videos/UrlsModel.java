package data.models.videos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.constants.StringConstants;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UrlsModel extends ApiEntity {

    @SerializedName("live_url")
    @JsonProperty("live_url")
    private String liveUrl;
    @SerializedName("video_files")
    @JsonProperty("video_files")
    private List<VideoFileModel> videoFiles;
    @SerializedName("external_url")
    @JsonProperty("external_url")
    private String externalUrl;
    @SerializedName("canonical_url")
    @JsonProperty("canonical_url")
    private String canonicalUrl;
    @SerializedName("public_url_amp")
    @JsonProperty("public_url_amp")
    private String publicUrlAmp;
    @SerializedName("public_url_mobile")
    @JsonProperty("public_url_mobile")
    private String publicUrlMobile;
    @SerializedName("public_url_desktop")
    @JsonProperty("public_url_desktop")
    private String publicUrlDesktop;
    private String url;
    private String type;
    @SerializedName("original_source_url")
    @JsonProperty("original_source_url")
    private String originalSourceUrl;
    @SerializedName("skip_url_generation")
    @JsonProperty("skip_url_generation")
    private boolean skipUrlGeneration;

    @Override
    public String getEntityId() {
        return StringConstants.EMPTY_STRING;
    }
}