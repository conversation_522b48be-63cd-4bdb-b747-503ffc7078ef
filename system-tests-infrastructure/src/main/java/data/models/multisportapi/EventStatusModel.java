package data.models.multisportapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EventStatusModel extends ApiEntity {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<TranslationModel> translations;
    private String id;
    private String type;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;

    @Override
    public String getEntityId() {
        return getId();
    }
}