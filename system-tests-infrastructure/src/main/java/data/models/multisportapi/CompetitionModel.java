package data.models.multisportapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompetitionModel extends ApiEntity {

    private String id;
    private String name;
    private String slug;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String sport;
    private List<TranslationModel> translations;
    @SerializedName("display_assets")
    @JsonProperty("display_assets")
    private DisplayAssetModel displayAssets;
    @SerializedName("country_id")
    @JsonProperty("country_id")
    private String countryId;
    private CountryModel country;
    private String gender;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    @SerializedName("display_asset")
    @JsonProperty("display_asset")
    private DisplayAssetModel displayAsset;
    @SerializedName("tag_score")
    @JsonProperty("tag_score")
    private Object tagScore;
    @SerializedName("elastic_score")
    @JsonProperty("elastic_score")
    private Object elasticScore;
    private Object region;

    @Override
    public String getEntityId() {
        return getId();
    }
}