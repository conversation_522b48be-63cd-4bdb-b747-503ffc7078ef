package data.models.multisportapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.searchapi.DisplayAsset;
import data.models.searchapi.Translation;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ParticipantModel extends ApiEntity {
    
    private String name;
    private List<Translation> translations;
    private String id;
    private String slug;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private String position;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
    @SerializedName("display_asset")
    @JsonProperty("display_asset")
    private DisplayAsset displayAsset;
    private String winner;
    @SerializedName("current_server")
    @JsonProperty("current_server")
    private String currentServer;

    @Override
    public String getEntityId() {
        return getId();
    }
}