package data.models.multisportapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.searchapi.Translation;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EventStageModel extends ApiEntity {

    private String name;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<Translation> translations;
    private String id;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;

    @Override
    public String getEntityId() {
        return getId();
    }
}