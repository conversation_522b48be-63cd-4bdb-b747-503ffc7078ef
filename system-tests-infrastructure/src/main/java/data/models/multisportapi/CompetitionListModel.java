package data.models.multisportapi;

import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompetitionListModel extends ApiEntity {

    private String code;
    private String name;
    private String description;
    private List<CompetitionsModel> competitions;

    @Override
    public String getEntityId() {
        return getCode();
    }
}