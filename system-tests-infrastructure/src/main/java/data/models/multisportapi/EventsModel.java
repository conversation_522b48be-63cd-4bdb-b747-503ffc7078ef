package data.models.multisportapi;

import com.google.gson.annotations.SerializedName;
import lombok.*;
import repositories.core.ApiEntity;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EventsModel extends ApiEntity {

    @SerializedName(value = "eventId")
    private String eventId;
    private int order;

    @Override
    public String getEntityId() {
        return getEventId();
    }
}