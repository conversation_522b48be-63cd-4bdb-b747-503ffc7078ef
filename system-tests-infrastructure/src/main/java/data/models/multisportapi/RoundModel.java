package data.models.multisportapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoundModel {

    @SerializedName("end_date")
    @JsonProperty("end_date")
    private String endDate;
    private String id;
    private String name;
    private String status;
    private String type;
    private String uuid;
    @SerializedName("short_name")
    @JsonProperty("short_name")
    private String shortName;
    @SerializedName("three_letter_code")
    @JsonProperty("three_letter_code")
    private String threeLetterCode;
    private List<TranslationModel> translations;
    @SerializedName("legacy_id")
    @JsonProperty("legacy_id")
    private String legacyId;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
}