package data.models.multisportapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompetitionsModel implements Comparable<CompetitionsModel> {

    private CompetitionModel competition;
    private Integer order;

    @Override
    public int compareTo(CompetitionsModel competitionsModel) {
        return order.compareTo(competitionsModel.order);
    }
}