package data.models.wikipages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import data.models.articles.BodyItem;
import data.models.articles.MainMediaItem;
import data.models.articles.Seo;
import lombok.*;
import repositories.core.ApiEntity;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WikiModel extends ApiEntity {

    private String id;
    @SerializedName("entity_type")
    @JsonProperty("entity_type")
    private String entityType;
    private String title;
    private String subtitle;
    private String strapline;
    private List<BodyItem> body;
    private Seo seo;
    @SerializedName("translation_group_id")
    @JsonProperty("translation_group_id")
    private String translationGroupId;
    @SerializedName("published_at")
    @JsonProperty("published_at")
    private String publishedAt;
    @SerializedName("published_until")
    @JsonProperty("published_until")
    private String publishedUntil;
    private String status;
    private String type;
    private List<String> authors;
    @SerializedName("custom_author")
    @JsonProperty("custom_author")
    private String customAuthor;
    private String language;
    @SerializedName("image_id")
    @JsonProperty("image_id")
    private String imageId;
    @SerializedName("image_description")
    @JsonProperty("image_description")
    private String imageDescription;
    private String footer;
    @SerializedName("main_media")
    @JsonProperty("main_media")
    private List<MainMediaItem> mainMedia;
    private boolean important;
    @SerializedName("run_ads")
    @JsonProperty("run_ads")
    private boolean runAds;

    @Override
    public String getEntityId() {
        return getId();
    }
}