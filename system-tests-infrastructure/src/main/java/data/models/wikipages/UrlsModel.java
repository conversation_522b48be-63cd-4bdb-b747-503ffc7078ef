package data.models.wikipages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UrlsModel {

    @SerializedName("audio_url")
    @JsonProperty("audio_url")
    private String audioUrl;
    @SerializedName("skip_url_generation")
    @JsonProperty("skip_url_generation")
    private boolean skipUrlGeneration;
}