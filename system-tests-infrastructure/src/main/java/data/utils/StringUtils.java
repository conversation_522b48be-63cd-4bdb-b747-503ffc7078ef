package data.utils;

import com.github.javafaker.Faker;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class StringUtils {

    public static String[] splitTextBasedOnWord(String text, int spaceCountFromEnd) {
        String[] words = text.split("\\s+");

        // Calculate the split index
        int splitIndex = Math.max(0, words.length - spaceCountFromEnd);
        StringBuilder firstPart = new StringBuilder();
        StringBuilder secondPart = new StringBuilder();

        // Construct the first and second parts of the split text
        for (int i = 0; i < words.length; i++) {
            if (i < splitIndex) {
                firstPart.append(words[i]);
                if (i < splitIndex - 1) {
                    firstPart.append(" ");
                }
            } else {
                secondPart.append(words[i]);
                if (i < words.length - 1) {
                    secondPart.append(" ");
                }
            }
        }
        return new String[]{firstPart.toString(), secondPart.toString()};
    }

    public static int getNumberOfSpacesInText(String text) {
        return text.length() - text.replace(" ", "").length();
    }

    public static String generateSlug(String text) {
        return text.toLowerCase().replace(" ", "-").replace("[", "").replace("]", "");
    }

    /**
     * Generates a string with a given number of paragraphs and sentences.
     * The last sentence of each paragraph ends with an exclamation mark (!).
     *
     * @param numberOfParagraphs    count of paragraphs to generate
     * @param sentencesPerParagraph sentences per paragraph
     * @return the generated string
     */
    public static String generateParagraphsWithSentences(int numberOfParagraphs, int sentencesPerParagraph) {
        Faker faker = new Faker();
        StringBuilder text = new StringBuilder();

        for (int i = 0; i < numberOfParagraphs; i++) {
            for (int j = 0; j < sentencesPerParagraph; j++) {
                // Generate a normal sentence for all but the last one in a paragraph
                if (j < sentencesPerParagraph - 1) {
                    text.append(faker.lorem().sentence()).append(" ");
                } else {
                    // For the last sentence, ensure it ends with an exclamation mark
                    String sentence = faker.lorem().sentence();
                    // Remove the period and add an exclamation mark
                    sentence = sentence.substring(0, sentence.length() - 1) + "!";
                    text.append(sentence);
                }
            }
            // Add two line breaks after each paragraph for separation, except after the last one
            if (i < numberOfParagraphs - 1) {
                text.append("\n\n");
            }
        }

        return text.toString();
    }

    public static String[] splitText(String text, String splitBy) {
        return text.split(splitBy);
    }

    public static String capitalizeFirstLetter(String input) {
        if (input == null || input.isEmpty()) {
            throw new IllegalArgumentException("Input cannot be null or empty.");
        }
        if (input.length() == 1) {
            return input.toUpperCase();
        }
        char[] chars = input.toLowerCase().toCharArray();
        chars[0] = Character.toUpperCase(chars[0]);
        return new String(chars);
    }

    public static String replaceSpacesWithSymbol(String text, String symbol) {
        return text.replaceAll("\\s+", symbol);
    }

    public static String urlEncodeString(String text) {
        return URLEncoder.encode(text, StandardCharsets.UTF_8)
                .replace("+", "%20")
                .replace("%5B", "[")
                .replace("%5D", "]")
                .replace("%5F", "/")
                .replace("%3A", ":");
    }

    public static String generateRandomString(int length) {
        String charSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder(length);
        int charSetLength = charSet.length();

        for (int i = 0; i < length; i++) {
            int index = ThreadLocalRandom.current().nextInt(charSetLength);
            char randomChar = charSet.charAt(index);
            sb.append(randomChar);
        }

        return sb.toString();
    }

    public static void appendParameter(StringBuilder sb, String key, Object value) {
        sb.append(String.format("%s:%s;", key, value));
    }

    public static String formatDateTime(ZonedDateTime dateTime) {
        final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
        final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * Extracts all paragraph tags with their content from the input string.
     *
     * @param input The HTML string containing paragraph tags
     * @return A List of Strings where each element is a complete <p>text</p> tag
     */
    public static List<String> extractParagraphTags(String input) {
        List<String> paragraphTags = new ArrayList<>();

        int startIndex = 0;
        while (startIndex < input.length()) {
            int openTagStart = input.indexOf("<p>", startIndex);
            if (openTagStart == -1) {
                break;
            }

            int closeTagEnd = input.indexOf("</p>", openTagStart) + 4;
            if (closeTagEnd < 4) {
                break;
            }

            String paragraph = input.substring(openTagStart, closeTagEnd);
            paragraphTags.add(paragraph);

            startIndex = closeTagEnd;
        }

        return paragraphTags;
    }
}