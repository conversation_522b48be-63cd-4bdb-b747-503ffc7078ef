package data.utils;

import data.constants.StringConstants;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Locale;

public class DateUtils {

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("dd MMM");

    public static String convertDateFormat(LocalDate date, Locale... locale) {
        if (locale.length > 0) {
            OUTPUT_FORMATTER = OUTPUT_FORMATTER.withLocale(locale[0]);
        }
        return date.format(OUTPUT_FORMATTER).toUpperCase();
    }

    /**
     * Returns the current UTC time as an Instant, truncated to centi second precision.
     * This method ensures that the returned Instant, when formatted, will have exactly
     * two digits after the decimal point for seconds (e.g., .89Z).
     *
     * @return Instant representing current UTC time with centi second precision
     */
    public static Instant getCurrentUTC() {
        Instant now = Instant.now();
        long nanos = now.getNano();
        long centiSeconds = Math.round(nanos / 10_000_000.0); // Round to nearest centi second

        // If rounding resulted in 100 centi seconds, we need to add a second and set centi seconds to 0
        if (centiSeconds == 100) {
            return now.truncatedTo(ChronoUnit.SECONDS).plusSeconds(1);
        }

        return now.truncatedTo(ChronoUnit.SECONDS).plusNanos(centiSeconds * 10_000_000);
    }

    public static Instant getYesterdayUTCFromNow() {
        return getCurrentUTC().minus(1, ChronoUnit.DAYS);
    }

    public static Instant getTomorrowUTCFromNow() {
        return getCurrentUTC().plus(1, ChronoUnit.DAYS);
    }

    /**
     * Gets the UTC date from Instant time.
     *
     * @return LocalDate representing the current date in UTC
     */

    public static LocalDate getDateFromUTC(Instant time) {
        return time.atZone(ZoneOffset.UTC).toLocalDate();
    }

    /**
     * Formats a date string by adding an ordinal suffix to the day of the month.
     * <p>
     * This method takes a date string in the format "MMM d yyyy, HH:mm" (e.g., "Oct 3 2024, 14:30"),
     * parses it to extract the month, day, year, and time components, and then formats it to include
     * the correct ordinal suffix ("st", "nd", "rd", or "th") for the day of the month.
     *
     * @param inputDate the input date string in the format "MMM d yyyy, HH:mm"
     * @return a formatted date string in the format "MMM d[suffix] yyyy, HH:mm"
     * (e.g., "Oct 3rd 2024, 14:30")
     */

    public static String formatDate(String inputDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MMM d yyyy, HH:mm");
        LocalDateTime dateTime = LocalDateTime.parse(inputDate, inputFormatter);

        int day = dateTime.getDayOfMonth();
        String ordinal = getOrdinalIndicator(day);

        String month = dateTime.format(DateTimeFormatter.ofPattern("MMM"));
        String year = dateTime.format(DateTimeFormatter.ofPattern("yyyy"));
        String time = dateTime.format(DateTimeFormatter.ofPattern("HH:mm"));

        return String.format("%s %d%s %s, %s",
                month, day, ordinal, year, time);
    }

    /**
     * Generates a formatted UTC timestamp string with a 20-millisecond offset from the current time.
     * The timestamp is formatted according to the pattern "yyyy-MM-dd'T'HH:mm:ss+00:00".
     *
     * @return A string representing the UTC timestamp with the 20ms offset, formatted as "yyyy-MM-dd'T'HH:mm:ss+00:00"
     * Example output: "2025-01-10T14:30:45+00:00"
     */
    public static String getFormattedUtcDateTimeWithMillisOffset() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+00:00");
        return ZonedDateTime.now()
                .plus(20, ChronoUnit.MILLIS)
                .withZoneSameInstant(ZoneId.of(StringConstants.UTC_TIME_STRING))
                .format(formatter);
    }

    private static String getOrdinalIndicator(int day) {
        if (day >= 11 && day <= 13) {
            return "th";
        }
        return switch (day % 10) {
            case 1 -> "st";
            case 2 -> "nd";
            case 3 -> "rd";
            default -> "th";
        };
    }

    /**
     * Formats a datetime string to ISO date format.
     * Convenience method using ISO_DATE_FORMAT_PATTERN_STRING.
     *
     * @param dateTimeString ISO datetime string
     * @return formatted date in ISO format
     */
    public static String formatISODateTime(String dateTimeString) {
        Instant instant = Instant.parse(dateTimeString);
        return getDateFromUTC(instant).format(INPUT_FORMATTER);
    }

    /**
     * Splits an ISO 8601 formatted date-time string and returns the date portion.
     * For example, from "2025-04-24T17:00:00Z" it returns "2025-04-24".
     *
     * @param isoDateTimeString the ISO 8601 formatted date-time string
     * @return the date portion of the string (everything before 'T')
     */
    public static String getDateFromIsoDateTime(String isoDateTimeString) {
        if (isoDateTimeString == null || isoDateTimeString.isEmpty()) {
            return null;
        }

        return isoDateTimeString.split("T")[0];
    }
}