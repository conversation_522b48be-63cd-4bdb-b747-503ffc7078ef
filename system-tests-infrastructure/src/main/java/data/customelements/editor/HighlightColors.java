package data.customelements.editor;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

@Getter
public enum HighlightColors {

    ORANGE_MARKER("Orange marker", "marker-orange", "254, 133, 11"),
    GREEN_MARKER("Green marker", "marker-green", "98, 249, 98"),

    RED_PEN("Red pen", "pen-red", "231, 19, 19"),
    GREEN_PEN("Green pen", "pen-green", "18, 138, 0");

    private final String title;
    private final String classValueInDOM;
    private final String rgbValue;

    HighlightColors(String title, String classValueInDOM, String rgbValue) {
        this.title = title;
        this.classValueInDOM = classValueInDOM;
        this.rgbValue = rgbValue;
    }

    public static HighlightColors getRandomHighlightColor() {
        Random random = new Random();
        ArrayList<HighlightColors> values = new ArrayList<>(Arrays.asList(HighlightColors.values()));
        return values.get(random.nextInt(values.size()));
    }
}