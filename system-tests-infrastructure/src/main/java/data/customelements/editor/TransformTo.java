package data.customelements.editor;

import lombok.Getter;

@Getter
public enum TransformTo {

    PARAGRAPH("Paragraph", "<p>", "</p>"),
    HEADING_1("Heading 1", "<h1>", "</h1>"),
    HEADING_2("Heading 2", "<h2>", "</h2>"),
    HEADING_3("Heading 3", "<h3>", "</h3>"),
    HEADING_4("Heading 4",  "<h4>", "</h4>"),
    QUOTE("Quote", "<blockquote><p>", "</p></blockquote>"),;

    private final String value;
    private final String openTag;
    private final String closingTag;

    TransformTo(String value, String openTag, String closingTag) {
        this.value = value;
        this.openTag = openTag;
        this.closingTag = closingTag;
    }
}