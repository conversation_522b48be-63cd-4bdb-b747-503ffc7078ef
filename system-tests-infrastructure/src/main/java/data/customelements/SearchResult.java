package data.customelements;

import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.TextArea;
import solutions.bellatrix.web.components.WebComponent;

public class SearchResult extends WebComponent {
    private Button selectButton() { return createByXPath(Button.class, ".//button"); } //[@id='suggested-list-btn-select-undefined']
    private TextArea titleTextArea() { return createByXPath(TextArea.class, ".//a"); }
    private Span createdDateSpan() { return createByXPath(Span.class, ".//span[contains(@class, 'date')]"); }

    public  void select() {
        this.selectButton().toBeClickable();
        this.selectButton().click();
    }

    public String getCreatedDate() {
        return this.createdDateSpan().getText();
    }

    public String getText() {
        this.titleTextArea().toBeVisible();
        return this.titleTextArea().getText().substring(0, titleTextArea().getText().indexOf("\n"));
    }
}
