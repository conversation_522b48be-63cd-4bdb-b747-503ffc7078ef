package data.customelements;

import solutions.bellatrix.web.components.contracts.ComponentPlaceholder;

import java.util.List;

public class MultiValueSelect extends DropDownComponent implements ComponentPlaceholder {

    public MultiValueSelect() {
        super();
        this.isClearSupported = true;
        this.isMultiSelectSupported = true;
        this.hasPredefinedOptions = true;
    }

    public void selectOptionsByText(List<String> options) {
        options.forEach(this::selectOptionByText);
    }

    public void searchSelectOptionsByText(List<String> options) {
        options.forEach(this::searchSelectByText);
    }
}