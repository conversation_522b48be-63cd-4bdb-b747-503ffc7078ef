package data.customelements;

import solutions.bellatrix.web.components.Button;

import java.util.List;

public class TournamentDropDownComponent extends SingleValueSearchSelect {

    public TournamentDropDownComponent() {
        super();
        this.isClearSupported = true;
        this.isMultiSelectSupported = false;
        this.hasPredefinedOptions = true;
    }

    @Override
    public List<Button> getOptionButtons() {
        return createAllByXPath(Button.class, "//div[contains(@id,'react-select-') or text()='No options']");
    }
}