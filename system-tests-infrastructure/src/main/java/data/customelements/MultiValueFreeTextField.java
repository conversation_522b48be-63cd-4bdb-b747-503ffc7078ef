package data.customelements;

import org.openqa.selenium.Keys;
import solutions.bellatrix.core.plugins.EventListener;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.ComponentActionEventArgs;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.components.contracts.ComponentList;

import java.util.ArrayList;
import java.util.List;

public class MultiValueFreeTextField extends WebComponent implements ComponentList {
    protected List<Span> fieldContent() { return createAllByXPath(Span.class, ".//parent::span//span"); }
    private Button closeButton(String optionName) { return createByXPath(Button.class, String.format(".//parent::span//span[contains(text(), '%1$s')]//a", optionName)); }
    private List<Button> closeButtons() { return createAllByXPath(Button.class, ".//parent::span//span//a"); }
    public final static EventListener<ComponentActionEventArgs> SETTING_TEXT = new EventListener<>();
    public final static EventListener<ComponentActionEventArgs> TEXT_SET = new EventListener<>();

    /**
     * @param valueName If it is null - deletes all entries
     * */
    public void clearFieldValue(String valueName) {
        if (valueName != null) {
            closeButton(valueName).click();
        } else {
            closeButtons().forEach(e -> e.click());
        }
    }

    public List<String> getFieldValues() {
        List<String> filterValuesList = new ArrayList<>();

        fieldContent().forEach(e -> filterValuesList.add(e.getText()));
        return filterValuesList;
    }

    public void setText(String value) {
        defaultSetText(SETTING_TEXT, TEXT_SET, value);
        defaultSetText(SETTING_TEXT, TEXT_SET, Keys.ENTER.toString());
    }

    @Override
    public String getList() {
        return getFieldValues().toString();
    }
}