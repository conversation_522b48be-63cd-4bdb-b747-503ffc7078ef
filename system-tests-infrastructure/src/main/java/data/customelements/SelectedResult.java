package data.customelements;

import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.TextArea;

public class SelectedResult extends SearchResult {

    public Button removeButton() {
        return createByXPath(Button.class, ".//button[contains(@id, 'content-item-image-delete-btn')]");
    }

    private Button removeVideoButton() {
        return createByXPath(Button.class, ".//button[contains(@id, 'remove-related-video')]");
    }

    private Button addToListButton() {
        return createByXPath(Button.class, ".//button[contains(@id, 'sidebar-list-content-add-item-button')]");
    }

    private Button dragButton() {
        return createByXPath(Button.class, ".//span[@class='cursor-draggable']");
    }

    private TextArea titleTextArea() {
        return createByXPath(TextArea.class, ".//a");
    }

    private TextArea lableTextArea() {
        return createByXPath(TextArea.class, ".//label");
    }

    public void removeVideo() {
        this.removeVideoButton().click();
    }

    public void addToList() {
        this.addToListButton().click();
    }

    public void changePosition() {
        dragButton().hover();
    }

    @Override
    public String getText() {
        var fullString = "";
        var list = createAllByXPath(TextArea.class, this.titleTextArea().getFindStrategy().getValue());

        if (!list.isEmpty()) {
            fullString = this.titleTextArea().getText();
        } else {
            fullString = this.lableTextArea().getText();
        }

        if (fullString.contains("\n")) {
            fullString = fullString.substring(0, fullString.indexOf("\n"));
        }

        return fullString;
    }
}
