package data.customelements;

import data.customelements.editor.TipTapEditor;
import lombok.Getter;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.WebComponent;

import java.util.List;

public class LiveBlogPost extends WebComponent {

    private Div minute() {
        return createByXPath(Div.class, ".//div[contains(@class, 'live-blog-post-minute')]");
    }

    private Div author() {
        return createByXPath(Div.class, ".//div[@class='live-blog-post-author']");
    }

    public TipTapEditor postEditor() {
        return createByXPath(TipTapEditor.class, ".//div[@data-qa='collaborative-editor']");
    }

    //content
    private List<Div> sponsorLogos() {
        return createAllByXPath(Div.class, ".//div[@class='live-blog-post-sponsor-logo']");
    }

    private Div timeStamp() {
        return createByXPath(Div.class, ".//div[@class='live-blog-post-timestamp']");
    }

    private Button menu() {
        return createByXPath(Button.class, ".//div[@class='live-blog-post-menu-button']");
    }

    private Button postActionButton(LiveBlogPostMenuOptions option) {
        return createByXPath(Button.class, "//button[text()='%s']".formatted(option.getValue()));
    }

    public void editPost() {
        menu().click();
        postActionButton(LiveBlogPostMenuOptions.EDIT).click();
        Wait.forMilliseconds(1000);
    }

    public void deletePost() {
        menu().click();
        postActionButton(LiveBlogPostMenuOptions.DELETE).click();
    }

    public void pinPost() {
        menu().click();
        postActionButton(LiveBlogPostMenuOptions.PIN).click();
        browserService.waitForAjax();
    }

    public void highlightPost() {
        menu().click();
        postActionButton(LiveBlogPostMenuOptions.HIGHLIGHT).click();
        browserService.waitForAjax();
    }

    // Validations
    public void validateMinuteDisplayed(String minute) {
        minute().toBeVisible().waitToBe();
        minute().validateIsVisible();
        minute().validateTextIs("%s'".formatted(minute));
    }

    @Getter
    enum LiveBlogPostMenuOptions {
        EDIT("Edit"),
        DELETE("Delete"),
        PIN("Pin"),
        HIGHLIGHT("Highlight");

        private final String value;

        LiveBlogPostMenuOptions(String value) {
            this.value = value;
        }
    }
}