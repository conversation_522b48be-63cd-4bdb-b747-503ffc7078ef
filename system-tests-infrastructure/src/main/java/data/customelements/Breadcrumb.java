package data.customelements;

import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.WebComponent;

import java.util.ArrayList;
import java.util.List;

public class Breadcrumb extends WebComponent {
    private List<Anchor> breadcrumbItems() { return createAllByXPath(Anchor.class, ".//li[contains(@class, 'breadcrumb-item')]"); }
    private Div activeBreadcrumbItem() { return createByXPath(Div.class, ".//li[contains(@class, 'breadcrumb-item active')]"); }

    public List<String> getBreadcrumbsItems() {
        List<String> breadcrumbsTitlesList = new ArrayList<>();

        breadcrumbItems().forEach(e -> {
            breadcrumbsTitlesList.add(e.getText());
        });

        return breadcrumbsTitlesList;
    }

    public String getActiveBreadcrumbsItemText() {
        return activeBreadcrumbItem().getText();
    }

    public void navigateToBreadcrumbItem(String title) {
        breadcrumbItems().stream().filter(e -> e.getText().contains(title)).findFirst().get().click();
    }
}
