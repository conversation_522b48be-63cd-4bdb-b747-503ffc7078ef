package data.customelements;

import data.constants.ContentApiUrl;
import data.constants.SportsSearchApiUrl;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.RadioButton;
import solutions.bellatrix.web.components.TextArea;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.components.contracts.ComponentText;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MatchResult extends WebComponent implements ComponentText {
    private Button selectButton() {
        return createByXPath(Button.class, ".//button");
    }

    private RadioButton inputButton() {
        return createByXPath(RadioButton.class, ".//input");
    }

    private TextArea titleTextArea() {
        return createByXPath(TextArea.class, ".//label");
    }

    public void select() {
        Wait.forMilliseconds(200);
        browserService.waitForAjax();
        List<WebComponent> selectButtonsList = createAllByXPath(WebComponent.class, selectButton().getFindStrategy().getValue());
        if (selectButtonsList.isEmpty()) {
            inputButton().toBeClickable().waitToBe();
            inputButton().getWrappedElement().click();
        } else {
            selectButton().toBeClickable().waitToBe();
            selectButton().click();
        }
    }

    @Override
    public String getText() {
        titleTextArea().toBeVisible().waitToBe();
        return titleTextArea().getText();
    }

    public Map<String, String> getUpcomingMatchData() {
        Map<String, String> matchData = new HashMap<>();
        var matchResultString = getText();

        var firstSeparatorIndex = matchResultString.indexOf("-");
        var secondSeparatorIndex = matchResultString.lastIndexOf("-");
        matchData.put("Date", matchResultString.substring(0, firstSeparatorIndex).trim());
        matchData.put("FirstTeam", matchResultString.substring(firstSeparatorIndex + 1, secondSeparatorIndex).trim());
        matchData.put("SecondTeam", matchResultString.substring(secondSeparatorIndex + 1).trim());

        return matchData;
    }

    public String getUpcomingMatchTeams() {
        var matchResultString = getText();
        var firstSeparatorIndex = matchResultString.indexOf("-");

        return matchResultString.substring(firstSeparatorIndex + 1).trim();
    }

    public Map<String, String> getPastMatchData() {
        Map<String, String> matchData = new HashMap<>();
        Pattern pattern = Pattern.compile("(?<date>\\d+\\s\\w+\\s\\d+)\\s-\\s(?<firstTeam>\\w+)\\s(?<result>\\d:\\d)\\s(?<secondTeam>\\w+)");
        Matcher matcher = pattern.matcher(getText());

        if (matcher.find()) {
            matchData.put("Date", matcher.group("date"));
            matchData.put("Result", matcher.group("result"));
            matchData.put("FirstTeam", matcher.group("firstTeam"));
            matchData.put("SecondTeam", matcher.group("secondTeam"));
        }

        return matchData;
    }
}