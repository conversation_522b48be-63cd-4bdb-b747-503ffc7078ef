package data.customelements;

import solutions.bellatrix.web.components.Button;

public class CompetitionFilterToggleButton extends ToggleButton {

    private Button selectButton() {
        return createByXPath(Button.class, "//div[@id='all-competitions-toggle-input']//span");
    }

    @Override
    public void toggleOn() {
        if (!isToggleOn()) {
            this.selectButton().click();
        }
    }

    @Override
    public void toggleOff() {
        if (isToggleOn()) {
            this.selectButton().click();
        }
    }

    @Override
    public boolean isToggleOn() {
        return this.selectButton().getAttribute("style").contains("toggle-on");
    }

    public boolean isToggleOff() {
        return this.selectButton().getAttribute("style").contains("toggle-off");
    }
}