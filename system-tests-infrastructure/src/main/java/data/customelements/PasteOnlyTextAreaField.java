package data.customelements;

import services.KeyboardShortcutsService;
import solutions.bellatrix.web.components.WebComponent;

public class PasteOnlyTextAreaField extends WebComponent {

    public void pasteTextFromClipboard() {
        PasteOnlyTextAreaField field = createByXPath(PasteOnlyTextAreaField.class, "//textarea[contains(@placeholder, 'Paste text at')]");
        field.toBeVisible().toBeClickable().waitToBe();
        field.getWrappedElement().sendKeys(KeyboardShortcutsService.getModifierKey() + "v");
    }
}