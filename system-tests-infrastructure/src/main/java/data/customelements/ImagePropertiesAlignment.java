package data.customelements;

import data.constants.AssertMessages;
import data.constants.ImageAlignmentEnum;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.components.RadioButton;
import solutions.bellatrix.web.components.WebComponent;

import java.util.List;

public class ImagePropertiesAlignment extends WebComponent {

    private List<RadioButton> imageAlignmentOptions() {
        return createAllByXPath(RadioButton.class, ".//input");
    }

    public ImagePropertiesAlignment selectImageAlignment(ImageAlignmentEnum imageAlignment) {
        imageAlignmentOptions().stream().filter(e -> e.getValue().equals(imageAlignment.getValue()))
                .findFirst()
                .orElseThrow(() -> new AssertionError("Image alignment option not found."))
                .click();
        return this;
    }

    public ImagePropertiesAlignment validateImageAlignmentOptionChecked(ImageAlignmentEnum imageAlignmentEnum) {
        imageAlignmentOptions().stream().filter(e -> e.getValue().equals(imageAlignmentEnum.getValue()))
                .findFirst()
                .orElseThrow(() -> new AssertionError("Image alignment option not found."))
                .validateIsChecked();
        return this;
    }

    public ImagePropertiesAlignment validateOptionsCount(int expectedImageAlignmentOptionsCount) {
        Assertions.assertEquals(expectedImageAlignmentOptionsCount, imageAlignmentOptions().size(),
                AssertMessages.entityCountNotCorrect("Image alignment options"));
        return this;
    }
}
