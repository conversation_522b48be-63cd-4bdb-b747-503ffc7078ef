package data.customelements;

import solutions.bellatrix.web.components.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class LiveBlogTableEntity extends WebComponent {
    private Anchor titleAnchor() {
        return createByXPath(Anchor.class, ".//td[contains(@class,'title')]//a");
    }

    private Span titleBadgeSpan() {
        return createByXPath(Span.class, ".//td[contains(@class,'title')]//span[1]");
    }

    private Span titleSpan() {
        return createByXPath(Span.class, ".//td[contains(@class,'title')]//span[2]");
    }

    private Div statusDiv() {
        return createByXPath(Div.class, ".//td[2]");
    }

    private Div startTimeDiv() {
        return createByXPath(Div.class, ".//td[3]");
    }

    private Button editButton() {
        return createByXPath(Button.class, ".//a[contains(@href,'#/smp/live-blogs/configuration/edit/')]//button");
    }

    private Button arrowDropDownButton() {
        return createByXPath(Button.class, ".//a[contains(@id,'toggle-menu-caret')]");
    }

    private Button deleteButton() {
        return createByXPath(Button.class, ".//div[contains(@class,'dropdown-menu')]//button");
    }

    public String getBadgeStatus() {
        var elementClass = titleBadgeSpan().getHtmlClass();
        var status = "";

        switch (elementClass) {
            case "badge-success":
                status = "Success";
                break;
        }

        return status;
    }

    public String getTitle() {
        return titleSpan().getText();
    }

    public String getStatus() {
        return statusDiv().getText();
    }

    public String getStartTimeAsString() {
        return startTimeDiv().getText();
    }

    public LocalDateTime getStartTimeAsLocalDateTime() {
        var stringDate = getStartTimeAsString();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM yyyy, HH:mm", Locale.ENGLISH);

        return LocalDateTime.parse(stringDate, formatter);
    }

    public void openLiveBlogEditorial() {
        titleAnchor().click();
    }

    public void openEditScreen() {
        editButton().click();
    }

    public void delete() {
        javaScriptService.execute("document.querySelector('[id*=toggle-menu-caret]').click()");
        deleteButton().toBeVisible().toBeClickable().waitToBe();
        deleteButton().click();
    }
}