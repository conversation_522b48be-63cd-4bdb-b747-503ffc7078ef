package data.customelements;

import org.openqa.selenium.interactions.Actions;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Image;
import solutions.bellatrix.web.components.WebComponent;

import java.time.Duration;

public class ImageCropPopUp extends WebComponent {

    public SingleValueSelect imageRatioPopUpSelect() {
        return createByXPath(SingleValueSelect.class, ".//div[contains(@class,'control')]");
    }

    public Image imageCropContainer() {
        return createByXPath(Image.class, ".//img[@id='image-crop-container']");
    }

    public Button imageDownRightCropButton() {
        return createByXPath(Button.class, ".//span[@class='cropper-point point-se']");
    }

    public Button imageDownMiddleCropButton() {
        return createByXPath(Button.class, ".//span[@class='cropper-point point-s']");
    }

    public Button saveButton() {
        return createByXPath(Button.class, ".//button[text()='Save']");
    }

    public void moveImageCropContainer() {
        var xOffset = -10;
        var yOffset = -30;
        imageDownRightCropButton().toBeVisible().waitToBe();
        var currentOffset = imageDownRightCropButton().getLocation();
        Actions actions = new Actions(getWrappedDriver());
        actions.dragAndDropBy(imageDownRightCropButton().getWrappedElement(), xOffset, yOffset).pause(Duration.ofSeconds(1)).build().perform();
        var changedOffset = imageDownRightCropButton().getLocation();

        getBrowserService().waitUntil(e -> currentOffset != changedOffset);
    }
}