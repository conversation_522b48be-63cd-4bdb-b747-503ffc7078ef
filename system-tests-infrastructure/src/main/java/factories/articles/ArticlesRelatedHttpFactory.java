package factories.articles;

import data.constants.EntityTypeEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.VenueEnum;
import data.models.articles.ArticleResponseModel;
import data.models.articles.tennisblocky.DisplayAsset;
import data.models.blockymodels.football.FootballTopScorersBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.v2.MatchV2Model;
import data.models.galleries.GalleryResponseModel;
import data.models.related.*;
import data.models.searchapi.ResultModel;
import data.models.tags.TagResponseModel;
import data.models.videos.VideoResponseModel;
import factories.EntityFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class ArticlesRelatedHttpFactory extends EntityFactory {

    public static RelatedModel buildPlayerRelatedModel(FootballTopScorersBlockyFieldsModel footballTournament) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildPlayerDataListObject(footballTournament))))
                .build();
    }

    public static RelatedModel buildPlayerRelatedModel(ResultModel footballTournament) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildPlayerDataListObject(footballTournament))))
                .build();
    }

    public static RelatedModel buildTeamRelatedModel(MatchV2Model matchModel) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildTeamDataListObject(matchModel))))
                .build();
    }

    public static RelatedModel buildTeamRelatedModel(ResultModel team) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildTeamDataListObject(team))))
                .build();
    }

    public static RelatedModel buildCoachRelatedModel(ResultModel coach) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildCoachDataListObject(coach))))
                .build();
    }

    public RelatedModel buildTournamentRelatedModel(MatchV2Model matchModel) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildTournamentDataListObject(matchModel))))
                .build();
    }

    public static RelatedModel buildTournamentRelatedModel(ResultModel tournament) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildTournamentDataListObject(tournament))))
                .build();
    }

    public static RelatedModel buildVenueRelatedModel() {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildVenueDataListObject())))
                .build();
    }

    public static RelatedModel buildVenueRelatedModel(ResultModel venue) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildVenueDataListObject(venue))))
                .build();
    }

    public static RelatedModel buildArenaRelatedModel(ResultModel arena) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildArenaDataListObject(arena))))
                .build();
    }

    public RelatedModel buildFootballMatchRelatedModel(MatchV2Model matchModel) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildFootballMatchDataListObject(matchModel))))
                .build();
    }

    public RelatedModel buildArticleRelatedModel(ArticleResponseModel article) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildArticleDataListObject(article))))
                .build();
    }

    public RelatedModel buildVideoRelatedModel(VideoResponseModel video) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildVideoDataListObject(video))))
                .build();
    }

    public RelatedModel buildGalleryRelatedModel(GalleryResponseModel gallery) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildGalleryDataListObject(gallery))))
                .build();
    }

    public RelatedModel buildTagRelatedModel(TagResponseModel tag) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildTagDataListObject(tag))))
                .build();
    }

    public RelatedModel buildFootballSeasonRelatedModel(MatchV2Model matchModel) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Arrays.asList(buildSeasonDataListObject(matchModel))))
                .build();
    }

    //Custom entities
    public static RelatedModel buildPersonRelatedModel(ResultModel person) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Collections.singletonList(buildPersonDataListObject(person))))
                .build();
    }

    public static RelatedModel buildOrganizationRelatedModel(ResultModel organization) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Collections.singletonList(buildOrganizationDataListObject(organization))))
                .build();
    }

    public static RelatedModel buildPlaceRelatedModel(ResultModel place) {
        return RelatedModel.builder()
                .data(new ArrayList<>(Collections.singletonList(buildPlaceDataListObject(place))))
                .build();
    }

    public RelatedModel buildEmptyModel() {
        return RelatedModel.builder().build();
    }

    private static DataListObject buildPlayerDataListObject(FootballTopScorersBlockyFieldsModel footballTournament) {
        return DataListObject.builder()
                .type(StringConstants.PLAYER_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildPlayerDataObject(footballTournament))
                .build();
    }

    private static DataListObject buildPlayerDataListObject(ResultModel player) {
        return DataListObject.builder()
                .type(StringConstants.PLAYER_STRING)
                .provider(getProvider(player.getSport()))
                .data(buildPlayerDataObject(player))
                .build();
    }

    private static DataListObject buildTeamDataListObject(MatchV2Model matchModel) {
        return DataListObject.builder()
                .type(StringConstants.TEAM_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildTeamDataObject(matchModel))
                .build();
    }

    private static DataListObject buildTeamDataListObject(ResultModel team) {
        return DataListObject.builder()
                .type(StringConstants.TEAM_STRING)
                .provider(getProvider(team.getSport()))
                .data(buildTeamDataObject(team))
                .build();
    }

    private DataListObject buildFootballMatchDataListObject(MatchV2Model matchModel) {
        return DataListObject.builder()
                .type(StringConstants.MATCH_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildMatchDataObject(matchModel))
                .build();
    }

    private static DataListObject buildVenueDataListObject() {
        return DataListObject.builder()
                .type(StringConstants.VENUE_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildVenueDataObject())
                .build();
    }

    private static DataListObject buildVenueDataListObject(ResultModel venue) {
        return DataListObject.builder()
                .type(EntityTypeEnum.VENUE.getValue())
                .provider(getProvider(venue.getSport()))
                .data(buildVenueDataObject(venue))
                .build();
    }

    private static DataListObject buildArenaDataListObject(ResultModel arena) {
        return DataListObject.builder()
                .provider(getProvider(arena.getSport()))
                .type(EntityTypeEnum.ARENA.getValue())
                .data(buildArenaDataObject(arena))
                .build();
    }

    private DataListObject buildArticleDataListObject(ArticleResponseModel article) {
        return DataListObject.builder()
                .type(StringConstants.ARTICLE_STRING)
                .provider(StringConstants.SMP_STRING)
                .dataId(article.getId())
                .build();
    }

    private DataListObject buildGalleryDataListObject(GalleryResponseModel getGallery) {
        return DataListObject.builder()
                .type(StringConstants.GALLERY_STRING)
                .provider(StringConstants.SMP_STRING)
                .dataId(getGallery.getId())
                .build();
    }

    private DataListObject buildVideoDataListObject(VideoResponseModel video) {
        return DataListObject.builder()
                .type(StringConstants.VIDEO_STRING)
                .provider(StringConstants.SMP_STRING)
                .dataId(video.getId())
                .build();
    }

    private DataListObject buildTagDataListObject(TagResponseModel tag) {
        return DataListObject.builder()
                .type(StringConstants.TAG_STRING)
                .provider(StringConstants.SMP_STRING)
                .dataId(tag.getId())
                .build();
    }

    private DataListObject buildSeasonDataListObject(MatchV2Model matchModel) {
        return DataListObject.builder()
                .type(StringConstants.SEASON_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildSeasonDaTaListObject(matchModel))
                .build();
    }

    private DataListObject buildTournamentDataListObject(MatchV2Model matchModel) {
        return DataListObject.builder()
                .type(StringConstants.TOURNAMENT_STRING)
                .provider(StringConstants.FOOTBALL_API_STRING)
                .data(buildTournamentDataObject(matchModel))
                .build();
    }

    private static DataListObject buildTournamentDataListObject(ResultModel tournament) {
        return DataListObject.builder()
                .type(StringConstants.TOURNAMENT_STRING)
                .provider(getProvider(tournament.getSport()))
                .data(buildTournamentDataObject(tournament))
                .build();
    }

    private static DataListObject buildCoachDataListObject(ResultModel coach) {
        return DataListObject.builder()
                .type(StringConstants.COACH_STRING)
                .provider(getProvider(coach.getSport()))
                .data(buildCoachDataObject(coach))
                .build();
    }

    //Custom entities
    private static DataListObject buildPersonDataListObject(ResultModel person) {
        return DataListObject.builder()
                .type(person.getEntityType())
                .provider(getProvider(person.getSport()))
                .data(buildPersonDataObject(person))
                .build();
    }

    private static DataListObject buildOrganizationDataListObject(ResultModel organization) {
        return DataListObject.builder()
                .type(organization.getEntityType())
                .provider(getProvider(organization.getSport()))
                .data(buildOrganizationDataObject(organization))
                .build();
    }

    private static DataListObject buildPlaceDataListObject(ResultModel place) {
        return DataListObject.builder()
                .type(place.getEntityType())
                .provider(getProvider(place.getSport()))
                .data(buildPlaceDataObject(place))
                .build();
    }

    private static Data buildPlayerDataObject(FootballTopScorersBlockyFieldsModel footballPlayer) {
        return Data.builder()
                .entityType(footballPlayer.getPlayerToHighLight().getPlayer().getEntityType())
                .id(footballPlayer.getPlayerToHighLight().getPlayer().getId())
                .name(footballPlayer.getPlayerToHighLight().getPlayer().getName())
                .gender(footballPlayer.getPlayerToHighLight().getPlayer().getGender())
                .birthdate(footballPlayer.getPlayerToHighLight().getPlayer().getBirthdate())
                .position(footballPlayer.getPlayerToHighLight().getPlayer().getPosition())
                .slug(footballPlayer.getPlayerToHighLight().getPlayer().getSlug())
                .uuid(footballPlayer.getPlayerToHighLight().getPlayer().getUuid())
                .urlImage(footballPlayer.getPlayerToHighLight().getPlayer().getUrlImage())
                .urlThumb(footballPlayer.getPlayerToHighLight().getPlayer().getUrlThumb())
                .build();
    }

    private static Data buildPlayerDataObject(ResultModel footballPlayer) {
        Data.DataBuilder dataBuilder = Data.builder()
                .entityType(footballPlayer.getEntityType())
                .name(footballPlayer.getName())
                .gender(footballPlayer.getGender())
                .birthdate(footballPlayer.getBirthdate())
                .position(footballPlayer.getPosition())
                .slug(footballPlayer.getSlug());

        setLegacyIdForFootballEntity(footballPlayer, dataBuilder);
        return dataBuilder.build();
    }

    private Data buildMatchDataObject(MatchV2Model matchModel) {
        return Data.builder()
                .entityType(StringConstants.MATCH_STRING)
                .id(Long.valueOf(matchModel.getId()))
                .originIds(List.of())
                .homeTeam(TeamModel.builder()
                        .id(matchModel.getHomeTeam().getId())
                        .name(matchModel.getHomeTeam().getName())
                        .slug(matchModel.getHomeTeam().getSlug())
                        .type(matchModel.getHomeTeam().getType())
                        .threeLetterCode(matchModel.getHomeTeam().getThreeLetterCode())
                        .urlLogo(matchModel.getHomeTeam().getUrlLogo())
                        .uuid(matchModel.getHomeTeam().getUuid())
                        .build())
                .awayTeam(TeamModel.builder()
                        .id(matchModel.getAwayTeam().getId())
                        .name(matchModel.getAwayTeam().getName())
                        .slug(matchModel.getAwayTeam().getSlug())
                        .type(matchModel.getAwayTeam().getType())
                        .threeLetterCode(matchModel.getAwayTeam().getThreeLetterCode())
                        .urlLogo(matchModel.getAwayTeam().getUrlLogo())
                        .uuid(matchModel.getAwayTeam().getUuid())
                        .build())
                .slug(matchModel.getSlug())
                .build();
    }

    private static Data buildVenueDataObject() {
        return Data.builder()
                .entityType(StringConstants.VENUE_STRING)
                .id(Long.valueOf(VenueEnum.OROGEL_STADIUM_DINO_MANUZZI.getId()))
                .name(VenueEnum.OROGEL_STADIUM_DINO_MANUZZI.getName())
                .originIds(new ArrayList<>())
                .slug(VenueEnum.OROGEL_STADIUM_DINO_MANUZZI.getSlug())
                .urlImage(StringConstants.EMPTY_STRING)
                .build();
    }

    private static Data buildVenueDataObject(ResultModel venue) {
        Data.DataBuilder dataBuilder = Data.builder()
                .entityType(venue.getEntityType())
                .name(venue.getName())
                .originIds(new ArrayList<>())
                .slug(venue.getSlug())
                .urlImage(StringConstants.EMPTY_STRING);

        setLegacyIdForFootballEntity(venue, dataBuilder);
        return dataBuilder.build();
    }

    private static Data buildArenaDataObject(ResultModel arena) {
        return Data.builder()
                .id(arena.getId())
                .name(arena.getName())
                .sport(arena.getSport())
                .countryId(arena.getCountryId())
                .slug(arena.getSlug())
                .build();
    }

    private Data buildTournamentDataObject(MatchV2Model matchModel) {
        return Data.builder()
                .entityType(matchModel.getSeason().getTournament().getEntityType())
                .id(Long.valueOf(matchModel.getSeason().getTournament().getId()))
                .name(matchModel.getSeason().getTournament().getName())
                .originIds(List.of())
                .slug(matchModel.getSeason().getTournament().getSlug())
                .urlLogo(matchModel.getSeason().getTournament().getUrlLogo())
                .build();
    }

    private static Data buildTournamentDataObject(ResultModel tournament) {
        Data.DataBuilder dataBuilder = Data.builder()
                .entityType(tournament.getEntityType())
                .name(tournament.getName())
                .originIds(List.of())
                .slug(tournament.getSlug())
                .urlLogo(tournament.getDisplayAsset().getUrl());

        setLegacyIdForFootballEntity(tournament, dataBuilder);
        return dataBuilder.build();
    }

    private static Data buildCoachDataObject(ResultModel coach) {
        Data.DataBuilder dataBuilder = Data.builder()
                .entityType(coach.getEntityType())
                .name(coach.getName())
                .originIds(List.of())
                .slug(coach.getSlug())
                .urlLogo(coach.getUrlLogo());

        setLegacyIdForFootballEntity(coach, dataBuilder);
        return dataBuilder.build();
    }

    private static Data buildTeamDataObject(MatchV2Model matchModel) {
        return Data.builder()
                .entityType(matchModel.getHomeTeam().getEntityType())
                .id(Long.valueOf(matchModel.getHomeTeam().getId()))
                .name(matchModel.getHomeTeam().getName())
                .gender(matchModel.getHomeTeam().getGender())
                .uuid(matchModel.getHomeTeam().getUuid())
                .slug(matchModel.getHomeTeam().getSlug())
                .urlLogo(matchModel.getHomeTeam().getUrlLogo())
                .build();
    }

    private static Data buildTeamDataObject(ResultModel team) {
        Data.DataBuilder dataBuilder = Data.builder()
                .entityType(team.getEntityType())
                .name(team.getName())
                .gender(team.getGender())
                .slug(team.getSlug())
                .urlLogo(team.getUrlLogo());

        setLegacyIdForFootballEntity(team, dataBuilder);
        return dataBuilder.build();
    }

    private Data buildSeasonDaTaListObject(MatchV2Model matchModel) {
        return Data.builder()
                .id(Long.valueOf(matchModel.getSeason().getId()))
                .name(matchModel.getSeason().getName())
                .slug(matchModel.getSeason().getSlug())
                .tournament(Tournament.builder()
                        .country(Country.builder()
                                .id((long)matchModel.getSeason().getTournament().getId())
                                .name(matchModel.getSeason().getTournament().getName())
                                .slug(matchModel.getSeason().getTournament().getSlug())
                                .urlFlag(matchModel.getSeason().getTournament().getUrlLogo())
                                .translations(List.of())
                                .displayAsset(DisplayAsset.builder()
                                        .url(StringConstants.EMPTY_STRING)
                                        .build())
                                .assets(null)
                                .build())
                        .build())
                .build();
    }

    // Custom entities
    private static Data buildPersonDataObject(ResultModel person) {
        return Data.builder()
                .name(person.getName())
                .shortName(person.getShortName())
                .threeLetterCode(person.getThreeLetterCode())
                .id(person.getId())
                .entityType(person.getEntityType())
                .slug(person.getSlug())
                .build();
    }

    private static Data buildOrganizationDataObject(ResultModel organization) {
        return Data.builder()
                .name(organization.getName())
                .shortName(organization.getShortName())
                .threeLetterCode(organization.getThreeLetterCode())
                .id(organization.getId())
                .entityType(organization.getEntityType())
                .slug(organization.getSlug())
                .build();
    }

    private static Data buildPlaceDataObject(ResultModel place) {
        return Data.builder()
                .name(place.getName())
                .shortName(place.getShortName())
                .threeLetterCode(place.getThreeLetterCode())
                .id(place.getId())
                .entityType(place.getEntityType())
                .slug(place.getSlug())
                .build();
    }

    private static String getProvider(String sport) {
        return (sport != null && sport.equalsIgnoreCase(SupportedSports.FOOTBALL.getValue()))
                ? StringConstants.FOOTBALL_API_STRING
                : StringConstants.SPORTS_SEARCH_API_STRING;
    }

    private static void setLegacyIdForFootballEntity(ResultModel entity, Data.DataBuilder dataBuilder) {
        if (entity.getSport().equalsIgnoreCase(SupportedSports.FOOTBALL.getValue())) {
            dataBuilder.id(Long.valueOf(entity.getLegacyId()));
            dataBuilder.uuid(entity.getId());
        } else {
            dataBuilder.id(entity.getId());
        }
    }
}