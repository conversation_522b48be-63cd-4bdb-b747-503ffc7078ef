package factories.articles;

import data.models.uimodels.ArticleFormModel;
import factories.EntityFactory;

import java.time.LocalDateTime;
import java.util.Arrays;

public class ArticleFieldsFactory extends EntityFactory {

    public static ArticleFormModel buildArticleRequiredFields() {
        return ArticleFormModel
                .builder()
                .title("AutoArticle %s".formatted(LocalDateTime.now().getNano()))
                .mainCategory(getFaker().options().nextElement(Arrays.asList("Football", "UEFA", "CONMEBOL", "CAF", "AFC")))
                .build();
    }
}