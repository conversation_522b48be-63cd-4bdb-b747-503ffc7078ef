package factories.documents;

import data.models.collaborationapi.subdocument.Meta;
import data.models.collaborationapi.subdocument.SubDocumentModel;
import data.models.collaborationapi.subdocument.UpdatedBy;
import factories.EntityFactory;

public class SubDocumentsFactory extends EntityFactory {
    public SubDocumentModel buildPostSubDocumentBodyModel(String subDocumentId, Meta meta) {
        return SubDocumentModel
                .builder()
                .id(subDocumentId)
                .meta(meta)
                .build();
    }

    public SubDocumentModel buildUpdateSubDocumentBodyModel(Meta meta) {
        return SubDocumentModel
                .builder()
                .meta(meta)
                .build();
    }

    public SubDocumentModel buildDeleteSubDocumentBodyModel(String adminId, String name) {
        return SubDocumentModel
                .builder()
                .adminId(adminId)
                .name(name)
                .build();
    }

    public Meta buildMetaModel(UpdatedBy updatedBy) {
        return Meta
                .builder()
                .updatedBy(updatedBy)
                .build();
    }

    public UpdatedBy buildUpdatedByModel(String adminId, String name) {
        return UpdatedBy
                .builder()
                .id(adminId)
                .name(name)
                .build();
    }
}
