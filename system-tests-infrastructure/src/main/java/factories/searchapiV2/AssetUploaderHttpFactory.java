package factories.searchapiV2;

import data.constants.StringConstants;
import data.models.searchapi.AssetUploaderModel;
import data.utils.ImageUtils;
import factories.EntityFactory;

public class AssetUploaderHttpFactory extends EntityFactory {

    private AssetUploaderHttpFactory() {
    }

    public static AssetUploaderModel buildImage() {
        return AssetUploaderModel.builder()
                .file(ImageUtils.getTestImagePath(StringConstants.TEST_IMAGE_PNG))
                .build();
    }

    public static AssetUploaderModel buildImageBigSize() {
        return AssetUploaderModel.builder()
                .file(ImageUtils.getTestImagePath(StringConstants.TEST_IMAGE_BIG_SIZE_JPG))
                .build();
    }

    public static AssetUploaderModel buildImagePdfFormat() {
        return AssetUploaderModel.builder()
                .file(ImageUtils.getTestImagePath(StringConstants.TEST_IMAGE_PDF))
                .build();
    }
}
