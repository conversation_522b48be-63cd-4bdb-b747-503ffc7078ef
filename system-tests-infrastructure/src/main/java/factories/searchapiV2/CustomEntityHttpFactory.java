package factories.searchapiV2;

import data.constants.Language;
import data.constants.SocialMediaEnum;
import data.constants.enums.CustomEntityEnum;
import data.models.searchapi.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class CustomEntityHttpFactory extends SearchEntityHttpFactory {

    private CustomEntityHttpFactory() {
    }

    public static CustomEntityModel buildEmptyRequestBody() {
        return CustomEntityModel.builder().build();
    }

    public static CustomEntityModel buildRoleMandatoryPropertiesRequestBody() {
        int number = LocalDateTime.now().getNano();

        return CustomEntityModel.builder()
                .name("Automation QA Role %s".formatted(number))
                .defaultLanguage(Language.ENGLISH.getCode())
                .slug("automation-qa-role-%s".formatted(number))
                .build();
    }

    public static CustomEntityModel buildRoleAllPropertiesRequestBody() {
        CustomEntityModel role = buildRoleMandatoryPropertiesRequestBody();
        role.setShortName("Auto%s".formatted(CustomEntityEnum.ROLE.getValue()));
        role.setThreeLetterCode("AUR");
        role.setTranslations(buildTranslationsFor(CustomEntityEnum.ROLE));
        return role;
    }

    public static CustomEntityModel buildRoleUpdateRequestBodyMandatoryProperties(CustomEntityModel createdRole) {
        return CustomEntityModel.builder()
                .name(createdRole.getName())
                .defaultLanguage(createdRole.getDefaultLanguage())
                .slug(createdRole.getSlug())
                .id(createdRole.getId())
                .build();
    }

    public static CustomEntityModel buildPersonMandatoryPropertiesRequestBody(String domainId) {
        return buildCustomEntityRequestBodyMandatoryProperties(domainId, CustomEntityEnum.PERSON);
    }

    public static CustomEntityModel buildPersonAllPropertiesRequestBody(String domainId, CustomEntityModel place,
                                                                        CustomEntityModel organization, CustomEntityModel role) {
        String minAllowedHeight = "0.5";
        String maxAllowedHeight = "3.00";
        String minAllowedWeight = "20";
        String maxAllowedWeight = "1000";

        CustomEntityModel person = buildPersonMandatoryPropertiesRequestBody(domainId);
        person.setShortName("Auto%s".formatted(CustomEntityEnum.PERSON.getValue()));
        person.setThreeLetterCode("AUP");
        person.setTranslations(buildTranslationsFor(CustomEntityEnum.PERSON));
        person.setDisplayAsset(DisplayAssetPropertyFactory.buildDisplayAsset());
        person.setIcon(IconModel.builder().url("https://mycustomtesticon.org").build());
        person.setBirthdate("1990-01-01");
        person.setGender("FEMALE");
        person.setHeight(new String[]{minAllowedHeight, maxAllowedHeight}[new Random().nextInt(2)]);
        person.setWeight(new String[]{minAllowedWeight, maxAllowedWeight}[new Random().nextInt(2)]);
        person.setWebsite("https://mycustomtestwebsite.org");
        person.setEmail("<EMAIL>");
        person.setNationality(place.getId());
        person.setBirthPlace(place.getId());
        person.setSocialMediaLinks(List.of(
                SocialMediaLinksModel.builder()
                        .slug(SocialMediaEnum.FACEBOOK.name())
                        .value("https://facebook.com/")
                        .build()
        ));
        person.setRoles(List.of(
                Roles.builder()
                        .role(role.getId())
                        .organization(organization.getId())
                        .place(place.getId())
                        .build()
        ));
        return person;
    }

    public static CustomEntityModel buildPersonUpdateRequestBodyMandatoryProperties(CustomEntityModel createdPerson) {
        return CustomEntityModel.builder()
                .name(createdPerson.getName())
                .defaultLanguage(createdPerson.getDefaultLanguage())
                .slug(createdPerson.getSlug())
                .id(createdPerson.getId())
                .containedInDomain(((ContainedInDomainModel)createdPerson.getContainedInDomain()).getId())
                .build();
    }

    public static CustomEntityModel buildOrganizationMandatoryPropertiesRequestBody(String domainId) {
        return buildCustomEntityRequestBodyMandatoryProperties(domainId, CustomEntityEnum.ORGANIZATION);
    }

    public static CustomEntityModel buildOrganizationAllPropertiesRequestBody(String domainId, CustomEntityModel place, CustomEntityModel organization) {
        CustomEntityModel org = buildOrganizationMandatoryPropertiesRequestBody(domainId);
        org.setShortName("Auto%s".formatted(CustomEntityEnum.ORGANIZATION.getValue()));
        org.setThreeLetterCode("AUO");
        org.setTranslations(buildTranslationsFor(CustomEntityEnum.ORGANIZATION));
        org.setAddress("Auto Street 13");
        org.setWebsite("https://mycustomtestwebsite.org");
        org.setEmail("<EMAIL>");
        org.setTelephone(TelephoneModel.builder()
                .countryCode("+359")
                .number("888123456")
                .countryIsoCode(Language.BULGARIAN.getCode().toUpperCase())
                .build());
        org.setDisplayAsset(DisplayAssetPropertyFactory.buildDisplayAsset());
        org.setIcon(IconModel.builder().url("https://mycustomtesticon.org").build());
        org.setFoundingDate("1990-01-01");
        org.setFoundingYear("1990");
        org.setSocialMediaLinks(List.of(
                SocialMediaLinksModel.builder()
                        .slug(SocialMediaEnum.FACEBOOK.name())
                        .value("https://facebook.com/")
                        .build()
        ));
        org.setContainedInPlace(place.getId());
        org.setContainedInOrganization(organization.getId());
        return org;
    }

    public static CustomEntityModel buildOrganizationUpdateRequestBodyMandatoryProperties(CustomEntityModel createdOrganization) {
        return CustomEntityModel.builder()
                .name(createdOrganization.getName())
                .defaultLanguage(createdOrganization.getDefaultLanguage())
                .slug(createdOrganization.getSlug())
                .containedInDomain(((ContainedInDomainModel)createdOrganization.getContainedInDomain()).getId())
                .id(createdOrganization.getId())
                .build();
    }

    public static CustomEntityModel buildDomainMandatoryPropertiesRequestBody() {
        return buildCustomEntityRequestBodyMandatoryProperties(null, CustomEntityEnum.DOMAIN);
    }

    public static CustomEntityModel buildDomainAllPropertiesRequestBody() {
        CustomEntityModel domain = buildDomainMandatoryPropertiesRequestBody();
        domain.setShortName("Auto%s".formatted(CustomEntityEnum.DOMAIN.getValue()));
        domain.setThreeLetterCode("AUD");
        domain.setTranslations(buildTranslationsFor(CustomEntityEnum.DOMAIN));
        domain.setDisplayAsset(DisplayAssetPropertyFactory.buildDisplayAsset());
        domain.setIcon(IconModel.builder().url("https://mycustomtesticon.org").build());
        return domain;
    }

    public static CustomEntityModel buildDomainUpdateRequestBodyMandatoryProperties(CustomEntityModel createdDomain) {
        return CustomEntityModel.builder()
                .name(createdDomain.getName())
                .defaultLanguage(createdDomain.getDefaultLanguage())
                .slug(createdDomain.getSlug())
                .id(createdDomain.getId())
                .build();
    }

    public static CustomEntityModel buildPlaceMandatoryPropertiesRequestBody(String domainId) {
        return buildCustomEntityRequestBodyMandatoryProperties(domainId, CustomEntityEnum.PLACE);
    }

    public static CustomEntityModel buildPlaceAllPropertiesRequestBody(String domainId, String placeId) {
        CustomEntityModel place = buildPlaceMandatoryPropertiesRequestBody(domainId);
        place.setShortName("Auto%s".formatted(CustomEntityEnum.PLACE.getValue()));
        place.setThreeLetterCode("AUP");
        place.setTranslations(buildTranslationsFor(CustomEntityEnum.PLACE));
        place.setAddress("Auto Street 13");
        place.setGeo(buildPlaceGeoProperties("42.6977", "23.3219"));
        place.setDisplayAsset(DisplayAssetPropertyFactory.buildDisplayAsset());
        place.setIcon(IconModel.builder().url("https://mycustomtesticon.org").build());
        place.setWebsite("https://mycustomtestwebsite.org");
        place.setTelephone(TelephoneModel.builder()
                .countryCode("+359")
                .number("888123456")
                .countryIsoCode("BG")
                .build());
        place.setSocialMediaLinks(List.of(
                SocialMediaLinksModel.builder()
                        .slug(SocialMediaEnum.FACEBOOK.name())
                        .value("https://facebook.com/")
                        .build()
        ));
        place.setContainedInPlace(placeId);
        return place;
    }

    public static CustomEntityModel buildPlaceUpdateRequestBodyMandatoryProperties(CustomEntityModel createdPlace) {
        return CustomEntityModel.builder()
                .name(createdPlace.getName())
                .defaultLanguage(createdPlace.getDefaultLanguage())
                .slug(createdPlace.getSlug())
                .id(createdPlace.getId())
                .containedInDomain(((ContainedInDomainModel)createdPlace.getContainedInDomain()).getId())
                .build();
    }

    public static GeoModel buildPlaceGeoProperties(String latitude, String longitude) {
        return GeoModel.builder()
                .latitude(latitude)
                .longitude(longitude)
                .build();
    }

    public static DomainsOrderModel buildDomainsOrderRequestBody(String... domainSlugs) {
        return DomainsOrderModel.builder()
                .order(Arrays.asList(domainSlugs))
                .build();
    }

    private static CustomEntityModel buildCustomEntityRequestBodyMandatoryProperties(String domainId, CustomEntityEnum customEntity) {
        int number = LocalDateTime.now().getNano();
        String entityValue = customEntity.getValue();

        return CustomEntityModel.builder()
                .name("Automation %s %s".formatted(entityValue, number))
                .defaultLanguage(Language.ENGLISH.getCode())
                .slug("automation-%s-%s".formatted(entityValue.toLowerCase(), number))
                .containedInDomain(domainId)
                .build();
    }
}
