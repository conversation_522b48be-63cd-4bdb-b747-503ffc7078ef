package factories.searchapiV2;

import data.constants.Language;
import data.constants.StatusEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.SportEntityEnum;
import data.constants.enums.football.FootballTeamType;
import data.models.multisportapi.TranslationModel;
import data.models.searchapi.*;
import data.models.tennisapi.TypeModel;
import data.utils.NumberUtils;
import data.widgets.options.enums.DataTeamEnum;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static factories.searchapiV2.DisplayAssetPropertyFactory.buildDisplayAsset;

public class SportEntityHttpFactory extends SearchEntityHttpFactory {

    private SportEntityHttpFactory() {

    }

    public static SportEntityModel buildEmptyRequestBody() {
        return SportEntityModel.builder().build();
    }

    public static SportEntityModel buildVenueMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.VENUE);
    }

    public static SportEntityModel buildUpdateSportEntityRequestBodyMandatoryProperties(SportEntityModel sportEntity) {
        SportEntityModel entity = SportEntityModel.builder()
                .id(sportEntity.getId())
                .name(sportEntity.getName())
                .sport(sportEntity.getSport())
                .build();

        if (sportEntity.getCompetitionId() != null) {
            entity.setCompetitionId(sportEntity.getCompetitionId());
        }

        return entity;
    }

    public static SportEntityModel buildTeamMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.TEAM);
    }

    public static SportEntityModel buildRefereeMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.REFEREE);
    }

    public static SportEntityModel buildPlayerMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.PLAYER);
    }

    public static SportEntityModel buildHorseMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.HORSE);
    }

    public static SportEntityModel buildCompetitionMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.COMPETITION);
    }

    public static SportEntityModel buildCoachMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.COACH);
    }

    public static SportEntityModel buildEventMandatoryProperties(SupportedSports sport) {
        return buildMandatoryProperties(sport, SportEntityEnum.EVENT);
    }

    public static SportEntityModel buildVenueAllProperties(SupportedSports sport) {
        SportEntityModel venue = buildMandatoryProperties(sport, SportEntityEnum.VENUE);
        int number = LocalDateTime.now().getNano();

        venue.setShortName("Auto%s%s".formatted(SportEntityEnum.VENUE.getValue(), number));
        venue.setThreeLetterCode("AUV");
        venue.setTranslations(buildTranslationsFor(SportEntityEnum.VENUE));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            venue.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        venue.setDisplayAsset(buildDisplayAsset());
        venue.setCountry(buildCountryModel());
        venue.setCity("AutomationCity");
        venue.setTeamIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        venue.setCompetitionIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        return venue;
    }

    public static SportEntityModel buildTeamAllProperties(SupportedSports sport) {
        SportEntityModel team = buildMandatoryProperties(sport, SportEntityEnum.TEAM);
        int number = LocalDateTime.now().getNano();

        team.setShortName("Auto%s%s".formatted(SportEntityEnum.TEAM.getValue(), number));
        team.setThreeLetterCode("AUT");
        team.setTranslations(buildTranslationsFor(SportEntityEnum.TEAM));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            team.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        team.setDisplayAsset(buildDisplayAsset());
        team.setCountry(buildCountryModel());
        team.setGender("OTHER");
        team.setCompetitionIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            team.setType(FootballTeamType.CLUB.name());
        } else {
            team.setType("TYPE");
        }
        return team;
    }

    public static SportEntityModel buildRefereeAllProperties(SupportedSports sport) {
        SportEntityModel referee = buildMandatoryProperties(sport, SportEntityEnum.REFEREE);
        int number = LocalDateTime.now().getNano();

        referee.setShortName("Auto%s%s".formatted(SportEntityEnum.REFEREE.getValue(), number));
        referee.setThreeLetterCode("AUR");
        referee.setTranslations(buildTranslationsFor(SportEntityEnum.REFEREE));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            referee.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        referee.setDisplayAsset(buildDisplayAsset());
        referee.setCountryId(UUID.randomUUID().toString());
        referee.setGender("OTHER");
        referee.setCompetitionIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        referee.setBirthdate("1990-01-01");
        return referee;
    }

    public static SportEntityModel buildPlayerAllProperties(SupportedSports sport) {
        SportEntityModel player = buildMandatoryProperties(sport, SportEntityEnum.PLAYER);
        int number = LocalDateTime.now().getNano();

        player.setShortName("Auto%s%s".formatted(SportEntityEnum.PLAYER.getValue(), number));
        player.setThreeLetterCode("AUP");
        player.setTranslations(buildTranslationsFor(SportEntityEnum.PLAYER));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            player.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        player.setDisplayAsset(buildDisplayAsset());
        player.setCountry(buildCountryModel());
        player.setGender("MALE");
        player.setCompetitionIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        player.setTeamIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        player.setBirthdate("1990-01-01");
        player.setPosition("DEFENDER");
        return player;
    }

    public static SportEntityModel buildHorseAllProperties(SupportedSports sport) {
        SportEntityModel horse = buildMandatoryProperties(sport, SportEntityEnum.HORSE);
        int number = LocalDateTime.now().getNano();

        horse.setShortName("Auto%s%s".formatted(SportEntityEnum.HORSE.getValue(), number));
        horse.setThreeLetterCode("AUH");
        horse.setTranslations(buildTranslationsFor(SportEntityEnum.HORSE));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            horse.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        horse.setDisplayAsset(buildDisplayAsset());
        horse.setCountry(buildCountryModel());
        horse.setGender("MALE");
        return horse;
    }

    public static SportEntityModel buildEventAllProperties(SupportedSports sport, String competitionId) {
        SportEntityModel event = buildEventMandatoryProperties(sport);
        int number = LocalDateTime.now().getNano();

        event.setShortName("Auto%s%s".formatted(SportEntityEnum.EVENT.getValue(), number));
        event.setThreeLetterCode("AUE");
        event.setTranslations(buildTranslationsFor(SportEntityEnum.EVENT));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            event.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        event.setDisplayAsset(buildDisplayAsset());
        event.setStatus(StatusModel.builder()
                .name(StatusEnum.CURRENT.name())
                .shortName("CURR")
                .threeLetterCode("CUR")
                .translations(List.of(
                        Translation.builder()
                                .language(Language.BULGARIAN.getCode())
                                .name("Текущ")
                                .shortName("Текущ")
                                .threeLetterCode("ТК")
                                .build()
                ))
                .id(UUID.randomUUID().toString())
                .legacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)))
                .type("STATUS")
                .build());
        event.setParticipants(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        event.setResults(List.of(EventResultModel.builder()
                .name("Event result")
                .shortName("Event result")
                .threeLetterCode("EVR")
                .translations(List.of(
                        TranslationModel.builder()
                                .language(Language.BULGARIAN.getCode())
                                .name("Резултат от събитие")
                                .shortName("Резултат от събитие")
                                .threeLetterCode("РС")
                                .build()
                ))
                .id(UUID.randomUUID().toString())
                .value("VALUE")
                .position("POSITION")
                .build()));
        event.setStartTime("2024-04-03T12:45:23.025Z");
        event.setStage(EventStageModel.builder()
                .name("Event stage")
                .shortName("Event stage")
                .threeLetterCode("EVS")
                .translations(List.of(
                        Translation.builder()
                                .language(Language.BULGARIAN.getCode())
                                .name("Етап на събитие")
                                .shortName("Етап на събитие")
                                .threeLetterCode("ЕС")
                                .build()
                ))
                .id(UUID.randomUUID().toString())
                .legacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)))
                .build());
        event.setCompetitionId(competitionId);
        event.setParticipantDetails(List.of(
                ParticipantDetailsModel.builder()
                        .participant(ParticipantModel.builder()
                                .name("Participant")
                                .shortName("Participant")
                                .threeLetterCode("PAR")
                                .translations(List.of(
                                        Translation.builder()
                                                .language(Language.BULGARIAN.getCode())
                                                .name("Участник")
                                                .shortName("Участник")
                                                .threeLetterCode("УЧ")
                                                .build()
                                ))
                                .id(UUID.randomUUID().toString())
                                .legacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)))
                                .entityType("PARTICIPANT")
                                .position("POSITION")
                                .displayAsset(buildDisplayAsset())
                                .winner("WINNER")
                                .currentServer("CURRENT_SERVER")
                                .build())
                        .results(List.of(
                                ResultsModel.builder()
                                        .type(TypeModel.builder()
                                                .code("TYPE_CODE")
                                                .name("TYPE_NAME")
                                                .build())
                                        .value("VALUE")
                                        .winner("WINNER")
                                        .tiebreakValue("TIEBREAK_VALUE")
                                        .build()
                        ))
                        .build()
        ));
        return event;
    }

    public static SportEntityModel buildEventAllProperties(SupportedSports sport) {
        return buildEventAllProperties(sport, UUID.randomUUID().toString());
    }

    public static SportEntityModel buildEventAllProperties(SupportedSports sport, String competitionId, StatusEnum status, DataTeamEnum firstTeam, DataTeamEnum secondTeam) {
        SportEntityModel event = buildEventAllProperties(sport, competitionId);
        event.setElapsed("20");
        event.setStatus(StatusModel.builder()
                .name(status.getName())
                .shortName(status.getShortName())
                .threeLetterCode(status.getThreeLetterCode())
                .translations(List.of(
                        Translation.builder()
                                .language(Language.BULGARIAN.getCode())
                                .name(status.getNameBg())
                                .shortName(status.getShortName())
                                .threeLetterCode(status.getThreeLetterCode())
                                .build()
                ))
                .id(UUID.randomUUID().toString())
                .legacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)))
                .type(status.getType())
                .build());
        event.setStatusType(status.getType());
        event.setParticipantDetails(List.of(
                // First participant
                buildParticipantDetails(firstTeam, "NO"),
                // Second participant
                buildParticipantDetails(secondTeam, "YES")
        ));
        event.setResults(List.of(
                buildEventResult(firstTeam, "1", "1"),
                buildEventResult(secondTeam, "4", "2")
        ));
        return event;
    }

    private static EventResultModel buildEventResult(DataTeamEnum participant, String value, String position) {
        return EventResultModel.builder()
                .entityType("result")
                .name(participant.getFullName())
                .shortName(participant.getShortName())
                .threeLetterCode(participant.getThreeLetterCode())
                .value(value)
                .position(position)
                .id(participant.getId())
                .build();
    }

    private static ResultsModel buildResult(String code, String name, String value, String winner) {
        return ResultsModel.builder()
                .type(TypeModel.builder()
                        .code(code)
                        .name(name)
                        .build())
                .value(value)
                .winner(winner)
                .tiebreakValue(StringConstants.EMPTY_STRING)
                .build();
    }

    private static ParticipantDetailsModel buildParticipantDetails(DataTeamEnum team, String winner) {
        return ParticipantDetailsModel.builder()
                .participant(ParticipantModel.builder()
                        .name(team.getFullName())
                        .shortName(team.getShortName())
                        .threeLetterCode(team.getThreeLetterCode())
                        .translations(List.of(
                                Translation.builder()
                                        .language(Language.BULGARIAN.getCode())
                                        .name("Участник 1")
                                        .shortName("Участник 1")
                                        .threeLetterCode("УЧ1")
                                        .build()
                        ))
                        .id(team.getId())
                        .legacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)))
                        .entityType("PARTICIPANT")
                        .position("POSITION")
                        .displayAsset(buildDisplayAsset())
                        .winner("WINNER")
                        .currentServer("CURRENT_SERVER")
                        .build())
                .results(List.of(
                        buildResult("period1", "Period 1", "0", winner),
                        buildResult("period2", "Period 2", "1", winner),
                        buildResult("period3", "Period 3", "1", winner),
                        buildResult("display", "Display Score", "1", winner),
                        buildResult("ordinarytime", "Ordinary time", "1", winner),
                        buildResult("finalresult", "Final Result", "1", winner)
                )).build();
    }

    public static SportEntityModel buildCompetitionAllProperties(SupportedSports sport) {
        SportEntityModel competition = buildCompetitionMandatoryProperties(sport);
        int number = LocalDateTime.now().getNano();

        competition.setShortName("Auto%s%s".formatted(SportEntityEnum.COMPETITION.getValue(), number));
        competition.setThreeLetterCode("AUC");
        competition.setTranslations(buildTranslationsFor(SportEntityEnum.COMPETITION));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            competition.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        competition.setDisplayAsset(buildDisplayAsset());
        competition.setCountry(buildCountryModel());
        competition.setGender("MALE");
        return competition;
    }

    public static SportEntityModel buildCoachAllProperties(SupportedSports sport) {
        SportEntityModel coach = buildCoachMandatoryProperties(sport);
        int number = LocalDateTime.now().getNano();

        coach.setShortName("Auto%s%s".formatted(SportEntityEnum.COACH.getValue(), number));
        coach.setThreeLetterCode("AUC");
        coach.setTranslations(buildTranslationsFor(SportEntityEnum.COACH));
        if (sport.equals(SupportedSports.FOOTBALL)) {
            coach.setLegacyId(String.valueOf(NumberUtils.getRandomNumberInRange(111, 9999)));
        }
        coach.setDisplayAsset(buildDisplayAsset());
        coach.setCountry(buildCountryModel());
        coach.setGender("FEMALE");
        coach.setCompetitionIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        coach.setTeamIds(List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        coach.setBirthdate("1990-01-01");
        return coach;
    }

    private static SportEntityModel buildMandatoryProperties(SupportedSports sport, SportEntityEnum sportEntity) {
        return SportEntityModel.builder()
                .id(UUID.randomUUID().toString())
                .name("Automation-%s %s".formatted(sportEntity.getValue(), LocalDateTime.now().getNano()))
                .sport(sport == null ? null : sport.getValue())
                .build();
    }
}