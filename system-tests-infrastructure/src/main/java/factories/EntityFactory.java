package factories;

import com.github.javafaker.Faker;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Getter
public abstract class EntityFactory {

    private static final DateTimeFormatter HH_MM_SS_Z = DateTimeFormatter.ofPattern("HH:mm:ss'Z'");

    public static Faker getFaker() {
        return new Faker();
    }

    /**
     * Returns date in future based on provided days in future
     *
     * @param daysInFuture number of days in future
     * @return date in future in format yyyy-MM-ddT
     */
    protected static String getDateInFuture(int daysInFuture) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate date = now.plusDays(daysInFuture).toLocalDate();
        return "%sT".formatted(date);
    }

    /**
     * Returns date in past based on provided days in past
     *
     * @param daysInPast number of days in past
     * @return date in past in format yyyy-MM-ddT
     */
    protected static String getDateInPast(int daysInPast) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate date = now.minusDays(daysInPast).toLocalDate();
        return "%sT".formatted(date);
    }

    /**
     * Returns current date and time in format yyyy-MM-ddTHH:mm:ssZ
     *
     * @return current date and time in format yyyy-MM-ddTHH:mm:ssZ
     */
    protected static String getCurrentZonedDateTime() {
        return ZonedDateTime.now().format(HH_MM_SS_Z);
    }

    /**
     * Returns time in future based on specific time as String
     *
     * @param specificTime    in format HH:mm:ss
     * @param minutesInFuture number of minutes in future
     * @return time in future based on specific time as String
     */
    protected static String getTimeInFutureBasedOnSpecificTime(String specificTime, int minutesInFuture) {
        return ZonedDateTime.now()
                .with(LocalTime.parse(specificTime, DateTimeFormatter.ISO_TIME))
                .plusMinutes(minutesInFuture)
                .format(HH_MM_SS_Z);
    }

    /**
     * Returns random date in format yyyy-MM-dd in range from 1800 to current year
     *
     * @return random date as String in format yyyy-MM-dd in range from 1800 to current year
     */
    protected String getRandomDate() {
        int randomYear = getFaker().number().numberBetween(1800, LocalDate.now().getYear());
        int randomMonth = getFaker().number().numberBetween(1, 12);
        int randomDay = getFaker().number().numberBetween(1, 28);
        return LocalDate.of(randomYear, randomMonth, randomDay).toString();
    }

    protected static int getUniqueIdentifier() {
        return LocalDateTime.now().getNano();
    }
}