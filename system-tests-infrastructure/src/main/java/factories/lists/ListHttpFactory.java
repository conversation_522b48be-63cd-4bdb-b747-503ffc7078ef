package factories.lists;

import data.constants.*;
import data.constants.automatedlists.TagTypeEnum;
import data.constants.enums.AutomaticContentModeEnum;
import data.models.articles.ArticleResponseModel;

import data.models.categories.Category;
import data.models.lists.ConfigurationModel;
import data.models.lists.CriteriaModel;
import data.models.lists.ListModel;
import data.models.lists.PopularitySettingsModel;
import data.widgets.options.enums.*;
import data.models.lists.*;
import data.models.searchapi.Meta;
import data.models.searchapi.ResultModel;
import data.utils.StringUtils;
import data.widgets.options.enums.NumberAsStringEnum;
import factories.EntityFactory;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import static data.constants.StringConstants.FOOTBALL_API_STRING;
import static data.constants.StringConstants.SPORTS_SEARCH_API_STRING;

public class ListHttpFactory extends EntityFactory {

    public static ListModel buildEditorialListDefault() {
        return ListModel
                .builder()
                .title("[from API] AutoTestEditorialList %s %s".formatted(getFaker().animal().name(), LocalDateTime.now().getNano()))
                .configuration(ConfigurationModel.builder()
                        .category(buildFootballCategory())
                        .minItems(NumberAsStringEnum.ONE.getValue())
                        .maxItems(NumberAsStringEnum.TEN.getValue())
                        .build())
                .language(Language.ENGLISH.getCode())
                .contentType(ListContentType.EDITORIAL.getValue())
                .build();
    }

    public static ListModel buildEditorialListWithCategory(Category category) {
        ListModel listModel = buildEditorialListDefault();
        listModel.getConfiguration().setCategory(category);
        return listModel;
    }

    public static ListModel buildEditorialListWithSports(List<SportModel> sportModels) {
        ListModel listModel = buildEditorialListDefault();
        listModel.getConfiguration().setSports(sportModels);
        return listModel;
    }

    public static List<SportModel> buildSportModel(List<ResultModel> sportEntities) {
        List<SportModel> sports = new ArrayList<>();

        for (ResultModel sportEntity : sportEntities) {
            String provider = sportEntity.getSport().equals(SupportedSports.FOOTBALL.getValue()) ?
                    FOOTBALL_API_STRING : SPORTS_SEARCH_API_STRING;

            sports.add(SportModel.builder()
                    .provider(provider)
                    .type(sportEntity.getEntityType())
                    .data(DataModel.builder()
                            .id(Integer.valueOf(sportEntity.getLegacyId()))
                            .uuid(sportEntity.getId())
                            .name(sportEntity.getName())
                            .entityType(sportEntity.getEntityType())
                            .build())
                    .build());
        }
        return sports;

    }


    public ListModel buildScheduledListDefault() {
        return ListModel
                .builder()
                .title("[from API] AutoTestScheduledList %s %s".formatted(getFaker().animal().name(), LocalDateTime.now().getNano()))
                .language(Language.ENGLISH.getCode())
                .contentType(ListContentType.SCHEDULED.getValue())
                .configuration(ConfigurationModel.builder()
                        .category(buildFootballCategory())
                        .build())
                .build();
    }

    public ListModel buildTagsAndSportsConnectionsListDefault() {
        return ListModel
                .builder()
                .title("[from API] AutoTestTagsAndSportsConnectionsList %s %s".formatted(getFaker().animal().name(), LocalDateTime.now().getNano()))
                .language(Language.ENGLISH.getCode())
                .contentType(ListContentType.TAG_SPORTS_CONNECTIONS.getValue())
                .configuration(ConfigurationModel.builder()
                        .minItems(NumberAsStringEnum.ONE.getValue())
                        .maxItems(NumberAsStringEnum.TEN.getValue())
                        .build())
                .build();
    }

    public static ListModel buildAutomatedListRequiredProperties(AutomaticContentModeEnum automaticContentMode) {
        ListModel listModel = ListModel.builder()
                .title("[from API] Automated List %s Auto Test %s".formatted(automaticContentMode, System.currentTimeMillis()))
                .configuration(ConfigurationModel.builder()
                        .minItems(NumberAsStringEnum.ZERO.getValue())
                        .maxItems(NumberAsStringEnum.FIVE.getValue())
                        .build())
                .language(Language.ENGLISH.getCode())
                .automaticContent(Boolean.TRUE)
                .automaticContentThreshold(2)
                .automaticContentMode(automaticContentMode.name().toLowerCase())
                .build();

        if (automaticContentMode.equals(AutomaticContentModeEnum.MOST_POPULAR)) {
            listModel.setPopularitySettings(buildPopularitySettings());
        } else if (automaticContentMode.equals(AutomaticContentModeEnum.MOST_RECENT)) {
            listModel.setCriteria(ListHttpFactory.buildCriteriaEntityTypes(StringConstants.ARTICLE_STRING));
        }
        return listModel;
    }

    public static PopularitySettingsModel buildPopularitySettings() {
        return PopularitySettingsModel.builder()
                .dataSource("GA4")
                .updateFrequency(FrequencyUpdateEnum.DAILY.getValue())
                .timeRange(TimeRangeEnum.HOURS_24.getValue())
                .build();
    }

    public static ListModel buildAutomatedListAllProperties(AutomaticContentModeEnum automaticContentMode) {
        ListModel properties = buildAutomatedListRequiredProperties(automaticContentMode);
        properties.setSlug("automated-list-slug-%s".formatted(System.currentTimeMillis()));
        properties.getConfiguration().setMinItems(NumberAsStringEnum.ONE.getValue());
        properties.getConfiguration().setCategory(buildFootballCategory());
        properties.setStatus(StatusTypeEnum.ACTIVE.name().toLowerCase());
        properties.setType(StringConstants.EMPTY_STRING);
        properties.setCriteria(CriteriaModel.builder()
                .entityTypes(Arrays.asList(
                        StringConstants.ARTICLE_STRING,
                        StringConstants.GALLERY_STRING,
                        StringUtils.replaceSpacesWithSymbol(StringConstants.LIVE_BLOG_STRING, StringConstants.EMPTY_STRING).toLowerCase(),
                        StringConstants.VIDEO_STRING))
                .build());

        return properties;
    }

    public static CriteriaModel buildCriteriaEntityTypes(String... entityTypes) {
        return CriteriaModel.builder()
                .entityTypes(List.of(entityTypes))
                .build();
    }

    public static CriteriaModel buildCriteriaFilters(String tagType, List<String> tagIds, String... entityTypes) {
        tagType = switch (tagType) {
            case StringConstants.TOURNAMENT_STRING -> TagTypeEnum.COMPETITION.name().toLowerCase();
            case StringConstants.MATCH_STRING -> TagTypeEnum.EVENT.name().toLowerCase();
            default -> tagType;
        };

        CriteriaModel criteriaModel = buildCriteriaEntityTypes(entityTypes);
        criteriaModel.setFilters(FiltersModel.builder()
                .tagType(tagType)
                .tagIds(tagIds)
                .build());
        return criteriaModel;
    }

    public static List<ListItemModel> buildItemsArticlesOnly(List<ArticleResponseModel> createdArticles, int countOfItems) {
        List<ListItemModel> items = new ArrayList<>();
        for (int i = 0; i < countOfItems; i++) {
            items.add(ListItemModel.builder()
                    .id(createdArticles.get(i).getId())
                    .data(ListItemDataModel.builder()
                            .id(createdArticles.get(i).getId())
                            .title(createdArticles.get(i).getTitle())
                            .build())
                    .type(StringConstants.ARTICLE_STRING.toUpperCase())
                    .weight(i)
                    .meta(Meta.builder().build())
                    .build());
        }
        return items;
    }

    private static Category buildFootballCategory() {
        return Category
                .builder()
                .id(CategoryEnum.FOOTBALL.getId())
                .title(CategoryEnum.FOOTBALL.getName())
                .active(Boolean.TRUE)
                .build();
    }
}