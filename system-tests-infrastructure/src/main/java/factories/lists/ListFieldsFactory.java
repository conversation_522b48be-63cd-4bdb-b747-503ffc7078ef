package factories.lists;

import data.models.uimodels.ListFormModel;
import factories.EntityFactory;

import java.time.LocalDateTime;

public class ListFieldsFactory extends EntityFactory {

    public static ListFormModel buildListRequiredFields() {
        return ListFormModel
                .builder()
                .title(String.format("[AutoTest] List %s %s", getFaker().superhero().name(), LocalDateTime.now().getNano()))
                .build();
    }
}