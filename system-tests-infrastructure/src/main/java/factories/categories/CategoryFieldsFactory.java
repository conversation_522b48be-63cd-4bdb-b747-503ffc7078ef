package factories.categories;

import data.models.categories.Category;
import factories.EntityFactory;
import java.time.LocalDateTime;

public class CategoryFieldsFactory extends EntityFactory {

    public Category buildCategoryRequiredFields() {
        return Category
                .builder()
                .title(String.format("AutoTestCategory %s %s", getFaker().pokemon().name(), LocalDateTime.now().getNano()))
                .build();
    }
}