package factories.authors;

import data.models.authors.AuthorModel;
import factories.EntityFactory;

import java.time.LocalDateTime;

public class AuthorHttpFactory extends EntityFactory {

    public static AuthorModel buildDefaultAuthor() {
        return AuthorModel.builder()
                .name("[from api] AutoTestAuthor %s %s".formatted(getFaker().hitchhikersGuideToTheGalaxy().character(), LocalDateTime.now().getNano()))
                .bio("[from api] AutoTestAuthor Bio")
                .bio_html("[from api] AutoTestAuthor Bio HTML")
                .build();
    }
}