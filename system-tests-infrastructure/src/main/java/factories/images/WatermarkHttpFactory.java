package factories.images;

import data.constants.StringConstants;
import data.constants.enums.ImageWatermarkPositionEnum;
import data.models.images.watermarks.WatermarkModel;
import data.models.images.watermarks.WatermarksImageModel;
import data.utils.EnumUtils;
import data.utils.ImageUtils;
import data.utils.NumberUtils;
import factories.EntityFactory;

public class WatermarkHttpFactory extends EntityFactory {

    public static WatermarkModel buildDefaultWatermark() {
        return WatermarkModel.builder()
                .file(ImageUtils.getTestImagePath(StringConstants.TEST_WATERMARK_IMAGE_PNG))
                .name("AutoTestWatermark-%s".formatted(System.currentTimeMillis()))
                .opacity(String.valueOf(NumberUtils.getRandomNumberInRange(0, 99)))
                .position(EnumUtils.getRandomEnumValue(ImageWatermarkPositionEnum.class).name().toLowerCase())
                .build();
    }

    public static WatermarksImageModel buildDefaultWatermarkImage(String imagePath, String watermarkId) {
        return WatermarksImageModel.builder()
                .deleteExisting(StringConstants.NO_STRING.toLowerCase())
                .imagePath(imagePath)
                .watermarkId(watermarkId)
                .build();
    }
}