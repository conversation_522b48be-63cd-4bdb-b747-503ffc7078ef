package factories.tags;

import data.constants.TagOrderType;
import data.models.tags.TagModel;
import factories.EntityFactory;

import java.time.LocalDateTime;

public class TagFieldsFactory extends EntityFactory {

    public TagModel buildTagRequiredFields() {
        return TagModel
                .builder()
                .title(String.format("AutoTestTag %s %s", getFaker().pokemon().name(), LocalDateTime.now().getNano()))
                .orderType(TagOrderType.UNORDERED.toString().toLowerCase())
                .build();
    }
}