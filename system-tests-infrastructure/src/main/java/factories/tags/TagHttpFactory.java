package factories.tags;

import data.constants.Language;
import data.constants.StringConstants;
import data.constants.TagOrderType;
import data.models.articles.CreatedBy;
import data.models.tags.SeoModel;
import data.models.tags.TagModel;
import data.models.tags.TagResponseModel;
import data.models.tags.UrlsModel;
import factories.EntityFactory;
import plugins.authentication.User;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class TagHttpFactory extends EntityFactory {

    public static TagModel buildDefaultTag() {
        return TagModel
                .builder()
                .title("[from api] AutoTestTag %s %s".formatted(getFaker().pokemon().name(), LocalDateTime.now().getNano()))
                .orderType(TagOrderType.ORDERED.toString().toLowerCase())
                .build();
    }

    public static TagModel buildTagPageAllProperties() {
        return TagModel.builder()
                .type(StringConstants.EMPTY_STRING)
                .title("FROM API AutoTag %s".formatted(LocalDateTime.now().getNano()))
                .seo(SeoModel.builder()
                        .slug("slug-autoTests-" + getFaker().internet().slug() + getFaker().number().randomNumber(3, true))
                        .title("seo autoTests " + getFaker().superhero().name() + getFaker().number().randomNumber(3, true))
                        .description(StringConstants.ALL_SPORTS_STRING)
                        .keywords(List.of("all", "sports"))
                        .index(true)
                        .follow(true)
                        .redirectType(StringConstants.TEMPORARY_STRING)
                        .jsonld(null)
                        .build())
                .orderType(TagOrderType.ORDERED.toString().toLowerCase())
                .language(Language.ENGLISH.getCode())
                .mainMedia(new ArrayList<>())
                .weight(0)
                .externalUrl(StringConstants.SPORTAL_BG_NEWS_URL_STRING)
                .canonicalUrl(StringConstants.SPORTAL_BG_CANNONICAL_URL_STRING)
                .description("Auto Tag Description %s %s".formatted(getFaker().harryPotter().spell(), LocalDateTime.now().getNano()))
                .build();
    }

    public static TagResponseModel buildExpectedTagCreateResponse(TagModel requestBody, String tagId, User user) {
        OffsetDateTime formattedDateTime = ZonedDateTime.now().plus(20, ChronoUnit.MILLIS).withZoneSameInstant(ZoneId.of("UTC")).toOffsetDateTime();

        return TagResponseModel.builder()
                .id(tagId)
                .title(requestBody.getTitle())
                .description(requestBody.getDescription())
                .seo(SeoModel.builder()
                        .title(requestBody.getSeo().getTitle())
                        .description(requestBody.getSeo().getDescription())
                        .slug(requestBody.getSeo().getSlug())
                        .keywords(requestBody.getSeo().getKeywords())
                        .index(requestBody.getSeo().getIndex())
                        .follow(requestBody.getSeo().getFollow())
                        .redirectType(requestBody.getSeo().getRedirectType())
                        .automaticSlug(requestBody.getSeo().getAutomaticSlug())
                        .automaticSeoTitle(requestBody.getSeo().getAutomaticSeoTitle())
                        .jsonld(requestBody.getSeo().getJsonld())
                        .build())
                .urls(UrlsModel.builder()
                        .canonicalUrl(requestBody.getCanonicalUrl())
                        .externalUrl(requestBody.getExternalUrl())
                        .build())
                .orderType(requestBody.getOrderType())
                .generic(requestBody.getGeneric())
                .entityType(StringConstants.TAG_STRING)
                .updatedAt(formattedDateTime)
                .createdAt(formattedDateTime)
                .language(requestBody.getLanguage())
                .mainMedia(requestBody.getMainMedia())
                .weight(requestBody.getWeight())
                .createdBy(CreatedBy.builder()
                        .id(user.getId())
                        .fullName(user.getFullName())
                        .build())
                .build();
    }
}