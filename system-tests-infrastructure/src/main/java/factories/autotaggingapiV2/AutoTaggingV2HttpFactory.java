package factories.autotaggingapiV2;

import data.constants.Language;
import data.models.autotaging.AutoTaggingRequestBodyModel;
import factories.EntityFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class AutoTaggingV2HttpFactory extends EntityFactory {

    private AutoTaggingV2HttpFactory() {
    }

    public static AutoTaggingRequestBodyModel buildEmptyRequestBody() {
        return AutoTaggingRequestBodyModel.builder().build();
    }

    public static AutoTaggingRequestBodyModel buildMandatoryPropertiesRequestBody(String text) {
        return AutoTaggingRequestBodyModel.builder()
                .text(text)
                .inputLanguage(Language.ENGLISH.getCode())
                .build();
    }

    public static AutoTaggingRequestBodyModel buildAllPropertiesRequestBody(Set<String> sports, List<String> competitionIds, String text) {
        AutoTaggingRequestBodyModel properties = buildMandatoryPropertiesRequestBody(text);
        properties.setSports(sports);
        properties.setCompetitionIds(competitionIds);

        return properties;
    }
}