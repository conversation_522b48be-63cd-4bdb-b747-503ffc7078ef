package factories.footballapi;

import data.constants.CountryEnum;
import data.models.footballapi.v2.referees.RefereeV2Model;
import factories.EntityFactory;

import java.time.LocalTime;

public class RefereesV2Factory extends EntityFactory {

    public RefereeV2Model buildDefaultCreateRefereePostRequestBody() {
        return RefereeV2Model.builder()
                .name("Auto Referee %s".formatted(LocalTime.now().getNano()))
                .countryId(CountryEnum.BULGARIA.getId())
                .birthdate(getRandomDate())
                .active(true)
                .build();
    }

    public RefereeV2Model buildDefaultUpdateRefereeRequestBody() {
        return RefereeV2Model.builder()
                .name("Auto Referee %s UPDATED".formatted(LocalTime.now().getNano()))
                .countryId(CountryEnum.ITALY.getId())
                .birthdate(getRandomDate())
                .active(false)
                .build();
    }
}