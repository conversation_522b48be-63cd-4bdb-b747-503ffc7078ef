package factories.footballapi;

import data.models.footballapi.v2.scoremodels.ScoreV2Model;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MatchParameters {

    private String homeTeamId;
    private String awayTeamId;
    private String seasonId;
    private String statusId;
    private ScoreV2Model score;
    private String coverage;
    private String kickoffTime;
    private String finishedAt;
    private String phaseStartedAt;
}
