package factories.footballapi;

import data.constants.CountryEnum;
import data.models.footballapi.v2.ProfileModel;
import data.models.footballapi.v2.VenueModel;
import factories.EntityFactory;

import java.time.LocalTime;

public class VenuesV2Factory extends EntityFactory {

    private static final String MILANO_CITY_ID = "532";
    private static final String PARIS_CITY_ID = "252";

    public VenueModel buildCreateVenueRequestBody() {
        return VenueModel.builder()
                .name("Auto Venue %s".formatted(LocalTime.now().getNano()))
                .countryId(CountryEnum.ITALY.getId())
                .cityId(MILANO_CITY_ID)
                .profile(ProfileModel.builder()
                        .lat(45.465421)
                        .lng(9.185924)
                        .capacity(80000)
                        .build())
                .build();
    }

    public VenueModel buildUpdateVenueRequestBody() {
        return VenueModel.builder()
                .name("Auto Venue %s".formatted(LocalTime.now().getNano()))
                .countryId(CountryEnum.FRANCE.getId())
                .cityId(PARIS_CITY_ID)
                .profile(ProfileModel.builder()
                        .lat(13.465421)
                        .lng(91.185924)
                        .capacity(90000)
                        .build())
                .build();
    }
}