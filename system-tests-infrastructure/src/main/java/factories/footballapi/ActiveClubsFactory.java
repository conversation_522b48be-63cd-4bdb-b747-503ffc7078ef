package factories.footballapi;

import data.constants.ContractType;
import data.models.footballapi.v2.ActiveClubsRequestModel;
import data.models.footballapi.v2.ClubModel;
import factories.EntityFactory;

import java.util.List;

public class ActiveClubsFactory extends EntityFactory {

    public ActiveClubsRequestModel buildActiveClubRequestBody(String teamId) {
        return ActiveClubsRequestModel.builder()
                .clubs(List.of(
                        ClubModel.builder()
                                .teamId(teamId)
                                .contractType(ContractType.PERMANENT.toString())
                                .shirtNumber("10")
                                .build()
                ))
                .build();
    }
}