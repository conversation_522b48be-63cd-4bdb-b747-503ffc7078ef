package factories.galleries;

import data.constants.Language;
import data.constants.StringConstants;
import data.models.articles.*;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.galleries.GalleryRequestModel;
import data.models.galleries.GalleryResponseModel;
import data.models.galleries.Item;
import data.models.images.ImageModel;
import data.utils.DateUtils;
import factories.EntityFactory;
import plugins.authentication.User;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class GalleryHttpFactory extends EntityFactory {

    public static GalleryRequestModel buildDefaultGallery(String imageId, String categoryId) {
        return GalleryRequestModel
                .builder()
                .title("[from api] AutoTestGallery %s %s".formatted(getFaker().artist().name(), LocalDateTime.now().getNano()))
                .categoryId(categoryId)
                .language(Language.ENGLISH.getCode())
                .mainMedia(List.of(
                        MainMediaItem.builder()
                                .data(ArticleResponseModel.builder()
                                        .id(imageId)
                                        .build())
                                .provider(StringConstants.SMP_STRING)
                                .resourceId(imageId)
                                .resourceSubtype(StringConstants.MAIN_IMAGE_STRING)
                                .resourceType(StringConstants.IMAGE_STRING)
                                .build()
                ))
                .items(List.of(Item.builder()
                        .id(imageId)
                        .type(StringConstants.IMAGE_STRING)
                        .build()
                ))
                .build();
    }

    public static GalleryRequestModel buildGalleryMandatoryProperties(Category activeCategory) {
        return GalleryRequestModel.builder()
                .title("FROM API AutoGallery %s".formatted(LocalDateTime.now().getNano()))
                .categoryId(activeCategory.getId())
                .language(Language.ENGLISH.getCode())
                .build();
    }

    public static GalleryRequestModel buildGalleryAllProperties(Category category, AuthorModel author, CommonModel comment,
                                                                Category additionalCategories, CommonModel publishChannel, CommonModel publishRegion,
                                                                CommonModel origin, ImageModel image) {
        String formattedDateTime = DateUtils.getFormattedUtcDateTimeWithMillisOffset();
        return GalleryRequestModel
                .builder()
                .title("FROM API AutoGallery %s".formatted(LocalDateTime.now().getNano()))
                .subtitle("AutoGallery Summary %s".formatted(getFaker().harryPotter().spell()))
                .strapline("AutoGallery strap line %s".formatted(getFaker().harryPotter().spell()))
                .footer("Снимки: " + getFaker().superhero().name() + getFaker().number().randomNumber(3, true))
                .categoryId(category.getId())
                .mainMedia(new ArrayList<>())
                .body(new ArrayList<>())
                .additionalCategories(List.of(additionalCategories.getId()))
                .publishedAt(formattedDateTime)
                .publishedUntil(formattedDateTime)
                .translationGroupId("")
                .status(StringConstants.ACTIVE_STRING)
                .type(StringConstants.EMPTY_STRING)
                .customAuthor("Author autoTest " + getFaker().name().fullName() + getFaker().number().randomNumber(3, true))
                .seo(Seo.builder()
                        .slug("slug-autoTests-" + getFaker().internet().slug() + getFaker().number().randomNumber(3, true))
                        .title("seo autoTests " + getFaker().superhero().name() + getFaker().number().randomNumber(3, true))
                        .description(StringConstants.ALL_SPORTS_STRING)
                        .keywords(List.of("all", "sports"))
                        .index(true)
                        .follow(true)
                        .redirectType(StringConstants.TEMPORARY_STRING)
                        .jsonld(null)
                        .build())
                .originId(origin.getId())
                .commentPolicyId(comment.getId())
                .language(Language.ENGLISH.getCode())
                .authors(List.of(author.getId()))
                .customAuthor("Author autoTest " + getFaker().name().fullName() + getFaker().number().randomNumber(3, true))
                .commentCollectionId("Custom Author Test " + getFaker().harryPotter().spell())
                .allowComments(false)
                .externalUrl(StringConstants.SPORTAL_BG_NEWS_URL_STRING)
                .canonicalUrl(StringConstants.SPORTAL_BG_CANNONICAL_URL_STRING)
                .publishedChannels(List.of(publishChannel.getId()))
                .publishedRegions(List.of(publishRegion.getId()))
                .imageId(image.getId())
                .build();
    }

    public static GalleryResponseModel buildExpectedGalleryCreateResponse(GalleryRequestModel requestModel, String galleryId, Category category,
                                                                          AuthorModel author, User user, CommonModel origin,
                                                                          CommonModel publishChannel, CommonModel publishRegion, CommonModel comment,
                                                                          Category additionalCategory, ImageModel image) {
        OffsetDateTime formattedDateTime = ZonedDateTime.now().plus(20, ChronoUnit.MILLIS)
                .withZoneSameInstant(ZoneId.of("UTC")).toOffsetDateTime();

        return GalleryResponseModel.builder()
                .id(galleryId)
                .title(requestModel.getTitle())
                .subtitle(requestModel.getSubtitle())
                .strapline(requestModel.getStrapline())
                .footer(requestModel.getFooter())
                .entityType(StringConstants.GALLERY_STRING)
                .mainMedia(requestModel.getMainMedia())
                .category(category)
                .versions(List.of())
                .authors(List.of(author))
                .body(requestModel.getBody())
                .publishedUntil(requestModel.getPublishedUntil())
                .publishedAt(requestModel.getPublishedAt())
                .updatedAt(formattedDateTime)
                .createdAt(formattedDateTime)
                .status(requestModel.getStatus())
                .type(null) // Set to null because field is always null
                .origin(origin)
                .status(requestModel.getStatus())
                .customAuthor(requestModel.getCustomAuthor())
                .seo(requestModel.getSeo())
                .items(new ArrayList<>())
                .createdBy(CreatedBy.builder()
                        .id(user.getId())
                        .fullName(user.getFullName())
                        .build())
                .comments(Comments.builder()
                        .policy(Policy.builder()
                                .id(comment.getId())
                                .slug(comment.getSlug())
                                .name(comment.getName())
                                .description(comment.getDescription())
                                .createdAt(comment.getCreatedAt())
                                .updatedAt(comment.getUpdatedAt())
                                .defaultBoolean(comment.getDefaultBoolean())
                                .createdBy(CreatedBy.builder()
                                        .fullName(comment.getCreatedBy().getFullName())
                                        .id(comment.getCreatedBy().getId())
                                        .build())
                                .build())
                        .build())
                .language(requestModel.getLanguage())
                .authors(List.of(author))
                .customAuthor(requestModel.getCustomAuthor())
                .urls(Urls.builder()
                        .canonicalUrl(requestModel.getCanonicalUrl())
                        .externalUrl(requestModel.getExternalUrl())
                        .build())
                .runAds(requestModel.isRunAds())
                .adultContent(requestModel.isAdultContent())
                .isAdultContent(requestModel.isAdultContent())
                .isSensitiveContent(requestModel.isSensitiveContent())
                .bettingContent(requestModel.isBettingContent())
                .sensitiveContent(requestModel.isSensitiveContent())
                .image(Image.builder()
                        .data(image)
                        .build())
                .additionalCategories(List.of(additionalCategory))
                .publishedChannels(List.of(publishChannel))
                .publishedRegions(List.of(publishRegion))
                .contentUpdatedAt(requestModel.getPublishedAt())
                .isContentUpdatedAtSetAutomatically(true)
                .build();
    }
}