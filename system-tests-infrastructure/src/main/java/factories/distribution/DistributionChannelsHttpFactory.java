package factories.distribution;

import data.models.common.CommonModel;
import factories.EntityFactory;

public class DistributionChannelsHttpFactory extends EntityFactory {

    public static CommonModel buildDefaultDistributionChannel() {
        long number = System.currentTimeMillis();

        return CommonModel.builder()
                .slug("distribution-channel-%s".formatted(number))
                .name("distribution-channel-%s".formatted(number))
                .defaultBoolean(false)
                .description("distribution channel")
                .build();
    }
}