package factories.blockies.tennis;

import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.widgets.options.enums.DataRefreshTimeEnum;

import java.time.LocalDate;

public class TennisLivescoreBlockyFieldsFactory {

    public static LivescoreBlockyFieldsModel buildDynamicFieldsOnly(LocalDate date, boolean isDisplayOdds,
                                                                    Bookmaker bookmaker) {
        return LivescoreBlockyFieldsModel.builder()
                .date(date)
                .displayOdds(isDisplayOdds)
                .bookmaker(bookmaker)
                .build();
    }

    public static LivescoreBlockyFieldsModel buildDynamicFieldsOnly(LivescoreBlockyFieldsModel blockyData) {
        return LivescoreBlockyFieldsModel.builder()
                .date(blockyData.getDate())
                .displayOdds(blockyData.isDisplayOdds())
                .bookmaker(blockyData.getBookmaker())
                .build();
    }

    public static LivescoreBlockyFieldsModel buildAllFields(LivescoreBlockyFieldsModel blockyData) {
        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static LivescoreBlockyFieldsModel buildMandatoryFields(LivescoreBlockyFieldsModel blockyData) {
        return LivescoreBlockyFieldsModel.builder()
                .date(blockyData.getDate())
                .displayOdds(false)
                .build();
    }
}