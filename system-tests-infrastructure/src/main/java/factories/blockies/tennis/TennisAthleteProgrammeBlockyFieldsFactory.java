package factories.blockies.tennis;

import data.models.blockymodels.tennis.TennisAthleteProgrammeBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.tennisapi.PlayerModel;
import data.models.tennisapi.RoundModel;
import data.models.tennisapi.TournamentModel;
import data.widgets.options.enums.DataGameTypeEnum;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DataSortDirectionEnum;
import data.widgets.options.models.DataDate;

import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.List;

public class TennisAthleteProgrammeBlockyFieldsFactory {

    public static TennisAthleteProgrammeBlockyFieldsModel buildDynamicFieldsOnly(PlayerModel playerModel, TournamentModel tournament,
                                                                                 List<RoundModel> rounds, Bookmaker bookmaker) {
        return TennisAthleteProgrammeBlockyFieldsModel.builder()
                .player(playerModel)
                .tournament(tournament)
                .rounds(new LinkedHashSet<>(rounds))
                .bookmaker(bookmaker)
                .build();
    }

    public static TennisAthleteProgrammeBlockyFieldsModel buildDynamicFieldsOnly(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        return TennisAthleteProgrammeBlockyFieldsModel.builder()
                .player(blockyData.getPlayer())
                .tournament(blockyData.getTournament())
                .rounds(new LinkedHashSet<>(blockyData.getRounds()))
                .build();
    }

    public static TennisAthleteProgrammeBlockyFieldsModel buildAllFields(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusDays(10);
        String dateFormat = "YYYY-MM-DD";

        TennisAthleteProgrammeBlockyFieldsModel dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setSortDirection(DataSortDirectionEnum.getRandomValue());
        dynamicFields.setMatchType(DataGameTypeEnum.getRandomValue());
        dynamicFields.setDateTo(DataDate.builder()
                .date((futureDate).toString())
                .dateFormat(dateFormat)
                .build());
        dynamicFields.setDateFrom(DataDate.builder()
                .date((currentDate).toString())
                .dateFormat(dateFormat)
                .build());
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());
        return dynamicFields;
    }

    public static TennisAthleteProgrammeBlockyFieldsModel buildMandatoryFields(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        return TennisAthleteProgrammeBlockyFieldsModel.builder()
                .player(blockyData.getPlayer())
                .build();
    }
}
