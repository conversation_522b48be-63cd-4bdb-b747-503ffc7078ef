package factories.blockies.football;

import data.constants.enums.PlayerDataElementsStatisticsEnum;
import data.models.blockymodels.football.FootballPlayerH2HBlockyFieldsModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.player.PlayerModel;
import data.models.related.Tournament;
import data.widgets.options.enums.DataRefreshTimeEnum;

import java.util.ArrayList;

public class FootballPlayerH2HBlockyFieldsFactory {

    public static FootballPlayerH2HBlockyFieldsModel buildDynamicFieldsOnly(PlayerModel playerOne, Season playerOneSeason, Tournament playerOneStage, PlayerModel playerTwo, Season playerTwoSeason, Tournament playerTwoStage) {
        return FootballPlayerH2HBlockyFieldsModel.builder()
                .playerOne(playerOne)
                .playerOneSeason(playerOneSeason)
                .playerOneStage(playerOneStage)
                .playerTwo(playerTwo)
                .playerTwoSeason(playerTwoSeason)
                .playerTwoStage(playerTwoStage)
                .build();
    }

    public static FootballPlayerH2HBlockyFieldsModel buildDynamicFieldsOnly(FootballPlayerH2HBlockyFieldsModel blockyData) {
        return FootballPlayerH2HBlockyFieldsModel.builder()
                .playerOne(blockyData.getPlayerOne())
                .playerOneSeason(blockyData.getPlayerOneSeason())
                .playerOneStage(blockyData.getPlayerOneStage())
                .playerTwo(blockyData.getPlayerTwo())
                .playerTwoSeason(blockyData.getPlayerTwoSeason())
                .playerTwoStage(blockyData.getPlayerTwoStage())
                .build();
    }

    public static FootballPlayerH2HBlockyFieldsModel buildAllFields(FootballPlayerH2HBlockyFieldsModel blockyData) {
        var statistics = new ArrayList<PlayerDataElementsStatisticsEnum>();
        statistics.add(PlayerDataElementsStatisticsEnum.MATCHES_PLAYED);
        statistics.add(PlayerDataElementsStatisticsEnum.GOALS);
        statistics.add(PlayerDataElementsStatisticsEnum.ASSISTS);

        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setStatistics(statistics);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static FootballPlayerH2HBlockyFieldsModel buildMandatoryFields(FootballPlayerH2HBlockyFieldsModel blockyData) {
        return buildDynamicFieldsOnly(blockyData);
    }
}