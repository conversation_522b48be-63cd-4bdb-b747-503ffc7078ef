package factories.blockies.football;

import data.models.blockymodels.football.FootballStandingsBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Stage;
import data.models.footballapi.stage.Standing;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.widgets.options.enums.DataRefreshTimeEnum;

import java.util.List;

public class FootballStandingsBlockyFieldsFactory {

    public static FootballStandingsBlockyFieldsModel buildDynamicFieldsOnly(TournamentSeasonModel seasonModel, CommonResultModel tournamentModel, Stage stageModel,
                                                                            List<Standing> teams) {
        return FootballStandingsBlockyFieldsModel.builder()
                .tournament(tournamentModel)
                .season(seasonModel)
                .stage(stageModel)
                .allTeams(teams)
                .build();
    }

    public static FootballStandingsBlockyFieldsModel buildDynamicFieldsOnly(FootballStandingsBlockyFieldsModel blockyData) {
        return FootballStandingsBlockyFieldsModel.builder()
                .stage(blockyData.getStage())
                .tournament(blockyData.getTournament())
                .season(blockyData.getSeason())
                .allTeams(blockyData.getAllTeams())
                .build();
    }

    public static FootballStandingsBlockyFieldsModel buildAllFields(FootballStandingsBlockyFieldsModel blockyData) {
        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setTeamsForHighlight(List.of(blockyData.getAllTeams().get(0)));
        dynamicFields.setLimit("9");
        dynamicFields.setStartFromPosition("1");
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static FootballStandingsBlockyFieldsModel buildMandatoryFields(FootballStandingsBlockyFieldsModel blockyData) {
        return FootballStandingsBlockyFieldsModel.builder()
                .tournament(blockyData.getTournament())
                .season(blockyData.getSeason())
                .build();
    }
}