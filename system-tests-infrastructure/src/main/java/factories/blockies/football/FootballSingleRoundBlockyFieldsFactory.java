package factories.blockies.football;

import data.constants.StatusEnum;
import data.constants.enums.RoundChangeDropdownEnum;
import data.models.blockymodels.football.FootballSingleRoundBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.v2.seasons.SeasonV2DetailsModel;
import data.widgets.options.enums.DataLimitEnum;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DataSortDirectionEnum;
import data.widgets.options.models.DataDate;

import java.time.LocalDate;

public class FootballSingleRoundBlockyFieldsFactory {

    public static FootballSingleRoundBlockyFieldsModel buildDynamicFieldsOnly(CommonResultModel tournament, SeasonV2DetailsModel seasonDetails, Bookmaker bookmaker) {
        return FootballSingleRoundBlockyFieldsModel.builder()
                .tournament(tournament)
                .season(tournament.getSeasons().get(0))
                .stage(seasonDetails.getStages().get(0).getStage())
                .round(seasonDetails.getStages().get(0).getRounds().stream()
                        .filter(r -> r.getStatus().equals(StatusEnum.ACTIVE.name()))
                        .findFirst()
                        .orElse(null))
                .bookmaker(bookmaker)
                .build();
    }

    public static FootballSingleRoundBlockyFieldsModel buildDynamicFieldsOnly(FootballSingleRoundBlockyFieldsModel blockyData) {
        return FootballSingleRoundBlockyFieldsModel.builder()
                .tournament(blockyData.getTournament())
                .season(blockyData.getSeason())
                .stage(blockyData.getStage())
                .round(blockyData.getRound())
                .bookmaker(blockyData.getBookmaker())
                .build();
    }

    public static FootballSingleRoundBlockyFieldsModel buildAllFields(FootballSingleRoundBlockyFieldsModel blockyData) {
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusDays(10);
        String dateFormat = "YYYY-MM-DD";

        FootballSingleRoundBlockyFieldsModel dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setRoundChangeDropdown(RoundChangeDropdownEnum.getRandomValue());
        dynamicFields.setSortDirection(DataSortDirectionEnum.getRandomValue());
        dynamicFields.setLimit(DataLimitEnum.FIVE.getValue());
        dynamicFields.setDateTo(DataDate.builder()
                .date((futureDate).toString())
                .dateFormat(dateFormat)
                .build());
        dynamicFields.setDateFrom(DataDate.builder()
                .date((currentDate).toString())
                .dateFormat(dateFormat)
                .build());
        dynamicFields.setDataHeaderDisplay(true);
        dynamicFields.setDisplayOdds(true);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());
        return dynamicFields;
    }

    public static FootballSingleRoundBlockyFieldsModel buildMandatoryFields(FootballSingleRoundBlockyFieldsModel blockyData) {
        FootballSingleRoundBlockyFieldsModel dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setRoundChangeDropdown(RoundChangeDropdownEnum.getRandomValue());
        dynamicFields.setDisplayOdds(false);
        dynamicFields.setBookmaker(null);
        return dynamicFields;
    }
}