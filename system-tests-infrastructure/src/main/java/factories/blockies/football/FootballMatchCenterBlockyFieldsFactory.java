package factories.blockies.football;

import data.constants.EventStatusType;
import data.models.blockymodels.football.FootballMatchCenterBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.v2.MatchV2Model;
import data.models.liveblogapi.Team;
import data.models.searchapi.ParticipantDetailsModel;
import data.models.searchapi.ParticipantModel;
import data.models.searchapi.ResultModel;
import data.widgets.options.enums.DataRefreshTimeEnum;

import java.util.List;

public class FootballMatchCenterBlockyFieldsFactory {

    public static FootballMatchCenterBlockyFieldsModel buildDynamicFieldsOnly(ResultModel teamModel, ResultModel event,
                                                                              Bookmaker bookmaker) {
        return FootballMatchCenterBlockyFieldsModel.builder()
                .team(teamModel)
                .event(event)
                .displayOdds(event.getStatusType().equals(EventStatusType.FINISHED.toString()))
                .bookmaker(event.getStatusType().equals(EventStatusType.FINISHED.toString()) ? bookmaker : null)
                .build();
    }

    public static FootballMatchCenterBlockyFieldsModel buildDynamicFieldsOnly(Team teamModel, MatchV2Model event) {
        return FootballMatchCenterBlockyFieldsModel.builder()
                .team(
                        ResultModel
                                .builder()
                                .id(teamModel.getId())
                                .name(teamModel.getName())
                                .build())
                .event(
                        ResultModel
                                .builder()
                                .id(event.getId())
                                .statusType(event.getStatus().getType())
                                .name("%s - %s".formatted(event.getHomeTeam().getName(), event.getAwayTeam().getName()))
                                .participantDetails(
                                        List.of(
                                                ParticipantDetailsModel.builder().participant(
                                                        ParticipantModel.builder()
                                                                .name(event.getHomeTeam().getName())
                                                                .id(event.getHomeTeam().getId())
                                                                .build()).build(),
                                                ParticipantDetailsModel.builder().participant(
                                                        ParticipantModel.builder()
                                                                .name(event.getAwayTeam().getName())
                                                                .id(event.getAwayTeam().getId())
                                                                .build()).build()))
                                .build())
                .build();
    }

    public static FootballMatchCenterBlockyFieldsModel buildDynamicFieldsOnly(FootballMatchCenterBlockyFieldsModel blockyData) {
        return FootballMatchCenterBlockyFieldsModel.builder()
                .team(blockyData.getTeam())
                .event(blockyData.getEvent())
                .bookmaker(blockyData.getBookmaker())
                .build();
    }

    public static FootballMatchCenterBlockyFieldsModel buildAllFields(FootballMatchCenterBlockyFieldsModel blockyData) {
        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setDisplayMainEvent(true);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static FootballMatchCenterBlockyFieldsModel buildMandatoryFields(FootballMatchCenterBlockyFieldsModel blockyData) {
        return FootballMatchCenterBlockyFieldsModel.builder()
                .team(blockyData.getTeam())
                .event(blockyData.getEvent())
                .build();
    }
}
