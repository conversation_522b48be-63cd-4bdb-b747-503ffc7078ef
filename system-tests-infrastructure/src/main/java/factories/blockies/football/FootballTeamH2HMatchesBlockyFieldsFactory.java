package factories.blockies.football;

import data.constants.api.queryparamenums.StatusType;
import data.constants.enums.MatchDataElementsStatisticsEnum;
import data.models.articles.NestedMatch;
import data.models.blockymodels.football.FootballTeamH2HMatchesBlockyFieldsModel;
import data.models.footballapi.teams.MatchesV2TeamModel;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.options.enums.DataRefreshTimeEnum;

import java.util.ArrayList;

public class FootballTeamH2HMatchesBlockyFieldsFactory {

    public static FootballTeamH2HMatchesBlockyFieldsModel buildDynamicFieldsOnly(MatchesV2TeamModel teamOne, MatchesV2TeamModel teamTwo, MatchV2Model match, NestedMatch matchV1) {
        return FootballTeamH2HMatchesBlockyFieldsModel.builder()
                .teamOne(teamOne)
                .teamTwo(teamTwo)
                .match(match)
                .build();
    }

    public static FootballTeamH2HMatchesBlockyFieldsModel buildDynamicFieldsOnly(FootballTeamH2HMatchesBlockyFieldsModel blockyData) {
        return FootballTeamH2HMatchesBlockyFieldsModel.builder()
                .teamOne(blockyData.getTeamOne())
                .teamTwo(blockyData.getTeamTwo())
                .match(blockyData.getMatch())
                .build();
    }

    public static FootballTeamH2HMatchesBlockyFieldsModel buildAllFields(FootballTeamH2HMatchesBlockyFieldsModel blockyData) {
        var statistics = new ArrayList<MatchDataElementsStatisticsEnum>();
        statistics.add(MatchDataElementsStatisticsEnum.GOALS);
        statistics.add(MatchDataElementsStatisticsEnum.CORNERS);
        statistics.add(MatchDataElementsStatisticsEnum.POSSESSION);

        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setStatistics(statistics);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.MEDIUM);
        if (blockyData.getMatch().getStatus().getType().equals(StatusType.NOT_STARTED.toString())) {
            dynamicFields.setDisplayOdds(true);
            dynamicFields.setBookmaker(blockyData.getBookmaker());
        }
        dynamicFields.setDisplayHeader(blockyData.isDisplayHeader());
        dynamicFields.setStatistics(statistics);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static FootballTeamH2HMatchesBlockyFieldsModel buildMandatoryFields(FootballTeamH2HMatchesBlockyFieldsModel blockyData) {
        return FootballTeamH2HMatchesBlockyFieldsModel.builder()
                .teamOne(blockyData.getTeamOne())
                .teamTwo(blockyData.getTeamTwo())
                .match(blockyData.getMatch())
                .statistics(new ArrayList<>())
                .build();
    }
}