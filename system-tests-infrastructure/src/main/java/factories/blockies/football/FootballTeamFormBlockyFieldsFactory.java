package factories.blockies.football;

import data.models.blockymodels.football.FootballFormBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.tournamentseason.TournamentSeasonModel;
import data.models.related.Tournament;
import data.widgets.options.enums.DataRefreshTimeEnum;

public class FootballTeamFormBlockyFieldsFactory {

    public static FootballFormBlockyFieldsModel buildDynamicFieldsOnly(FootballFormBlockyFieldsModel blockyData) {
        return FootballFormBlockyFieldsModel.builder()
                .team(blockyData.getTeam())
                .stage(blockyData.getStage())
                .season(blockyData.getSeason())
                .build();
    }

    public static FootballFormBlockyFieldsModel buildDynamicFieldsOnly(TeamModel team, TournamentSeasonModel season, Tournament tournament) {
        return FootballFormBlockyFieldsModel.builder()
                .team(team)
                .season(season)
                .stage(tournament)
                .build();
    }

    public static FootballFormBlockyFieldsModel buildAllFields(FootballFormBlockyFieldsModel blockyData) {
        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setLimit("5");
        dynamicFields.setTitle("Test Title");
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());

        return dynamicFields;
    }

    public static FootballFormBlockyFieldsModel buildMandatoryFields(FootballFormBlockyFieldsModel blockyData) {
        return buildDynamicFieldsOnly(blockyData);
    }
}