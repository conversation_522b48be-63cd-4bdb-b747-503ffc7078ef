package factories.blockies.basketball;

import data.models.basketball.competition.CompetitionModel;
import data.models.basketball.season.SeasonDetailsModel;
import data.models.basketball.season.Stage;
import data.models.blockymodels.basketball.BasketballStandingsBlockyFieldsModel;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DefaultHeaderOptionEnum;

public class BasketballStandingsBlockyFieldsFactory {

    public static BasketballStandingsBlockyFieldsModel buildDynamicFieldsOnly(CompetitionModel competition, SeasonDetailsModel season,
                                                                              Stage stage) {
        return BasketballStandingsBlockyFieldsModel.builder()
                .competition(competition)
                .season(season)
                .stage(stage)
                .build();
    }

    public static BasketballStandingsBlockyFieldsModel buildDynamicFieldsOnly(BasketballStandingsBlockyFieldsModel blockyData) {
        return BasketballStandingsBlockyFieldsModel.builder()
                .competition(blockyData.getCompetition())
                .season(blockyData.getSeason())
                .stage(blockyData.getStage())
                .build();
    }

    public static BasketballStandingsBlockyFieldsModel buildAllFields(BasketballStandingsBlockyFieldsModel blockyData) {
        var dynamicFields = buildDynamicFieldsOnly(blockyData);
        dynamicFields.setDefaultHeaderOption(DefaultHeaderOptionEnum.DIVISION);
        dynamicFields.setDataHeaderDisplay(true);
        dynamicFields.setRefreshTime(DataRefreshTimeEnum.getRandomValue());
        return dynamicFields;
    }

    public static BasketballStandingsBlockyFieldsModel buildMandatoryFields(BasketballStandingsBlockyFieldsModel blockyData) {
        return BasketballStandingsBlockyFieldsModel.builder()
                .competition(blockyData.getCompetition())
                .build();
    }
}