package plugins.visualregression;

import org.openqa.selenium.Dimension;
import services.VisualRegressionService;
import solutions.bellatrix.core.plugins.Plugin;
import solutions.bellatrix.core.plugins.TestResult;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.services.App;

import java.lang.reflect.Method;

public class VisualRegressionTestingPlugin extends Plugin {
    private App app;

    public VisualRegressionTestingPlugin() {
        this.app = new App();
    }

    @Override
    public void postAfterTest(TestResult testResult, Method memberInfo, Throwable failedTestException) {
        VisualRegressionService.stop();
    }

    @Override
    public void preBeforeTest(TestResult testResult, Method memberInfo) {
        VisualRegression configuration = getVisualRegressionConfiguration(memberInfo);
        if (configuration != null) {
            VisualRegressionService.initService(configuration);
            VisualRegressionService.start();

            // Resize according to target viewport
            Log.info("Resizing browser to resolution: " + configuration.viewportSize());
            app.browser().getWrappedDriver().manage().window().setSize(new Dimension(configuration.viewportSize().getWidth(), configuration.viewportSize().getHeight()));
            var actualSize = app.browser().getWrappedDriver().manage().window().getSize();
            Log.info("Browser resized to resolution: %s x %s".formatted(actualSize.getWidth(), actualSize.getHeight()));
        }
    }

    private VisualRegression getVisualRegressionConfiguration(Method memberInfo) {
        var classAttribute = (VisualRegression)memberInfo.getDeclaringClass().getDeclaredAnnotation(VisualRegression.class);
        var methodAttribute = (VisualRegression)memberInfo.getDeclaredAnnotation(VisualRegression.class);
        if (methodAttribute != null) {
            return  methodAttribute;
        }

        return classAttribute;
    }
}