package plugins.authentication;

import lombok.AccessLevel;
import lombok.Getter;
import solutions.bellatrix.core.configuration.ConfigurationService;

@Getter
public enum User {

    NONE("", "", "", ""),
    DEFAULT("2020082809425652151", "QA Default", "", ""),
    FULLSETUP("", "QA Multi Automation", "2023042708272414494", "2020022010025370305");

    private final String id;
    private final String fullName;
    @Getter(AccessLevel.PRIVATE)
    private final String idStaging;
    @Getter(AccessLevel.PRIVATE)
    private final String idIntegration;

    User(String id, String fullName, String idStaging, String idIntegration) {
        this.id = id;
        this.fullName = fullName;
        this.idStaging = idStaging;
        this.idIntegration = idIntegration;
    }

    public String getId() {
        if (ConfigurationService.getEnvironment().equals("integration")) {
            return idIntegration;
        }
        return idStaging;
    }
}