package plugins.authentication.dashboardpage;

import data.configuration.SportalSettings;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.pages.WebPage;

public class DashboardPage extends WebPage<Map, Asserts> {
    private SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);
    @Override
    protected String getUrl() { return sportalSettings.getCmsInstanceUrl() + "#/dashboard"; }

    @Override
    protected void waitForPageLoad() {
        map().heading().toExist().waitToBe();
        map().heading().getText().contains("Dashboard");
    }
}
