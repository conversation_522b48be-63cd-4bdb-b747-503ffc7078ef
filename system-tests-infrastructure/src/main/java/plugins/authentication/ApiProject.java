package plugins.authentication;

import lombok.Getter;

@Getter
public enum ApiProject {

    NONE("", ""),
    SPORTS_DATA_SPORTAL365_QA("Sport Data QA", "sports-data-sportal365-qa"),
    SPORTS_DATA_SPORTAL365_AUTOMATION("Sport Data Automation", "sports-data-sportal365-automation");

    private final String value;
    private final String domain;

    ApiProject(String value, String domain) {
        this.value = value;
        this.domain = domain;
    }
}