package plugins.authentication.loginpage;

import data.configuration.SportalSettings;
import plugins.authentication.User;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.core.utilities.SecretsResolver;
import solutions.bellatrix.web.pages.WebPage;

public class LoginPage extends WebPage<Map, Asserts> {
    private SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);

    @Override
    protected String getUrl() {
        return sportalSettings.getCmsInstanceUrl() + "#/login";
    }

    @Override
    protected void waitForPageLoad() {
        map().heading().toExist().waitToBe();
    }

    public void login(User user) {
        String username = "";
        String password = "";

        switch (user){
            case DEFAULT:
                username = SecretsResolver.getSecret(ConfigurationService.get(SportalSettings.class).getCmsSingleUserName());
                password = SecretsResolver.getSecret(ConfigurationService.get(SportalSettings.class).getCmsSinglePassword());
                break;
            case FULLSETUP:
                username = SecretsResolver.getSecret(ConfigurationService.get(SportalSettings.class).getCmsMultiUserName());
                password = SecretsResolver.getSecret(ConfigurationService.get(SportalSettings.class).getCmsMultiPassword());
                break;
        }

        waitForPageLoad();
        map().usernameTextField().setText(username);
        map().passwordTextField().setText(password);
        map().loginButton().click();
    }
}
