package plugins.authentication;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@UtilityClass
public class ExecutionContext {
    private static ThreadLocal<Map<String, User>> currentUser;
    private static ThreadLocal<Map<String, Project>> currentProject;

    static {
        currentUser = new ThreadLocal<>();
        currentUser.set(new HashMap<String, User>());

        currentProject = new ThreadLocal<>();
        currentProject.set(new HashMap<String, Project>());
    }

    public static User getUser(Class<?> classType) {
        if (currentUser.get().containsKey(classType.getName())) {
            return currentUser.get().get(classType.getName());
        }

        return User.NONE;
    }

    public static void setUser(Class<?> classType, User user) {
        currentUser.get().put(classType.getName(), user);
    }

    public static Project getProject(Class<?> classType) {
        if (currentProject.get().containsKey(classType.getName())) {
            return currentProject.get().get(classType.getName());
        }

        return Project.NONE;
    }

    public static void setProject(Class<?> classType, Project project) {
        currentProject.get().put(classType.getName(), project);
    }
}
