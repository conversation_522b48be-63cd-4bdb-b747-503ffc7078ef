package categories;

public interface WidgetsTags {
    
    String FOOTBALL = "football";
    String BASKETBALL = "basketball";
    String TENNIS = "tennis";
    String ICE_HOCKEY = "iceHockey";
    String MULTISPORT = "multisport";
    String LIVESCORE = "liveScore";
    String KNOCKOUT = "knockout";
    String PLAYER = "player";
    String PROFILE_WIDGETS = "profileWidgets";
    String PROGRAMME_WIDGETS = "programmeWidgets";
    String EVENTS_WIDGETS = "eventsWidgets";
    String STATISTICS_WIDGETS = "statisticsWidgets";
    String ODDS = "odds";
    String MOSTDECORATEDPLAYERS = "mostDecoratedPlayers";
    String TOPSCORERS = "topScorers";
    String PLAYERH2H = "playerh2h";
    String MATCHES_H2H = "matchesH2H";
    String SINGLEEVENT = "singleEvent";
    String EVENT_STATUS_WIDGETS = "eventStatusWidgets";
    String STANDINGS = "standings";
    String TEAM = "team";
    String MATCH_CENTER = "matchCenter";
    String LINEUPS = "lineups";
    String TEAMH2H = "teamh2h";
    String TEAMPROGRAMME = "teamProgramme";
    String TOURNAMENT_PROGRAMME = "tournamentProgramme";
    String ATHLETE_PROGRAMME = "athleteProgramme";
    String TEAMSQUAD = "teamSquad";
    String RANKING = "ranking";
    String PLAYOFF = "playoff";
    String MULTI_WIDGETS = "multiWidgetsOnThePage";
    String DARK_THEME = "darkTheme";
    String REFRESH_TIME_ATTRIBUTE = "refreshTime";
    String STAKES = "stakes";
}