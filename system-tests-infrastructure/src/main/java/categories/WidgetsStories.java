package categories;

public interface WidgetsStories {
    String LIVESCORE = "livescoreWidget";
    String STANDINGS = "standingsWidget";
    String KNOCKOUT = "knockoutWidget";
    String SINGLEEVENT = "singleEventWidget";
    String PROGRAMME = "programmeWidget";
    String H2H = "H2HWidget";
    String PROFILE = "profileWidget";
    String ODDS = "oddsWidget";
    String CORE_ATTRIBUTES = "coreAttributes";
    String SPORT_DATA_ATTRIBUTES = "dataAttributes";
    String DATA_ENTITY_LINKS = "dataEntityLinks";
    String DATA_THEME = "dataTheme";
    String DATA_LABELS = "dataLabels";
    String DATA_ELEMENTS = "dataElements";
    String MULTISPORT = "multisportWidget";
    String LINEUPS = "lineupsWidget";
    String STAKES = "stakesWidget";
    String SDK_OPTIONS = "sdkOptions";
}