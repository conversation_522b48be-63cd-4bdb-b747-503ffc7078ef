package categories;

public interface APITags {

    String SCHEMA_TESTS_API = "schemaTestsApi";
    String CONTENT_API = "contentApi";
    String LIVE_BLOG_API = "liveBlogApi";
    String BASKETBALL_API = "basketballApi";
    String FOOTBALL_API = "footballApi";
    String TENNIS_API = "tennisApi";
    String MULTI_SPORT_API = "multiSportApi";
    String SEARCH_API_V2 = "searchApiV2";
    String PLAYOFFS_API = "playoffsApi";
    String STANDINGS_API = "standingsApi";
    String COLLABORATION_API = "collaborationApi";
    String CLIENT_API = "clientApi";
    String AUTOTAGGING_API_V2 = "autotaggingApiV2";
    String FORM_GUIDE_API = "formGuideApi";
    String CUSTOMIZATION_API = "customizationApi";
    String CONTENT_API_ARTICLES = "contentApiArticles";
    String CONTENT_API_VIDEOS = "contentApiVideos";
    String CONTENT_API_WIKI_PAGES = "contentApiWikiPages";
    String CONTENT_API_TAGS = "contentApiTags";
    String CONTENT_API_GALLERIES = "contentApiVGalleries";
    String CONTENT_API_V2_ARTICLES = "contentApiV2Articles";
    String CONTENT_API_LISTS = "contentApiLists";
    String CONTENT_API_LISTS_AUTOMATED = "contentApiListsAutomated";
    String CONTENT_API_RELATED_ARTICLES = "contentApiRelatedArticles";
    String CONTENT_API_RELATED_VIDEOS = "contentApiRelatedVideos";
    String CONTENT_API_ARTICLES_SEARCH = "contentApiArticlesSearch";
    String CONTENT_API_V2_ARTICLES_SEARCH = "contentApiV2ArticlesSearch";
    String SPORT_ENTITY_CUSTOMIZATION_API = "sportEntityCustomizationApi";
    String ARTICLE_SCHEDULER_API = "articleSchedulerApi";
    String ODDS_API = "oddsApi";
    String IMAGE_API = "imageApi";
    String WIDGETS_API = "widgetsApi";
    String TAG_SCORE_API = "tagScoreApi";
    String SPORTS_STATISTICS_API = "sportsStatisticsApi";
    String CONTENT_API_NEWS_FORGE = "contentApiNewsForge";
}