package repositories.standings;

import data.constants.apiurls.StandingApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.standingapi.StandingTypeModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import repositories.core.StandingsApiCredentials;

public class StandingsStandingTypesHttpRepository extends SportApiHttpRepository<StandingTypeModel, StandingTypeModel, MetaModel> {

    public StandingsStandingTypesHttpRepository(Project projectName) {
        super(StandingTypeModel.class,
                StandingTypeModel.class,
                MetaModel.class,
                StandingsApiCredentials.getProjectApiName(projectName),
                StandingsApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(StandingApiUrl.STANDING_TYPES.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}