package repositories.standings.availablestandings;

import data.constants.StringConstants;
import data.constants.apiurls.StandingApiUrl;
import data.models.MetaModel;
import data.models.standingapi.AvailableStandingsModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import repositories.core.StandingsApiCredentials;

public class StandingsAvailableStandingsHttpRepository extends SportApiHttpRepository<AvailableStandingsModel, AvailableStandingsModel, MetaModel> {

    public StandingsAvailableStandingsHttpRepository(Project projectName, String sport) {
        super(AvailableStandingsModel.class,
                AvailableStandingsModel.class,
                MetaModel.class,
                StandingsApiCredentials.getProjectApiName(projectName),
                StandingsApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(StandingApiUrl.AVAILABLE_STANDINGS.getUrl().formatted(sport))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}