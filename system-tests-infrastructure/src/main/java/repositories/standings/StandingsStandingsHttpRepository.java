package repositories.standings;

import data.constants.apiurls.StandingApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.standingapi.StandingsDataModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import repositories.core.StandingsApiCredentials;

public class StandingsStandingsHttpRepository extends SportApiHttpRepository<StandingsDataModel, StandingsDataModel, MetaModel> {

    public StandingsStandingsHttpRepository(Project projectName, String sport) {
        super(StandingsDataModel.class,
                StandingsDataModel.class,
                MetaModel.class,
                StandingsApiCredentials.getProjectApiName(projectName),
                StandingsApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBase<PERSON>ri(StandingApiUrl.STANDINGS.getUrl().formatted(sport))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}