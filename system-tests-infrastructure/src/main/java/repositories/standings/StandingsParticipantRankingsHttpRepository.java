package repositories.standings;

import data.constants.StringConstants;
import data.constants.apiurls.StandingApiUrl;
import data.models.MetaModel;
import data.models.standingapi.RankingsModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import repositories.core.StandingsApiCredentials;

import java.util.List;
import java.util.Map;

public class StandingsParticipantRankingsHttpRepository extends SportApiHttpRepository<RankingsModel, RankingsModel, MetaModel> {

    public StandingsParticipantRankingsHttpRepository(Project projectName, String sport) {
        super(RankingsModel.class,
                RankingsModel.class,
                MetaModel.class,
                StandingsApiCredentials.getProjectApiName(projectName),
                StandingsApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(StandingApiUrl.PARTICIPANT_RANKINGS.getUrl().formatted(sport))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<List<RankingsModel>> getAvailableRankings(Map<String, Object> requiredQueryParams) {
        Response response = request().queryParams(requiredQueryParams).get("available");
        checkFor(response);
        List<RankingsModel> entitiesFromResponse = getEntitiesFromResponse(response);

        return new ApiResponse(response, entitiesFromResponse);
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}