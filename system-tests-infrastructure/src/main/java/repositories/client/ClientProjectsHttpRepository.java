package repositories.client;

import data.constants.StringConstants;
import data.constants.apiurls.ClientApiUrl;
import data.models.MetaModel;
import data.models.clientapi.ProjectModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ClientApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class ClientProjectsHttpRepository extends SportApiHttpRepository<ProjectModel, ProjectModel, MetaModel> {

    public ClientProjectsHttpRepository(Project projectName) {
        super(ProjectModel.class,
                ProjectModel.class,
                MetaModel.class,
                ClientApiCredentials.getProjectApiName(projectName),
                ClientApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ClientApiUrl.PROJECTS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}