package repositories.client;

import data.constants.StringConstants;
import data.constants.apiurls.ClientApiUrl;
import data.models.MetaModel;
import data.models.clientapi.ProjectFeatureModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ClientApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class ClientProjectsFeatureHttpRepository extends SportApiHttpRepository<ProjectFeatureModel, ProjectFeatureModel, MetaModel> {

    public ClientProjectsFeatureHttpRepository(Project projectName) {
        super(ProjectFeatureModel.class,
                ProjectFeatureModel.class,
                MetaModel.class,
                ClientApiCredentials.getProjectApiName(projectName),
                ClientApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBase<PERSON>ri(ClientApiUrl.FEATURE.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}