package repositories.image;

import data.constants.ImageApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.images.api.ImageUploadResponseModel;
import data.models.images.watermarks.WatermarkModel;
import data.models.images.watermarks.WatermarksImageModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.commons.lang3.StringUtils;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.HttpRepository;
import repositories.core.ImageApiCredentials;
import repositories.core.RestAssuredConfigFactory;

import java.io.File;
import java.util.Collections;
import java.util.Map;

public class ImageWatermarksHttpRepository extends HttpRepository<WatermarkModel, WatermarkModel, MetaModel> {

    public ImageWatermarksHttpRepository(Project projectName) {
        super(WatermarkModel.class,
                WatermarkModel.class,
                MetaModel.class,
                new ImageApiCredentials().getProjectApiName(projectName),
                new ImageApiCredentials().getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(ImageApiUrl.WATERMARKS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public ApiResponse<WatermarkModel> create(WatermarkModel toBeCreated) {
        return create(toBeCreated, Collections.emptyMap());
    }

    @Override
    public ApiResponse<WatermarkModel> create(WatermarkModel toBeCreated, Map<String, Object> params) {
        Response response = buildRequest(toBeCreated, params).post();
        return new ApiResponse<>(response, handleResponse(response));
    }

    public ApiResponse<WatermarkModel> setWatermarkDefault(String watermarkId) {
        RequestSpecification request = request().contentType(StringConstants.MULTIPART_FORM_DATA_STRING);
        addFileMultiPart(request, StringConstants.ID_STRING, watermarkId);
        Response response = request.put("/default");
        return new ApiResponse<>(response, handleResponse(response));
    }

    public Response delete(WatermarkModel watermark) {
        Response response = null;

        if (watermark != null && watermark.getId() != null) {
            response = super.delete(watermark.getId());
        }
        return response;
    }

    @Override
    public ApiResponse<WatermarkModel> update(String id, WatermarkModel toBeUpdated) {
        return super.update(id, toBeUpdated);
    }

    public ApiResponse<ImageUploadResponseModel> addWatermarkToImage(WatermarksImageModel watermarkImage) {
        RequestSpecification request = request().contentType(StringConstants.MULTIPART_FORM_DATA_STRING);
        addFileMultiPart(request, StringConstants.DELETE_EXISTING_STRING, watermarkImage.getDeleteExisting());
        addMultiPart(request, StringConstants.IMAGE_PATH_STRING, watermarkImage.getImagePath());
        addMultiPart(request, StringConstants.WATERMARK_ID_STRING, watermarkImage.getWatermarkId());

        Response response = request.post("/image");
        return new ApiResponse<>(response, response.as(ImageUploadResponseModel.class));
    }

    private RequestSpecification buildRequest(WatermarkModel toBeCreated, Map<String, Object> params) {
        RequestSpecification request = request().contentType(StringConstants.MULTIPART_FORM_DATA_STRING);
        addFileMultiPart(request, StringConstants.FILE_STRING, toBeCreated.getFile());
        addMultiPart(request, StringConstants.OPACITY_STRING, toBeCreated.getOpacity());
        addMultiPart(request, StringConstants.POSITION_STRING, toBeCreated.getPosition());

        if (!params.isEmpty()) {
            request.queryParams(params);
        }
        return request;
    }

    private void addMultiPart(RequestSpecification request, String name, String value) {
        if (StringUtils.isNotBlank(value)) {
            request.multiPart(name, value);
        }
    }

    private void addFileMultiPart(RequestSpecification request, String name, String filePath) {
        if (StringUtils.isNotBlank(filePath)) {
            File file = new File(filePath);
            if (file.exists()) {
                request.multiPart(name, file);
            } else {
                request.multiPart(name, filePath);
            }
        }
    }

    private WatermarkModel handleResponse(Response response) {
        if (successCodesList.contains(response.getStatusCode())) {
            return getEntityFromResponse(response);
        } else {
            logEntityNot(CREATED, response);
            return null;
        }
    }
}