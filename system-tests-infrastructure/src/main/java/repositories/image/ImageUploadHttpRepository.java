package repositories.image;

import data.constants.ImageApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.images.api.ImageUploadRequestModel;
import data.models.images.api.ImageUploadResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.HttpRepository;
import repositories.core.ImageApiCredentials;
import repositories.core.RestAssuredConfigFactory;

import java.io.File;
import java.util.Collections;
import java.util.Map;

public class ImageUploadHttpRepository extends HttpRepository<ImageUploadRequestModel, ImageUploadResponseModel, MetaModel> {

    public ImageUploadHttpRepository(Project projectName) {
        super(ImageUploadRequestModel.class,
                ImageUploadResponseModel.class,
                MetaModel.class,
                new ImageApiCredentials().getProjectApiName(projectName),
                new ImageApiCredentials().getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(ImageApiUrl.UPLOAD.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public ApiResponse<ImageUploadResponseModel> create(ImageUploadRequestModel toBeCreated) {
        return create(toBeCreated, Collections.emptyMap());
    }

    @Override
    public ApiResponse<ImageUploadResponseModel> create(ImageUploadRequestModel toBeCreated, Map<String, Object> params) {
        Response response = buildRequest(toBeCreated, params).post();
        ImageUploadResponseModel entity = handleResponse(response);
        return new ApiResponse<>(response, entity);
    }

    private RequestSpecification buildRequest(ImageUploadRequestModel toBeCreated, Map<String, Object> params) {
        RequestSpecification request = request().contentType("multipart/form-data");

        if (toBeCreated.getFile() != null && !toBeCreated.getFile().isEmpty()) {
            request.multiPart(StringConstants.FILE_STRING, new File(toBeCreated.getFile()))
                    .multiPart("position", toBeCreated.getPosition())
                    .multiPart("watermark_id", toBeCreated.getWatermarkId());
        } else {
            request.multiPart(StringConstants.FILE_STRING, StringConstants.EMPTY_STRING);
        }

        if (!params.isEmpty()) {
            request.queryParams(params);
        }

        return request;
    }

    private ImageUploadResponseModel handleResponse(Response response) {
        if (!successCodesList.contains(response.getStatusCode())) {
            logEntityNot(CREATED, response);
            return null;
        }
        return getEntityFromResponse(response);
    }
}