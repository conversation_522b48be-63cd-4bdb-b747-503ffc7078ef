package repositories.converters;

import com.google.gson.*;
import data.models.footballapi.search.PersonResultModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import data.models.liveblogapi.post.SportTag;
import repositories.core.ApiEntity;

import java.lang.reflect.Type;

public class ContentApiLivePostsSportsTagDeserializer<TEntity extends ApiEntity> implements JsonDeserializer<LiveBlogPostModel> {
    private final Gson gson;

    public ContentApiLivePostsSportsTagDeserializer() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gson = gsonBuilder.setPrettyPrinting().create();
    }

    @Override
    public LiveBlogPostModel deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        LiveBlogPostModel liveBlogResponse = gson.fromJson(jsonElement, type);

        if (jsonElement.getAsJsonObject().has("sport_tags")) {
            var sportTagsArray = jsonElement.getAsJsonObject().get("sport_tags").getAsJsonArray();

            for (int sportTagNumber = 0; sportTagNumber < sportTagsArray.size(); sportTagNumber++) {
                var sportTagObject =  sportTagsArray.get(sportTagNumber).getAsJsonObject();
                liveBlogResponse.getSportTags().remove(sportTagNumber);

                if (sportTagObject.has("country")) {
                    var sportTag = gson.fromJson(sportTagObject, (Type)PersonResultModel.class);
                    liveBlogResponse.getSportTags().add(sportTag);
                } else {
                    var sportTag = gson.fromJson(sportTagObject, (Type)SportTag.class);
                    liveBlogResponse.getSportTags().add(sportTag);
                }
            }
        }

        return liveBlogResponse;
    }
}