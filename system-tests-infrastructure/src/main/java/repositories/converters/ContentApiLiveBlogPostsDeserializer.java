package repositories.converters;

import com.google.gson.*;
import data.constants.StringConstants;
import data.models.articles.ArticleResponseModel;
import data.models.banners.BannerModel;
import data.models.footballapi.odd_providers.OddProviderModel;
import data.models.footballapi.search.PersonResultModel;
import data.models.galleries.GalleryResponseModel;
import data.models.images.ImageModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import data.models.liveblogapi.post.Object;
import data.models.liveblogapi.post.SportTag;
import data.models.videos.VideoResponseModel;
import repositories.core.ApiEntity;
import solutions.bellatrix.web.infrastructure.EmptyObjectTypeAdapterFactory;
import solutions.bellatrix.web.infrastructure.OffsetDateTimeTypeAdapter;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

public class ContentApiLiveBlogPostsDeserializer<TEntity extends ApiEntity> implements JsonDeserializer<LiveBlogPostModel>, JsonSerializer<LiveBlogPostModel> {
    private final Gson gson;

    public ContentApiLiveBlogPostsDeserializer() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gsonBuilder.registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter());
        gsonBuilder.registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeTypeAdapter());
        gsonBuilder.registerTypeAdapterFactory(new EmptyObjectTypeAdapterFactory());
        gson = gsonBuilder.setPrettyPrinting().create();
    }

    @Override
    public LiveBlogPostModel deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        var blocksArray = jsonElement.getAsJsonObject().get("blocks").getAsJsonArray();
        LiveBlogPostModel liveBlogResponse = gson.fromJson(jsonElement, type);

        for (int blockNumber = 0; blockNumber < blocksArray.size(); blockNumber++) {
            var blockType = blocksArray.get(blockNumber).getAsJsonObject().getAsJsonObject("data").get("type").getAsString();
            var blockObject = blocksArray.get(blockNumber).getAsJsonObject().getAsJsonObject("data").getAsJsonObject("object");

            if (blockType != null) {
                switch (blockType) {
                    case "gallery":
                        var object = gson.fromJson(blockObject, (Type)GalleryResponseModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    case "article":
                        object = gson.fromJson(blockObject, (Type)ArticleResponseModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    case "video":
                        object = gson.fromJson(blockObject, (Type)VideoResponseModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    case "image":
                        object = gson.fromJson(blockObject, (Type)ImageModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    case "widgetOdds":
                        object = gson.fromJson(blockObject, (Type)OddProviderModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    case "banner":
                        object = gson.fromJson(blockObject, (Type)BannerModel.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                    default:
                        object = gson.fromJson(blockObject, (Type)Object.class);
                        liveBlogResponse.getBlocks().get(blockNumber).getData().setObject(object);
                        break;
                }
            }
        }

        if (jsonElement.getAsJsonObject().has("sport_tags")) {
            var sportTagsArray = jsonElement.getAsJsonObject().get("sport_tags").getAsJsonArray();

            for (int sportTagNumber = 0; sportTagNumber < sportTagsArray.size(); sportTagNumber++) {
                var sportTagObject = sportTagsArray.get(sportTagNumber).getAsJsonObject();
                liveBlogResponse.getSportTags().remove(sportTagNumber);

                if (sportTagObject.has("country")) {
                    var sportTag = gson.fromJson(sportTagObject, (Type)PersonResultModel.class);
                    liveBlogResponse.getSportTags().add(sportTag);
                } else {
                    var sportTag = gson.fromJson(sportTagObject, (Type)SportTag.class);
                    liveBlogResponse.getSportTags().add(sportTag);
                }
            }
        }

        return liveBlogResponse;
    }

    @Override
    public JsonElement serialize(LiveBlogPostModel liveBlogPostModel, Type type, JsonSerializationContext jsonSerializationContext) {
        JsonElement jsonElement = gson.toJsonTree(liveBlogPostModel, type);
        var sportEventJson = jsonElement.getAsJsonObject().get(StringConstants.SPORT_EVENT_STRING);
        if (sportEventJson == null) {
            return jsonElement;
        }

        var sportEventJObject = sportEventJson.getAsJsonObject();
        var eventId = sportEventJObject.get(StringConstants.ID_STRING);
        if (eventId != null) {
            jsonElement.getAsJsonObject().remove(StringConstants.SPORT_EVENT_STRING);
            jsonElement.getAsJsonObject().addProperty(StringConstants.SPORT_EVENT_STRING, eventId.getAsString());
        }

        return jsonElement;
    }
}