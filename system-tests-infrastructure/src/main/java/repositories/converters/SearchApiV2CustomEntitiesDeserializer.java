package repositories.converters;

import com.google.gson.*;
import data.constants.StringConstants;
import data.models.searchapi.*;
import solutions.bellatrix.web.infrastructure.EmptyObjectTypeAdapterFactory;
import solutions.bellatrix.web.infrastructure.OffsetDateTimeTypeAdapter;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

public class SearchApiV2CustomEntitiesDeserializer implements JsonDeserializer<CustomEntityModel> {

    private final Gson gson;

    public SearchApiV2CustomEntitiesDeserializer() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gsonBuilder.registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter());
        gsonBuilder.registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeTypeAdapter());
        gsonBuilder.registerTypeAdapterFactory(new EmptyObjectTypeAdapterFactory());
        gson = gsonBuilder.setPrettyPrinting().create();
    }

    @Override
    public CustomEntityModel deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext context) throws JsonParseException {
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        CustomEntityModel customEntityModel = gson.fromJson(jsonElement, type);

        customEntityModel.setContainedInDomain(
                deserializeProperty(jsonObject.get(StringConstants.CONTAINED_IN_DOMAIN_STRING), context, ContainedInDomainModel.class));
        customEntityModel.setContainedInPlace(
                deserializeProperty(jsonObject.get(StringConstants.CONTAINED_IN_PLACE_STRING), context, ContainedInPlaceModel.class));
        customEntityModel.setContainedInOrganization(
                deserializeProperty(jsonObject.get(StringConstants.CONTAINED_IN_ORGANIZATION_STRING), context, ContainedInOrganizationModel.class));
        customEntityModel.setNationality(
                deserializeProperty(jsonObject.get(StringConstants.NATIONALITY_STRING), context, NationalityModel.class));
        customEntityModel.setBirthPlace(
                deserializeProperty(jsonObject.get(StringConstants.BIRTH_PLACE_STRING), context, BirthPlaceModel.class));

        if (jsonObject.has(StringConstants.ROLES_STRING)) {
            JsonArray rolesArray = jsonObject.getAsJsonArray(StringConstants.ROLES_STRING);
            List<Roles> roles = new ArrayList<>();
            for (JsonElement roleElement : rolesArray) {
                roles.add(deserializeRole(roleElement, context));
            }
            customEntityModel.setRoles(roles);
        }

        return customEntityModel;
    }

    private Object deserializeProperty(JsonElement element, JsonDeserializationContext context, Class<?> clazz) {
        if (element == null) {
            return null;
        }
        return element.isJsonObject() ? context.deserialize(element, clazz) : element.getAsString();
    }

    private Roles deserializeRole(JsonElement element, JsonDeserializationContext context) {
        JsonObject roleObject = element.getAsJsonObject();
        Roles roles = Roles.builder().build();

        roles.setRole(deserializeField(roleObject.get(StringConstants.ROLE_STRING), context, RoleModel.class));
        roles.setOrganization(deserializeField(roleObject.get(StringConstants.ORGANIZATION_STRING), context, OrganizationModel.class));
        roles.setPlace(deserializeField(roleObject.get(StringConstants.PLACE_STRING), context, PlaceModel.class));

        return roles;
    }

    private Object deserializeField(JsonElement element, JsonDeserializationContext context, Class<?> clazz) {
        if (element == null) {
            return null;
        }
        return element.isJsonObject() ? context.deserialize(element, clazz) : element.getAsString();
    }
}
