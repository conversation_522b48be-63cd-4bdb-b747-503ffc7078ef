package repositories.liveblog;

import data.constants.LiveBlogApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.HttpRepository;
import repositories.core.LiveBlogApiCredentials;
import repositories.core.RestAssuredConfigFactory;

public class LiveBlogPostsHttpRepository extends HttpRepository<LiveBlogPostModel, LiveBlogPostModel, MetaModel> {
    public LiveBlogPostsHttpRepository(Project projectName, String liveBlogId) {
        super(LiveBlogPostModel.class,
                LiveBlogPostModel.class,
                MetaModel.class,
                LiveBlogApiCredentials.getProjectApiName(projectName),
                LiveBlogApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(String.format(LiveBlogApiUrl.LIVE_BLOG_POSTS.url, liveBlogId))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
