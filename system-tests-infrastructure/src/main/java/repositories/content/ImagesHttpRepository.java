package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.images.ImagesModel;
import data.models.images.ImageModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

import java.util.List;

public class ImagesHttpRepository extends HttpRepository<ImageModel, ImageModel, MetaModel> {

    public ImagesHttpRepository(Project projectName) {
        super(ImageModel.class,
                ImageModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.IMAGES.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    public ApiResponse<ImageModel> uploadImage(ImagesModel imagesModel) {
        Response response = request()
                .body(getGson().toJson(imagesModel))
                .when()
                .post();

        return new ApiResponse<>(response, getEntityFromResponse(response));
    }

    public ApiResponse<ImageModel> addWatermarkToImage(String imageId, ImagesModel imagesModel) {
        Response response = request()
                .body(getGson().toJson(imagesModel))
                .when()
                .patch(imageId);

        return new ApiResponse<>(response, getEntityFromResponse(response));
    }

    public ApiResponse<List<ImageModel>> getByPage(int page) {
        Response response = request()
                .when()
                .get("?page=" + page);

        return new ApiResponse<>(response, getEntitiesFromResponse(response));
    }
}