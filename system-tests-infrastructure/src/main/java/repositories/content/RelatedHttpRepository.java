package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.related.DataListObject;
import data.models.related.RelatedModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;
import solutions.bellatrix.core.utilities.Log;

import java.util.List;

public class RelatedHttpRepository extends HttpRepository<RelatedModel, DataListObject, MetaModel> {

    public RelatedHttpRepository(Project projectName, String entityId, String entityName) {
        super(RelatedModel.class,
                DataListObject.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(String.format(ContentApiUrl.RELATED_ENTITY.getUrl(), entityName, entityId))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<DataListObject> getById(String relatedId) {
        Response response = request()
                .get("/" + relatedId);

        DataListObject entity = null;

        if (!successCodesList.contains(response.getStatusCode())) {
            Log.error("Failed to fetch the related article!");
            Log.error("Response status code: " + response.getStatusCode());
            Log.error("Response error message: " + response.getBody().print());
        } else {
            entity = getEntityFromResponse(response);
        }

        return new ApiResponse<>(response, entity);
    }

    public ApiResponse<List<DataListObject>> createAll(List<DataListObject> toBeCreated) {
        Response response = request()
                .body(getGson().toJson(toBeCreated))  // Convert ArrayList<DataListObject> to JSON
                .when()
                .post();

        List<DataListObject> entity = null;

        if (!successCodesList.contains(response.getStatusCode())) {
            Log.error("The entity is not created!");
            Log.error("Response status code: " + response.getStatusCode());
            Log.error("Response error message: " + response.getBody().print());
        } else {
            entity = getEntitiesFromResponse(response);
        }

        return new ApiResponse<>(response, entity);
    }

    public ApiResponse<DataListObject> createWithNoResponse(RelatedModel model) {
        Response response = request()
                .body(getGson().toJson(model))
                .when()
                .post();

        DataListObject entity = null;

        if (!successCodesList.contains(response.getStatusCode())) {
            Log.error("The entity is not created!");
            Log.error("Response status code: " + response.getStatusCode());
            Log.error("Response error message: " + response.getBody().print());
        } else {
            entity = getEntityFromResponse(response);
        }

        return new ApiResponse<>(response, entity);
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}