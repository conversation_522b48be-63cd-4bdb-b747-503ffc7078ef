package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.galleries.GalleryRequestModel;
import data.models.galleries.GalleryResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

import java.util.List;

public class GalleriesHttpRepository extends HttpRepository<GalleryRequestModel, GalleryResponseModel, MetaModel> {

    public GalleriesHttpRepository(Project projectName) {
        super(GalleryRequestModel.class,
                GalleryResponseModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.GALLERIES.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    public ApiResponse<List<GalleryResponseModel>> getByTitle(String title) {
        Response response = request()
                .when()
                .get("/search?query=title=" + title);

        return new ApiResponse<>(response, getEntitiesFromResponse(response));
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}