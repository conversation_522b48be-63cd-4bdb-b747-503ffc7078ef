package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.contentapi.latest.LatestModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

public class LatestHttpRepository extends HttpRepository<LatestModel, LatestModel, MetaModel> {

    public LatestHttpRepository(Project projectName) {
        super(LatestModel.class,
                LatestModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.LATEST.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}