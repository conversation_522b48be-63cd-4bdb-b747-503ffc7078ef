package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.liveblogapi.LiveBlogModel;
import data.models.liveblogapi.LiveBlogResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

import java.util.List;
import java.util.Map;

public class ContentLiveBlogHttpRepository extends HttpRepository<LiveBlogModel, LiveBlogResponseModel, MetaModel> {
    public ContentLiveBlogHttpRepository(Project projectName) {
        super(LiveBlogModel.class,
                LiveBlogResponseModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.LIVE_BLOGS.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    public ApiResponse<List<LiveBlogResponseModel>> getAllEntities() {
        var limit = "20";
        var entitiesList = getAll(Map.of("limit", limit));
        var metaData = getMetaData().getResult();

        if(metaData.getTotal() > metaData.getCount()) {
            limit = metaData.getTotal().toString();
            entitiesList = getAll(Map.of("limit", limit));
        }

        return entitiesList;
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
