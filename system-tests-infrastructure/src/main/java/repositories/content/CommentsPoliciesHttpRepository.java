package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.common.CommonModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

public class CommentsPoliciesHttpRepository extends HttpRepository<CommonModel, CommonModel, MetaModel> {

    public CommentsPoliciesHttpRepository(Project projectName, String contentType) {
        super(CommonModel.class,
                CommonModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.COMMENTS_POLICIES.getUrl().formatted(contentType))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}