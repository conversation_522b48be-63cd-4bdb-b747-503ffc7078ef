package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.lists.ListModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

public class ListHttpRepository extends HttpRepository<ListModel, ListModel, MetaModel> {

    public ListHttpRepository(Project projectName) {
        super(ListModel.class,
                ListModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.LISTS.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public ApiResponse<ListModel> update(ListModel toBeUpdated) {
        var response = request()
                .body(getGson().toJson(toBeUpdated))
                .when()
                .patch("/" + toBeUpdated.getEntityId());

        if (!successCodesList.contains(response.getStatusCode())) {
            logEntityNot(UPDATED, response);
        }

        ListModel entity = getEntityFromResponse(response);
        return new ApiResponse<>(response, entity);
    }
}