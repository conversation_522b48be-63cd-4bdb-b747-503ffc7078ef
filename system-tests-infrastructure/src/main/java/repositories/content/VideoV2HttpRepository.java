package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.videos.VideoRequestModel;
import data.models.videos.VideoResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

import java.util.List;

public class VideoV2HttpRepository extends HttpRepository<VideoRequestModel, VideoResponseModel, MetaModel> {

    public VideoV2HttpRepository(Project projectName) {
        super(VideoRequestModel.class,
                VideoResponseModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.VIDEOS_V2.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }

    public List<VideoResponseModel> search(String query) {
        return getAll(String.format(StringConstants.SEARCH_QUERY_STRING, query)).getResult();
    }
}