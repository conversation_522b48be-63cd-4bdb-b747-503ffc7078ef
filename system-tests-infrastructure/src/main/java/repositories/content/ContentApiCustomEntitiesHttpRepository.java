package repositories.content;

import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.searchapi.CustomEntityModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ContentApiCustomEntitiesHttpRepository extends HttpRepository<CustomEntityModel, CustomEntityModel, MetaModel> {

    public ContentApiCustomEntitiesHttpRepository(Project projectName) {
        super(CustomEntityModel.class,
                CustomEntityModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(projectName),
                ContentApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(ContentApiUrl.CUSTOM_ENTITIES.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<List<CustomEntityModel>> getDomains(Map<String, Object>... optionalParams) {
        return getAll("/domains", optionalQueryParams(optionalParams));
    }

    private ApiResponse<CustomEntityModel> getEntityByIdentifier(String entityType, String identifier) {
        return get(String.format("%s/%s", entityType, identifier), new HashMap<>());
    }

    public ApiResponse<CustomEntityModel> getEntityByTypeAndSlug(String entityType, String slug) {
        return getEntityByIdentifier(entityType, slug);
    }

    public ApiResponse<CustomEntityModel> getEntityByTypeAndId(String entityType, String id) {
        return getEntityByIdentifier(entityType, id);
    }

    public ApiResponse<List<CustomEntityModel>> getEntitiesByType(String entityType, Map<String, Object>... optionalParams) {
        return getAll(entityType, optionalQueryParams(optionalParams));
    }

    private Map<String, Object> optionalQueryParams(Map<String, Object>... optionalParams) {
        Map<String, Object> queryParams = new HashMap<>();

        if (optionalParams != null && optionalParams.length > 0) {
            queryParams.putAll(optionalParams[0]);
        }

        return queryParams;
    }

    @Override
    public String getRootName() {
        return StringConstants.RESULTS_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}