package repositories.enetpulse;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import data.configuration.SportalSettings;
import data.constants.StringConstants;
import data.models.idmapping.IdMappingResponseModel;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.http.ContentType;
import io.restassured.http.Header;
import io.restassured.specification.RequestSpecification;
import plugins.authentication.ApiProject;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.filterapi.CurlLoggingFilter;
import solutions.bellatrix.core.configuration.ConfigurationService;

import java.util.HashMap;

import static com.google.gson.FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES;

public class EnetpulseIdMapping {
    private String baseUrl;
    private ApiProject project;
    private Gson gson;

    protected void configureGson() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.setFieldNamingPolicy(LOWER_CASE_WITH_UNDERSCORES);
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gsonBuilder.setPrettyPrinting();
        gson = gsonBuilder.create();
    }

    protected RequestSpecification request() {
        return RestAssured.given()
                .spec(new RequestSpecBuilder()
                        .setBaseUri(baseUrl)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .addFilter(new CurlLoggingFilter())
                        .build())
                .log()
                .all()
                .contentType(ContentType.JSON);
    }

    public EnetpulseIdMapping(ApiProject project) {
        this.baseUrl = ConfigurationService.get(SportalSettings.class).getIdMappingApiUrl();
        this.project = project;
        configureGson();
    }

    public IdMappingResponseModel getEnetpulseIdMapping(String eventId) {
        var queryString = new HashMap<String, Object>();
        queryString.put("internal_id", eventId);
        queryString.put("foreign_provider", "enetpulse");

        return request()
                .header(new Header(StringConstants.X_PROJECT_STRING, project.getDomain()))
                .queryParams(queryString)
                .body("{\"entity_data\": {}}")
                .get("mappings/find")
                .as(IdMappingResponseModel.class);
    }
}
