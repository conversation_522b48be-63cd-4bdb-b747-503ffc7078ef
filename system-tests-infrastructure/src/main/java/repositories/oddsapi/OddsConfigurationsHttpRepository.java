package repositories.oddsapi;

import data.constants.OddsApiUrl;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.MetaModel;
import data.models.footballapi.odds.OddsModel;
import data.models.oddsapi.OddClientId;
import data.models.oddsapi.OddsConfigurationModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.OddsApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class OddsConfigurationsHttpRepository extends SportApiHttpRepository<OddsConfigurationModel, OddsConfigurationModel, MetaModel> {

    public OddsConfigurationsHttpRepository(Project projectName) {
        super(OddsConfigurationModel.class,
                OddsConfigurationModel.class,
                MetaModel.class,
                OddsApiCredentials.getProjectApiName(projectName),
                OddsApiCredentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(OddsApiUrl.CONFIGURATIONS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    public ApiResponse<OddsModel> getConfiguration(SupportedSports sport, String oddClientCode) {
        Response response = request().get("/%s/%s".formatted(sport.getValue(), oddClientCode));
        checkFor(response);
        return new ApiResponse<>(response, response.as(OddsModel.class));
    }

    public ApiResponse<OddsModel> getConfiguration(OddsConfigurationModel oddsConfiguration) {
        OddClientId oddClientId = oddsConfiguration.getOddClientId();
        return getConfiguration(SupportedSports.valueOf(oddClientId.getSport()), oddClientId.getCode());
    }

    public Response delete(OddsConfigurationModel oddsConfiguration) {
        if (oddsConfiguration == null) {
            return null;
        }
        OddClientId oddClientId = oddsConfiguration.getOddClientId();
        Response response = request().delete("/%s/%s".formatted(oddClientId.getSport(), oddClientId.getCode()));
        checkFor(response);
        return response;
    }

    public OddsConfigurationModel createOddsConfiguration(OddsConfigurationModel requestBody) {
        ApiResponse<OddsConfigurationModel> oddsConfigurationApiResponse = create(requestBody);
        oddsConfigurationApiResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return oddsConfigurationApiResponse.getResult();
    }
}