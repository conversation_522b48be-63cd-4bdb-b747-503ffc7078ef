package repositories.customization;

import data.constants.StringConstants;
import data.constants.apiurls.CustomizationApiUrl;
import data.constants.enums.CustomBlockStatusEnum;
import data.models.MetaModel;
import data.models.customizationapi.CustomBlockModel;
import factories.customization.CustomBlockHttpFactory;
import io.restassured.builder.RequestSpecBuilder;
import lombok.Getter;
import plugins.authentication.Project;
import repositories.core.CustomizationApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Getter
public class CustomizationCustomBlocksHttpRepository extends SportApiHttpRepository<CustomBlockModel, CustomBlockModel, MetaModel> {

    public CustomizationCustomBlocksHttpRepository(Project projectName) {
        super(CustomBlockModel.class,
                CustomBlockModel.class,
                MetaModel.class,
                CustomizationApiCredentials.getProjectApiName(projectName),
                CustomizationApiCredentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(CustomizationApiUrl.CUSTOM_BLOCKS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    public List<CustomBlockModel> createCustomBlocks(int countOfCustomBlocks) {
        List<CustomBlockModel> createdCustomBlocks = new ArrayList<>();
        for (int i = 0; i < countOfCustomBlocks; i++) {
            CustomBlockModel customBlockModel = CustomBlockHttpFactory.buildDefaultActiveCustomBlock();
            customBlockModel.setStatus(new Random().nextBoolean() ? CustomBlockStatusEnum.ENABLED.name() : CustomBlockStatusEnum.DISABLED.name());
            CustomBlockModel createdCustomBlock = create(customBlockModel).getResult();
            createdCustomBlocks.add(createdCustomBlock);
        }
        return createdCustomBlocks;
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}