package repositories.formguideapi;

import data.constants.FormGuideApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.formguideapi.FormGuideEventModel;

import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.FormGuideApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class FormGuideEventsHttpRepository extends SportApiHttpRepository<FormGuideEventModel, FormGuideEventModel, MetaModel> {

    public FormGuideEventsHttpRepository(Project projectName) {
        super(FormGuideEventModel.class,
                FormGuideEventModel.class,
                MetaModel.class,
                FormGuideApiCredentials.getProjectApiName(projectName),
                FormGuideApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FormGuideApiUrl.EVENTS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.DATA_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}