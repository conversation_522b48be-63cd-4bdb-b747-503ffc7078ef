package repositories.tennis;

import data.constants.StringConstants;
import data.constants.TennisApiUrl;
import data.models.MetaModel;
import data.models.tennisapi.MatchModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import repositories.core.TennisApiCredentials;

import java.util.List;
import java.util.Map;

import static data.constants.StringConstants.*;

public class TennisMatchesHttpRepository extends SportApiHttpRepository<MatchModel, MatchModel, MetaModel> {

    public TennisMatchesHttpRepository(Project projectName) {
        super(MatchModel.class,
                MatchModel.class,
                MetaModel.class,
                TennisApiCredentials.getProjectApiName(projectName),
                TennisApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(TennisApiUrl.MATCHES.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return "matches";
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }

    public ApiResponse<List<MatchModel>> getMatches(String playerId, String... tournamentId) {
        Map<String, Object> queryParams = Map.of(
                TOURNAMENT_IDS_STRING, String.join(",", tournamentId),
                PLAYER_ID_UNDERSCORED, playerId,
                TRANSLATION_LANGUAGE_STRING, "en",
                OFFSET_STRING, "0",
                LIMIT_STRING, "200"
        );

        return getAll(queryParams);
    }

    public Response getLivescore(Map<String, Object> requiredQueryParams) {
        Response response = request().queryParams(requiredQueryParams).get("livescore");
        checkFor(response);
        return response;
    }
}