package repositories;

import data.constants.StringConstants;
import data.models.MetaModel;
import data.widgets.WidgetsLatestVersionModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ContentApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

public class WidgetsLatestVersionHttpRepository extends HttpRepository<WidgetsLatestVersionModel, WidgetsLatestVersionModel, MetaModel> {

    public WidgetsLatestVersionHttpRepository() {
        super(WidgetsLatestVersionModel.class,
                WidgetsLatestVersionModel.class,
                MetaModel.class,
                ContentApiCredentials.getProjectApiName(Project.NONE),
                ContentApiCredentials.getProjectAuthorizationToken(Project.NONE),
                new RequestSpecBuilder()
                        .setBaseUri("https://widgets-staging.sportal365.com/metadata.json")
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public String getWidgetsLatestVersion() {
        return get().getResult().getWidgets().getLatestVersion();
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}