package repositories.core;

import data.configuration.SportalSettings;
import data.constants.StringConstants;
import plugins.authentication.Project;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class FormGuideApiCredentials {
    public static String getProjectApiName(Project projectName) {
        return switch (projectName) {
            case FULLSETUP -> ConfigurationService.get(SportalSettings.class).getApiFullFeaturesProject();
            case FULLSETUP_API -> ConfigurationService.get(SportalSettings.class).getApiFullFeaturesApiProject();
            case STATIC -> ConfigurationService.get(SportalSettings.class).getApiStaticProject();
            case DEFAULT -> ConfigurationService.get(SportalSettings.class).getApiDefaultProject();
            case DEFAULT_ALL_SPORTS -> ConfigurationService.get(SportalSettings.class).getApiSerbianProject();
            default -> StringConstants.EMPTY_STRING;
        };
    }

    public static String getProjectAuthorizationToken(Project projectName) {
        return switch (projectName) {
            case FULLSETUP -> ConfigurationService.get(SportalSettings.class).getFormGuideApiAuthorizationToken();
            case FULLSETUP_API -> ConfigurationService.get(SportalSettings.class).getFormGuideApiAuthorizationToken();
            case STATIC -> ConfigurationService.get(SportalSettings.class).getFormGuideApiAuthorizationToken();
            case DEFAULT -> ConfigurationService.get(SportalSettings.class).getFormGuideApiAuthorizationToken();
            case DEFAULT_ALL_SPORTS ->
                    ConfigurationService.get(SportalSettings.class).getFormGuideApiAuthorizationToken();
            default -> StringConstants.EMPTY_STRING;
        };
    }
}