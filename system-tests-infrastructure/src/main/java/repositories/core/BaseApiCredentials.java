package repositories.core;

import data.configuration.SportalSettings;
import data.constants.StringConstants;
import plugins.authentication.Project;
import solutions.bellatrix.core.configuration.ConfigurationService;

public abstract class BaseApiCredentials {

    protected static final SportalSettings SPORTAL_SETTINGS = ConfigurationService.get(SportalSettings.class);

    public String getProjectApiName(Project projectName) {
        return switch (projectName) {
            case FULLSETUP -> SPORTAL_SETTINGS.getApiFullFeaturesProject();
            case FULLSETUP_API -> SPORTAL_SETTINGS.getApiFullFeaturesApiProject();
            case STATIC -> SPORTAL_SETTINGS.getApiStaticProject();
            case DEFAULT -> SPORTAL_SETTINGS.getApiDefaultProject();
            case DEFAULT_ALL_SPORTS -> SPORTAL_SETTINGS.getApiSerbianProject();
            default -> StringConstants.EMPTY_STRING;
        };
    }

    public abstract String getProjectAuthorizationToken();
}