package repositories.core;

import data.configuration.SportalSettings;
import data.constants.StringConstants;
import plugins.authentication.Project;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class CustomizationApiCredentials {

    private static final SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);

    public static String getProjectApiName(Project projectName) {
        return switch (projectName) {
            case FULLSETUP -> sportalSettings.getApiFullFeaturesProject();
            case STATIC -> sportalSettings.getApiStaticProject();
            case DEFAULT -> sportalSettings.getApiDefaultProject();
            case DEFAULT_ALL_SPORTS -> sportalSettings.getApiSerbianProject();
            default -> StringConstants.EMPTY_STRING;
        };
    }

    public static String getProjectAuthorizationToken() {
        return sportalSettings.getCustomizationApiAuthorizationToken();
    }
}