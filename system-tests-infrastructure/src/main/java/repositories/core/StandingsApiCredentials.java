package repositories.core;

import data.configuration.SportalSettings;
import data.constants.StringConstants;
import plugins.authentication.Project;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class StandingsApiCredentials {

    public static String getProjectApiName(Project projectName) {
        SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);

        return switch (projectName) {
            case FULLSETUP -> sportalSettings.getApiFullFeaturesProject();
            case FULLSETUP_API -> sportalSettings.getApiFullFeaturesApiProject();
            case STATIC -> sportalSettings.getApiStaticProject();
            case DEFAULT -> sportalSettings.getApiDefaultProject();
            case DEFAULT_ALL_SPORTS -> sportalSettings.getApiSerbianProject();
            default -> StringConstants.EMPTY_STRING;
        };
    }

    public static String getProjectAuthorizationToken(Project projectName) {
        return switch (projectName) {
            case FULLSETUP, FULLSETUP_API, STATIC, DEFAULT, DEFAULT_ALL_SPORTS ->
                    ConfigurationService.get(SportalSettings.class).getStandingApiAuthorizationToken();
            default -> StringConstants.EMPTY_STRING;
        };
    }
}