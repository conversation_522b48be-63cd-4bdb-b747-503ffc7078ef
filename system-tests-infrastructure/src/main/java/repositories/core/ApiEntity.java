package repositories.core;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import repositories.converters.LocalDateTimeAdapter;
import solutions.bellatrix.web.infrastructure.EmptyObjectTypeAdapterFactory;
import solutions.bellatrix.web.infrastructure.OffsetDateTimeTypeAdapter;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

import static com.google.gson.FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES;

@SuperBuilder
@AllArgsConstructor
public abstract class ApiEntity {

    public abstract String getEntityId();

    protected Gson configureGson() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter());
        gsonBuilder.registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeTypeAdapter());
        gsonBuilder.registerTypeAdapterFactory(new EmptyObjectTypeAdapterFactory());
        gsonBuilder.setFieldNamingPolicy(LOWER_CASE_WITH_UNDERSCORES);
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        return gsonBuilder.setPrettyPrinting().create();
    }

    public <T extends ApiEntity> T convertToModel(Class<? extends ApiEntity> clazz) {
        Gson gson = configureGson();
        try {
            return gson.fromJson(gson.toJson(this), (Class<T>)clazz);
        }
        catch (Exception ex) {
            throw new RuntimeException("Failed to convert model from %s to %s".formatted(this.getClass().getName(), clazz.getName()), ex);
        }
    }

    public String toJson() {
        Gson gson = configureGson();
        return gson.toJson(this);
    }

    @Override
    public String toString() {
        return toJson();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        return this.toJson().equals(((ApiEntity)obj).toJson());
    }
}