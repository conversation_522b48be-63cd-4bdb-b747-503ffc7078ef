package repositories.core;

import io.restassured.specification.RequestSpecification;
import solutions.bellatrix.core.utilities.SecretsResolver;

import java.util.HashMap;
import java.util.Map;

import static data.constants.StringConstants.*;

public abstract class SportApiHttpRepository<TRequestEntity extends Api<PERSON>ntity, TResponseEntity extends Api<PERSON>ntity, T<PERSON>eta extends ApiEntity>
        extends HttpRepository<TRequestEntity, TResponseEntity, TMeta> {
    public static final Map<String, Object> DEFAULT_QUERY_PARAMS = new HashMap<>(Map.of(OFFSET_STRING, 0, LIMIT_STRING, 200));

    protected SportApiHttpRepository(Class<TRequestEntity> requestClass, Class<TResponseEntity> responseClass, Class<TMeta> metaClass,
                                     String projectName, String authenticationToken, RequestSpecification requestSpecification, boolean supportDeletion) {
        super(requestClass, responseClass, metaClass, requestSpecification, supportDeletion);

        String project = SecretsResolver.getSecret(projectName);
        String authToken = SecretsResolver.getSecret(authenticationToken);

        this.getRequestSpecification().header(X_PROJECT_STRING, project);
        this.getRequestSpecification().header("Authorization", authToken);
        this.getRequestSpecification().header("x-smp-cache-disable", "true");
    }
    protected SportApiHttpRepository(Class<TRequestEntity> requestClass, Class<TResponseEntity> responseClass, Class<TMeta> metaClass,
                                     String projectName, String authenticationToken, RequestSpecification requestSpecification) {
        this(requestClass, responseClass, metaClass,
                projectName, authenticationToken, requestSpecification, false);
        }
}