package repositories.searchapiV2;

import data.constants.SportsSearchApiUrl;
import data.constants.StatusEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.MetaModel;
import data.models.searchapi.SportEntityModel;
import data.widgets.options.enums.DataTeamEnum;
import factories.searchapiV2.SportEntityHttpFactory;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.*;
import solutions.bellatrix.core.utilities.Log;

import java.time.Instant;

import static org.apache.http.HttpStatus.SC_OK;

public class SearchV2EventHttpRepository extends SportApiHttpRepository<SportEntityModel, SportEntityModel, MetaModel> {

    public SearchV2EventHttpRepository(Project projectName) {
        super(SportEntityModel.class,
                SportEntityModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(SportsSearchApiUrl.EVENT.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    public Response delete(String id) {
        Response response = RestAssured
                .given()
                .queryParam(StringConstants.ID_STRING, id)
                .spec(getRequestSpecification())
                .when()
                .delete();

        if (!successCodesList.contains(response.getStatusCode())) {
            logEntityNot(DELETED, response);
        } else {
            Log.info("Response message: " + response.getBody().print());
        }
        CleanupService.removeItem(this, id);
        return response;
    }

    public SportEntityModel createEvent(SupportedSports sport, String competitionId, Instant eventStartDateTimeUTC) {
        SportEntityModel eventToBeCreated = SportEntityHttpFactory.buildEventAllProperties(sport, competitionId);
        eventToBeCreated.setStartTime(eventStartDateTimeUTC.toString());
        ApiResponse<SportEntityModel> sportEntityModelApiResponse = create(eventToBeCreated);
        sportEntityModelApiResponse.getResponse().then().statusCode(SC_OK);
        return eventToBeCreated;
    }

    public SportEntityModel createEvent(SupportedSports sport, String competitionId, Instant eventStartDateTimeUTC, StatusEnum status, DataTeamEnum firstTeam, DataTeamEnum secondTeam) {
        SportEntityModel eventToBeCreated = SportEntityHttpFactory.buildEventAllProperties(sport, competitionId, status, firstTeam, secondTeam);
        eventToBeCreated.setStartTime(eventStartDateTimeUTC.toString());
        ApiResponse<SportEntityModel> sportEntityModelApiResponse = create(eventToBeCreated);
        sportEntityModelApiResponse.getResponse().then().statusCode(SC_OK);
        return eventToBeCreated;
    }
}