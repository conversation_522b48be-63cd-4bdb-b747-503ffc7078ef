package repositories.searchapiV2;

import data.constants.SportsSearchApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.searchapi.SportEntityModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SearchApiV2Credentials;
import repositories.core.SportApiHttpRepository;

import java.util.Map;

public class SearchV2VenuesHttpRepository extends SportApiHttpRepository<SportEntityModel, SportEntityModel, MetaModel> {

    public SearchV2VenuesHttpRepository(Project projectName) {
        super(SportEntityModel.class,
                SportEntityModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(SportsSearchApiUrl.VENUE.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public Response delete(String id) {
        Response response = null;

        if (id != null) {
            response = delete(Map.of(StringConstants.ID_STRING, id));
        }

        return response;
    }
}