package repositories.searchapiV2;

import data.constants.SportsSearchApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.searchapi.CustomEntityModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SearchApiV2Credentials;
import repositories.core.SportApiHttpRepository;

public class SearchV2OrganizationsHttpRepository extends SportApiHttpRepository<CustomEntityModel, CustomEntityModel, MetaModel> {

    public SearchV2OrganizationsHttpRepository(Project projectName) {
        super(CustomEntityModel.class,
                CustomEntityModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBase<PERSON>ri(SportsSearchApiUrl.ORGANIZATION.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}