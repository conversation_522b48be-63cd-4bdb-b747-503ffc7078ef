package repositories.searchapiV2;

import data.constants.SportsSearchApiUrl;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.MetaModel;
import data.models.searchapi.SportEntityModel;
import factories.searchapiV2.SportEntityHttpFactory;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SearchApiV2Credentials;
import repositories.core.SportApiHttpRepository;

import java.util.Map;

public class SearchV2CompetitionsHttpRepository extends SportApiHttpRepository<SportEntityModel, SportEntityModel, MetaModel> {

    public SearchV2CompetitionsHttpRepository(Project projectName) {
        super(SportEntityModel.class,
                SportEntityModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(SportsSearchApiUrl.COMPETITION.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    public SportEntityModel createCompetition() {
        SportEntityModel requestBody = SportEntityHttpFactory.buildCompetitionMandatoryProperties(SupportedSports.FOOTBALL);
        create(requestBody).getResponse().then().statusCode(HttpStatus.SC_OK);
        return requestBody;
    }

    @Override
    public Response delete(String competitionId) {
        Response response = null;

        if (competitionId != null) {
            response = delete(Map.of(StringConstants.ID_STRING, competitionId));
        }

        return response;
    }
}