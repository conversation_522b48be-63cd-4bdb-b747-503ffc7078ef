package repositories.searchapiV2;

import com.fasterxml.jackson.core.type.TypeReference;
import data.constants.SportsSearchApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.searchapi.DomainsOrderModel;
import data.models.searchapi.ResultModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SearchApiV2Credentials;
import repositories.core.SportApiHttpRepository;

import java.util.Comparator;
import java.util.List;

public class SearchV2DomainsHttpRepository extends SportApiHttpRepository<DomainsOrderModel, DomainsOrderModel, MetaModel> {

    public SearchV2DomainsHttpRepository(Project projectName) {
        super(DomainsOrderModel.class,
                DomainsOrderModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(SportsSearchApiUrl.DOMAINS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }

    public ApiResponse<DomainsOrderModel> setCustomDomainsOrder(DomainsOrderModel requestBody) {
        Response response = request().body(requestBody)
                .post("/order");
        return new ApiResponse<>(response, getEntityFromResponse(response));
    }

    public List<ResultModel> getDomainsOrderByCustomOrder() {
        List<ResultModel> availableDomains = getDomains();
        for (ResultModel availableDomain : availableDomains) {
            if (availableDomain.getCustomOrder() == null) {
                throw new RuntimeException("Custom order is not set for domain with name: %s" + availableDomain.getName());
            }
        }
        return availableDomains;
    }

    public List<ResultModel> getDomainsOrderByCreatedAtAsc() {
        List<ResultModel> availableDomains = getDomains();
        availableDomains.sort(Comparator.comparing(ResultModel::getCreatedAt));
        return availableDomains;
    }

    public List<ResultModel> getDomains() {
        Response response = request().get();
        response.then().statusCode(HttpStatus.SC_OK);
        Object results = response.jsonPath().getJsonObject(StringConstants.RESULTS_STRING);
        return getObjectMapper().convertValue(results, new TypeReference<>() {
        });
    }
}