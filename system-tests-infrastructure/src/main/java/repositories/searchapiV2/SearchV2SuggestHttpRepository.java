package repositories.searchapiV2;

import data.constants.*;
import data.constants.enums.CustomEntityEnum;
import data.constants.enums.EntityEnum;
import data.constants.enums.SportEntityEnum;
import data.models.MetaModel;
import data.models.searchapi.ResultModel;
import data.models.searchapi.Translation;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SearchApiV2Credentials;
import repositories.core.SportApiHttpRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SearchV2SuggestHttpRepository extends SportApiHttpRepository<ResultModel, ResultModel, MetaModel> {

    public SearchV2SuggestHttpRepository(Project projectName) {
        super(ResultModel.class,
                ResultModel.class,
                MetaModel.class,
                SearchApiV2Credentials.getProjectApiName(projectName),
                SearchApiV2Credentials.getProjectAuthorizationToken(),
                new RequestSpecBuilder()
                        .setBaseUri(SportsSearchApiUrl.SUGGEST.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.RESULTS_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }

    public ResultModel getFirstDomain() {
        ApiResponse<List<ResultModel>> response = getAll(Map.of(StringConstants.ENTITY_TYPE_STRING, StringConstants.DOMAIN_STRING));
        response.getResponse().then().statusCode(HttpStatus.SC_OK);
        return response.getResult().get(0);
    }

    public ResultModel getPlace() {
        Map<String, Object> queryParams = Map.of(StringConstants.ENTITY_TYPE_STRING, CustomEntityEnum.PLACE.getValue().toLowerCase());
        return getAll(queryParams).getResult().get(0);
    }

    public ResultModel getEntityByName(String name, EntityEnum entity) {
        return getEntityByName(name, entity, null);
    }

    public ResultModel getEntityById(String id, EntityEnum entity) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.IDS_STRING, id,
                StringConstants.ENTITY_TYPE_STRING, entity.getValue().toLowerCase()
        );

        return getAll(queryParams).getResult().get(0);
    }

    public ResultModel getEntityById(String entityId) {
        return get(Map.of(StringConstants.IDS_STRING, entityId)).getResult();
    }

    public ResultModel getEntityByName(String name, EntityEnum entity, String competitionId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(StringConstants.NAME_STRING, name);
        queryParams.put(StringConstants.ENTITY_TYPE_STRING, entity.getValue().toLowerCase());

        if (competitionId != null) {
            queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionId);
        }

        return getAll(queryParams).getResult().get(0);
    }

    public List<ResultModel> getEntitiesByType(EntityTypeEnum entityType, SupportedSports... sport) {
        Map<String, Object> queryParams = new HashMap<>(Map.of(
                StringConstants.ENTITY_TYPE_STRING, entityType.name().toLowerCase(),
                StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode())
        );
        if (sport.length > 0) {
            queryParams.put(StringConstants.SPORT_STRING, sport[0].getValue());
        }
        return getAll(queryParams).getResult();
    }

    public ResultModel getEntityByType(EntityTypeEnum entityType, SupportedSports... sport) {
        return getEntitiesByType(entityType, sport).stream()
                .findAny()
                .orElseThrow(() -> new RuntimeException("No `%s` entity found for sport: %s".formatted(entityType, sport)));
    }

    public List<ResultModel> getPlayersByName(String playerName, SupportedSports sport) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.NAME_STRING, playerName,
                StringConstants.SPORT_STRING, sport.toString().toLowerCase(),
                StringConstants.ENTITY_TYPE_STRING, StringConstants.PLAYER_STRING
        );

        ApiResponse<List<ResultModel>> response = getAll(queryParams);
        response.getResponse().then().statusCode(HttpStatus.SC_OK);
        return response.getResult();
    }

    public List<ResultModel> getTeamsByName(String teamName, SupportedSports sport) {
        HashMap<String, Object> params = new HashMap<>(DEFAULT_QUERY_PARAMS);
        params.put(StringConstants.NAME_STRING, teamName);
        params.put(StringConstants.SPORT_STRING, sport.getValue());
        params.put(StringConstants.ENTITY_TYPE_STRING, StringConstants.TEAM_STRING);
        return getAll(params).getResult();
    }

    public List<ResultModel> getTeamsById(String id, SupportedSports sport) {
        HashMap<String, Object> params = new HashMap<>(DEFAULT_QUERY_PARAMS);
        params.put(StringConstants.IDS_STRING, id);
        params.put(StringConstants.SPORT_STRING, sport.getValue());
        params.put(StringConstants.ENTITY_TYPE_STRING, StringConstants.TEAM_STRING);
        return getAll(params).getResult();
    }

    public ApiResponse<List<ResultModel>> searchEntities(String entityName, SupportedSports sport, EntityEnum entityType) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.SPORT_STRING, sport.getValue(),
                StringConstants.NAME_STRING, entityName,
                StringConstants.ENTITY_TYPE_STRING, entityType.getValue().toLowerCase()
        );

        Response response = request().queryParams(queryParams).get();
        checkFor(response);
        response.then().statusCode(HttpStatus.SC_OK);

        List<ResultModel> entitiesFromResponse = getEntitiesFromResponse(response);
        return new ApiResponse<>(response, entitiesFromResponse);
    }

    public List<ResultModel> getByIds(String teamId, SupportedSports sport) {
        HashMap<String, Object> params = new HashMap<>(DEFAULT_QUERY_PARAMS);
        params.put(StringConstants.IDS_STRING, teamId);
        params.put(StringConstants.SPORT_STRING, sport.getValue());
        return getAll(params).getResult();
    }

    public List<ResultModel> getCompetitionsByName(String competitionName, SupportedSports sport) {
        ApiResponse<List<ResultModel>> resultResponse = getAll(Map.of(
                StringConstants.ENTITY_TYPE_STRING, SportEntityEnum.COMPETITION.name().toLowerCase(),
                StringConstants.NAME_STRING, competitionName,
                StringConstants.SPORT_STRING, sport.getValue()
        ));
        resultResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return resultResponse.getResult();
    }

    public List<ResultModel> getCompetitions(Map<String, Object> queryParams) {
        queryParams.put(StringConstants.ENTITY_TYPE_STRING, SportEntityEnum.COMPETITION.name().toLowerCase());
        ApiResponse<List<ResultModel>> resultResponse = getAll(queryParams);
        resultResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return resultResponse.getResult();
    }

    public ResultModel getEntityByIdAndSport(String entityId, SupportedSports sport) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.SPORT_STRING, sport.getValue(),
                StringConstants.IDS_STRING, entityId
        );
        return get(queryParams).getResult();
    }

    public Translation getTranslationOfEntityForLanguage(Language language, String entityId, SupportedSports sport) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.SPORT_STRING, sport.getValue(),
                StringConstants.IDS_STRING, entityId
        );

        return get(queryParams).getResult().getTranslations().stream()
                .filter(t -> t.getLanguage().equals(language.getCode()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No translation in %s found for entity with id: %s"
                        .formatted(language.getCode(), entityId)));
    }

    public ResultModel getEntityWithoutTagScoreBySportAndType(SupportedSports sport, EntityEnum entityType) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.LIMIT_STRING, 300,
                StringConstants.SPORT_STRING, sport.getValue(),
                StringConstants.ENTITY_TYPE_STRING, entityType.getValue().toLowerCase()
        );
        return getAll(queryParams).getResult().stream()
                .filter(e -> e.getTagScore() == null || e.getTagScore() == 2)
                .filter(e -> !e.getName().contains("Auto"))
                .findAny()
                .orElseThrow(() -> new RuntimeException("No entity found without tag score for sport: %s and type: %s"
                        .formatted(sport, entityType)));
    }

    public ResultModel getEntityWithoutTagScoreByType(EntityTypeEnum entityType) {
        Map<String, Object> queryParams = Map.of(
                StringConstants.LIMIT_STRING, 100,
                StringConstants.ENTITY_TYPE_STRING, entityType.getValue().toLowerCase()
        );
        return getAll(queryParams).getResult().stream()
                .filter(e -> e.getTagScore() == null || e.getTagScore() == 1)
                .filter(e -> e.getName().contains("Automation"))
                .findAny()
                .orElseThrow(() -> new RuntimeException("No entity found without tag score for type: %s".formatted(entityType)));
    }

    public ResultModel getFirstEventWithStartTime(MultiSportSportsEnum sport, Map<String, Object> queryParams) {
        return getAll(queryParams).getResult().stream()
                .filter(event -> event.getStartTime() != null && !event.getStartTime().isEmpty())
                .findFirst()
                .orElseThrow(() -> new AssertionError("No events found with valid start time for " + sport));
    }
}