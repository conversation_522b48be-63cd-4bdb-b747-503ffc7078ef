package repositories.football;

import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.MetaModel;
import data.models.articles.NestedMatch;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class FootballMatchesHttpRepository extends SportApiHttpRepository<NestedMatch, NestedMatch, MetaModel> {

    public FootballMatchesHttpRepository(Project projectName) {
        super(NestedMatch.class,
                NestedMatch.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrl.MATCHES.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public List<NestedMatch> getAllMatchesForTeam(String... teamId) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'");

        return getAll(String.format("?team_ids=%s&from_start_time=%s&sort_direction=%s", String.join(",", teamId), LocalDateTime.now().format(dateTimeFormatter), StringConstants.ASCENDING_STRING)).getResult();
    }

    @Override
    public String getRootName() {
        return "";
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
