package repositories.football.v2;

import data.constants.StringConstants;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.MetaModel;
import data.models.footballapi.v2.languages.LanguageV2Model;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class FootballLanguagesV2HttpRepository extends SportApiHttpRepository<LanguageV2Model, LanguageV2Model, MetaModel> {

    public FootballLanguagesV2HttpRepository(Project projectName) {
        super(LanguageV2Model.class,
                LanguageV2Model.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBase<PERSON>ri(FootballApiUrlV2.LANGUAGES.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}