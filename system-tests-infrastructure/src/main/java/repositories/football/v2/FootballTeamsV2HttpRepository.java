package repositories.football.v2;

import data.constants.Language;
import data.constants.StringConstants;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.MetaModel;
import data.models.footballapi.v2.TeamV2Model;
import data.models.footballapi.v2.TeamsV2ColorsModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.util.Map;

public class FootballTeamsV2HttpRepository extends SportApiHttpRepository<TeamV2Model, TeamV2Model, MetaModel> {

    public FootballTeamsV2HttpRepository(Project projectName) {
        super(TeamV2Model.class,
                TeamV2Model.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrlV2.TEAMS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<TeamsV2ColorsModel> createTeamColors(TeamsV2ColorsModel teamsV2ColorsModel) {
        Response response = request()
                .body(getGson().toJson(teamsV2ColorsModel))
                .post("colors");

        checkFor(response);
        return new ApiResponse<>(response, teamsV2ColorsModel);
    }

    @Override
    public ApiResponse<TeamV2Model> getById(String id) {
        Map<String, Object> queryParam = Map.of(StringConstants.LANGUAGE_CODE, Language.ENGLISH.getCode());
        return super.getById(id, queryParam);
    }

    @Override
    public String getRootName() {
        return StringConstants.TEAMS_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}