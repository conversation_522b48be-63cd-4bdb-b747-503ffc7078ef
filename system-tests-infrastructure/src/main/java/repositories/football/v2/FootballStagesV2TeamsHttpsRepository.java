package repositories.football.v2;

import data.constants.StringConstants;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.MetaModel;
import data.models.footballapi.v2.StageTeamRequestModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.util.List;

public class FootballStagesV2TeamsHttpsRepository extends SportApiHttpRepository<StageTeamRequestModel, StageTeamRequestModel, MetaModel> {

    public FootballStagesV2TeamsHttpsRepository(Project projectName, String stageId) {
        super(StageTeamRequestModel.class,
                StageTeamRequestModel.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrlV2.STAGE_TEAMS.getUrl().formatted(stageId))
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build(),
                true);
    }

    public ApiResponse<StageTeamRequestModel> updateTeamStage(StageTeamRequestModel teamRequest) {
        List<StageTeamRequestModel> requestBody = List.of(teamRequest);

        Response response = request()
                .body(getGson().toJson(requestBody))
                .when()
                .put("");

        checkFor(response);

        return new ApiResponse<>(response, getEntityFromResponse(response));
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}