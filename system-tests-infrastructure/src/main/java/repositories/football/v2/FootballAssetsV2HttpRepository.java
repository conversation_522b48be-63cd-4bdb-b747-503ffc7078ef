package repositories.football.v2;

import data.constants.StringConstants;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.MetaModel;
import data.models.footballapi.v2.assets.AssetModel;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;
import solutions.bellatrix.core.utilities.Log;

import java.util.List;

public class FootballAssetsV2HttpRepository extends SportApiHttpRepository<AssetModel, AssetModel, MetaModel> {

    public FootballAssetsV2HttpRepository(Project projectName) {
        super(AssetModel.class,
                AssetModel.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrlV2.ASSETS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<List<AssetModel>> createAsset(AssetModel toBeCreated) {
        Response response = request().body(getGson().toJson(List.of(toBeCreated))).post();
        List<AssetModel> entity = null;

        if (!successCodesList.contains(response.getStatusCode())) {
            Log.error("The entity is not created!");
            Log.error("Response status code: " + response.getStatusCode());
            Log.error("Response error message: " + response.getBody().print());
        } else {
            entity = getEntitiesFromResponse(response);
        }

        return new ApiResponse<>(response, entity);
    }

    public Response delete(AssetModel toBeDeleted) {
        return request().body(getGson().toJson(List.of(toBeDeleted))).delete();
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}