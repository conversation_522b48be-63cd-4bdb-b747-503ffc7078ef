package repositories.football.v2;

import data.constants.StringConstants;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.MetaModel;
import data.models.footballapi.v2.seasons.*;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.util.List;
import java.util.Map;

import static data.constants.StringConstants.DETAILS_STRING;

public class FootballSeasonsV2HttpRepository extends SportApiHttpRepository<SeasonV2Model, SeasonV2Model, MetaModel> {

    private static final String SEASON_ID_STAGES_PATH = "/{seasonId}/stages";

    public FootballSeasonsV2HttpRepository(Project projectName) {
        super(SeasonV2Model.class,
                SeasonV2Model.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrlV2.SEASONS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    public ApiResponse<SeasonV2Model> createSeason(CreateSeasonV2Model createSeasonV2Model) {
        Response response = request()
                .body(getGson().toJson(createSeasonV2Model))
                .when()
                .post();

        checkFor(response);
        return new ApiResponse<>(response, getEntityFromResponse(response));
    }

    public ApiResponse<SeasonV2DetailsModel> getSeasonsDetails(Map<String, Object> defaultQueryParams) {
        Response response = request().queryParams(defaultQueryParams).get("/%s".formatted(DETAILS_STRING));
        checkFor(response);

        SeasonV2DetailsModel seasonV2DetailsModel = response.as(SeasonV2DetailsModel.class);
        return new ApiResponse<>(response, seasonV2DetailsModel);
    }

    public ApiResponse<SeasonV2StagesModel> getSeasonsStages(String seasonId) {
        Response response = request().get(SEASON_ID_STAGES_PATH, seasonId);
        checkFor(response);

        SeasonV2StagesModel seasonV2StagesModel = response.as(SeasonV2StagesModel.class);
        return new ApiResponse<>(response, seasonV2StagesModel);
    }

    public ApiResponse<SeasonV2StagesModel> createSeasonStage(String seasonId, CreateSeasonV2StageModel createSeasonV2StageModel) {
        Response response = request()
                .body(serializeObjectWithNulls(List.of(createSeasonV2StageModel)))
                .when()
                .post(SEASON_ID_STAGES_PATH, seasonId);

        checkFor(response);
        return new ApiResponse<>(response, response.as(SeasonV2StagesModel.class));
    }


    public List<SeasonV2Model> getSeasonsForTournament(Long tournamentId) {
        var tournamentSeasons = getAll(Map.of(
                StringConstants.TOURNAMENT_ID_STRING, tournamentId
        ));
        tournamentSeasons.getResponse().then().statusCode(HttpStatus.SC_OK);
        return tournamentSeasons.getResult();
    }

    @Override
    public String getRootName() {
        return StringConstants.SEASONS_STRING;
    }

    @Override
    public String getMetaName() {
        return "";
    }
}