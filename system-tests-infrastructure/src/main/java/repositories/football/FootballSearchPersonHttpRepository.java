package repositories.football;

import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.MetaModel;
import data.models.footballapi.search.PersonResultModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class FootballSearchPersonHttpRepository extends SportApiHttpRepository<PersonResultModel, PersonResultModel, MetaModel> {

    public FootballSearchPersonHttpRepository(Project projectName) {
        super(PersonResultModel.class,
                PersonResultModel.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBase<PERSON><PERSON>(FootballApiUrl.SEARCH.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
