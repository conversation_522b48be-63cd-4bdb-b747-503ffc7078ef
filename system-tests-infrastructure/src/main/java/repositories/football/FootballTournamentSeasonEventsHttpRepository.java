package repositories.football;

import data.constants.HttpRepoHeaders;
import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.MetaModel;
import data.models.footballapi.tournamentseason.TournamentSeasonEventModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.ApiResponse;
import repositories.core.FootballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

import java.util.List;

public class FootballTournamentSeasonEventsHttpRepository extends SportApiHttpRepository<TournamentSeasonEventModel, TournamentSeasonEventModel, MetaModel> {

    public FootballTournamentSeasonEventsHttpRepository(Project projectName) {
        super(TournamentSeasonEventModel.class,
                TournamentSeasonEventModel.class,
                MetaModel.class,
                FootballApiCredentials.getProjectApiName(projectName),
                FootballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(FootballApiUrl.TOURNAMENTS_SEASON.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .addHeader(HttpRepoHeaders.X_ODD_CLIENT.name, "sportal")
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }

    public ApiResponse<List<TournamentSeasonEventModel>> getTournamentSeasonEventsById(String seasonId) {
        return getAll("%s/events".formatted(seasonId));
    }

    public ApiResponse<List<TournamentSeasonEventModel>> getTournamentSeasonByTeamsId(String seasonId) {
        return getAll("%s/teams".formatted(seasonId));
    }
}