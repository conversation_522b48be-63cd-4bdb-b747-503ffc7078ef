package repositories.collaboration;

import data.constants.CollaborationApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.collaborationapi.document.DocumentModel;
import data.models.collaborationapi.document.DocumentResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.CollaborationApiCredentials;
import repositories.core.HttpRepository;
import repositories.core.RestAssuredConfigFactory;

public class CollaborationDocumentsHttpRepository extends HttpRepository<DocumentModel, DocumentResponseModel, MetaModel> {
    public CollaborationDocumentsHttpRepository(Project projectName) {
        super(DocumentModel.class,
                DocumentResponseModel.class,
                MetaModel.class,
                CollaborationApiCredentials.getProjectApiName(projectName),
                CollaborationApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(CollaborationApiUrl.DOCUMENTS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return "documents";
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}