package repositories.basketball;

import data.constants.BasketballApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.basketball.CountryModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.BasketballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class BasketballCountriesHttpRepository extends SportApiHttpRepository<CountryModel, CountryModel, MetaModel> {

    public BasketballCountriesHttpRepository(Project projectName) {
        super(CountryModel.class,
                CountryModel.class,
                MetaModel.class,
                BasketballApiCredentials.getProjectApiName(projectName),
                BasketballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(BasketballApiUrl.COUNTRIES.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return "countries";
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
