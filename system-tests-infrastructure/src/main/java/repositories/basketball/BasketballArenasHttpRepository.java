package repositories.basketball;

import data.constants.BasketballApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.basketball.arena.ArenaModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.BasketballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class BasketballArenasHttpRepository extends SportApiHttpRepository<ArenaModel, ArenaModel, MetaModel> {

    public BasketballArenasHttpRepository(Project projectName) {
        super(ArenaModel.class,
                ArenaModel.class,
                MetaModel.class,
                BasketballApiCredentials.getProjectApiName(projectName),
                BasketballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(BasketballApiUrl.ARENAS.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return "arenas";
    }

    @Override
    public String getMetaName() {
        return StringConstants.META_STRING;
    }
}
