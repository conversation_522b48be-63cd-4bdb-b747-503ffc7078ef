package repositories.basketball;

import data.constants.BasketballApiUrl;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.basketball.team.TeamModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.BasketballApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class BasketballTeamsHttpRepository extends SportApiHttpRepository<TeamModel, TeamModel, MetaModel> {

    public BasketballTeamsHttpRepository(Project projectName) {
        super(TeamModel.class,
                TeamModel.class,
                MetaModel.class,
                BasketballApiCredentials.getProjectApiName(projectName),
                BasketballApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(BasketballApiUrl.TEAMS.url)
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return "teams";
    }

    @Override
    public String getMetaName() {
        return "page_meta";
    }
}
