package repositories.multisport;

import data.constants.StringConstants;
import data.constants.apiurls.MultiSportApiUrl;
import data.models.MetaModel;
import data.models.multisportapi.EventModel;
import data.models.multisportapi.EventsResponseModel;
import io.restassured.builder.RequestSpecBuilder;
import plugins.authentication.Project;
import repositories.core.MultiSportApiCredentials;
import repositories.core.RestAssuredConfigFactory;
import repositories.core.SportApiHttpRepository;

public class MultiSportEventsCompetitionsHttpRepository extends SportApiHttpRepository<EventModel, EventsResponseModel, MetaModel> {

    public MultiSportEventsCompetitionsHttpRepository(Project projectName) {
        super(EventModel.class,
                EventsResponseModel.class,
                MetaModel.class,
                MultiSportApiCredentials.getProjectApiName(projectName),
                MultiSportApiCredentials.getProjectAuthorizationToken(projectName),
                new RequestSpecBuilder()
                        .setBaseUri(MultiSportApiUrl.EVENTS_COMPETITIONS.getUrl())
                        .setConfig(new RestAssuredConfigFactory().createDefault())
                        .build());
    }

    @Override
    public String getRootName() {
        return StringConstants.EMPTY_STRING;
    }

    @Override
    public String getMetaName() {
        return StringConstants.EMPTY_STRING;
    }
}