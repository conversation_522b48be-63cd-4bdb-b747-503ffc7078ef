{"troubleshootingSettings": {"debugInformationEnabled": "true"}, "webSettings": {"baseUrl": "https://ecommerce-playground.lambdatest.io", "executionType": "regular", "defaultBrowser": "chrome", "defaultLifeCycle": "restart everytime time", "artificialDelayBeforeAction": "0", "automaticallyScrollToVisible": "false", "waitUntilReadyOnElementFound": "false", "waitForAngular": "false", "shouldHighlightElements": "true", "shouldCaptureHttpTraffic": "false", "screenshotsOnFailEnabled": "false", "screenshotsSaveLocation": "${user.home}/BELLATRIX/Screenshots", "videosOnFailEnabled": "false", "videosSaveLocation": "${user.home}/BELLATRIX/Videos", "timeoutSettings": {"elementWaitTimeout": "30", "pageLoadTimeout": "30", "scriptTimeout": "1", "waitForAjaxTimeout": "30", "sleepInterval": "1", "waitUntilReadyTimeout": "30", "waitForJavaScriptAnimationsTimeout": "30", "waitForAngularTimeout": "30", "waitForPartialUrl": "30", "validationsTimeout": "30", "elementToBeVisibleTimeout": "30", "elementToExistTimeout": "30", "elementToNotExistTimeout": "30", "elementToBeClickableTimeout": "30", "elementNotToBeVisibleTimeout": "30", "elementToHaveContentTimeout": "15"}, "gridSettings": [{"providerName": "saucelabs", "url": "http://ondemand.saucelabs.com:80/wd/hub", "arguments": [{"screenResolution": "1280x800", "recordVideo": "true", "recordScreenshots": "true", "username": "myUserName", "accessKey": "myPass", "name": "bellatrix_run"}]}, {"providerName": "browserstack", "url": "http://hub-cloud.browserstack.com/wd/hub/", "arguments": [{"resolution": "1280x800", "browserstack.video": "true", "browserstack.networkLogs": "true", "browserstack.debug": "true", "browserstack.console": "errors", "browserstack.user": "myUserName", "browserstack.key": "myPass", "build": "bellatrix_run"}]}, {"providerName": "crossbrowsertesting", "url": "http://hub.crossbrowsertesting.com:80/wd/hub", "arguments": [{"screen_resolution": "1280x800", "record_video": "true", "record_network": "true", "username": "myUserName", "password": "myPass", "name": "bellatrix_run"}]}, {"providerName": "selenoid", "url": "http://127.0.0.1:4444/wd/hub", "arguments": [{"screenResolution": "1280x800", "enableVNC": "true", "enableVideo": "true", "enableLog": "true", "name": "bellatrix_run"}]}, {"providerName": "grid", "url": "http://127.0.0.1:4444/wd/hub", "arguments": [{"name": "bellatrix_run"}]}]}, "urlSettings": {"shopUrl": "http://demos.bellatrix.solutions/cart/", "accountUrl": "http://demos.bellatrix.solutions/account/"}}