    1. Create a new testNG test class
    2. Use the BELLATRIX Browser attribute so that you execute all tests in Firefox and restart the browser every time.
    3. Navigate to http://demos.bellatrix.solutions/welcome/ for each test but don't navigate in the tests body.
    4. Create a test where you click on the Saturn V Sale! Button via locating the element by inner text, wait for the button to has content.
    5. Create a test where you find all Add to cart buttons, wait for all buttons to be visible.