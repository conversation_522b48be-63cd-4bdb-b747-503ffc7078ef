pipeline {
    agent {
        node {
            label 'qa-win'
        }
    }
    environment {
        WIDGETS_API_URL = 'https://widgets-integration.sportal365.com/metadata.json'
    }
    parameters {
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: true,
                description: 'Should execute tests, or just build the source.'
        )

        // Test Configuration
        choice(
                name: 'TEST_GROUPS',
                choices: [
                        '',
                        '&football',
                        '&tennis',
                        '&basketball',
                        '&icehockey',
                        '&smoke'
                ],
                description: 'Select tests to execute'
        )

        string(
                name: 'TESTS_FILTER',
                defaultValue: '',
                description: 'Select tests to execute'
        )

        // Environment Settings
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: [
                        'integration',
                        'staging'
                ],
                description: 'Environment to execute tests. Requires EXECUTE_TESTS set to TRUE.'
        )

        choice(
                name: 'DEFAULT_BROWSER',
                choices: [
                        'chrome_headless',
                        'firefox_headless',
                        'edge_headless',
                        'chrome',
                        'firefox',
                        'edge'
                ],
                description: 'Browser type to use for tests execution.'
        )

        // Widget Versions - kept for override purposes
        string(
                name: 'FOOTBALL_WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override Football Widgets Version. Leave empty to use version from API.'
        )

        string(
                name: 'BASKETBALL_WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override Basketball Widgets Version. Leave empty to use version from API.'
        )

        string(
                name: 'TENNIS_WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override Tennis Widgets Version. Leave empty to use version from API.'
        )

        string(
                name: 'ICE_HOCKEY_WIDGETS_VERSION',
                defaultValue: '',
                description: 'Optional: Override Ice Hockey Widgets Version. Leave empty to use version from API.'
        )

        // Notifications
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
                description: '''Slack channel for test results:
                 - #platform-reports: Platform test reports
                 - #qa-automation: Automation framework updates
                 - #test-executions: General test execution updates'''
        )
        booleanParam(name: 'AI_ANALYSIS', defaultValue: true, description: 'Add AI analysis to the results posting.')
    }
    stages {
        stage('Set Widget Versions') {
            steps {
                script {
                    // Fetch version from API
                    try {
                        def response = sh(script: """
                            curl -s ${WIDGETS_API_URL}
                        """, returnStdout: true).trim()

                        def metadata = readJSON text: response
                        def apiVersion = metadata.widgets.latest_version ?: '8.3.0'

                        // Set versions with parameter override logic
                        env.FINAL_FOOTBALL_VERSION = params.FOOTBALL_WIDGETS_VERSION?.trim() ?: apiVersion
                        env.FINAL_BASKETBALL_VERSION = params.BASKETBALL_WIDGETS_VERSION?.trim() ?: apiVersion
                        env.FINAL_TENNIS_VERSION = params.TENNIS_WIDGETS_VERSION?.trim() ?: apiVersion
                        env.FINAL_HOCKEY_VERSION = params.ICE_HOCKEY_WIDGETS_VERSION?.trim() ?: apiVersion

                        echo """Using widget versions:
                            Football: ${env.FINAL_FOOTBALL_VERSION} ${params.FOOTBALL_WIDGETS_VERSION?.trim() ? '(from parameter)' : '(from API)'}
                            Basketball: ${env.FINAL_BASKETBALL_VERSION} ${params.BASKETBALL_WIDGETS_VERSION?.trim() ? '(from parameter)' : '(from API)'}
                            Tennis: ${env.FINAL_TENNIS_VERSION} ${params.TENNIS_WIDGETS_VERSION?.trim() ? '(from parameter)' : '(from API)'}
                            Ice Hockey: ${env.FINAL_HOCKEY_VERSION} ${params.ICE_HOCKEY_WIDGETS_VERSION?.trim() ? '(from parameter)' : '(from API)'}"""
                    } catch (Exception e) {
                        echo "Failed to fetch widget version: ${e.getMessage()}"
                        echo "Using fallback version 8.3.0 where not specified"

                        // Set versions with fallback
                        env.FINAL_FOOTBALL_VERSION = params.FOOTBALL_WIDGETS_VERSION?.trim() ?: '8.3.0'
                        env.FINAL_BASKETBALL_VERSION = params.BASKETBALL_WIDGETS_VERSION?.trim() ?: '8.3.0'
                        env.FINAL_TENNIS_VERSION = params.TENNIS_WIDGETS_VERSION?.trim() ?: '8.3.0'
                        env.FINAL_HOCKEY_VERSION = params.ICE_HOCKEY_WIDGETS_VERSION?.trim() ?: '8.3.0'
                    }
                }
            }
        }

        stage('Build') {
            steps {
                bat 'mvn clean package -Dmaven.test.skip -pl ui-tests-widgets-v2 -am'
            }
        }

        stage('Test') {
            when {
                expression {
                    return params.EXECUTE_TESTS
                }
            }
            steps {
                slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#FFA500', message: "Testing has started :rocket:\n '${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                bat """mvn test -pl ui-tests-widgets-v2 -am -Dsurefire.failIfNoSpecifiedTests=false -Dgroups="widgetsV2${TESTS_FILTER}${TEST_GROUPS}&!visual" -DBUILD_TAG="${BUILD_TAG}" -Denvironment=${EXECUTION_ENVIRONMENT} -DfootballWidgetsVersion=${env.FINAL_FOOTBALL_VERSION} -DbasketballWidgetsVersion=${env.FINAL_BASKETBALL_VERSION} -DtennisWidgetsVersion=${env.FINAL_TENNIS_VERSION} -DiceHockeyWidgetsVersion=${env.FINAL_HOCKEY_VERSION}"""
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        env.TEST_SUMMARY = processTestResults()
                    }
                    allure([
                            includeProperties: false,
                            jdk: '',
                            properties: [],
                            reportBuildPolicy: 'ALWAYS',
                            results: [[path: '**/allure-results']]
                    ])
                    zip zipFile: './ui-tests-widgets-v2/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./ui-tests-widgets-v2/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${env.TEST_SUMMARY}\n More info at: ${env.BUILD_URL}")
                }
                failure {
                    script {
                        env.TEST_SUMMARY_AI = pipelineUtils.analyzeAllureResultsUsingAI(params.AI_ANALYSIS)
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\n ${env.TEST_SUMMARY_AI}\n More info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}

def processTestResults() {
    def testResults = junit '**/target/surefire-reports/*.xml'
    def testSummary = "Test Results:\n"
    testResults?.each { suite ->
        testSummary += "${suite.totalCount} tests were run\n"
        testSummary += "  Passed: ${suite.passCount}\n"
        testSummary += "  Failed: ${suite.failCount}\n"
        testSummary += "  Skipped: ${suite.skipCount}\n"
    }
    return testSummary
}
