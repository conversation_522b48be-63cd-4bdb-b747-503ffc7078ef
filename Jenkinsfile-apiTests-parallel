def pipelineUtils

pipeline {
    agent  {
        node {
            label 'qa-win'
        }
    }
    environment {
        // Define COMMIT_HASH
        COMMIT_HASH=''
    }
    parameters {
        booleanParam(name: 'EXECUTE_TESTS', defaultValue: true, description: 'Should execute tests, or just build the source.')
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
                description: '''Slack channel for test results:
                - #platform-reports: Platform test reports
                - #qa-automation: Automation framework updates
                - #test-executions: General test execution updates'''

        )
        booleanParam(name: 'AI_ANALYSIS', defaultValue: true, description: 'Add AI analysis to the results posting.')
    }
    stages {
        stage('Setup') {
            steps {
                script {
                    // Load the shared functions
                    pipelineUtils = load('jenkins/vars/pipelineUtils.groovy')
                    pipelineUtils.setAIEnvironmentVariables()
                }
            }
        }
        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl api-tests-backend-sports -am'
            }
        }
        stage('Test') {
            when {
                expression {
                    return params.EXECUTE_TESTS
                }
            }
            steps {
                script {
                    def parallelTests = [
                            'contentApi': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'contentApi|contentApiNewsForge',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports'
                                ])
                            },
                            'sportApis': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'basketballApi|footballApi|tennisApi|multiSportApi|oddsApi',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 180
                                ])
                            },
                            'searchApiV2': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'searchApiV2',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 220
                                ])
                            },
                            'standingsApis': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'playoffsApi|standingsApi',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 260
                                ])
                            },
                            'cmsApis': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'collaborationApi|clientApi|autotaggingApiV2|customizationApi|imageApi',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 300
                                ])
                            },
                            'otherApis': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'formGuideApi|sportEntityCustomizationApi|sportsStatisticsApi',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 340
                                ])
                            },
                            'articleSchedulerApi': {
                                pipelineUtils.executeTestsWithRetry([
                                        groups: 'articleSchedulerApi',
                                        environment: EXECUTION_ENVIRONMENT,
                                        project: 'api-tests-backend-sports',
                                        delay: 380
                                ])
                            },
                    ]

                    parallel parallelTests
                }
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        junit '**/target/surefire-reports/*.xml'
                        allure([
                                includeProperties: false,
                                jdk: '',
                                properties: [],
                                reportBuildPolicy: 'ALWAYS',
                                results: [[path: '**/target/allure-results']]
                        ])
                        zip zipFile: './system-tests-cms/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                        sh(script: '''
                        curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./system-tests-cms/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                        ''')
                    }
                }
                success {
                    script {
                        def testResults = junit testResults: '**/target/surefire-reports/*.xml'
                        def testSummary = pipelineUtils.formatTestResults(testResults)

                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary} \n More info at: ${env.BUILD_URL}")
                    }
                }
                failure {
                    script {
                        env.TEST_SUMMARY_AI = pipelineUtils.analyzeAllureResultsUsingAI(params.AI_ANALYSIS)
                        def testResults = junit testResults: '**/target/surefire-reports/*.xml'
                        def testSummary = pipelineUtils.formatTestResults(testResults)
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${testSummary}\n ${env.TEST_SUMMARY_AI}\n More info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure:\n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}
