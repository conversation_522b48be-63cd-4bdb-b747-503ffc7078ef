pipeline {
    agent  {
        node {
            label 'qa-win'
        }
    }

    parameters {
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: ['staging', 'integration'],
                description: '''Test execution environment:
                - staging: For pre-production testing
                - integration: For development testing'''
        )
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'
                ],
                description: '''Slack channel for test results:
                - #platform-reports: Platform test reports
                - #qa-automation: Automation framework updates
                - #test-executions: General test execution updates'''
        )
    }

    stages {
        stage('Environment Setup') {
            steps {
                echo "Preparing to run tests in the ${params.ENVIRONMENT} environment"
            }
        }

        stage('Run API Tests') {
            steps {
                script {
                    def jobsList = [
                            'AutoTaggingApiV2-Tests',
                            'BasketballApi-Tests',
                            'ClientApi-Tests',
                            'CollaborationApi-Tests',
                            'ContentApi-Tests',
                            'CustomizationApi-Tests',
                            'FootballApi-Tests',
                            'FormGuideApi-Tests',
                            'ImageApi-Tests',
                            'MultiSportApi-Tests',
                            'OddsApi-Tests',
                            'PlayoffsApi-Tests',
                            'SearchApiV2-Tests',
                            'SportEntityCustomizationApi-Tests',
                            'StandingsApi-Tests',
                            'TennisApi-Tests'
                    ]

                    def parallelStages = jobsList.collectEntries { job ->
                        ["${job}" : {
                            catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {
                                def result = build job: "Tests by API/${job}", parameters: [
                                        string(name: 'EXECUTION_ENVIRONMENT', value: params.ENVIRONMENT),
                                        string(name: 'SLACK_CHANNEL_NOTIFICATION', value: params.SLACK_CHANNEL_NOTIFICATION),
                                        booleanParam(name: 'EXECUTE_TESTS', value: true)
                                ], propagate: false
                                try {
                                    archiveArtifacts artifacts: '**/TestData/**/*', fingerprint: true
                                }
                                catch (exc) {
                                    echo 'No Test Artefacts Found!' + exc.getMessage()
                                }

                            }
                        }]
                    }

                    parallel parallelStages
                }
            }
        }
    }

    post {
        always {
            echo "Test execution in the ${params.ENVIRONMENT} environment has finished. Check the test results for details."
        }
        success {
            echo "All API tests passed successfully in the ${params.ENVIRONMENT} environment."
        }
        unstable {
            echo "Some tests failed in the ${params.ENVIRONMENT} environment. The build is marked as unstable."
        }
        failure {
            echo "The pipeline encountered a critical failure while running tests in the ${params.ENVIRONMENT} environment."
        }
    }
}