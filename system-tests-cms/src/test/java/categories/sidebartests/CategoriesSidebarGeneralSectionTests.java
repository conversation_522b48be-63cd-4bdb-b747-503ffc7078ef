package categories.sidebartests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.GeneralSectionProperty;
import data.models.categories.Category;
import factories.categories.CategoryFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Objects;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CATEGORIES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.GENERAL_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.CATEGORIES)
@Story(CMSStories.CONFIGURATION)
public class CategoriesSidebarGeneralSectionTests extends BaseSidebarTests {

    private static final String PROPERTY_STATE_ERROR_MESSAGE = "The property '%s' is not presented into the request or is not in the expected state!";
    private Category createdCategory;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        categoriesPage.openCreatePage();
        categoriesPage.waitPageToLoad();
        categoriesPage.generalSection().expand();
        ProxyServer.newHar();
        Category categoryRequiredFields = new CategoryFieldsFactory().buildCategoryRequiredFields();
        categoriesPage.setRequiredField(categoryRequiredFields.getTitle());
    }

    @Override
    public void afterEach() {
        super.afterEach();
        contentApiFacade.deleteCategory(createdCategory.getId());
    }

    @Test
    public void categorySaved_when_uncheckedProperties() {
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.IMPORTANT.getLabel()).uncheck();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.ADULT_CONTENT.getLabel()).uncheck();

        categoriesPage.saveCreateCategory();
        createdCategory = categoriesPage.getCreateCategoryResponse();
        categoriesPage.asserts().validateSaveSuccessful();

        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.FOR_TRANSLATION.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.BREAKING_NEWS.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.EVERGREEN.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.WITH_MATCH_ADS.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.SENSITIVE_CONTENT.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.BETTING_CONTENT.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.LIVE.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.IMPORTANT.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.ADS_ENABLED.getLabel()).validateIsUnchecked();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.ADULT_CONTENT.getLabel()).validateIsUnchecked();
    }

    @Test
    public void properDataExistsInContentApiRequest_when_checkProperty_and_save() {
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.FOR_TRANSLATION.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.BREAKING_NEWS.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.EVERGREEN.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.WITH_MATCH_ADS.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.SENSITIVE_CONTENT.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.BETTING_CONTENT.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.LIVE.getLabel()).check();
        categoriesPage.generalSection().map().propertyCheckbox(GeneralSectionProperty.ADS_ENABLED.getLabel()).check();

        categoriesPage.saveCreateCategory();
        createdCategory = categoriesPage.getCreateCategoryResponse();
        categoriesPage.asserts().validateSaveSuccessful();

        Category categoryRequest = Objects.requireNonNull(categoriesPage.getCreateCategoryResponse());

        Assertions.assertAll(
                () -> Assertions.assertTrue(categoryRequest.getForTranslation(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.FOR_TRANSLATION.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getBreakingNews(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.BREAKING_NEWS.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getEvergreen(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.EVERGREEN.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getWithMatchAds(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.WITH_MATCH_ADS.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getSensitiveContent(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.SENSITIVE_CONTENT.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getBettingContent(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.BETTING_CONTENT.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getLive(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.LIVE.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getAdsEnabled(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.ADS_ENABLED.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getImportant(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.IMPORTANT.getLabel())),
                () -> Assertions.assertTrue(categoryRequest.getAdultContent(),
                        String.format(PROPERTY_STATE_ERROR_MESSAGE, GeneralSectionProperty.ADULT_CONTENT.getLabel()))
        );
    }
}