package navigation;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.CmsPage;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.auditlogpage.AuditLogPage;
import pages.dashboardpage.DashboardPage;
import pages.liveblogspages.liveblogspage.LiveBlogsPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.NAVIGATION)
@Story(CMSStories.NAVIGATION)
public class AllFeaturesNavigationTests extends CmsWebTest {
    private DashboardPage dashboardPage;

    @Override
    protected void beforeEach() {
        dashboardPage = app().createPage(DashboardPage.class);
    }

    @Disabled
    @Tag(SMPCategories.DISABLED)
    @Test
    public void auditLogPageLoaded_when_navigateThroughGeneralSideBar() {
        AuditLogPage auditLogPage = new AuditLogPage();
        dashboardPage.sidebar().navigateTo(CmsPage.AUDIT_LOG);

        auditLogPage.asserts().pageLoaded();
    }

    @Test
    public void liveBlogsPageLoaded_when_navigateThroughGeneralSideBar() {
        LiveBlogsPage liveBlogsPage = new LiveBlogsPage();
        dashboardPage.sidebar().navigateTo(CmsPage.LIVE_BLOGS_SMP);

        liveBlogsPage.asserts().pageLoaded();
    }
}