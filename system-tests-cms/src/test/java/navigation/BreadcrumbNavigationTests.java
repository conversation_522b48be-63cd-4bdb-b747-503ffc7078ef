package navigation;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.configuration.SportalSettings;
import data.constants.CmsPage;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.User;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.NAVIGATION)
@Story(CMSStories.NAVIGATION)
public class BreadcrumbNavigationTests extends CmsWebTest {

    private ArticleFormPage articleFormPage;

    @Override
    protected void beforeEach() {
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();
    }

    @Test
    public void breadcrumbContainsProperLinks_when_visitCreateContentPage() {
        var firstItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(0);
        var secondItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(1);
        var thirdItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(2);
        var activeItem = articleFormPage.breadcrumbNavigation().getActiveBreadcrumbsItemText();

        Assertions.assertEquals(CmsPage.DASHBOARD.toString().toLowerCase(), firstItem.toLowerCase());
        Assertions.assertEquals(CmsPage.ARTICLES.toString().toLowerCase(), secondItem.toLowerCase());
        Assertions.assertEquals(activeItem, thirdItem);
        Assertions.assertEquals("Create Article", activeItem);
    }

    @Test
    public void firstPageLoaded_when_clickFirstInactiveBreadcrumbElement() {
        var firstItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(0);

        Assertions.assertEquals(CmsPage.DASHBOARD.toString().toLowerCase(), firstItem.toLowerCase());
        articleFormPage.breadcrumbNavigation().navigateToBreadcrumbItem(firstItem);

        Assertions.assertEquals(ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.DASHBOARD.href, app().browser().getUrl());
    }

    @Test
    public void SMP_T526_secondPageLoaded_when_clickSecondInactiveBreadcrumbElement() {
        var secondItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(1);

        Assertions.assertEquals(CmsPage.ARTICLES.toString().toLowerCase(), secondItem.toLowerCase());
        articleFormPage.breadcrumbNavigation().navigateToBreadcrumbItem(secondItem);

        Assertions.assertEquals(ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.ARTICLES.href, app().browser().getUrl());
    }

    @Test
    public void noActionTaken_when_clickActiveBreadcrumbElement() {
        var thirdItem = articleFormPage.breadcrumbNavigation().getBreadcrumbsItems().get(2);
        var activeItem = articleFormPage.breadcrumbNavigation().getActiveBreadcrumbsItemText();
        Assertions.assertEquals(activeItem, thirdItem);
        Assertions.assertEquals("Create Article", activeItem);
        articleFormPage.breadcrumbNavigation().navigateToBreadcrumbItem(thirdItem);

        Assertions.assertEquals(ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.ARTICLES.href + "/create", app().browser().getUrl());
    }
}