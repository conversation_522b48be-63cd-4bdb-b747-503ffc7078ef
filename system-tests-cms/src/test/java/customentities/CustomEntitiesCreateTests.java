package customentities;

import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.CmsPage;
import data.constants.StringConstants;
import data.constants.enums.CustomEntitiesFieldsEnum;
import data.constants.enums.CustomEntityEnum;
import data.constants.enums.ToastMessageEnum;
import data.models.searchapi.ResultModel;
import data.models.uimodels.customentities.CustomEntitiesFormModel;
import factories.customentities.CustomEntitiesFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import pages.customentitiesformpage.CustomEntitiesFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CUSTOM_ENTITIES)
@Tag(CMSTags.CUSTOM_ENTITIES_CREATE)
@Story(CMSTags.CUSTOM_ENTITIES)
@Story(CMSTags.CUSTOM_ENTITIES_CREATE)
public class CustomEntitiesCreateTests extends CmsWebTest {

    private CustomEntitiesFormPage customEntitiesFormPage;
    private CustomEntitiesFormModel customEntityData;
    private ResultModel createdPlace;
    private ResultModel createdOrganization;
    private ResultModel domain;

    @Override
    protected void beforeAll() {
        super.beforeAll();
        domain = searchApiV2Facade.getDomain();
        createdPlace = searchApiV2Facade.createPlaceCustomEntityRequiredFields(domain);
        createdOrganization = searchApiV2Facade.createOrganizationCustomEntityRequiredFields(domain);
    }

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        customEntitiesFormPage = app().createPage(CustomEntitiesFormPage.class);
        customEntitiesFormPage.sidebar().navigateTo(CmsPage.CUSTOM_ENTITIES_LISTING);
        customEntitiesFormPage.sidebar().navigateTo(CmsPage.CUSTOM_ENTITIES_CREATE);
        customEntityData = CustomEntitiesFieldsFactory.buildRequiredFields(domain, CustomEntityEnum.PERSON);
    }

    @Override
    protected void afterClass() {
        searchApiV2Facade.deleteCreatedCustomEntitiesWithoutDomain();
    }

    @Test
    public void expectedPageElementsDisplayed_when_openCreateCustomEntitiesPage() {
        customEntitiesFormPage.asserts().assertDefaultPageElements();
    }

    @ParameterizedTest
    @EnumSource(value = CustomEntityEnum.class, names = {"PERSON", "ORGANIZATION", "PLACE", "ROLE"}, mode = EnumSource.Mode.INCLUDE)
    public void expectedErrorMessagesDisplayed_when_selectEntityTypeAndDomain_and_saveWithoutFillingRequiredFields(CustomEntityEnum customEntity) {
        customEntitiesFormPage.selectEntityType(customEntity)
                .selectDomain(domain.getName())
                .saveEntity()
                .asserts()
                .assertToastMessageDisplayedWithMessage(ToastMessageEnum.PLEASE_FILL_IN_REQUIRED_FIELDS)
                .assertErrorMessageForFieldDisplayed(CustomEntitiesFieldsEnum.SLUG);
    }

    @ParameterizedTest
    @EnumSource(value = CustomEntityEnum.class, names = {"DOMAIN"}, mode = EnumSource.Mode.EXCLUDE)
    public void customEntityCreatedSuccessfully_when_createCustomEntityWithMandatoryFieldsOnly(CustomEntityEnum entityType) {
        customEntityData.setEntityType(entityType);

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertEntityCreatedSuccessfully(customEntityData);
    }

    @ParameterizedTest
    @EnumSource(value = CustomEntityEnum.class, names = {"DOMAIN"}, mode = EnumSource.Mode.EXCLUDE)
    public void translationsLanguageDropdownDisabled_when_openCreateCustomEntitiesPage_and_selectEntityType(CustomEntityEnum customEntity) {
        customEntitiesFormPage.selectEntityType(customEntity)
                .selectDomain(domain.getName())
                .asserts()
                .assertTranslationsLanguageFieldDisabled();
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void expectedErrorMessageDisplayed_when_createPerson_withoutNameRequiredField(String name) {
        assertEntityNotCreatedWithoutNameRequiredField(CustomEntityEnum.PERSON, name);
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void expectedErrorMessageDisplayed_when_createOrganization_withoutNameRequiredField(String name) {
        assertEntityNotCreatedWithoutNameRequiredField(CustomEntityEnum.ORGANIZATION, name);
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void expectedErrorMessageDisplayed_when_createPlace_withoutNameRequiredField(String name) {
        assertEntityNotCreatedWithoutNameRequiredField(CustomEntityEnum.PLACE, name);
    }

    @ParameterizedTest
    @EnumSource(value = CustomEntityEnum.class, names = {"DOMAIN"}, mode = EnumSource.Mode.EXCLUDE)
    public void expectedErrorMessageDisplayed_when_createCustomEntity_and_setInvalidThreeLetterCode(CustomEntityEnum entityType) {
        customEntityData = CustomEntitiesFieldsFactory.buildRequiredFields(domain, entityType);
        customEntityData.setThreeLetterCode("xx");

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertToastMessageDisplayedWithMessage(ToastMessageEnum.FIELD_THREE_LETTER_CODE_INVALID);
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void expectedErrorMessageDisplayed_when_createRole_withoutNameRequiredField(String name) {
        assertEntityNotCreatedWithoutNameRequiredField(CustomEntityEnum.ROLE, name);
    }

    @Test
    public void personCreated_when_openCreateCustomEntitiesPage_and_createPersonWithAllFields() {
        customEntityData = CustomEntitiesFieldsFactory.buildPersonAllFields(domain, createdPlace.getName());

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertEntityCreatedSuccessfully(customEntityData);
    }

    @Test
    public void organizationCreated_when_openCreateCustomEntitiesPage_and_createOrganizationWithAllFields() {
        customEntityData = CustomEntitiesFieldsFactory
                .buildOrganizationAllFields(domain, createdPlace.getName(), createdOrganization.getName());

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertEntityCreatedSuccessfully(customEntityData);
    }

    @Test
    public void placeCreated_when_openCreateCustomEntitiesPage_and_createPlaceWithAllFields() {
        customEntityData = CustomEntitiesFieldsFactory.buildPlaceAllFields(domain, createdPlace.getName());

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertEntityCreatedSuccessfully(customEntityData);
    }

    @Test
    public void roleCreated_when_openCreateCustomEntitiesPage_and_createRoleWithAllFields() {
        customEntityData = CustomEntitiesFieldsFactory.buildRoleAllFields();

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertEntityCreatedSuccessfully(customEntityData);
    }

    @Test
    public void domainDropdownDisabled_when_openCreateCustomEntitiesPage_and_selectRoleEntityType() {
        customEntitiesFormPage.selectEntityType(CustomEntityEnum.ROLE)
                .asserts()
                .assertDomainFieldDisabled();
    }

    private void assertEntityNotCreatedWithoutNameRequiredField(CustomEntityEnum entityType, String nameValue) {
        customEntityData = CustomEntitiesFieldsFactory.buildRequiredFields(domain, entityType);
        customEntityData.setName(nameValue);

        customEntitiesFormPage.fillForm(customEntityData)
                .saveEntity()
                .asserts()
                .assertToastMessageDisplayedWithMessage(ToastMessageEnum.FIELD_NAME_CANNOT_BE_EMPTY);
    }
}