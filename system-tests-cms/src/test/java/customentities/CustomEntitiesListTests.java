package customentities;

import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.CmsPage;
import data.constants.enums.ToastMessageEnum;
import data.models.searchapi.ResultModel;
import data.models.uimodels.gridmodels.CustomEntitiesListGridModel;
import factories.ui.CustomEntitiesListGridModelFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.customentitieslistpage.CustomEntitiesListPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CUSTOM_ENTITIES)
@Tag(CMSTags.CUSTOM_ENTITIES_LIST)
@Story(CMSTags.CUSTOM_ENTITIES)
@Story(CMSTags.CUSTOM_ENTITIES_LIST)
public class CustomEntitiesListTests extends CmsWebTest {

    private CustomEntitiesListPage customEntitiesListPage;
    private ResultModel domain;
    private ResultModel createdPerson;
    private ResultModel createdOrganization;
    private ResultModel createdPlace;
    private ResultModel createdRole;
    private CustomEntitiesListGridModel expectedEntityGridRowValues;

    @Override
    protected void beforeAll() {
        super.beforeAll();
        domain = searchApiV2Facade.getDomain();
        createdPerson = searchApiV2Facade.createPersonCustomEntityRequiredFields(domain);
        createdOrganization = searchApiV2Facade.createOrganizationCustomEntityRequiredFields(domain);
        createdPlace = searchApiV2Facade.createPlaceCustomEntityRequiredFields(domain);
        createdRole = searchApiV2Facade.createRoleCustomEntityRequiredFields();
    }

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        customEntitiesListPage = app().createPage(CustomEntitiesListPage.class);
        customEntitiesListPage.sidebar().navigateTo(CmsPage.CUSTOM_ENTITIES_LISTING);
        customEntitiesListPage.clearSearchFilter();
    }

    @Test
    public void expectedRequestExecuted_when_openCustomEntitiesListPage() {
        customEntitiesListPage.asserts().assertSuggestRequestExecuted();
    }

    @Test
    public void expectedPageElementsDisplayed_when_openCustomEntitiesListPage() {
        customEntitiesListPage.asserts().assertPageElements();
    }

    @Test
    public void expectedValuesDisplayedInRowForDomain_when_searchDomainByName() {
        expectedEntityGridRowValues = CustomEntitiesListGridModelFactory.buildDomainExpectedGridRow(domain.getName());

        customEntitiesListPage.searchEntity(domain.getName())
                .asserts()
                .assertRowValues(expectedEntityGridRowValues);
    }

    @Test
    public void expectedValuesDisplayedInRowForPerson_when_searchPersonByName() {
        expectedEntityGridRowValues = CustomEntitiesListGridModelFactory
                .buildPersonExpectedGridRow(createdPerson.getName(), domain.getName());

        customEntitiesListPage.searchEntity(createdPerson.getName())
                .asserts()
                .assertRowValues(expectedEntityGridRowValues);
    }

    @Test
    public void expectedValuesDisplayedInRowForOrganization_when_searchOrganizationByName() {
        expectedEntityGridRowValues = CustomEntitiesListGridModelFactory
                .buildOrganizationExpectedGridRow(createdOrganization.getName(), domain.getName());

        customEntitiesListPage.searchEntity(createdOrganization.getName())
                .asserts()
                .assertRowValues(expectedEntityGridRowValues);
    }

    @Test
    public void expectedValuesDisplayedInRowForPlace_when_searchPlaceByName() {
        expectedEntityGridRowValues = CustomEntitiesListGridModelFactory
                .buildPlaceExpectedGridRow(createdPlace.getName(), domain.getName());

        customEntitiesListPage.searchEntity(createdPlace.getName())
                .asserts()
                .assertRowValues(expectedEntityGridRowValues);
    }

    @Test
    public void expectedValuesDisplayedInRowForRole_when_searchRoleByName() {
        expectedEntityGridRowValues = CustomEntitiesListGridModelFactory.buildRoleExpectedGridRow(createdRole.getName());

        customEntitiesListPage.searchEntity(createdRole.getName())
                .asserts()
                .assertRowValues(expectedEntityGridRowValues);
    }

    @Test
    public void customEntitySuccessfullyDeleted_when_deleteCustomEntity() {
        ResultModel createdPerson = searchApiV2Facade.createPersonCustomEntityRequiredFields(domain);
        customEntitiesListPage.browser().refresh();

        customEntitiesListPage.deleteEntity(createdPerson.getName())
                .asserts()
                .assertToastMessageDisplayedWithMessage(ToastMessageEnum.CUSTOM_ENTITY_SUCCESSFULLY_DELETED)
                .assertEntityDeleted(createdPerson.getName());
    }
}