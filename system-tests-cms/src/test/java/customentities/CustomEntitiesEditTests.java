package customentities;

import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.EntityTypeEnum;
import data.constants.enums.CustomEntityEnum;
import data.models.articles.ArticleResponseModel;
import data.models.searchapi.ResultModel;
import data.models.uimodels.customentities.CustomEntitiesFormModel;
import data.models.wikipages.WikiResponseModel;
import factories.customentities.CustomEntitiesFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.customentitiesformpage.CustomEntitiesFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CUSTOM_ENTITIES)
@Tag(CMSTags.CUSTOM_ENTITIES_EDIT)
@Story(CMSTags.CUSTOM_ENTITIES)
@Story(CMSTags.CUSTOM_ENTITIES_EDIT)
public class CustomEntitiesEditTests extends CmsWebTest {

    private CustomEntitiesFormPage customEntitiesFormPage;
    private ResultModel domain;
    private ResultModel createdPersonAPI;
    private ResultModel createdPersonWithWikiAPI;
    private ResultModel createdOrganizationAPI;
    private ResultModel createdOrganizationWithWikiAPI;
    private ResultModel createdPlaceAPI;
    private ResultModel createdRoleAPI;
    private WikiResponseModel wikiPageWithPerson;

    @Override
    protected void beforeAll() {
        super.beforeAll();
        domain = searchApiV2Facade.getDomain();
        createdPersonAPI = searchApiV2Facade.createPersonCustomEntityRequiredFields(domain);
        createdPersonWithWikiAPI = searchApiV2Facade.createPersonCustomEntityRequiredFields(domain);
        createdOrganizationAPI = searchApiV2Facade.createOrganizationCustomEntityRequiredFields(domain);
        createdOrganizationWithWikiAPI = searchApiV2Facade.createOrganizationCustomEntityRequiredFields(domain);
        createdPlaceAPI = searchApiV2Facade.createPlaceCustomEntityRequiredFields(domain);
        createdRoleAPI = searchApiV2Facade.createRoleCustomEntityRequiredFields();

        wikiPageWithPerson = contentApiFacade.createWiki();
        contentApiFacade.createPersonRelatedDataEntities(
                wikiPageWithPerson.convertToModel(ArticleResponseModel.class), EntityTypeEnum.WIKI_PAGES,
                createdPersonWithWikiAPI.getName()
        );
    }

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        customEntitiesFormPage = app().createPage(CustomEntitiesFormPage.class);
    }

    @Test
    public void expectedPageElementsDisplayed_when_openEditCustomEntitiesPage_person() {
        assertPageElementsAtEditScreenForCustomEntity(CustomEntityEnum.PERSON, createdPersonAPI.getId());
    }

    @Test
    public void expectedPageElementsDisplayed_when_openEditCustomEntitiesPage_organization() {
        assertPageElementsAtEditScreenForCustomEntity(CustomEntityEnum.ORGANIZATION, createdOrganizationAPI.getId());
    }

    @Test
    public void expectedPageElementsDisplayed_when_openEditCustomEntitiesPage_place() {
        assertPageElementsAtEditScreenForCustomEntity(CustomEntityEnum.PLACE, createdPlaceAPI.getId());
    }

    @Test
    public void expectedPageElementsDisplayed_when_openEditCustomEntitiesPage_role() {
        assertPageElementsAtEditScreenForCustomEntity(CustomEntityEnum.ROLE, createdRoleAPI.getId());
    }

    @Test
    public void personEntitySuccessfullyEdited_when_openEditCustomEntitiesPage_and_fillAllFields() {
        CustomEntitiesFormModel fieldsForEdit = CustomEntitiesFieldsFactory.buildPersonAllFields(domain, createdPlaceAPI.getName());

        customEntitiesFormPage.openEditPage(CustomEntityEnum.PERSON, createdPersonAPI.getId())
                .fillForm(fieldsForEdit)
                .saveEntity()
                .asserts()
                .assertEntityEditedSuccessfully(fieldsForEdit);
    }

    @Test
    public void organizationEntitySuccessfullyEdited_when_openEditCustomEntitiesPage_and_fillAllFields() {
        CustomEntitiesFormModel fieldsForEdit = CustomEntitiesFieldsFactory
                .buildOrganizationAllFields(domain, createdPlaceAPI.getName(), createdOrganizationWithWikiAPI.getName());

        customEntitiesFormPage.openEditPage(CustomEntityEnum.ORGANIZATION, createdOrganizationAPI.getId())
                .fillForm(fieldsForEdit)
                .saveEntity()
                .asserts()
                .assertEntityEditedSuccessfully(fieldsForEdit);
    }

    @Test
    public void placeEntitySuccessfullyEdited_when_openEditCustomEntitiesPage_and_fillAllFields() {
        ResultModel createdPlace = searchApiV2Facade.createPlaceCustomEntityRequiredFields(domain);
        CustomEntitiesFormModel fieldsForEdit = CustomEntitiesFieldsFactory.buildPlaceAllFields(domain, createdPlaceAPI.getName());

        customEntitiesFormPage.openEditPage(CustomEntityEnum.PLACE, createdPlace.getId())
                .fillForm(fieldsForEdit)
                .saveEntity()
                .asserts()
                .assertEntityEditedSuccessfully(fieldsForEdit);
    }

    @Test
    public void wikiPageFieldDisplayed_when_tagPersonInWikiPage_and_openEditPersonCustomEntityPage() {
        customEntitiesFormPage.openEditPage(CustomEntityEnum.PERSON, createdPersonWithWikiAPI.getId())
                .asserts()
                .assertWikiPageFieldDisplayed()
                .assertWikiPageFieldValue(wikiPageWithPerson);
    }

    @Test
    public void expectedWikiPageOpened_when_openEditPersonCustomEntityPage_and_clickOnWikiPageField() {
        customEntitiesFormPage.openEditPage(CustomEntityEnum.PERSON, createdPersonWithWikiAPI.getId())
                .clickOnWikiPageField()
                .asserts()
                .editPageLoaded(wikiPageWithPerson.getId());
    }

    @Test
    public void latestWikiPageAddedToCustomEntity_when_tagOrganizationInWikiPage_and_openEditOrganizationCustomEntityPage() {
        WikiResponseModel latestCreatedWiki = contentApiFacade.createWiki();
        contentApiFacade.createOrganizationRelatedDataEntities(
                latestCreatedWiki.convertToModel(ArticleResponseModel.class), EntityTypeEnum.WIKI_PAGES, createdOrganizationWithWikiAPI.getName()
        );

        customEntitiesFormPage.openEditPage(CustomEntityEnum.ORGANIZATION, createdOrganizationWithWikiAPI.getId())
                .asserts()
                .assertWikiPageFieldDisplayed()
                .assertWikiPageFieldValue(latestCreatedWiki);

        customEntitiesFormPage.clickOnWikiPageField()
                .asserts()
                .editPageLoaded(latestCreatedWiki.getId());
    }

    private void assertPageElementsAtEditScreenForCustomEntity(CustomEntityEnum entityType, String entityId) {
        customEntitiesFormPage.openEditPage(entityType, entityId)
                .asserts()
                .assertDefaultPageElements()
                .assertDefaultPageElementsForEachEntity()
                .assertPageElementsForEntity(entityType);
    }
}