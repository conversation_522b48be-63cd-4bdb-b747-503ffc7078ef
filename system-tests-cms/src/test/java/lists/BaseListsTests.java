package lists;

import core.CmsWebTest;
import data.models.articles.ArticleResponseModel;
import data.models.lists.ListModel;
import data.models.uimodels.AutomatedListFormModel;
import data.models.uimodels.ListFormModel;
import factories.lists.ListFieldsFactory;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

public class BaseListsTests extends CmsWebTest {

    protected static final String LIST_SUCCESSFULLY_CREATED = "List successfully created.";
    protected ListFormModel listedToBeCreated;
    protected AutomatedListFormModel automatedListedToBeCreated;
    protected ArticleResponseModel createdArticle;
    protected List<ArticleResponseModel> createdArticles;
    protected ListModel listCreatedViaAPI;
    protected String listIdFromUI;
    protected String listIdFromAPI;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        listedToBeCreated = ListFieldsFactory.buildListRequiredFields();
    }

    @Override
    public void afterEach() {
        super.afterEach();
        contentApiFacade.deleteList(listIdFromAPI, listIdFromUI);
        listIdFromAPI = null;
        listIdFromUI = null;
    }
}