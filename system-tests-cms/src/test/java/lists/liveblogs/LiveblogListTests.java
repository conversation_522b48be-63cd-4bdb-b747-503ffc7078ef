package lists.liveblogs;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.ListContentType;
import data.constants.StringConstants;
import data.models.lists.ListModel;
import data.models.lists.ListModelWithoutConfiguration;
import data.models.liveblogapi.LiveBlogResponseModel;
import facades.ContentApiFacade;
import facades.LiveBlogApiFacade;
import factories.liveblog.LiveBlogSportConfigurationSectionFieldsFactory;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CRUD)
@Tag(CMSTags.LIVEBLOG)
@Tag(CMSStories.LISTS)
@Tag(CMSStories.LIVEBLOG)
@Story(CMSStories.LISTS)
public class LiveblogListTests extends BaseLiveBlogTests {

    private LiveBlogResponseModel createdLiveBlog;
    protected ListModel listCreatedViaAPI;
    public LiveBlogResponseModel createdLiveblog;
    public LiveBlogApiFacade liveBlogApiFacade = new LiveBlogApiFacade(getCurrentTestProject());

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        configurationPage.openCreatePage();
        configurationPage.browser().refresh();
        liveBlogsPage.waitPageToLoad();
        createdLiveBlog = null;

        listCreatedViaAPI = new ContentApiFacade().createList(ListContentType.EDITORIAL);
        var sportConfigurationSectionData = LiveBlogSportConfigurationSectionFieldsFactory.buildAllFields(footballApiFacade.getNotStartedMatch());
        configurationPage.completeEventDetailsSection(eventDetailsSectionData);
        configurationPage.fillSportConfigurationSectionAndProceed(sportConfigurationSectionData);
        configurationPage.waitPageToLoad();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteList(listCreatedViaAPI.getId());
        liveBlogApiFacade.deleteLiveBlogs(createdLiveblog);
    }

    @Test
    public void liveBlogSuccessfullyRemoved_when_lastItemInEditorialList() {
        liveBlogsPage.configurationPage().clickCreateButton();
        configurationPage.openEditPageForLiveBlog(liveBlogsPage.getCreatedLiveBlogResponse().getId());
        configurationPage.clickAdditionalSettingsSection();
        additionalSettingsSection.addToList(listCreatedViaAPI.getTitle());

        createdLiveBlog = liveBlogsPage.getCreatedLiveBlogResponse();
        listContentPage.openEditContentPage(listCreatedViaAPI.getId());
        editorialListsPage.map().removeContentButton().click();
        editorialListsPage.asserts().validateUpdateSuccessful();
        ListModelWithoutConfiguration editedEditorialList = editorialListsPage.getEditListResponse(listCreatedViaAPI.getId());

        Assertions.assertNotNull(editedEditorialList, AssertMessages.requestNotCreated(StringConstants.EDITORIAL_LIST_STRING));
        Assertions.assertEquals(listCreatedViaAPI.getId(), editedEditorialList.getId(), AssertMessages.entityNotExpected(StringConstants.ID_STRING));
        Assertions.assertEquals(listCreatedViaAPI.getEntityType(), editedEditorialList.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));
        Assertions.assertEquals(listCreatedViaAPI.getContentType(), editedEditorialList.getContentType(), AssertMessages.responseNotContains(StringConstants.CONTENT_TYPE_STRING));
        editContentListsPage.asserts().assertCountOfSelectedContent("0", editContentListsPage.getNumberOfSelectedContent());
    }
}