package core;

import blockies.football.lineupsblocky.LineupsBlocky;
import data.constants.EventStatusType;
import data.constants.WidgetBlock;
import data.constants.api.queryparamenums.StatusType;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.constants.enums.enetpulseproxy.IceHockeyCompetition;
import data.constants.enums.football.FootballTeamEnum;
import data.constants.enums.football.FootballTournamentEnum;
import data.constants.enums.tennis.TennisCompetitionName;
import data.models.articles.ArticleModel;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballSingleEventBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballStandingsBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballTeamProgrammeBlockyFieldsModel;
import data.models.blockymodels.basketball.BasketballTournamentProgrammeBlockyFieldsModel;
import data.models.blockymodels.football.*;
import data.models.blockymodels.icehockey.IceHockeySingleEventBlockyFieldsModel;
import data.models.blockymodels.icehockey.IceHockeyStandingsBlockyFieldsModel;
import data.models.blockymodels.tennis.*;
import data.widgets.options.enums.FootballPlayerEnum;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.basketball.*;
import factories.blockies.football.*;
import factories.blockies.icehockey.IceHockeyLivescoreBlockyFieldsFactory;
import factories.blockies.icehockey.IceHockeySingleEventBlockyFieldsFactory;
import factories.blockies.icehockey.IceHockeyStandingsBlockyFieldsFactory;
import factories.blockies.tennis.*;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;

import static data.constants.enums.football.FootballTeamEnum.LIVERPOOL;

public class BaseSportArticleE2ETest extends BaseE2ETest {

    private static final FootballTeamEnum TEAM_1 = FootballTeamEnum.AL_NASSR_FC;
    private static final FootballTeamEnum TEAM_2 = FootballTeamEnum.INTER_MIAMI;
    private static final FootballPlayerEnum PLAYER_1 = FootballPlayerEnum.CRISTIANO_RONALDO;
    private static final FootballPlayerEnum PLAYER_2 = FootballPlayerEnum.LIONEL_MESSI;

    protected IceHockeySingleEventBlockyFieldsModel addIceHockeySingleEventBlockyFieldsModel() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_SINGLE_EVENT);

        IceHockeySingleEventBlockyFieldsModel blockyData = iceHockeyApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getIceHockeySingleEventBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);

        ProxyServer.newHar();
        return blockyData;
    }

    protected IceHockeyStandingsBlockyFieldsModel addIceHockeyStanindgsBlockyFieldsModel() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_STANDINGS_WIDGET);

        IceHockeyStandingsBlockyFieldsModel blockyData = iceHockeyApiFacade.getStandingsSportData(IceHockeyCompetition.NHL);
        blockyData = IceHockeyStandingsBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getIceHockeyStandingsBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);

        ProxyServer.newHar();
        return blockyData;
    }

    protected LivescoreBlockyFieldsModel addIceHockeyLivescoreBlockyFieldsModel() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_LIVESCORE);

        LivescoreBlockyFieldsModel blockyData = iceHockeyApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = IceHockeyLivescoreBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getIceHockeyLivescoreBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);

        ProxyServer.newHar();
        return blockyData;
    }

    protected TennisAthleteProgrammeBlockyFieldsModel addTennisAthleteProgrammeBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.TENNIS_ATHLETE_PROGRAMME);

        TennisAthleteProgrammeBlockyFieldsModel blockyData = tennisApiFacade.getAthleteProgrammeBlockySportData();
        blockyData = TennisAthleteProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisAthleteProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock();

        ProxyServer.newHar();
        return blockyData;
    }

    protected LivescoreBlockyFieldsModel addLivescoreTennisBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.TENNIS_LIVESCORE);

        LivescoreBlockyFieldsModel blockyData = tennisApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = TennisLivescoreBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisLivescoreBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected TennisPlayoffBlockyFieldsModel addTennisPlayoffBlocky() {
        articleFormPage.addBlocky(WidgetBlock.TENNIS_PLAYOFF);

        TennisPlayoffBlockyFieldsModel blockyData = tennisApiFacade.getPlayoffBlockySportData(TennisCompetitionName.IASI_OPEN);
        blockyData = TennisPlayoffBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisPlayoffBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected TennisRankingBlockyFieldsModel addTennisRankingBlocky() {
        articleFormPage.addBlocky(WidgetBlock.TENNIS_RANKING);

        TennisRankingBlockyFieldsModel blockyData = tennisApiFacade.getRankingBlockySportData();
        blockyData = TennisRankingBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisRankingBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock();

        ProxyServer.newHar();
        return blockyData;
    }

    protected TennisSingleEventBlockyFieldsModel addTennisSingleEventBlocky() {
        articleFormPage.addBlocky(WidgetBlock.TENNIS_SINGLE_EVENT);

        TennisSingleEventBlockyFieldsModel blockyData = tennisApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);
        blockyData = TennisSingleEventBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisSingleEventBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        ProxyServer.newHar();
        return blockyData;
    }

    protected TennisTournamentProgrammeBlockyFieldsModel addTennisTournamentProgrammeBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.TENNIS_TOURNAMENT_PROGRAMME);

        TennisTournamentProgrammeBlockyFieldsModel blockyData = tennisApiFacade.getTournamentProgrammeSportData(TennisCompetitionName.WIMBLEDON);
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTennisTournamentProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballKnockoutBlockyFieldsModel addKnockoutFootballBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_KNOCKOUT_SCHEME_WIDGET);

        FootballKnockoutBlockyFieldsModel blockyData = footballApiFacade.getKnockoutBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballKnockoutBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getKnockoutSchemeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballLineupsBlockyFieldsModel addFootballLineupBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        handleDraftDetectedStartFromScratch();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LINEUPS);

        FootballLineupsBlockyFieldsModel blockyData = footballApiFacade.getLineupsBlockySportData(EventStatusType.FINISHED);
        blockyData = FootballLineupsBlockyFieldsFactory.buildAllFields(blockyData);

        LineupsBlocky blockySection = articleFormPage.mainSection().getLineupsBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        return blockyData;
    }

    protected LivescoreBlockyFieldsModel addLivescoreFootballBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LIVESCORE_WIDGET);

        LivescoreBlockyFieldsModel blockyData = footballApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = FootballLivescoreBlockyFieldsFactory.buildAllFields(blockyData);

        var livescoreBlocky = articleFormPage.mainSection().getFootballLivescoreBlocks().get(0);
        blockyData.setEmbedCode(livescoreBlocky.getExpectedEmbedCode(blockyData));
        livescoreBlocky.waitEditScreenToLoad();
        livescoreBlocky.fillForm(blockyData);
        livescoreBlocky.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballMatchesH2HBlockyFieldsModel addFootballMatchesH2HBlocky() {
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_MATCHES_H2H);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();

        FootballMatchesH2HBlockyFieldsModel blockyData = footballApiFacade.getMatchesH2HBlockySportData(TEAM_1, TEAM_2);
        blockyData = FootballMatchesH2HBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getFootballMatchesH2HBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballMostDecoratedPlayersBlockyFieldsModel addFootballMostDecoratedPlayersBlocky() {
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_MOST_DECORATED_PLAYERS);

        FootballMostDecoratedPlayersBlockyFieldsModel blockyData = footballApiFacade.getMostDecoratedPlayersBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballMostDecoratedPlayersBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getMostDecoratedBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballOddsBlockyFieldsModel addFootballOddsBlocky() {
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_ODDS);

        FootballOddsBlockyFieldsModel blockyData = footballApiFacade.getOddsBlockySportData();
        blockyData = FootballOddsBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getOddsBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballPlayerH2HBlockyFieldsModel addFootballPlayerH2HBlocky() {
        articleFormPage.addBlocky(WidgetBlock.PLAYER_H2H);

        FootballPlayerH2HBlockyFieldsModel blockyData = footballApiFacade.getPlayerH2HBlockySportData(PLAYER_1, PLAYER_2);
        blockyData = FootballPlayerH2HBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getPlayerH2hBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballPlayerProfileBlockyFieldsModel addFootballPlayerProfileBlocky() {
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_PLAYER_WIDGET);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();

        FootballPlayerProfileBlockyFieldsModel blockyData = footballApiFacade.getPlayerProfileBlockySportData(PLAYER_1);
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getPlayerProfileBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballSingleEventBlockyFieldsModel addFootballSingleEventBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_SINGLE_EVENT);

        FootballSingleEventBlockyFieldsModel blockyData = footballApiFacade.getSingleEventBlockySportData(TEAM_1.getName(), EventStatusType.FINISHED);
        blockyData = FootballSingleEventBlockyFieldsFactory.buildAllFields(blockyData);
        blockyData.setDataHeaderDisplay(false);

        var blockySection = articleFormPage.mainSection().getFootballSingleEventBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballStandingsBlockyFieldsModel addFootballStandingBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_STANDINGS_WIDGET);

        FootballStandingsBlockyFieldsModel blockyData = footballApiFacade.getStandingsBlockySportData(FootballTournamentEnum.SAUDI_PRO_LEAGUE);
        blockyData = FootballStandingsBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getStandingsBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballFormBlockyFieldsModel addFootballTeamFormBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.TEAM_FORM_WIDGET);

        FootballFormBlockyFieldsModel blockyData = footballApiFacade.getTeamFormBlockySportData(LIVERPOOL);
        blockyData = FootballTeamFormBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamFormBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTeamH2HBlockyFieldsModel addFootballTeamH2HBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_H2H);

        FootballTeamH2HBlockyFieldsModel blockyData = footballApiFacade.getTeamH2HBlockySportData(TEAM_1, TEAM_2);
        blockyData = FootballTeamH2HBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamH2hBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTeamH2HMatchesBlockyFieldsModel addFootballTeamH2HMatchesBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.map().paragraphList().get(0).addBlock(WidgetBlock.FOOTBALL_MATCHES_H2H);

        FootballTeamH2HMatchesBlockyFieldsModel blockyData = footballApiFacade.getTeamH2HMatchesBlockySportData(StatusType.FINISHED);
        blockyData = FootballTeamH2HMatchesBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamH2hWithMatchBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTeamProfileBlockyFieldsModel addFootballTeamProfileBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_PROFILE_WIDGET);

        FootballTeamProfileBlockyFieldsModel blockyData = footballApiFacade.getTeamProfileBlockySportData();
        blockyData = FootballTeamProfileBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamProfileBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTeamProgrammeBlockyFieldsModel addFootballTeamProgrammerBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_PROGRAMME_WIDGET);

        FootballTeamProgrammeBlockyFieldsModel blockyData = footballApiFacade.getTeamProgrammeBlockySportData(TEAM_1);
        blockyData = FootballTeamProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTeamSquadBlockyFieldsModel addFootballTeamSquadBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_SQUAD_WIDGET);

        FootballTeamSquadBlockyFieldsModel blockyData = footballApiFacade.getTeamSquadBlockySportData(TEAM_1);
        blockyData = FootballTeamSquadBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTeamSquadBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTopScorersBlockyFieldsModel addFootballTopScorersBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOP_SCORERS);

        FootballTopScorersBlockyFieldsModel blockyData = footballApiFacade.getTopScorersBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballTopScorersBlockyFieldsFactory.buildAllFields(blockyData);
        /*
          TODO: ipetkov @ 22.08.2024: This is a temporary solution. Remove it once we found out how to get players in team for specific season
          Currently we are not able to get players in team for specific season
         */
        blockyData.setTeam(null);

        var blockySection = articleFormPage.mainSection().getTopScorersBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected FootballTournamentProgrammeBlockyFieldsModel addFootballTournamentProgrammeBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOURNAMENT_PROGRAMME_WIDGET);

        FootballTournamentProgrammeBlockyFieldsModel blockyData = footballApiFacade.getTournamentProgrammeBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        ProxyServer.newHar();
        return blockyData;
    }

    protected LivescoreBlockyFieldsModel addLivescoreBasketballBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_LIVESCORE_WIDGET);
        var date = LocalDate.now();

        LivescoreBlockyFieldsModel blockyData = basketballApiFacade.getLivescoreBlockySportData(date);
        blockyData = BasketballLivescoreBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getBasketballLivescoreBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        return blockyData;
    }

    protected BasketballSingleEventBlockyFieldsModel addBasketballSingleEventBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_SINGLE_EVENT);

        BasketballSingleEventBlockyFieldsModel blockyData = basketballApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getBasketballSingleEventBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();

        return blockyData;
    }

    protected BasketballStandingsBlockyFieldsModel addBasketballStandingsBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_STANDINGS_WIDGET);

        BasketballStandingsBlockyFieldsModel blockyData = basketballApiFacade.getStandingsSportData(BasketballCompetitionName.NBA);
        blockyData = BasketballStandingsBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getBasketballStandingsBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        return blockyData;
    }

    protected BasketballTeamProgrammeBlockyFieldsModel addBasketballTeamProgrammeBlocky() {
        BasketballTeamEnum teamName = BasketballTeamEnum.CHICAGO_BULLS;
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_TEAM_PROGRAMME_WIDGET);

        BasketballTeamProgrammeBlockyFieldsModel blockyData = basketballApiFacade.getTeamProgrammeSportData(teamName);
        blockyData = BasketballTeamProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getBasketballTeamProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        return blockyData;
    }

    protected BasketballTournamentProgrammeBlockyFieldsModel addBasketballTournamentProgrammeBlocky() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET);

        BasketballTournamentProgrammeBlockyFieldsModel blockyData = basketballApiFacade.getTournamentProgrammeSportData(BasketballCompetitionName.NBA);
        blockyData = BasketballTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);

        var blockySection = articleFormPage.mainSection().getBasketballTournamentProgrammeBlocks().get(0);
        blockyData.setEmbedCode(blockySection.getExpectedEmbedCode(blockyData));
        blockySection.waitEditScreenToLoad();
        blockySection.fillForm(blockyData);
        blockySection.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);
        return blockyData;
    }

    protected void assertIceHockeyLivescoreBlockyRequest(LivescoreBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getIceHockeyLivescoreBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElseThrow();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertIceHockeyStandingBlockyRequest(IceHockeyStandingsBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getIceHockeyStandingsBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertIceHockeySingleEventBlockyRequest(IceHockeySingleEventBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getIceHockeySingleEventBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballLivescoreBlockyRequest(LivescoreBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getFootballLivescoreBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTournamentProgrammeBlockyRequest(FootballTournamentProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTopScorersBlockyRequest(FootballTopScorersBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTopScorersBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTeamProgrammeBlockyRequest(FootballTeamProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTeamProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTeamFormBlockyRequest(FootballFormBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTeamFormBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballPlayerBlockyRequest(FootballPlayerProfileBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getPlayerProfileBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTeamProfileBlockyRequest(FootballTeamProfileBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTeamProfileBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTeamsH2HBlockyRequest(FootballTeamH2HBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTeamH2hBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballPlayerH2HBlockyRequest(FootballPlayerH2HBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getPlayerH2hBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballLineupsBlockyRequest(FootballLineupsBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getLineupsBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballOddsBlockyRequest(FootballOddsBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getOddsBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballSingleEventBlockyRequest(FootballSingleEventBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getFootballSingleEventBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballTeamSquadBlockyRequest(FootballTeamSquadBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTeamSquadBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballMatchesH2HBlockyRequest(FootballMatchesH2HBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getFootballMatchesH2HBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballStandingsBlockyRequest(FootballStandingsBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getStandingsBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballMostDecoratedPlayersBlockyRequest(FootballMostDecoratedPlayersBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getMostDecoratedBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertFootballKnockoutBlockyRequest(FootballKnockoutBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getKnockoutSchemeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertBasketballTournamentProgrammeBlockyRequest(BasketballTournamentProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getBasketballTournamentProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertBasketballLivescoreBlockyRequest(LivescoreBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getBasketballLivescoreBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertBasketballSingleEventBlockyRequest(BasketballSingleEventBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getBasketballSingleEventBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertBasketballStandingsBlockyRequest(BasketballStandingsBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getBasketballStandingsBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().orElse(null);
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertBasketballTeamProgrammeRequest(BasketballTeamProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getBasketballTeamProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisAthleteBlockyRequest(TennisAthleteProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisAthleteProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisLivescoreRequest(LivescoreBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisLivescoreBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisPlayoffBlockyRequest(TennisPlayoffBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisPlayoffBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisRankingBlockyRequest(TennisRankingBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisRankingBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisSingleEventRequest(TennisSingleEventBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisSingleEventBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }

    protected void assertTennisTournamentRequest(TennisTournamentProgrammeBlockyFieldsModel blockyData, ArticleModel request) {
        var blocky = articleFormPage.mainSection().getTennisTournamentProgrammeBlocks().get(0);
        var bodyObject = request.getBody().stream().filter(x -> x.getData().getWidgetType() != null && x.getData().getWidgetType().equals(blocky.getBlockyType().getWidgetType())).findFirst().get();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, bodyObject);
    }
}