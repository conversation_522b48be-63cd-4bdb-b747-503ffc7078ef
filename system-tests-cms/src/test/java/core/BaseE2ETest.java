package core;

import com.github.javafaker.Faker;
import data.constants.*;
import data.constants.video.VideoConstants;
import data.constants.video.VideoUrlsType;
import data.models.articles.ArticleResponseModel;
import data.models.authors.AuthorModel;
import data.models.banners.BannerModel;
import data.models.categories.Category;
import data.models.galleries.GalleryResponseModel;
import data.models.images.ImageModel;
import data.models.lists.ListModel;
import data.models.tags.TagModel;
import data.models.uimodels.ImageUpdateFormModel;
import data.models.uimodels.VideoFormModel;
import data.models.videos.VideoFileModel;
import data.models.videos.VideoResponseModel;
import data.models.wikipages.WikiResponseModel;
import data.utils.StringUtils;
import factories.images.ImageUpdateFieldsFactory;
import factories.lists.ListFieldsFactory;
import factories.tags.TagFieldsFactory;
import factories.video.VideoFieldsFactory;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.core.assertions.EntitiesAsserter;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDateTime;
import java.util.List;

import static data.constants.StringConstants.*;

public abstract class BaseE2ETest extends CmsWebTest {

    protected static final String EXPECTED_TITLE = "E2E Auto Critical Functionality Title";
    protected static final String EXPECTED_ARTICLE_CONTENT = "E2E Auto Critical Functionality Paragraph Text";
    protected static final String EXPECTED_UPDATED_TITLE = "%s %s".formatted(UPDATED_STRING, EXPECTED_TITLE);
    protected static final String EXPECTED_UPDATED_BANNER_CODE = "[Updated] Banner CODE";
    protected static final String EXPECTED_BANNER_CODE = "Banner CODE";
    protected static final String EXPECTED_FOOTBALL_CATEGORY = CategoryEnum.FOOTBALL.getName();
    protected static final String EXPECTED_TENNIS_CATEGORY = CategoryEnum.TENNIS.getName();
    protected static final String EXPECTED_ICE_HOCKEY_CATEGORY = CategoryEnum.ICE_HOCKEY.getName();
    protected static final String EXPECTED_UPDATED_BASKETBALL_CATEGORY = CategoryEnum.BASKETBALL.getName();
    protected static final String EXPECTED_AUTHOR_NAME = "E2E AutoTest Author";
    protected static final String CREATING_MESSAGE_FOR = "------------Creating %s...------------";
    protected static final String CREATED_MESSAGE_FOR = "------------%s created!------------";
    protected static final String EDITING_MESSAGE_FOR = "------------Editing %s...------------";
    protected static final String EDITED_MESSAGE_FOR = "------------%s edited!------------";
    protected static final String UPLOADED_MESSAGE_FOR = "------------%s uploaded!------------";
    protected static final String UPLOADING_MESSAGE_FOR = "------------Uploading %s...------------";
    protected GalleryResponseModel createdGallery;
    protected ArticleResponseModel createdArticle;
    protected WikiResponseModel createdWikiPage;
    protected Category createdCategory;
    protected TagModel createdTag;
    protected AuthorModel createdAuthor;
    protected ImageModel uploadedImage;
    protected BannerModel createdBanner;
    protected VideoResponseModel createdVideo;
    protected ListModel createdEditorialList;

    @Override
    public void beforeEach() {
        super.beforeEach();
        createdArticle = ArticleResponseModel.builder().build();
        createdGallery = GalleryResponseModel.builder().build();
        createdCategory = Category.builder().build();
        createdTag = TagModel.builder().build();
        createdAuthor = AuthorModel.builder().build();
        createdBanner = BannerModel.builder().build();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteArticle(createdArticle);
        contentApiFacade.deleteVideo(createdVideo);
        contentApiFacade.deleteGallery(createdGallery);
        contentApiFacade.deleteCategories(createdCategory != null ? List.of(createdCategory.getTitle()) : List.of());
        contentApiFacade.deleteTag(createdTag != null ? createdTag.getId() : null);
        contentApiFacade.deleteAuthor(createdAuthor != null ? createdAuthor.getId() : null);
        contentApiFacade.deleteBanner(createdBanner);
        contentApiFacade.deleteLists(List.of(createdEditorialList));
    }

    protected void createTagsList() {
        ProxyServer.newHar();
        tagsPage.openCreatePage();
        var tagRequiredFields = new TagFieldsFactory().buildTagRequiredFields();
        tagsPage.setRequiredField(tagRequiredFields.getTitle(), tagRequiredFields.getOrderType());
        tagsPage.saveTag();
//        tagsPage.map().alertMessage().validateMessageIs(ToastMessageEnum.TAG_SUCCESSFULLY_CREATED);

        createdTag = tagsPage.getCreateTagResponse();
        Assertions.assertNotNull(createdTag, AssertMessages.requestNotCreated(TAG_STRING));

        Assertions.assertEquals(tagRequiredFields.getTitle(), createdTag.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(tagRequiredFields.getOrderType(), createdTag.getOrderType(), AssertMessages.entityNotExpected("order_type"));
    }

    protected void addAuthorToArticle(ArticleResponseModel createdArticle, AuthorModel createdAuthor) {
        articleFormPage.generalSection().map().authorSelect().clearSelection();
        articleFormPage.browser().waitForAjax();
        articleFormPage.generalSection().map().authorSelect().searchSelectByText(createdAuthor.getName());
    }

    protected ListModel createEditorialList() {
        ProxyServer.newHar();
        editorialListsPage.openCreatePage();

        var listFormRequiredFields = new ListFieldsFactory().buildListRequiredFields();
        editorialListsPage.setRequiredFields(listFormRequiredFields.getTitle());
        editorialListsPage.saveList();

        createdEditorialList = editorialListsPage.getCreateListResponse();
        Assertions.assertNotNull(createdEditorialList, AssertMessages.requestNotCreated(StringConstants.EDITORIAL_LIST_STRING));

        editorialListsPage.asserts().editPageLoaded(createdEditorialList.getId());
        return createdEditorialList;
    }

    protected VideoResponseModel editVideo(VideoResponseModel createdVideo) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        VideoFormModel videoPageRequiredFields = new VideoFieldsFactory().buildVideoRequiredFields();
        videoPageRequiredFields.setTitle("%s %s".formatted(StringConstants.UPDATED_STRING + Faker.instance().number().randomNumber(), createdVideo.getTitle()));

        videosListPage.openEditPage(createdVideo.getId());
        videosListPage.waitForSpinners();
        videosListPage.setRequiredFields(videoPageRequiredFields.getTitle(), videoPageRequiredFields.getMainCategory());
        videosListPage.editVideoFromUrlsSection(VideoConstants.BUNNY_VIDEO_URL, VideoUrlsType.NEW);
        videosListPage.saveVideo();
        videosListPage.browser().waitForRequest(ContentApiUrl.VIDEOS_RELATED.getUrl().formatted(createdVideo.getId()));
        VideoResponseModel editedVideo = videosListPage.getEditVideoResponse(createdVideo.getId());

        Assertions.assertNotNull(editedVideo, AssertMessages.requestNotCreated(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        VideoFileModel actualUpdatedVideoData = editedVideo.getUrls().getVideoFiles().get(0);

        Assertions.assertEquals(videoPageRequiredFields.getTitle(), editedVideo.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(videoPageRequiredFields.getMainCategory(), editedVideo.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(VIDEO_FIRST_CASE_UPPERCASED_STRING.toLowerCase(), editedVideo.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING));
        Assertions.assertEquals(VideoConstants.BUNNY_VIDEO_URL, actualUpdatedVideoData.getUrl(), AssertMessages.responseNotContains("url"));
        Assertions.assertEquals(VideoUrlsType.NEW.getValue(), actualUpdatedVideoData.getType(), AssertMessages.responseNotContains("type"));
        Log.info(EDITED_MESSAGE_FOR.formatted(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        return editedVideo;
    }

    protected void addCategoryToArticle(ArticleResponseModel createdArticle, Category createdCategory) {
        articleFormPage.generalSection().map().mainCategorySelect().searchSelectByText(createdCategory.getTitle());
    }

    protected void addArticleTags(ArticleResponseModel createdArticle, TagModel createdTag) {
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().map().tagsSelect().searchSelectByText(createdTag.getTitle());
        articleFormPage.tagsSection().collapse();
    }

    protected void addExistingVideoToArticle(ArticleResponseModel createdArticle, VideoResponseModel createdVideo) {
        if (!app().browser().getUrl().contains(createdArticle.getId())) {
            articleFormPage.openEditPageForArticle(createdArticle.getId());
        }

        articleFormPage.addBlocky(WidgetBlock.VIDEO_WIDGET);
        articleFormPage.mainSection().getVideoBlocks().get(0).waitEditScreenToLoad();
        articleFormPage.mainSection().getVideoBlocks().get(0).chooseVideo(createdVideo.getTitle());
        articleFormPage.mainSection().getVideoBlocks().get(0).saveBlock();
    }

    protected void addExistingBannerToArticle(ArticleResponseModel createdArticle, BannerModel createdBanner) {
        if (!app().browser().getUrl().contains(createdArticle.getId())) {
            articleFormPage.openEditPageForArticle(createdArticle.getId());
        }

        articleFormPage.addBlocky(WidgetBlock.BANNER_WIDGET);
        articleFormPage.mainSection().getBannerBlocks().get(0).filterBanners(createdBanner.getTitle());
        articleFormPage.mainSection().getBannerBlocks().get(0).saveBlock();
    }

    protected void addArticleToList(ArticleResponseModel createdArticle, ListModel createdEditorialList) {
        if (!app().browser().getUrl().contains(createdArticle.getId())) {
            articleFormPage.openEditPageForArticle(createdArticle.getId());
        }

        articleFormPage.listSection().expand();
        articleFormPage.listSection().map().listSelect().searchSelectByText(createdEditorialList.getTitle());
        articleFormPage.listSection().map().saveListButton().click();
    }

    protected void addExistingImageToArticle(ArticleResponseModel createdArticle, ImageModel uploadedImage) {
        if (!app().browser().getUrl().contains(createdArticle.getId())) {
            articleFormPage.openEditPageForArticle(createdArticle.getId());
        }

        articleFormPage.addBlocky(WidgetBlock.IMAGE_WIDGET);
        articleFormPage.mainSection().getImageBlocks().get(0).waitEditScreenToLoad();
        articleFormPage.mainSection().getImageBlocks().get(0).filterImages(uploadedImage.getDescription());
        articleFormPage.mainSection().getImageBlocks().get(0).imagesPreviewImages().get(0).click();
        articleFormPage.mainSection().getImageBlocks().get(0).saveBlock();
    }

    protected void addExistingGalleryToArticle(ArticleResponseModel createdArticle, GalleryResponseModel createdGallery) {
        if (!app().browser().getUrl().contains(createdArticle.getId())) {
            articleFormPage.openEditPageForArticle(createdArticle.getId());
        }

        articleFormPage.addBlocky(WidgetBlock.GALLERY_WIDGET);
        articleFormPage.mainSection().getGalleryBlocks().get(0).waitEditScreenToLoad();
        articleFormPage.mainSection().getGalleryBlocks().get(0).selectGallery(createdGallery.getTitle());
        articleFormPage.mainSection().getGalleryBlocks().get(0).saveBlock();
        articleFormPage.waitForSpinners();
    }

    protected ArticleResponseModel createArticle() {
        return createArticle(EXPECTED_FOOTBALL_CATEGORY);
    }

    protected ArticleResponseModel createArticle(String sportCategory) {
        ProxyServer.newHar();
        openArticleCreatePage();

        articleFormPage.setRequiredFields(EXPECTED_TITLE, sportCategory);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        Assertions.assertNotNull(createdArticle, AssertMessages.requestNotCreated(ARTICLE_STRING));
        articleFormPage.asserts().editPageLoaded(createdArticle.getId());

        Assertions.assertEquals(EXPECTED_TITLE, createdArticle.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(sportCategory, createdArticle.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(ARTICLE_STRING, createdArticle.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(ARTICLE_STRING));
        return createdArticle;
    }

    protected void openArticleCreatePage() {
        dashboardPage.sidebar().navigateTo(CmsPage.ARTICLES);
        articleListPage.asserts().pageLoaded();
        articleListPage.map().newArticleButton().click();
    }

    protected VideoResponseModel createVideo() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.VIDEOS);
        videosListPage.openCreatePage();
        VideoFormModel videoRequiredFields = new VideoFieldsFactory().buildVideoRequiredFields();

        videosListPage.setRequiredFields(videoRequiredFields.getTitle(), videoRequiredFields.getMainCategory());
        videosListPage.addVideoFromUrlsSection(VideoConstants.SPORTAL_VIDEO_URL, VideoUrlsType.LIVE);
        videosListPage.saveVideo();
//        videosListPage.map().alertMessage().validateMessageIs(ToastMessageEnum.VIDEO_SUCCESSFULLY_CREATED);

        createdVideo = videosListPage.getCreateVideoResponse();
        Assertions.assertNotNull(createdVideo, AssertMessages.requestNotCreated(VIDEO_FIRST_CASE_UPPERCASED_STRING));

        VideoFileModel actualCreatedVideoData = createdVideo.getUrls().getVideoFiles().get(0);
        Assertions.assertEquals(videoRequiredFields.getTitle(), createdVideo.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(videoRequiredFields.getMainCategory(), createdVideo.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(VIDEO_FIRST_CASE_UPPERCASED_STRING.toLowerCase(), createdVideo.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING));
        Assertions.assertEquals(VideoConstants.SPORTAL_VIDEO_URL, actualCreatedVideoData.getUrl(), AssertMessages.responseNotContains("url"));
        Assertions.assertEquals(VideoUrlsType.LIVE.getValue(), actualCreatedVideoData.getType(), AssertMessages.responseNotContains("type"));
        Log.info(CREATED_MESSAGE_FOR.formatted(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        return createdVideo;
    }

    protected ArticleResponseModel editArticle(ArticleResponseModel createdArticle) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(ARTICLE_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.ARTICLES);
        articleListPage.map().editButton(createdArticle.getId()).click();
        articleFormPage.asserts().editPageLoaded(createdArticle.getId());
        articleFormPage.waitForPageLoad();

        articleFormPage.setRequiredFields(EXPECTED_UPDATED_TITLE, EXPECTED_UPDATED_BASKETBALL_CATEGORY);
        articleFormPage.map().activeParagraph().textEditor().setText(EXPECTED_ARTICLE_CONTENT);
        ProxyServer.newHar();
        articleFormPage.saveEditArticle(true);

        ArticleResponseModel updatedArticle = articleFormPage.getArticleUpdateResponse();
        Assertions.assertNotNull(updatedArticle, AssertMessages.requestNotCreated(ARTICLE_STRING));

        createdArticle.setTitle(EXPECTED_UPDATED_TITLE);
        createdArticle.getSeo().setTitle(updatedArticle.getSeo().getTitle());
        createdArticle.getSeo().setSlug(updatedArticle.getSeo().getSlug());
        createdArticle.setCategory(updatedArticle.getCategory());
        createdArticle.setUpdatedAt(updatedArticle.getUpdatedAt());
        createdArticle.setBody(updatedArticle.getBody());
        createdArticle.setIsContentUpdatedAtSetAutomatically(updatedArticle.getIsContentUpdatedAtSetAutomatically());
        createdArticle.setAuthors(updatedArticle.getAuthors());
        EntitiesAsserter.areEqual(createdArticle, updatedArticle, AssertMessages.responsesAreNotEqual(ARTICLE_STRING));
        Log.info(EDITED_MESSAGE_FOR.formatted(ARTICLE_STRING));
        ProxyServer.newHar();
        return createdArticle;
    }

    protected GalleryResponseModel createGallery() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(GALLERY_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.GALLERIES);
        galleriesPage.asserts().pageLoaded();
        galleriesPage.map().newGalleryButton().click();

        String expectedTitle = "Gallery " + Faker.instance().number().randomNumber() + EXPECTED_TITLE;

        galleriesPage.setRequiredFields(expectedTitle, EXPECTED_FOOTBALL_CATEGORY);
        galleriesPage.mediaSection().expand();
        galleriesPage.mediaSection().selectFirstLoadedImage();

        galleriesPage.saveGalleryFromContentTab();
//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_CREATED);

        createdGallery = galleriesPage.getCreateGalleryResponse();
        Assertions.assertNotNull(createdGallery, AssertMessages.requestNotCreated(GALLERY_STRING));
        galleriesPage.asserts().editPageLoaded(createdGallery.getId());

        Assertions.assertEquals(expectedTitle, createdGallery.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(EXPECTED_FOOTBALL_CATEGORY, createdGallery.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(GALLERY_STRING, createdGallery.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(GALLERY_STRING));
        return createdGallery;
    }

    protected GalleryResponseModel editGallery(GalleryResponseModel createdGallery) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(GALLERY_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.GALLERIES);
        galleriesPage.map().editButton(createdGallery.getId()).click();
        galleriesPage.asserts().editPageLoaded(createdGallery.getId());

        galleriesPage.setRequiredFields(EXPECTED_UPDATED_TITLE + Faker.instance().number().randomNumber(), EXPECTED_UPDATED_BASKETBALL_CATEGORY);
        galleriesPage.clickItemsTab();
        galleriesPage.selectAlreadyUploadedImage();
        galleriesPage.saveGalleryFromItemsTab();
//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_UPDATED);

        GalleryResponseModel updatedGallery = galleriesPage.getUpdateGalleryResponse(createdGallery.getId());
        Assertions.assertNotNull(updatedGallery, AssertMessages.requestNotCreated(GALLERY_STRING));

        createdGallery.setTitle(updatedGallery.getTitle());
        createdGallery.getSeo().setTitle(updatedGallery.getTitle());
        createdGallery.getSeo().setSlug(StringUtils.generateSlug(updatedGallery.getTitle()));
        createdGallery.setCategory(updatedGallery.getCategory());
        createdGallery.setUpdatedAt(updatedGallery.getUpdatedAt());
        createdGallery.setItems(updatedGallery.getItems());
        createdGallery.getBody().get(0).setId(updatedGallery.getBody().get(0).getId());
        createdGallery.setIsContentUpdatedAtSetAutomatically(updatedGallery.getIsContentUpdatedAtSetAutomatically());
        Assertions.assertEquals(createdGallery.toJson(), updatedGallery.toJson(), AssertMessages.responsesAreNotEqual(GALLERY_STRING));
        Log.info(EDITED_MESSAGE_FOR.formatted(GALLERY_STRING));
        return updatedGallery;
    }

    protected ImageModel uploadImage() {
        ProxyServer.newHar();
        Log.info(UPLOADING_MESSAGE_FOR.formatted(StringConstants.TEST_IMAGE_PNG));
        dashboardPage.sidebar().navigateTo(CmsPage.ALL_IMAGES);
        imagesPage.asserts().pageLoaded();

        uploadedImage = imagesPage.uploadImage(getTestFilePath(StringConstants.TEST_IMAGE_PNG));
//        imagesPage.map().alertMessage().validateImageUploadSuccessful();

        Assertions.assertNotNull(uploadedImage, AssertMessages.requestNotCreated(IMAGE_STRING));
        Assertions.assertNotNull(uploadedImage.getId(), AssertMessages.objectValueNotCorrect(ID_STRING, DATA_STRING));
        Assertions.assertEquals(IMAGE_STRING, uploadedImage.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Log.info(UPLOADED_MESSAGE_FOR.formatted(StringConstants.TEST_IMAGE_PNG));
        return uploadedImage;
    }

    protected ImageModel editImage(ImageModel uploadedImage) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(IMAGE_STRING));

        app().browser().refresh();
        ImageUpdateFormModel expectedImageFields = new ImageUpdateFieldsFactory().buildImageUpdateRequiredFields();

        imagesPage.asserts().pageLoaded();
        imagesPage.clickEditImageButton(uploadedImage.getId());
        imagesPage.fillImageUpdateFields(uploadedImage.getId(), expectedImageFields);
        imagesPage.map().saveButton().click();
//        imagesPage.map().alertMessage().validateImageUpdateSuccessful();

        uploadedImage = imagesPage.getUpdatedImageResponse(uploadedImage.getId());
        Assertions.assertNotNull(uploadedImage, AssertMessages.requestNotCreated(IMAGE_STRING));
        Assertions.assertEquals(expectedImageFields.getImageDescription(), uploadedImage.getDescription(), AssertMessages.entityNotExpected(DESCRIPTION_STRING));
        Assertions.assertEquals(expectedImageFields.getOrigin(), uploadedImage.getOrigin().getName(), AssertMessages.entityNotExpected("origin name"));
        Assertions.assertEquals(expectedImageFields.getImageAlt(), uploadedImage.getAlt(), AssertMessages.entityNotExpected(ALT_STRING));
        Assertions.assertEquals(expectedImageFields.getImageCaption(), uploadedImage.getCaption(), AssertMessages.entityNotExpected("caption"));
        Assertions.assertEquals(expectedImageFields.getType().toLowerCase(), uploadedImage.getType(), AssertMessages.entityNotExpected(TYPE_STRING));
        Assertions.assertEquals(expectedImageFields.getAuthor(), uploadedImage.getAuthors().get(0).getName(), AssertMessages.entityNotExpected("author name"));
        Log.info(EDITED_MESSAGE_FOR.formatted(IMAGE_STRING));
        return uploadedImage;
    }

    protected Category createCategory() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(CATEGORY_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.CATEGORIES);
        categoriesPage.map().newCategoryButton().click();
        var expectedTitle = EXPECTED_TITLE + LocalDateTime.now();
        categoriesPage.setRequiredField(expectedTitle);
        categoriesPage.saveCreateCategory();
        categoriesPage.asserts().validateSaveSuccessful();

        createdCategory = categoriesPage.getCreateCategoryResponse();
        Assertions.assertEquals(expectedTitle, createdCategory.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertTrue(createdCategory.getActive(), AssertMessages.entityNotExpected(ACTIVE_STRING));
        Assertions.assertEquals(CATEGORY_STRING, createdCategory.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(CATEGORY_STRING));
        return createdCategory;
    }

    protected void editCategory(Category createdCategory) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(CATEGORY_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.CATEGORIES);
        categoriesPage.map().editButton(createdCategory.getId()).click();

        categoriesPage.setRequiredField(EXPECTED_UPDATED_TITLE);
        categoriesPage.saveUpdateCategory();

        Category updatedCategory = categoriesPage.getUpdateCategoryResponse(createdCategory.getId());
        Assertions.assertNotNull(updatedCategory, AssertMessages.requestNotCreated(CATEGORY_STRING));

        createdCategory.setTitle(EXPECTED_UPDATED_TITLE);
        createdCategory.setUpdatedAt(updatedCategory.getUpdatedAt());
        createdCategory.setSeo(updatedCategory.getSeo());
        Assertions.assertEquals(createdCategory, updatedCategory, AssertMessages.responsesAreNotEqual(CATEGORY_STRING));
        Log.info(EDITED_MESSAGE_FOR.formatted(CATEGORY_STRING));
    }

    protected TagModel createTag() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(TAG_STRING));
        ProxyServer.newHar();
        dashboardPage.sidebar().navigateTo(CmsPage.TAGS);
        tagsPage.map().newTagButton().click();
        var expectedTitle = EXPECTED_TITLE + LocalDateTime.now();
        var expectedOrderType = TagOrderType.ORDERED.toString().toLowerCase();
        tagsPage.setRequiredField(expectedTitle, expectedOrderType);
        tagsPage.saveTag();
//        tagsPage.map().alertMessage().validateMessageIs(ToastMessageEnum.TAG_SUCCESSFULLY_CREATED);

        createdTag = tagsPage.getCreateTagResponse();
        Assertions.assertNotNull(createdTag, AssertMessages.requestNotCreated(TAG_STRING));
        tagsPage.asserts().editPageLoaded(createdTag.getId());

        Assertions.assertEquals(expectedTitle, createdTag.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(expectedOrderType, createdTag.getOrderType(), AssertMessages.entityNotExpected(ORDER_TYPE_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(TAG_STRING));
        return createdTag;
    }

    protected void editTag(TagModel createdTag) {
        Log.info(EDITING_MESSAGE_FOR.formatted(TAG_STRING));
        ProxyServer.newHar();
        dashboardPage.sidebar().navigateTo(CmsPage.TAGS);
        tagsPage.map().editButton(createdTag.getId()).click();
        tagsPage.asserts().editPageLoaded(createdTag.getId());

        tagsPage.setRequiredField(EXPECTED_UPDATED_TITLE, TagOrderType.UNORDERED.toString().toLowerCase());
        tagsPage.saveTag();
//        tagsPage.map().alertMessage().validateMessageIs(ToastMessageEnum.TAG_UPDATED_SUCCESSFULLY);

        TagModel updatedTag = tagsPage.getUpdateTagResponse(createdTag.getId());
        Assertions.assertNotNull(updatedTag, AssertMessages.requestNotCreated(TAG_STRING));

        createdTag.setTitle(EXPECTED_UPDATED_TITLE);
        createdTag.setUpdatedAt(updatedTag.getUpdatedAt());
        Assertions.assertEquals(createdTag, updatedTag, AssertMessages.responsesAreNotEqual());
        Log.info(EDITED_MESSAGE_FOR.formatted(TAG_STRING));
    }

    protected AuthorModel createAuthor() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(AUTHOR_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.AUTHORS);
        authorsPage.asserts().pageLoaded();
        authorsPage.map().newAuthorButton().click();
        var expectedName = EXPECTED_AUTHOR_NAME + LocalDateTime.now();
        authorsPage.setRequiredField(expectedName);
        authorsPage.saveAuthor();
        authorsPage.asserts().validateSaveSuccessful();
        createdAuthor = authorsPage.getCreateAuthorResponse();
        authorsPage.asserts().editPageLoaded(createdAuthor.getId());

        Assertions.assertNotNull(createdAuthor, AssertMessages.requestNotCreated(AUTHOR_STRING));
        Assertions.assertEquals(AUTHOR_STRING.toLowerCase(), createdAuthor.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Assertions.assertEquals(expectedName, createdAuthor.getName(), AssertMessages.entityNotExpected(NAME_STRING));
        Assertions.assertTrue(createdAuthor.getActive(), AssertMessages.entityNotExpected(ACTIVE_STRING));
        Assertions.assertFalse(createdAuthor.getDefaultBoolean(), AssertMessages.entityNotExpected(DEFAULT_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(AUTHOR_STRING));
        return createdAuthor;
    }

    protected void editAuthor(AuthorModel createdAuthor) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(AUTHOR_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.AUTHORS);
        authorsPage.asserts().pageLoaded();
        authorsPage.map().editButton(createdAuthor.getId()).click();
        authorsPage.asserts().editPageLoaded(createdAuthor.getId());

        String expectedUpdatedName = "%s %s".formatted(UPDATED_STRING, EXPECTED_AUTHOR_NAME);
        String expectedUpdatedBiography = "%s %s".formatted(UPDATED_STRING, "biography");

        authorsPage.setRequiredTitleField(expectedUpdatedName);
        authorsPage.map().biographyTextEditorField().setText(expectedUpdatedBiography);
        authorsPage.saveAuthor();
        authorsPage.asserts().validateUpdateSuccessful();

        AuthorModel updatedAuthor = authorsPage.getUpdateAuthorResponse(createdAuthor.getId());
        Assertions.assertNotNull(updatedAuthor, AssertMessages.requestNotCreated(AUTHOR_STRING));

        createdAuthor.setName(expectedUpdatedName);
        createdAuthor.setBio(expectedUpdatedBiography);
        createdAuthor.setUpdatedAt(updatedAuthor.getUpdatedAt());
        createdAuthor.setSeo(updatedAuthor.getSeo());
        Assertions.assertEquals(createdAuthor, updatedAuthor, AssertMessages.responsesAreNotEqual(AUTHOR_STRING));
        Log.info(EDITED_MESSAGE_FOR.formatted(AUTHOR_STRING));
    }

    protected BannerModel createBanner() {
        ProxyServer.newHar();
        Log.info(CREATING_MESSAGE_FOR.formatted(BANNER_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.BANNERS);
        bannersPage.map().newBannerButton().click();
        var expectedTitle = EXPECTED_TITLE + LocalDateTime.now();
        var expectedCode = EXPECTED_BANNER_CODE + LocalDateTime.now();
        bannersPage.setRequiredField(expectedTitle, expectedCode);
        bannersPage.saveBanner();
//        bannersPage.map().alertMessage().validateMessageIs(ToastMessageEnum.BANNER_SUCCESSFULLY_CREATED);

        createdBanner = bannersPage.getCreateBannerResponse();
        Assertions.assertNotNull(createdBanner, AssertMessages.requestNotCreated(BANNER_STRING));
        bannersPage.asserts().editPageLoaded(createdBanner.getId());

        Assertions.assertNotNull(createdBanner.getId(), AssertMessages.objectValueNotCorrect(ID_STRING, DATA_STRING));
        Assertions.assertEquals(BANNER_STRING.toLowerCase(), createdBanner.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Assertions.assertEquals(expectedTitle, createdBanner.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(expectedCode, createdBanner.getCode(), AssertMessages.entityNotExpected(CODE_STRING));
        Assertions.assertFalse(createdBanner.getIsPreferred(), AssertMessages.entityNotExpected(IS_PREFERRED_STRING));
        Log.info(CREATED_MESSAGE_FOR.formatted(BANNER_STRING));
        return createdBanner;
    }

    protected BannerModel editBanner(BannerModel createdBanner) {
        ProxyServer.newHar();
        Log.info(EDITING_MESSAGE_FOR.formatted(BANNER_STRING));
        dashboardPage.sidebar().navigateTo(CmsPage.BANNERS);
        bannersPage.map().editButton(createdBanner.getId()).click();
        bannersPage.asserts().editPageLoaded(createdBanner.getId());
        bannersPage.waitForSpinners();
        String expectedTitle = EXPECTED_UPDATED_TITLE + LocalDateTime.now();
        String expectedCode = EXPECTED_UPDATED_BANNER_CODE + LocalDateTime.now();
        bannersPage.setRequiredField(expectedTitle, expectedCode);
        bannersPage.saveBanner();

        BannerModel updatedBanner = bannersPage.getUpdateBannerResponse(createdBanner.getId());
        Assertions.assertNotNull(updatedBanner, AssertMessages.requestNotCreated(BANNER_STRING));

        createdBanner.setTitle(expectedTitle);
        createdBanner.setCode(expectedCode);
        createdBanner.setUpdatedAt(updatedBanner.getUpdatedAt());
        Assertions.assertEquals(createdBanner.toString(), updatedBanner.toString(), AssertMessages.responsesAreNotEqual());
        Log.info(EDITED_MESSAGE_FOR.formatted(BANNER_STRING));
        return createdBanner;
    }
}