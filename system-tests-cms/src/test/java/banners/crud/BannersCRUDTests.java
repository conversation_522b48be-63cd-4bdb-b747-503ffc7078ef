package banners.crud;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.models.banners.BannerModel;
import data.models.uimodels.BannerFormModel;
import factories.banners.BannerFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import static data.constants.StringConstants.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CRUD)
@Tag(CMSStories.BANNERS)
@Story(CMSStories.BANNERS)
public class BannersCRUDTests extends CmsWebTest {

    private BannerFormModel bannerRequiredFields;
    private BannerModel createdBanner;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        createdBanner = null;
        bannerRequiredFields = new BannerFieldsFactory().buildBannerRequiredFields();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteBanner(createdBanner);
    }

    @Test
    public void errorMessagesDisplayed_when_createBanner_and_setMandatoryFieldsEmpty() {
        bannersPage.openCreatePage();
        bannersPage.asserts().createPageLoaded();

        bannersPage.setRequiredField(null, null);
        bannersPage.saveBanner();

//        bannersPage.map().alertMessage().validateCreateFailedFor(BANNER_STRING);
        bannersPage.asserts().validateErrorMessageDisplayedForTitleField();
        bannersPage.asserts().validateErrorMessageDisplayedForBannerCodeField();
    }

    @Test
    public void errorMessageDisplayed_when_createBanner_and_setTitleMandatoryFieldEmpty() {
        bannersPage.openCreatePage();
        bannersPage.setRequiredField(null, bannerRequiredFields.getBannerCode());
        bannersPage.saveBanner();

//        bannersPage.map().alertMessage().validateCreateFailedFor(BANNER_STRING);
        bannersPage.asserts().validateErrorMessageDisplayedForTitleField();
    }

    @Test
    public void errorMessageDisplayed_when_createBanner_and_setBannerCodeMandatoryFieldEmpty() {
        bannersPage.openCreatePage();
        bannersPage.setRequiredField(bannerRequiredFields.getTitle(), null);
        bannersPage.saveBanner();

//        bannersPage.map().alertMessage().validateCreateFailedFor(BANNER_STRING);
        bannersPage.asserts().validateErrorMessageDisplayedForBannerCodeField();
    }

    @Test
    public void bannerCreatedSuccessfully_when_createBanner_and_isPreferredCheckboxUnchecked() {
        bannersPage.openCreatePage();
        bannersPage.setRequiredField(bannerRequiredFields.getTitle(), bannerRequiredFields.getBannerCode());
        bannersPage.saveBanner();
//      bannersPage.map().alertMessage().validateCreateSuccessfulFor(BANNER_STRING);
        // TODO: ivan.petkov - 03.01.2024 - Remove this line and uncomment the above one when https://media-platform.atlassian.net/browse/SFE-4326 is done
//        bannersPage.map().alertMessage().validateSaveSuccessful("Banner successfully created.");


        createdBanner = bannersPage.getCreateBannerResponse();
        Assertions.assertNotNull(createdBanner, AssertMessages.requestNotCreated(BANNER_STRING));

        Assertions.assertNotNull(createdBanner.getId(), AssertMessages.objectValueNotCorrect(ID_STRING, DATA_STRING));
        Assertions.assertEquals(BANNER_STRING.toLowerCase(), createdBanner.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getTitle(), createdBanner.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getBannerCode(), createdBanner.getCode(), AssertMessages.entityNotExpected(CODE_STRING));
        Assertions.assertFalse(createdBanner.getIsPreferred(), AssertMessages.entityNotExpected(IS_PREFERRED_STRING));
    }

    @Test
    public void bannerCreatedSuccessfully_when_createBanner_and_isPreferredCheckboxChecked() {
        bannersPage.openCreatePage();
        bannersPage.setRequiredField(bannerRequiredFields.getTitle(), bannerRequiredFields.getBannerCode());
        bannersPage.map().isPreferredCheckbox().check();
        bannersPage.saveBanner();
//        bannersPage.map().alertMessage().validateCreateSuccessfulFor(BANNER_STRING);
        // TODO: ivan.petkov - 03.01.2024 - Remove this line and uncomment the above one when https://media-platform.atlassian.net/browse/SFE-4326 is done
//        bannersPage.map().alertMessage().validateSaveSuccessful("Banner successfully created.");

        createdBanner = bannersPage.getCreateBannerResponse();
        Assertions.assertNotNull(createdBanner, AssertMessages.requestNotCreated(BANNER_STRING));

        Assertions.assertNotNull(createdBanner.getId(), AssertMessages.objectValueNotCorrect(ID_STRING, DATA_STRING));
        Assertions.assertEquals(BANNER_STRING.toLowerCase(), createdBanner.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getTitle(), createdBanner.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getBannerCode(), createdBanner.getCode(), AssertMessages.entityNotExpected(CODE_STRING));
        Assertions.assertTrue(createdBanner.getIsPreferred(), AssertMessages.entityNotExpected(IS_PREFERRED_STRING));
    }

    @Test
    public void bannerUpdatedSuccessfully_when_updateBanner() {
        createdBanner = contentApiFacade.createBanner();
        bannerRequiredFields.setTitle(UPDATED_STRING + bannerRequiredFields.getTitle());
        bannerRequiredFields.setBannerCode(UPDATED_STRING + bannerRequiredFields.getBannerCode());

        bannersPage.openEditPage(createdBanner.getId());
        bannersPage.setRequiredField(bannerRequiredFields.getTitle(), bannerRequiredFields.getBannerCode());
        bannersPage.map().isPreferredCheckbox().check();
        bannersPage.saveBanner();
//        bannersPage.map().alertMessage().validateUpdateSuccessfulFor(BANNER_STRING);

        BannerModel updatedBanner = bannersPage.getUpdateBannerResponse(createdBanner.getId());
        Assertions.assertNotNull(updatedBanner, AssertMessages.requestNotCreated(BANNER_STRING));

        Assertions.assertNotNull(updatedBanner.getId(), AssertMessages.objectValueNotCorrect(ID_STRING, DATA_STRING));
        Assertions.assertEquals(BANNER_STRING.toLowerCase(), updatedBanner.getEntityType(), AssertMessages.entityNotExpected(ENTITY_TYPE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getTitle(), updatedBanner.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING));
        Assertions.assertEquals(bannerRequiredFields.getBannerCode(), updatedBanner.getCode(), AssertMessages.entityNotExpected(CODE_STRING));
        Assertions.assertTrue(updatedBanner.getIsPreferred(), AssertMessages.entityNotExpected(IS_PREFERRED_STRING));
    }

    @Test
    public void bannerDeletedSuccessfully_when_deleteBanner() {
        createdBanner = contentApiFacade.createBanner();

        bannersPage.open();
        bannersPage.deleteBanner(createdBanner.getId());
//        bannersPage.map().alertMessage().validateDeleteSuccessfulFor(BANNER_STRING);
        bannersPage.asserts().validateBannerDeleted(createdBanner.getTitle());
        createdBanner = null;
    }
}