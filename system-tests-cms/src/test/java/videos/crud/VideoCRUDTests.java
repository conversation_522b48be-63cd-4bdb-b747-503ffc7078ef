package videos.crud;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.video.VideoConstants;
import data.constants.video.VideoUrlsType;
import data.models.uimodels.VideoFormModel;
import data.models.videos.VideoFileModel;
import data.models.videos.VideoResponseModel;
import factories.video.VideoFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.videospage.VideosPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import static data.constants.StringConstants.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CRUD)
@Tag(CMSStories.VIDEOS)
@Story(CMSStories.VIDEOS)
public class VideoCRUDTests extends CmsWebTest {

    private VideosPage videoPage;
    private VideoFormModel videoPageRequiredFields;
    private VideoResponseModel createdVideoFromAPI;
    private String videoId;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        videoPage = app().createPage(VideosPage.class);
        videoPageRequiredFields = new VideoFieldsFactory().buildVideoRequiredFields();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteVideo(videoId);
    }

    @Test
    public void videoCreatedSuccessfully_when_createVideo_and_mandatoryFieldsFilled() {
        videoPage.openCreatePage();
        VideoFormModel videoRequiredFields = new VideoFieldsFactory().buildVideoRequiredFields();

        videoPage.setRequiredFields(videoRequiredFields.getTitle(), videoRequiredFields.getMainCategory());
        videoPage.addVideoFromUrlsSection(VideoConstants.SPORTAL_VIDEO_URL, VideoUrlsType.LIVE);
        videoPage.saveVideo();
//        videoPage.map().alertMessage().validateMessageIs(ToastMessageEnum.VIDEO_SUCCESSFULLY_CREATED);

        VideoResponseModel createdVideo = videoPage.getCreateVideoResponse();
        Assertions.assertNotNull(createdVideo, AssertMessages.requestNotCreated(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        videoId = createdVideo.getId();
        VideoFileModel actualCreatedVideoData = createdVideo.getUrls().getVideoFiles().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(videoRequiredFields.getTitle(), createdVideo.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING)),
                () -> Assertions.assertEquals(videoRequiredFields.getMainCategory(), createdVideo.getCategory().getTitle(), AssertMessages.entityNotExpected("main category")),
                () -> Assertions.assertEquals(VIDEO_FIRST_CASE_UPPERCASED_STRING.toLowerCase(), createdVideo.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING)),
                () -> Assertions.assertEquals(VideoConstants.SPORTAL_VIDEO_URL, actualCreatedVideoData.getUrl(), AssertMessages.responseNotContains("url")),
                () -> Assertions.assertEquals(VideoUrlsType.LIVE.getValue(), actualCreatedVideoData.getType(), AssertMessages.responseNotContains("type"))
        );
    }

    @Test
    public void videoUpdatedSuccessfully_when_updateVideo() {
        createdVideoFromAPI = createVideoViaAPI();
        videoId = createdVideoFromAPI.getId();
        videoPageRequiredFields.setTitle("%s %s".formatted(StringConstants.UPDATED_STRING, createdVideoFromAPI.getTitle()));

        videoPage.openEditPage(videoId);
        videoPage.setRequiredFields(videoPageRequiredFields.getTitle(), videoPageRequiredFields.getMainCategory());
        videoPage.editVideoFromUrlsSection(VideoConstants.BUNNY_VIDEO_URL, VideoUrlsType.NEW);
        videoPage.saveVideo();
        VideoResponseModel editedVideo = videoPage.getEditVideoResponse(videoId);

        Assertions.assertNotNull(editedVideo, AssertMessages.requestNotCreated(VIDEO_FIRST_CASE_UPPERCASED_STRING));
        VideoFileModel actualUpdatedVideoData = editedVideo.getUrls().getVideoFiles().stream().findAny().orElseThrow();

        Assertions.assertAll(
                () -> Assertions.assertEquals(videoPageRequiredFields.getTitle(), editedVideo.getTitle(), AssertMessages.entityNotExpected(TITLE_STRING)),
                () -> Assertions.assertEquals(videoPageRequiredFields.getMainCategory(), editedVideo.getCategory().getTitle(), AssertMessages.entityNotExpected("main category")),
                () -> Assertions.assertEquals(VIDEO_FIRST_CASE_UPPERCASED_STRING.toLowerCase(), editedVideo.getEntityType(), AssertMessages.responseNotContains(ENTITY_TYPE_STRING)),
                () -> Assertions.assertEquals(VideoConstants.BUNNY_VIDEO_URL, actualUpdatedVideoData.getUrl(), AssertMessages.responseNotContains("url")),
                () -> Assertions.assertEquals(VideoUrlsType.NEW.getValue(), actualUpdatedVideoData.getType(), AssertMessages.responseNotContains("type"))
        );
    }

    @Test
    public void videoDeletedSuccessfully_when_deleteVideo() {
        createdVideoFromAPI = createVideoViaAPI();
        videoId = createdVideoFromAPI.getId();

        videoPage.open();
        videoPage.deleteVideo(videoId);
//        videoPage.map().alertMessage().validateMessageIs(ToastMessageEnum.VIDEO_DELETED_SUCCESSFULLY);
        app().browser().refresh();
        videoPage.waitForPageLoad();
        videoPage.asserts().assertVideoDeleted(createdVideoFromAPI.getTitle());
        videoId = null;
    }

    public VideoResponseModel createVideoViaAPI() {
        return contentApiFacade.createVideo();
    }
}