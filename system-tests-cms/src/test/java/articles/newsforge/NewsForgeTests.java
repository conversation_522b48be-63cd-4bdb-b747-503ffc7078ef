package articles.newsforge;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.models.categories.Category;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.NEWS_FORGE)
@Story(CMSStories.NEWS_FORGE)
public class NewsForgeTests extends CmsWebTest {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        newsForgePage.open();
    }

    @Test
    public void newsForgeSectionOpened_when_openNewsForgePage() {
        newsForgePage.asserts().assertPageLoaded();
    }

    @Test
    public void expectedElementsDisplayed_when_openNewsForgePage() {
        newsForgePage.asserts().assertSectionElements();
    }

    @Test
    public void expectedCategoriesLoaded_when_openCategoryDropdown_and_checkOptions() {
        List<Category> categoriesAssignedToUser = contentApiFacade.getCategoriesAssignedToUser(User.FULLSETUP);

        newsForgePage.asserts().assertCategoryOptions(categoriesAssignedToUser);
    }

    @Test
    public void errorMessageDisplayed_when_extractAndTranslateArticle_and_setImportUrlLinkAndCategoryFieldsEmpty() {
        newsForgePage.map().extractAndTranslateButton().click();

        newsForgePage.asserts().validateErrorMessageDisplayedForImportUrlLinkField();
    }

    @Test
    public void errorMessageDisplayed_when_extractAndTranslateArticle_and_setImportUrlLinkFieldEmpty() {
        Category activeCategory = contentApiFacade.getActiveCategory();
        newsForgePage.map().categoryDropdown().searchSelectByText(activeCategory.getTitle());
        newsForgePage.map().extractAndTranslateButton().click();

        newsForgePage.asserts().validateErrorMessageDisplayedForImportUrlLinkField();
    }
}