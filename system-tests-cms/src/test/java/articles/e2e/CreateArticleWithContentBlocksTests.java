package articles.e2e;

import categories.CMSTags;
import categories.SMPCategories;
import core.BaseE2ETest;
import data.models.articles.ArticleModel;
import data.models.lists.ListModel;
import data.models.related.DataListTagObject;
import io.qameta.allure.Story;
import io.qameta.allure.TmsLink;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.E2E)
@Tag(CMSTags.CRITICAL_FUNCTIONALITY)
@Story(CMSTags.CRITICAL_FUNCTIONALITY)
public class CreateArticleWithContentBlocksTests extends BaseE2ETest {

    @Test
    @TmsLink("SMP-T772")
    @DisplayName("[E2E] Create Article with all content Blocks")
    public void articleSaved_when_addJustCreatedContentBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);

        uploadImage();
        uploadedImage = editImage(uploadedImage);
        createVideo();
        createdVideo = editVideo(createdVideo);
        createGallery();
        createdGallery = editGallery(createdGallery);
        createdBanner = createBanner();
        createdBanner = editBanner(createdBanner);

        editArticle(createdArticle);
        ProxyServer.newHar();

        addExistingVideoToArticle(createdArticle, createdVideo);
        addExistingGalleryToArticle(createdArticle, createdGallery);
        addExistingImageToArticle(createdArticle, uploadedImage);
        addExistingBannerToArticle(createdArticle, createdBanner);

        articleFormPage.saveArticle();
        ArticleModel request = articleFormPage.getArticleUpdateRequest();

        articleFormPage.asserts().validateBlockySaveRequest(request, createdGallery);
        articleFormPage.asserts().validateBlockySaveRequest(request, createdVideo);
        articleFormPage.asserts().validateBlockySaveRequest(request, uploadedImage);
        articleFormPage.asserts().validateBlockySaveRequest(request, createdBanner);
    }

    @Test
    @TmsLink("SMP-T772")
    @DisplayName("[E2E] Create Article with custom metadata - Tag, Category, Author, List")
    public void articleUpdated_when_setJustCreatedMetadataInSidebar() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        createdEditorialList = createEditorialList();
        createdCategory = createCategory();
        createdTag = createTag();
        createdAuthor = createAuthor();

        editArticle(createdArticle);
        ProxyServer.newHar();

        addArticleToList(createdArticle, createdEditorialList);
        addArticleTags(createdArticle, createdTag);
        addCategoryToArticle(createdArticle, createdCategory);

        articleFormPage.saveArticle();
        ArticleModel request = articleFormPage.getArticleUpdateRequest();
        List<DataListTagObject> relatedRequest = articleFormPage.getArticleRelatedTagRequest();
        ListModel listsRequest = articleFormPage.getListsApiRequest(createdEditorialList.getId());

        articleFormPage.asserts().validateBlockySaveRequest(listsRequest, createdEditorialList);
        articleFormPage.asserts().validateBlockySaveRequest(relatedRequest, createdTag);
        articleFormPage.asserts().validateBlockySaveRequest(request, createdCategory);
        createdVideo = null;
    }
}