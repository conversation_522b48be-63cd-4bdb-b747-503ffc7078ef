package articles.e2e;

import blockies.tennis.tournamentprogrammeblocky.TennisTournamentProgrammeBlocky;
import categories.CMSTags;
import categories.SMPCategories;
import core.BaseSportArticleE2ETest;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.basketball.BasketballTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.E2E)
@Tag(CMSTags.CRITICAL_FUNCTIONALITY)
@Story(CMSTags.CRITICAL_FUNCTIONALITY)
public class ArticleSidebarAutoTaggingIntegrationTests extends BaseSportArticleE2ETest {

    private final String BASKETBALL_COMPETITION = BasketballCompetitionName.NBA.getName();
    private final String BASKETBALL_TEAM = BasketballTeamEnum.LA_LAKERS.getName();

    @Test
    @DisplayName("[E2E] Add Tennis Blockies with Basketball Tags set in Sidebar")
    public void tennisTournamentsPrefilled_when_basketballTagsSelected() {
        openArticleCreatePage();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().selectSport(SupportedSports.BASKETBALL.getTitle());
        articleFormPage.tagsSection().filterSportsConnections(BASKETBALL_TEAM);
        articleFormPage.tagsSection().selectFirstFinishedEvent();

        addTennisTournamentProgrammeBlocky();

        TennisTournamentProgrammeBlocky blocky = articleFormPage.mainSection().getTennisTournamentProgrammeBlocks().get(0);
        blocky.editResult();
        blocky.competitionSelect().filter(BASKETBALL_COMPETITION, false);
        List<Button> tournaments = blocky.competitionSelect().getOptionButtons();
        Assertions.assertEquals(StringConstants.NO_OPTIONS_STRING, tournaments.get(0).getText(), "Basketball tournaments returned in tennis blocky.");
    }
}