package articles.e2e;

import categories.CMSTags;
import categories.SMPCategories;
import core.BaseSportArticleE2ETest;
import data.configuration.SportalSettings;
import data.constants.AssertMessages;
import data.constants.EventStatusType;
import data.constants.SupportedSports;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTeamEnum;
import data.customelements.MatchResult;
import data.models.articles.ArticleModel;
import data.models.autotaging.TagEntity;
import data.models.autotaging.TagResponseObject;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.blockymodels.football.*;
import factories.blockies.football.FootballSingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;
import java.util.Map;

import static data.widgets.options.enums.FootballPlayerEnum.LIONEL_MESSI;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.E2E)
@Tag(CMSTags.CRITICAL_FUNCTIONALITY)
@Story(CMSTags.CRITICAL_FUNCTIONALITY)
public class CreateArticleWithAllFootballBlocksTests extends BaseSportArticleE2ETest {

    private static final String TEAM_1 = FootballTeamEnum.BARCELONA.getName();
    private static final String EXPECTED_FOOTBALL_PLAYER = LIONEL_MESSI.getNameEn();
    private static final String PARAGRAPH = "Lionel Messi led Barcelona to LaLiga and Champions League titles the following season, which helped him capture an unprecedented third consecutive world player of the year award. In Nou Camp 2012 he netted his 233rd goal for Barcelona, becoming the club’s all-time leading scorer in LaLiga play when only 24 years old. Pep Guardiola signed with Barcelona, where he joined superstars Kylian Mbappe and Neymar, and that year he received yet another trophy.";
    private static final List<String> EXPECTED_TAGS_EXTENDED = List.of("Champions League", "Kylian Mbappe", "Estadio Nou Camp", "Barcelona", "LaLiga", "Pep Guardiola", "Neymar", "Lionel Messi");
    private String autotaggingApiUrl;

    @Test
    @DisplayName("[E2E] Create Article with listing football Blocks")
    public void articleSaved_when_addListingFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);

        LivescoreBlockyFieldsModel livescoreBlockyData = addLivescoreFootballBlocky();
        FootballTournamentProgrammeBlockyFieldsModel footballTournamentProgrammeBlockyData = addFootballTournamentProgrammeBlocky();
        FootballTopScorersBlockyFieldsModel topScorersBlockyData = addFootballTopScorersBlocky();
        FootballMostDecoratedPlayersBlockyFieldsModel mostDecoratedPlayerBlockyData = addFootballMostDecoratedPlayersBlocky();
        FootballStandingsBlockyFieldsModel footballStandingBlockyData = addFootballStandingBlocky();
        FootballTeamProgrammeBlockyFieldsModel footballTeamProgrammeBlockyData = addFootballTeamProgrammerBlocky();
        FootballKnockoutBlockyFieldsModel footballKnouckoutSchemeBlockyData = addKnockoutFootballBlocky();

        articleFormPage.mainSection().getFootballLivescoreBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTopScorersBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getMostDecoratedBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getStandingsBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamProgrammeBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getKnockoutSchemeBlocks().get(0).validationMessagesList();
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballLivescoreBlockyRequest(livescoreBlockyData, articleRequest);
        assertFootballTournamentProgrammeBlockyRequest(footballTournamentProgrammeBlockyData, articleRequest);
        assertFootballTopScorersBlockyRequest(topScorersBlockyData, articleRequest);
        assertFootballMostDecoratedPlayersBlockyRequest(mostDecoratedPlayerBlockyData, articleRequest);
        assertFootballStandingsBlockyRequest(footballStandingBlockyData, articleRequest);
        assertFootballTeamProgrammeBlockyRequest(footballTeamProgrammeBlockyData, articleRequest);
        assertFootballKnockoutBlockyRequest(footballKnouckoutSchemeBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Edit Article with listing football Blocks")
    public void articleEdit_when_addListingFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        editArticle(createdArticle);

        LivescoreBlockyFieldsModel livescoreBlockyData = addLivescoreFootballBlocky();
        FootballTournamentProgrammeBlockyFieldsModel footballTournamentProgrammeBlockyData = addFootballTournamentProgrammeBlocky();
        FootballTopScorersBlockyFieldsModel topScorersBlockyData = addFootballTopScorersBlocky();
        FootballMostDecoratedPlayersBlockyFieldsModel mostDecoratedPlayerBlockyData = addFootballMostDecoratedPlayersBlocky();
        FootballStandingsBlockyFieldsModel footballStandingBlockyData = addFootballStandingBlocky();
        FootballTeamProgrammeBlockyFieldsModel footballTeamProgrammeBlockyData = addFootballTeamProgrammerBlocky();
        FootballKnockoutBlockyFieldsModel footballKnockoutSchemeBlockyData = addKnockoutFootballBlocky();

        articleFormPage.mainSection().getFootballLivescoreBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTopScorersBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getMostDecoratedBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getStandingsBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamProgrammeBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getKnockoutSchemeBlocks().get(0).validationMessagesList();
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballLivescoreBlockyRequest(livescoreBlockyData, articleRequest);
        assertFootballTournamentProgrammeBlockyRequest(footballTournamentProgrammeBlockyData, articleRequest);
        assertFootballTopScorersBlockyRequest(topScorersBlockyData, articleRequest);
        assertFootballMostDecoratedPlayersBlockyRequest(mostDecoratedPlayerBlockyData, articleRequest);
        assertFootballStandingsBlockyRequest(footballStandingBlockyData, articleRequest);
        assertFootballTeamProgrammeBlockyRequest(footballTeamProgrammeBlockyData, articleRequest);
        assertFootballKnockoutBlockyRequest(footballKnockoutSchemeBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Create Article with single entity football Blocks")
    public void articleSaved_when_addSingleEntityFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);

        //TODO: ipetkov - 24.08.2024 - remove comment for team form blocky when the widget is implemented and used at all.
//        var footballTeamFormBlockyData = addFootballTeamFormBlocky();

        FootballLineupsBlockyFieldsModel footballLineupsBlockyData = addFootballLineupBlocky();
        FootballSingleEventBlockyFieldsModel footballSingleEventBlockyData = addFootballSingleEventBlocky();
        FootballTeamSquadBlockyFieldsModel footballTeamSquadBlockyData = addFootballTeamSquadBlocky();
        FootballOddsBlockyFieldsModel footballOddsBlockyData = addFootballOddsBlocky();

//        articleFormPage.mainSection().getTeamFormBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getLineupsBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamSquadBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getOddsBlocks().get(0).validationMessagesList();
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

//        assertFootballTeamFormBlockyRequest(footballTeamFormBlockyData, articleRequest);
        assertFootballLineupsBlockyRequest(footballLineupsBlockyData, articleRequest);
        assertFootballSingleEventBlockyRequest(footballSingleEventBlockyData, articleRequest);
        assertFootballTeamSquadBlockyRequest(footballTeamSquadBlockyData, articleRequest);
        assertFootballOddsBlockyRequest(footballOddsBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Edit Article with single entity football Blocks")
    public void articleEdit_when_addSingleEntityFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        editArticle(createdArticle);

        //TODO: ipetkov - 24.08.2024 - remove comment for team form blocky when the widget is implemented and used at all.
//        var footballTeamFormBlockyData = addFootballTeamFormBlocky();
        FootballLineupsBlockyFieldsModel footballLineupsBlockyData = addFootballLineupBlocky();
        FootballSingleEventBlockyFieldsModel footballSingleEventBlockyData = addFootballSingleEventBlocky();
        FootballTeamSquadBlockyFieldsModel footballTeamSquadBlockyData = addFootballTeamSquadBlocky();
        FootballOddsBlockyFieldsModel footballOddsBlockyData = addFootballOddsBlocky();

//        articleFormPage.mainSection().getTeamFormBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getLineupsBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamSquadBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getOddsBlocks().get(0).validationMessagesList();
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

//        assertFootballTeamFormBlockyRequest(footballTeamFormBlockyData, articleRequest);
        assertFootballLineupsBlockyRequest(footballLineupsBlockyData, articleRequest);
        assertFootballSingleEventBlockyRequest(footballSingleEventBlockyData, articleRequest);
        assertFootballTeamSquadBlockyRequest(footballTeamSquadBlockyData, articleRequest);
        assertFootballOddsBlockyRequest(footballOddsBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Create Article with profile football Blocks")
    public void articleSaved_when_addProfileFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);

        FootballPlayerProfileBlockyFieldsModel footballPlayerBlockyData = addFootballPlayerProfileBlocky();
        FootballTeamProfileBlockyFieldsModel footballTeamProfileBlockyData = addFootballTeamProfileBlocky();
        FootballTeamH2HBlockyFieldsModel footballTeamH2HBlockyData = addFootballTeamH2HBlocky();
        FootballPlayerH2HBlockyFieldsModel footballPlayerH2HBlockyData = addFootballPlayerH2HBlocky();
        FootballMatchesH2HBlockyFieldsModel footballMatchesBlockyData = addFootballMatchesH2HBlocky();
        articleFormPage.saveArticle();

        articleFormPage.mainSection().getPlayerProfileBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamProfileBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamH2hBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getPlayerH2hBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getFootballMatchesH2HBlocks().get(0).validationMessagesList();

        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballPlayerBlockyRequest(footballPlayerBlockyData, articleRequest);
        assertFootballTeamProfileBlockyRequest(footballTeamProfileBlockyData, articleRequest);
        assertFootballTeamsH2HBlockyRequest(footballTeamH2HBlockyData, articleRequest);
        assertFootballPlayerH2HBlockyRequest(footballPlayerH2HBlockyData, articleRequest);
        assertFootballMatchesH2HBlockyRequest(footballMatchesBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Edit Article with profile football Blocks")
    public void articleEdit_when_addProfileFootballBlocks() {
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        editArticle(createdArticle);

        FootballPlayerProfileBlockyFieldsModel footballPlayerBlockyData = addFootballPlayerProfileBlocky();
        FootballTeamProfileBlockyFieldsModel footballTeamProfileBlockyData = addFootballTeamProfileBlocky();
        FootballTeamH2HBlockyFieldsModel footballTeamH2HBlockyData = addFootballTeamH2HBlocky();
        FootballPlayerH2HBlockyFieldsModel footballPlayerH2HBlockyData = addFootballPlayerH2HBlocky();
        FootballMatchesH2HBlockyFieldsModel footballMatchesBlockyData = addFootballMatchesH2HBlocky();
        articleFormPage.saveArticle();

        articleFormPage.mainSection().getPlayerProfileBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamProfileBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getTeamH2hBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getPlayerH2hBlocks().get(0).validationMessagesList();
        articleFormPage.mainSection().getFootballMatchesH2HBlocks().get(0).validationMessagesList();

        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballPlayerBlockyRequest(footballPlayerBlockyData, articleRequest);
        assertFootballTeamProfileBlockyRequest(footballTeamProfileBlockyData, articleRequest);
        assertFootballTeamsH2HBlockyRequest(footballTeamH2HBlockyData, articleRequest);
        assertFootballPlayerH2HBlockyRequest(footballPlayerH2HBlockyData, articleRequest);
        assertFootballMatchesH2HBlockyRequest(footballMatchesBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Create article, apply auto-tagging, and add pre-filled block")
    public void autoTagging_when_createArticle_addPreFilledBlock() {
        int expectedEntitiesCount = 1;
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        FootballSingleEventBlockyFieldsModel singleEventBlockyData = footballApiFacade.getSingleEventBlockySportData(TEAM_1, EventStatusType.FINISHED);
        singleEventBlockyData = FootballSingleEventBlockyFieldsFactory.buildDynamicFieldsOnly(singleEventBlockyData);

        articleFormPage.generalSection().collapse();
        autotaggingApiUrl = ConfigurationService.get(SportalSettings.class).getAutotaggingApiUrl();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().selectSport(SupportedSports.FOOTBALL.getTitle());

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH);
        articleFormPage.map().activeParagraph().refreshTags();
        Map<String, List<TagResponseObject>> response = articleFormPage.tagsSection().getAutoTaggingResponse(autotaggingApiUrl);
        List<String> actualTagStrings = response.values().stream().toList().get(0).stream().map(x -> x.getEntities().get(0).getName()).toList();
        List<TagEntity> actualFootballTags = response.values().stream().toList().get(0).get(response.values().stream().toList().get(0).size() - 1).getEntities();

        Assertions.assertAll(
                () -> Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTagStrings), AssertMessages.tagsNotProperlyDisplayed(EXPECTED_TAGS_EXTENDED, actualTagStrings)),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getName().equals(EXPECTED_FOOTBALL_PLAYER)), AssertMessages.tagNotFound(actualFootballTags.stream().toList())),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getEntityType().equals("player")), AssertMessages.expectedEntityTypeNotFound(EXPECTED_FOOTBALL_PLAYER, "player", actualFootballTags.stream().toList())),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getSport().equals("football")), AssertMessages.expectedSportNotFound(EXPECTED_FOOTBALL_PLAYER, "football", actualFootballTags.stream().toList())),
                () -> Assertions.assertEquals(expectedEntitiesCount, actualFootballTags.size(), AssertMessages.unexpectedNumberOfSuggestions(EXPECTED_FOOTBALL_PLAYER)));

        articleFormPage.tagsSection().filterFootballConnections(
                singleEventBlockyData.getTeam().getName(),
                singleEventBlockyData.getEvent().getCompetition().getName());

        articleFormPage.tagsSection().map().pastMatchesTabButton().click();

        app().browser().waitUntil(e -> !articleFormPage.tagsSection().map().pastMatchResults().isEmpty());
        List<MatchResult> matchResults = articleFormPage.tagsSection().map().pastMatchResults();
        MatchResult selectedMatch = matchResults.get(0);
        selectedMatch.select();

        app().browser().waitUntil(e -> articleFormPage.tagsSection().map().selectedMatchesList().size() > 0);

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_SINGLE_EVENT);
        articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).saveBlock();
        singleEventBlockyData.setEmbedCode(articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).getExpectedEmbedCode(singleEventBlockyData));
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballSingleEventBlockyRequest(singleEventBlockyData, articleRequest);
    }

    @Test
    @DisplayName("[E2E] Edit article, apply auto-tagging, and add pre-filled block")
    public void autoTagging_when_EditArticle_addPreFilledBlock() {
        int expectedEntitiesCount = 1;
        createdArticle = createArticle(EXPECTED_FOOTBALL_CATEGORY);
        editArticle(createdArticle);
        FootballSingleEventBlockyFieldsModel blockyData = footballApiFacade.getSingleEventBlockySportData(TEAM_1, EventStatusType.FINISHED);
        blockyData = FootballSingleEventBlockyFieldsFactory.buildDynamicFieldsOnly(blockyData);

        articleFormPage.generalSection().collapse();
        autotaggingApiUrl = ConfigurationService.get(SportalSettings.class).getAutotaggingApiUrl();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().selectSport(SupportedSports.FOOTBALL.getTitle());

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH);
        articleFormPage.map().activeParagraph().refreshTags();
        Map<String, List<TagResponseObject>> response = articleFormPage.tagsSection().getAutoTaggingResponse(autotaggingApiUrl);
        List<String> actualTagStrings = response.values().stream().toList().get(0).stream().map(x -> x.getEntities().get(0).getName()).toList();
        List<TagEntity> actualFootballTags = response.values().stream().toList().get(0).get(response.values().stream().toList().get(0).size() - 1).getEntities();

        Assertions.assertAll(
                () -> Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTagStrings), AssertMessages.tagsNotProperlyDisplayed(EXPECTED_TAGS_EXTENDED, actualTagStrings)),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getName().equals(EXPECTED_FOOTBALL_PLAYER)), AssertMessages.tagNotFound(actualFootballTags.stream().toList())),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getEntityType().equals("player")), AssertMessages.expectedEntityTypeNotFound(EXPECTED_FOOTBALL_PLAYER, "player", actualFootballTags.stream().toList())),
                () -> Assertions.assertTrue(actualFootballTags.stream().anyMatch(tag -> tag.getSport().equals("football")), AssertMessages.expectedSportNotFound(EXPECTED_FOOTBALL_PLAYER, "football", actualFootballTags.stream().toList())),
                () -> Assertions.assertEquals(expectedEntitiesCount, actualFootballTags.size(), AssertMessages.unexpectedNumberOfSuggestions(EXPECTED_FOOTBALL_PLAYER)));

        articleFormPage.tagsSection().filterFootballConnections(
                blockyData.getTeam().getName(),
                blockyData.getEvent().getCompetition().getName());

        articleFormPage.tagsSection().map().pastMatchesTabButton().click();

        app().browser().waitUntil(e -> !articleFormPage.tagsSection().map().pastMatchResults().isEmpty());
        List<MatchResult> matchResults = articleFormPage.tagsSection().map().pastMatchResults();
        MatchResult selectedMatch = matchResults.get(0);
        selectedMatch.select();

        app().browser().waitUntil(e -> articleFormPage.tagsSection().map().selectedMatchesList().size() > 0);

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_SINGLE_EVENT);
        articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).saveBlock();
        blockyData.setEmbedCode(articleFormPage.mainSection().getFootballSingleEventBlocks().get(0).getExpectedEmbedCode(blockyData));
        articleFormPage.saveArticle();
        ArticleModel articleRequest = articleFormPage.getArticleUpdateRequest();

        assertFootballSingleEventBlockyRequest(blockyData, articleRequest);
    }
}