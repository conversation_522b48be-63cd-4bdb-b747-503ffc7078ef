package articles.aiarticles;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.StringConstants;
import data.constants.enums.AIArticlesSection;
import data.constants.enums.GenerateArticleBasedOn;
import data.models.categories.Category;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.aiarticlespage.generatearticlesection.GenerateArticleSection;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.Arrays;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.AI_ARTICLES)
@Story(CMSStories.AI_ARTICLES)
public class GenerateArticleTests extends CmsWebTest {

    private static final String OPTIONS_NOT_EXPECTED = "%s options are not as expected";
    private static final List<String> YES_NO_OPTIONS = List.of(StringConstants.YES_STRING, StringConstants.NO_STRING);
    private GenerateArticleSection generateArticleSection;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        aiArticlesPage.open();
        generateArticleSection = aiArticlesPage.generateArticleSection();
    }

    @Test
    public void generateArticleSectionOpened_when_openAIArticlesPage() {
        aiArticlesPage.asserts().assertSectionIsOpened(AIArticlesSection.GENERATE_ARTICLE);
    }

    @Test
    public void expectedElementsDisplayed_when_openGenerateArticleSection() {
        generateArticleSection.asserts().assertSectionElements();
    }

    @Test
    public void expectedElementsDisplayed_when_selectEventOptionFromGenerateArticleBasedOnDropdown() {
        generateArticleSection.selectGenerateArticleBasedOn(GenerateArticleBasedOn.EVENT)
                .asserts()
                .assertSectionElements(GenerateArticleBasedOn.EVENT);
    }

    @Test
    public void expectedElementsDisplayed_when_selectDateOptionFromGenerateArticleBasedOnDropdown() {
        generateArticleSection.selectGenerateArticleBasedOn(GenerateArticleBasedOn.DATE)
                .asserts()
                .assertSectionElements(GenerateArticleBasedOn.DATE);
    }

    @Test
    @Issue("PLT-701")
    public void expectedCategoriesLoaded_when_openCategoryDropdown_and_checkOptions() {
        List<Category> categoriesAssignedToUser = contentApiFacade.getCategoriesAssignedToUser(User.FULLSETUP);
        generateArticleSection.asserts().assertCategoryOptions(categoriesAssignedToUser);
    }

    @Test
    public void expectedOptionsLoaded_when_openGenerateStraplineDropdown_and_checkOptions() {
        List<String> actualOptions = generateArticleSection.map().generateStraplineDropdown().getOptionsValues();
        Assertions.assertLinesMatch(YES_NO_OPTIONS, actualOptions, OPTIONS_NOT_EXPECTED.formatted("Generate strapline"));
    }

    @Test
    public void expectedOptionsLoaded_when_openGenerateSummaryDropdown_and_checkOptions() {
        List<String> actualOptions = generateArticleSection.map().generateSummaryDropdown().getOptionsValues();
        Assertions.assertLinesMatch(YES_NO_OPTIONS, actualOptions, OPTIONS_NOT_EXPECTED.formatted("Generate summary"));
    }

    @Test
    public void expectedOptionsLoaded_when_openGenerateArticleBasedOnDropdown_and_checkOptions() {
        List<String> expectedOptions = Arrays.stream(GenerateArticleBasedOn.values())
                .map(GenerateArticleBasedOn::getValue)
                .toList();
        List<String> actualOptions = generateArticleSection.map().generateArticleBasedOnDropdown().getOptionsValues();
        Assertions.assertLinesMatch(expectedOptions, actualOptions, OPTIONS_NOT_EXPECTED.formatted("Generate article based on"));
    }
}