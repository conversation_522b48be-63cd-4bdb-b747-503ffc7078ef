package articles.blockytests.icehockey.livescoretests;

import blockies.icehockey.livescoreblocky.IceHockeyLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.icehockey.IceHockeyLivescoreBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.ICE_HOCKEY)
@Tag(SportBlocky.ICE_HOCKEY_LIVESCORE)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class IceHockeyLivescoreBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private IceHockeyLivescoreBlocky blocky;
    private LivescoreBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_LIVESCORE);
        blocky = articleFormPage.mainSection().getIceHockeyLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();
        var date = LocalDate.now();

        blockyData = iceHockeyApiFacade.getLivescoreBlockySportData(date);
        blockyData = IceHockeyLivescoreBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void embedCodeCopied_when_fillAllFields_saveIceHockeyLivescoreBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void embedCodeCopied_when_fillMandatoryFields_saveIceHockeyLivescoreBlocky_and_clickCopyEmbedCodeButton() {
        blockyData = IceHockeyLivescoreBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }
}