package articles.blockytests.icehockey.livescoretests;

import blockies.icehockey.livescoreblocky.IceHockeyLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.ICE_HOCKEY)
@Tag(SportBlocky.ICE_HOCKEY_LIVESCORE)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class IceHockeyLivescoreBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private IceHockeyLivescoreBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_LIVESCORE);
        blocky = articleFormPage.mainSection().getIceHockeyLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Ice Hockey livescore blocky and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addIceHockeyLivescoreBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}