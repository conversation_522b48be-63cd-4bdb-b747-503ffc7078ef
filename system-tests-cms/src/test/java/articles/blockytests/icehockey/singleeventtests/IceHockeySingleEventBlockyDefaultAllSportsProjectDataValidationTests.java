package articles.blockytests.icehockey.singleeventtests;

import blockies.icehockey.singleeventblocky.IceHockeySingleEventBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.EventStatusType;
import data.constants.WidgetBlock;
import data.models.blockymodels.icehockey.IceHockeySingleEventBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.icehockey.IceHockeySingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.ICE_HOCKEY)
@Tag(SportBlocky.ICE_HOCKEY_SINGLE_EVENT)
@Tag(CMSStories.SINGLEEVENT_BLOCKY)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class IceHockeySingleEventBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private IceHockeySingleEventBlocky blocky;
    private IceHockeySingleEventBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_SINGLE_EVENT);
        blocky = articleFormPage.mainSection().getIceHockeySingleEventBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = iceHockeyApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Ice Hockey single event blocky and select upcoming game and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addIceHockeySingleEventBlocky_and_selectUpcomingGame() {
        blocky.selectTeam(blockyData.getTeam().getName(), blockyData.getTeam().getId());
        blocky.selectEvent(blockyData.getEvent());

        blocky.displayOddsCheckbox().validateIsChecked();
    }
}