package articles.blockytests.icehockey.singleeventtests;

import blockies.icehockey.singleeventblocky.IceHockeySingleEventBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.*;
import data.constants.enums.enetpulseproxy.IceHockeyTeamEnum;
import data.customelements.MatchResult;
import data.models.blockymodels.icehockey.IceHockeySingleEventBlockyFieldsModel;
import data.models.searchapi.ResultModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.icehockey.IceHockeySingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.ICE_HOCKEY)
@Tag(SportBlocky.ICE_HOCKEY_SINGLE_EVENT)
@Tag(CMSStories.SINGLEEVENT_BLOCKY)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class IceHockeySingleEventBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final IceHockeyTeamEnum ICE_HOCKEY_TEAM = IceHockeyTeamEnum.NEW_YORK_RANGERS;
    private IceHockeySingleEventBlocky blocky;
    private IceHockeySingleEventBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.ICE_HOCKEY_SINGLE_EVENT);
        blocky = articleFormPage.mainSection().getIceHockeySingleEventBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = iceHockeyApiFacade.getSingleEventBlockySportData(EventStatusType.FINISHED);
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void expectedBlockyElementsDisplayed_when_addIceHockeySingleEventBlocky() {
        blocky.asserts()
                .assertBlockyElements()
                .assertFieldsDefaultState();
    }

    @Test
    public void requiredMessageDisplayed_when_addIceHockeySingleEventBlocky_and_saveWithEmptyTeam() {
        blocky.saveBlock();
        blocky.validateValidationMessageDisplayed(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING);
        blocky.validateValidationMessageDisplayed(StringConstants.NO_GAMES_SELECTED_STRING);
    }

    @Test
    public void teamFieldCanHoldMultipleEntities_when_addIceHockeySingleEventBlocky_and_addMoreThanOneTeam() {
        var secondTeam = IceHockeyTeamEnum.DALLAS_STARS.getName();
        blocky.filterMultipleTeams(ICE_HOCKEY_TEAM.getName(), secondTeam);

        Assertions.assertTrue(blocky.getSelectedTeams().contains(ICE_HOCKEY_TEAM.getName()), AssertMessages.entityNotVisible("Selected First Team", ICE_HOCKEY_TEAM.getName(), blocky.getSelectedTeams()));
        Assertions.assertTrue(blocky.getSelectedTeams().contains(secondTeam), AssertMessages.entityNotVisible("Selected Second Team", secondTeam, blocky.getSelectedTeams()));
    }

    @Test
    public void upcomingMatchesDisplayed_when_addIceHockeySingleEventBlocky_and_selectTeam() {
        ResultModel notStartedEvent = searchApiV2Facade.getNotStartedEventFor(SupportedSports.ICE_HOCKEY);
        String teamName = notStartedEvent.getParticipantDetails().get(0).getParticipant().getName();
        String teamId = notStartedEvent.getParticipantDetails().get(0).getParticipant().getId();

        blocky.selectTeam(teamName, teamId);
        List<MatchResult> upcomingMatches = blocky.eventsList().getUpcomingMatches();

        Assertions.assertFalse(upcomingMatches.isEmpty(), AssertMessages.entityNotVisible("Upcoming matches"));
    }

    @Test
    public void pastMatchesDisplayed_when_addIceHockeySingleEventBlocky_and_selectTeam() {
        blocky.selectTeam(ICE_HOCKEY_TEAM.getName(), ICE_HOCKEY_TEAM.getId());
        List<MatchResult> pastMatches = blocky.eventsList().getPastMatches();

        Assertions.assertFalse(pastMatches.isEmpty(), AssertMessages.entityNotVisible("Past matches"));
    }

    @Test
    public void blockySaved_when_addIceHockeySingleEventBlocky_and_saveWithEmptyBookmaker() {
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void expectedOptionsLoaded_when_addIceHockeySingleEventBlocky_and_checkRefreshTime() {
        List<String> expectedRefreshTimeValues = RefreshTime.getEnumValues();
        List<String> actualRefreshTimeValues = blocky.refreshTimeSelect().getOptionsValues();

        Assertions.assertLinesMatch(expectedRefreshTimeValues, actualRefreshTimeValues, AssertMessages.entityNotExpected("Refresh Time options"));
    }

    @Test
    public void iceHockeySingleEventBlockySaved_when_fillRequiredFields_and_clickSaveButton() {
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void iceHockeySingleEventBlockyHided_when_blockyNotSaved_and_clickCancelButton() {
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
    }

    @Test
    public void iceHockeySingleEventBlockyHidden_when_blockySaved_and_clickCancelButton() {
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult()
                .cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void expectedControlsDisplayed_when_saveIceHockeySingleEventBlocky() {
        blockyData = IceHockeySingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void previewDisplayed_when_saveIceHockeySingleEventBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void editModeControlsDisplayed_when_saveIceHockeySingleEventBlocky_and_clickEditButton_and_checkPastGames() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertEditSectionIsVisible(blockyData);
    }

    @Test
    public void blockyRemoved_when_saveIceHockeySingleEventBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Ice Hockey single event blocky and select upcoming game and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addIceHockeySingleEventBlocky_and_selectUpcomingGame() {
        blockyData = iceHockeyApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);

        blocky.selectTeam(blockyData.getTeam().getName(), blockyData.getTeam().getId());
        blocky.selectEvent(blockyData.getEvent());

        blocky.displayOddsCheckbox().validateIsChecked();
    }
}