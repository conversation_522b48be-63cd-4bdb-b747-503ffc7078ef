package articles.blockytests.text;

import core.CmsWebTest;
import data.constants.AssertMessages;
import data.models.articles.ArticleResponseModel;
import data.models.articles.BodyItem;
import factories.articles.ArticleFieldsFactory;
import org.junit.jupiter.api.Assertions;
import pages.articleformpage.ArticleFormPage;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

import static data.constants.StringConstants.*;

public class BaseTextBlockTests extends CmsWebTest {

    protected static final String TEXT = "This is a test text for text blocky";
    protected static final String URL = SPORTAL_URL;
    protected List<ArticleResponseModel> articleIds;

    @Override
    protected void beforeEach() {
        articleIds = new ArrayList<>();
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        articleFormPage.waitForPageLoad();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteArticles(articleIds);
    }

    protected ArticleResponseModel saveArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        ArticleResponseModel createdArticleResponse = articleFormPage.getArticleCreateResponse();
        articleIds.add(createdArticleResponse);

        return createdArticleResponse;
    }

    protected void validateBodyObjectInContentAPIResponse(String expectedTextBlockType, BodyItem bodyObject, String expectedContent) {
        Assertions.assertEquals(expectedTextBlockType, bodyObject.getData().getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, DATA_STRING));
        Assertions.assertEquals(expectedContent, bodyObject.getData().getContent(), AssertMessages.objectValueNotCorrect(CONTENT_STRING, DATA_STRING));
        Assertions.assertEquals(EDITOR_BLOCK_STRING, bodyObject.getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, "body"));
    }
}