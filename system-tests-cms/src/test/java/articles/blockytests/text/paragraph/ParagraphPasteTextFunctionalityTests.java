package articles.blockytests.text.paragraph;

import articles.blockytests.text.BaseTextBlockTests;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.ButtonPositionEnum;
import data.models.articles.BodyItem;
import data.utils.StringUtils;
import io.qameta.allure.TmsLink;
import io.qameta.allure.TmsLinks;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.utilities.ClipboardManager;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(CMSTags.TEXT)
@Tag(CMSTags.PARAGRAPH)
@Tag(CMSTags.CLIPBOARD)
public class ParagraphPasteTextFunctionalityTests extends BaseTextBlockTests {

    private static final int PARAGRAPHS_COUNT_TO_BE_CREATED = 5;
    private static final int SENTENCES_COUNT_PER_PARAGRAPH = 10;
    private String expectedTextForCopy;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        expectedTextForCopy = StringUtils.generateParagraphsWithSentences(PARAGRAPHS_COUNT_TO_BE_CREATED, SENTENCES_COUNT_PER_PARAGRAPH);
        ClipboardManager.copyTextToClipboard(expectedTextForCopy);
        ClipboardManager.getLastEntity();
    }

    @ParameterizedTest
    @TmsLinks({@TmsLink("SMP-T620"), @TmsLink("SMP-T621")})
    @EnumSource(ButtonPositionEnum.class)
    public void SMP_T620_paragraphsAddedWithValidContent_when_pasteTextAt(ButtonPositionEnum position) {
        int expectedParagraphCount = PARAGRAPHS_COUNT_TO_BE_CREATED + 1;

        articleFormPage.pasteTextAt(position);

        int actualParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();

        Assertions.assertEquals(expectedParagraphCount, actualParagraphCount, AssertMessages.paragraphCountNotCorrect());
        articleFormPage.asserts().assertParagraphsTextAfterPasteAt(position, expectedTextForCopy, false);
    }

    @ParameterizedTest
    @TmsLinks({@TmsLink("SMP-T622"), @TmsLink("SMP-T623")})
    @EnumSource(ButtonPositionEnum.class)
    public void SMP_T622_paragraphsAddedWithValidContent_when_pasteTextAt_and_saveArticle(ButtonPositionEnum position) {
        articleFormPage.pasteTextAt(position);

        int countOfParagraphsAfterPasteText = articleFormPage.mainSection().getParagraphBlocks().size();

        List<BodyItem> bodyObject = saveArticle().getBody();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.waitForPageLoad();
        articleFormPage.waitForParagraphsLoaded();
        int countOfParagraphsAfterSaveArticle = articleFormPage.map().paragraphList().size();
        Assertions.assertEquals(countOfParagraphsAfterPasteText - 1, countOfParagraphsAfterSaveArticle, "Count of paragraphs after save article is not as expected.");

        String[] expectedTextPerParagraph = StringUtils.splitText(expectedTextForCopy, "\n\n");

        Assertions.assertEquals(PARAGRAPHS_COUNT_TO_BE_CREATED, bodyObject.size(), "Count of objects for 'body' property is not as expected.");

        for (int i = 0; i < bodyObject.size(); i++) {
            String expectedTextInParagraph = "<p>%s</p>".formatted(expectedTextPerParagraph[i]);
            Assertions.assertEquals(expectedTextInParagraph, bodyObject.get(i).getData().getContent(), AssertMessages.paragraphTextNotCorrect(i + 1));
        }

        articleFormPage.asserts().assertParagraphsTextAfterPasteAt(position, expectedTextForCopy, true);
    }
}
