package articles.blockytests.text.paragraph;

import articles.blockytests.text.BaseTextBlockTests;
import blockies.text.ParagraphBlock;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.TooltipEnum;
import data.constants.WidgetBlock;
import data.customelements.editor.AlignmentDirection;
import data.customelements.editor.HighlightColors;
import data.customelements.editor.SpecialCharacterEnum;
import data.customelements.editor.TransformTo;
import data.models.articles.BodyItem;
import data.utils.StringUtils;
import io.qameta.allure.TmsLink;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;
import java.util.Random;

import static data.constants.StringConstants.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(CMSTags.TEXT)
@Tag(CMSTags.PARAGRAPH)
@TmsLink("SMP_T199")
public class ParagraphBlockTests extends BaseTextBlockTests {

    @Test
    public void SMP_T199_newParagraphAdded_when_addItFromTextMenu() {
        int expectedParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();

        articleFormPage.addBlocky(WidgetBlock.PARAGRAPH);

        int actualParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();
        Assertions.assertEquals(expectedParagraphCount + 1, actualParagraphCount, AssertMessages.paragraphBlockNotAdded());
    }

    @Test
    public void SMP_T199_newParagraphAdded_when_addItFromTooltipMenu() {
        int expectedParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();

        articleFormPage.addBlockFromToolTipMenu(TooltipEnum.PARAGRAPH);

        int actualParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();
        Assertions.assertEquals(expectedParagraphCount + 1, actualParagraphCount, AssertMessages.paragraphBlockNotAdded());
    }

    @Test
    public void SMP_T199_newParagraphAdded_when_pressEnterKey() {
        int expectedParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();

        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .enter();

        int actualParagraphCount = articleFormPage.mainSection().getParagraphBlocks().size();

        Assertions.assertEquals(expectedParagraphCount + 1, actualParagraphCount, AssertMessages.paragraphBlockNotAdded());
    }

    @Test
    //@Issue("SFE-4536")
    public void SMP_T199_secondParagraphAddedWithText_when_placeCursorAfterSpecificWordInFirstParagraph_and_pressEnterKey_and_saveArticle() {
        int timesOfCursorMovementFromEnd = new Random().nextInt(1, StringUtils.getNumberOfSpacesInText(TEXT));
        int expectedCountOfParagraphs = 2;
        String[] paragraphsText = StringUtils.splitTextBasedOnWord(TEXT, timesOfCursorMovementFromEnd);
        String expectedFirstParagraphText = paragraphsText[0];
        String expectedSecondParagraphText = paragraphsText[1];

        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .moveCursorInText(timesOfCursorMovementFromEnd)
                .enter();

        Assertions.assertEquals(expectedCountOfParagraphs, articleFormPage.mainSection().getParagraphBlocks().size(), AssertMessages.paragraphBlockNotAdded());

        ParagraphBlock secondParagraphBlock = articleFormPage.mainSection().getParagraphBlocks().get(1);

        Assertions.assertFalse(articleFormPage.mainSection().getParagraphBlocks().get(0).getText().isEmpty(), "First paragraph is empty");
        Assertions.assertEquals(expectedFirstParagraphText, articleFormPage.mainSection().getParagraphBlocks().get(0).getText().replace("\u00a0", ""),
                "First paragraph text is not correct");

        Assertions.assertFalse(secondParagraphBlock.getText().isEmpty(), "Second paragraph is empty");
        Assertions.assertEquals(expectedSecondParagraphText, secondParagraphBlock.getText(),
                "Second paragraph text is not correct");

        List<BodyItem> bodyObject = saveArticle().getBody();
        createdArticle = articleFormPage.getArticleCreateResponse();
        BodyItem firstBodyObject = bodyObject.get(0);
        BodyItem secondBodyObject = bodyObject.get(1);

        Assertions.assertEquals(PARAGRAPH_STRING, firstBodyObject.getData().getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, DATA_STRING));
        Assertions.assertEquals("<p>%s </p>".formatted(expectedFirstParagraphText), firstBodyObject.getData().getContent(),
                AssertMessages.objectValueNotCorrect(CONTENT_STRING, DATA_STRING));

        Assertions.assertEquals(PARAGRAPH_STRING, secondBodyObject.getData().getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, DATA_STRING));
        Assertions.assertEquals("<p>%s</p>".formatted(expectedSecondParagraphText), secondBodyObject.getData().getContent(),
                AssertMessages.objectValueNotCorrect(CONTENT_STRING, DATA_STRING));
    }

    @Test
    public void SMP_T199_paragraphTextBolded_when_formatTextBold_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .bold();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><strong>%s</strong></p>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_paragraphTextItalic_when_formatTextItalic_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .italic();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><i>%s</i></p>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_paragraphTextUnderlined_when_formatTextUnderline_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .underline();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><u>%s</u></p>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_linkAddedToParagraph_when_doNotAutoLinkEnabled_and_openInNewTabEnabled_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .toolbar()
                .link(URL, true, true);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p>" +
                "<a target=\"_blank\" class=\"editor-link autolink-disabled\" href=\"%s\">%s</a>".formatted(URL, URL) +
                "</p>";
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_linkAddedToParagraph_when_doNotAutoLinkDisabled_and_openInNewTabDisabled_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .toolbar()
                .link(URL, false, false);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p>" +
                "<a target=\"_self\" class=\"editor-link\" href=\"%s\">%s</a>".formatted(URL, URL) +
                "</p>";
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = HighlightColors.class, names = {"GREEN_MARKER", "ORANGE_MARKER"}, mode = EnumSource.Mode.INCLUDE)
    public void SMP_T199_paragraphTextHighlighted_when_highlightTextWithMarkerColorOptions_and_saveArticle(HighlightColors markerColor) {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .highlight(markerColor);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><mark data-color=\"rgb(%s)\" style=\"background-color: rgb(%s);\" class=\"%s\">%s</mark></p>"
                .formatted(markerColor.getRgbValue(), markerColor.getRgbValue(), markerColor.getClassValueInDOM(), TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }


    @ParameterizedTest
    @EnumSource(value = HighlightColors.class, names = {"RED_PEN", "GREEN_PEN"}, mode = EnumSource.Mode.INCLUDE)
    public void SMP_T199_paragraphTextHighlighted_when_highlightTextWithPenColorOptions_and_saveArticle(HighlightColors penColor) {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .highlight(penColor);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><mark style=\"color: rgb(%s); background-color: inherit;\" class=\"%s\">%s</mark></p>".formatted(penColor.getRgbValue(), penColor.getClassValueInDOM(), TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = TransformTo.class, names = "PARAGRAPH", mode = EnumSource.Mode.EXCLUDE)
    //@Issue("SFE-4537")
    public void SMP_T199_paragraphTransformed_when_transformParagraph_and_saveArticle(TransformTo transformedType) {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .transformTo(transformedType);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "%s%s%s".formatted(transformedType.getOpenTag(), TEXT, transformedType.getClosingTag());
        validateBodyObjectInContentAPIResponse(transformedType.getValue().split("\\s+")[0].toLowerCase(), bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = AlignmentDirection.class, names = {"ALIGN_LEFT"}, mode = EnumSource.Mode.EXCLUDE)
    public void SMP_T199_paragraphTextAligned_when_alignText_and_saveArticle(AlignmentDirection alignmentDirection) {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .toolbar()
                .textAlignment(alignmentDirection);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p style=\"text-align: %s\">%s</p>".formatted(alignmentDirection.getValueInDOM(), TEXT);
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_specialCharacterAddedToParagraph_when_addSpecialCharacter_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .toolbar()
                .specialCharacter(SpecialCharacterEnum.GRINNING_FACE);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p>%s%s</p>".formatted(TEXT, SpecialCharacterEnum.GRINNING_FACE.getUnicode());
        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void orderedListParagraph_when_orderListText_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .toolbar()
                .orderedList();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();
        var expectedContent = "<ol><li><p>%s</p></li></ol>".formatted(TEXT);

        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void unorderedListParagraph_when_unOrderListText_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .toolbar()
                .unorderedList();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();
        var expectedContent = "<ul><li><p>%s</p></li></ul>".formatted(TEXT);

        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void createNewListItems_when_inUnorderedList_and_pressingEnter(){
        var block = articleFormPage.mainSection().getParagraphBlocks().get(0).textEditor();

        block.setText("First row: " + TEXT)
                .toolbar()
                .unorderedList()
                .enter();
        block.appendText("Second row: " + TEXT)
                .enter()
                .appendText("Third row: " + TEXT);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();
        var expectedContent = "<ul><li><p>First row: %s</p></li><li><p>Second row: %s</p></li><li><p>Third row: %s</p></li></ul>"
                .formatted(TEXT, TEXT, TEXT);

        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void createNewListItems_when_inOrderedList_and_pressingEnter() {
        var block = articleFormPage.mainSection().getParagraphBlocks().get(0).textEditor();

        block.setText("First row: " + TEXT)
                .toolbar()
                .orderedList()
                .enter();
        block.appendText("Second row: " + TEXT)
                .enter()
                .appendText("Third row: " + TEXT);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();
        var expectedContent = "<ol><li><p>First row: %s</p></li><li><p>Second row: %s</p></li><li><p>Third row: %s</p></li></ol>"
                .formatted(TEXT, TEXT, TEXT);

        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }

    @Test
    public void createSeparateListStructures_when_switchingListTypes_and_pressingEnter() {
        var block = articleFormPage.mainSection().getParagraphBlocks().get(0).textEditor();

        block.setText("Ordered List: " + TEXT)
                .toolbar()
                .orderedList()
                .enter()
                .orderedList();
        block.appendText("Unordered List: " + TEXT)
                .toolbar()
                .unorderedList();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();
        var expectedContent = "<ol><li><p>Ordered List: %s</p></li></ol><ul><li><p>Unordered List: %s</p></li></ul>"
                .formatted(TEXT, TEXT);

        validateBodyObjectInContentAPIResponse(PARAGRAPH_STRING, bodyObject, expectedContent);
    }
}