package articles.blockytests.text;

import blockies.text.QuoteBlock;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.TooltipEnum;
import data.constants.WidgetBlock;
import data.customelements.editor.AlignmentDirection;
import data.customelements.editor.HighlightColors;
import data.customelements.editor.SpecialCharacterEnum;
import data.customelements.editor.TransformTo;
import data.models.articles.BodyItem;
import data.utils.StringUtils;
import io.qameta.allure.Issue;
import io.qameta.allure.TmsLink;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

import java.util.List;
import java.util.Random;

import static data.constants.StringConstants.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(CMSTags.TEXT)
@Tag(CMSTags.QUOTE)
@TmsLink("SMP_T199")
public class QuoteBlockTests extends BaseTextBlockTests {

    @Test
    public void SMP_T199_newQuoteAdded_when_addItFromTextMenu() {
        int expectedQuoteCount = 0;

        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        int actualQuoteCount = articleFormPage.mainSection().getQuoteBlocks().size();

        Assertions.assertEquals(expectedQuoteCount + 1, actualQuoteCount, AssertMessages.quoteBlockNotAdded());
    }

    @Test
    public void SMP_T199_newQuoteAdded_when_addItFromTooltipMenu() {
        int expectedQuoteCount = 0;

        articleFormPage.addBlockFromToolTipMenu(TooltipEnum.QUOTE);

        int actualQuoteCount = articleFormPage.mainSection().getQuoteBlocks().size();

        Assertions.assertEquals(expectedQuoteCount + 1, actualQuoteCount, AssertMessages.quoteBlockNotAdded());
    }

    @Test
    public void SMP_T199_secondEmptyParagraphAdded_when_placeCursorAfterSpecificWordInQuoteBlock_and_pressEnterKey_and_saveArticle() {
        String text = "This is a test text for text blocky";
        int timesOfCursorMovementFromEnd = new Random().nextInt(0, StringUtils.getNumberOfSpacesInText(text));
        int expectedCountOfQuotes, expectedSizeOfBodyObject;
        expectedSizeOfBodyObject = expectedCountOfQuotes = 1;
        int expectedCountOfParagraphs = 2;

        articleFormPage.addBlocky(WidgetBlock.QUOTE);
        QuoteBlock quoteBlock = articleFormPage.mainSection().getQuoteBlocks().get(0);

        quoteBlock.textEditor()
                .setText(text)
                .moveCursorInText(timesOfCursorMovementFromEnd)
                .getBrowserService().waitForAjax();
        quoteBlock.textEditor()
                .enter();

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedCountOfQuotes, articleFormPage.mainSection().getQuoteBlocks().size(), AssertMessages.quoteBlockNotAdded()),
                () -> Assertions.assertEquals(expectedCountOfParagraphs, articleFormPage.map().paragraphList().size(), AssertMessages.paragraphBlockNotAdded()),
                () -> Assertions.assertTrue(articleFormPage.map().paragraphList().get(1).getText().isEmpty(), AssertMessages.secondParagraphNotEmpty()),
                () -> Assertions.assertFalse(quoteBlock.getText().isEmpty(), AssertMessages.quoteBlockIsEmpty()),
                () -> Assertions.assertEquals(text, quoteBlock.getText(), AssertMessages.quoteBlockTextNotCorrect(text, quoteBlock.getText()))
        );

        List<BodyItem> bodyObject = saveArticle().getBody();
        String expectedContent = "<p>%s</p>".formatted(TEXT);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedSizeOfBodyObject, bodyObject.size(), AssertMessages.sizeOfBodyObjectNotCorrect(expectedSizeOfBodyObject, bodyObject.size())),
                () -> Assertions.assertEquals(QUOTE_STRING, bodyObject.get(0).getData().getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, DATA_STRING)),
                () -> Assertions.assertEquals(expectedContent, bodyObject.get(0).getData().getContent(), AssertMessages.objectValueNotCorrect(CONTENT_STRING, DATA_STRING)),
                () -> Assertions.assertTrue(articleFormPage.map().paragraphList().isEmpty(), AssertMessages.paragraphsNotEmptyAfterSave())
        );
    }

    @Test
    public void SMP_T199_quoteTextBolded_when_formatTextBold_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .setText(TEXT)
                .selectAll()
                .toolbar()
                .bold();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<p><strong>%s</strong></p>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_quoteTextItalic_when_formatTextItalic_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .selectAll()
                .toolbar()
                .italic();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedText = "<blockquote><p><i>%s</i></p></blockquote>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedText);
    }

    @Test
    public void SMP_T199_quoteTextUnderlined_when_formatTextUnderline_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .selectAll()
                .toolbar()
                .underline();

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<blockquote><p><u>%s</u></p></blockquote>".formatted(TEXT);
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_linkAddedToQuote_when_doNotAutoLinkEnabled_and_openInNewTabEnabled_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .toolbar()
                .link(URL, true, true);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<blockquote><p>" +
                "<a target=\"_blank\" class=\"editor-link autolink-disabled\" href=\"%s\">%s</a>".formatted(URL, URL) +
                "</p></blockquote>";
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @Test
    public void SMP_T199_linkAddedToQuote_when_doNotAutoLinkDisabled_and_openInNewTabDisabled_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .toolbar()
                .link(URL, false, false);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<blockquote><p>" +
                "<a target=\"_self\" class=\"editor-link\" href=\"%s\">%s</a>".formatted(URL, URL) +
                "</p></blockquote>";
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = HighlightColors.class, names = {"GREEN_MARKER", "ORANGE_MARKER"}, mode = EnumSource.Mode.INCLUDE)
    public void SMP_T199_quoteTextHighlighted_when_highlightTextWithMarkerColorOptions_and_saveArticle(HighlightColors markerColor) {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .selectAll()
                .toolbar()
                .highlight(markerColor);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<blockquote><p><mark data-color=\"rgb(%s)\" style=\"background-color: rgb(%s);\" class=\"%s\">%s</mark></p></blockquote>"
                .formatted(markerColor.getRgbValue(), markerColor.getRgbValue(), markerColor.getClassValueInDOM(), TEXT);
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = HighlightColors.class, names = {"RED_PEN", "GREEN_PEN"}, mode = EnumSource.Mode.INCLUDE)
    public void SMP_T199_paragraphTextHighlighted_when_highlightTextWithPenColorOptions_and_saveArticle(HighlightColors penColor) {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .selectAll()
                .toolbar()
                .highlight(penColor);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedContent = "<blockquote><p><mark style=\"color: rgb(%s); background-color: inherit;\" class=\"%s\">%s</mark></p></blockquote>".formatted(penColor.getRgbValue(), penColor.getClassValueInDOM(), TEXT);
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedContent);
    }

    @ParameterizedTest
    @EnumSource(value = TransformTo.class, names = "QUOTE", mode = EnumSource.Mode.EXCLUDE)
    //@Issue("SFE-4540")
    public void SMP_T199_quoteTransformed_when_transformQuote_and_saveArticle(TransformTo transformedType) {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .toolbar()
                .transformTo(transformedType);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedText = "%s%s%s".formatted(transformedType.getOpenTag(), TEXT, transformedType.getClosingTag());
        String expectedType = StringUtils.splitText(transformedType.getValue(), "\\s+")[0].toLowerCase();

        Assertions.assertEquals(expectedType, bodyObject.getData().getType(), AssertMessages.objectValueNotCorrect(TYPE_STRING, DATA_STRING));
        Assertions.assertEquals(expectedText, bodyObject.getData().getContent(), "Quote is not transformed to %s".formatted(transformedType.getValue()));
    }

    @ParameterizedTest
    @EnumSource(value = AlignmentDirection.class, names = {"ALIGN_LEFT"}, mode = EnumSource.Mode.EXCLUDE)
    public void SMP_T199_quoteTextAligned_when_alignText_and_saveArticle(AlignmentDirection alignmentDirection) {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .toolbar()
                .textAlignment(alignmentDirection);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedText = "<blockquote><p style=\"text-align: %s\">%s</p></blockquote>".formatted(alignmentDirection.getValueInDOM(), TEXT);
        Assertions.assertEquals(expectedText, bodyObject.getData().getContent(), "Quote text is not alignment to %s".formatted(alignmentDirection.getValueInDOM()));
    }

    @Test
    public void SMP_T199_specialCharacterAddedToQuote_when_addSpecialCharacter_and_saveArticle() {
        articleFormPage.addBlocky(WidgetBlock.QUOTE);

        articleFormPage.mainSection().getQuoteBlocks().get(0)
                .textEditor()
                .appendText(TEXT)
                .toolbar()
                .specialCharacter(SpecialCharacterEnum.GRINNING_FACE);

        BodyItem bodyObject = saveArticle().getBody().get(0);
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedText = "<blockquote><p>%s%s</p></blockquote>".formatted(TEXT, SpecialCharacterEnum.GRINNING_FACE.getUnicode());
        validateBodyObjectInContentAPIResponse(QUOTE_STRING, bodyObject, expectedText);
    }
}