package articles.blockytests.football.mostdecoratedplayerstests;

import blockies.football.mostdecoratedblocky.MostDecoratedBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.MostDecoratedPlayerColumnsEnum;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.blockymodels.football.FootballMostDecoratedPlayersBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import factories.blockies.football.FootballMostDecoratedPlayersBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_MOST_DECORATED_PLAYERS)
@Story(CMSStories.STANDINGS_BLOCKY)
public class FootballMostDecoratedPlayersBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final String EXPECTED_TOURNAMENTS_API_URL = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
    private MostDecoratedBlocky blocky;
    private FootballMostDecoratedPlayersBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();
        handleDraftDetectedStartFromScratch();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_MOST_DECORATED_PLAYERS);
        blocky = articleFormPage.mainSection().getMostDecoratedBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getMostDecoratedPlayersBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballMostDecoratedPlayersBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T707_expectedControlsDisplayed_when_addMostDecoratedPlayersBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T707_expectedRequestsCreated_when_addMostDecoratedPlayersBlocky() {
        List<CommonResultModel> tournamentsResponse = blocky.getTournamentsResponse();
        CommonResultModel firstTournamentResponse = blocky.getFirstTournamentResponse();
        String tournamentApiUrl = FootballApiUrl.TOURNAMENTS.getUrl() + tournamentsResponse.get(0).getId();

        ProxyServer.assertRequestMade(EXPECTED_TOURNAMENTS_API_URL);
        ProxyServer.assertRequestMade(tournamentApiUrl);
        Assertions.assertFalse(tournamentsResponse.isEmpty(), AssertMessages.responseNotContains("Tournaments List"));
        Assertions.assertEquals(firstTournamentResponse.getName(), blocky.tournamentSelect().getOptionsValueByIndex(0), AssertMessages.responseNotContains("First Tournament Name"));
    }

    @Test
    public void SMP_T707_expectedDefaultStateLoaded_when_addMostDecoratedPlayersBlocky() {
        var expectedPlaceholderStartsWithOneFourSeven = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();

        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), blocky.playersToHighlightSelect().getPlaceholder(), AssertMessages.entityNotExpected("Default state of the Players to highlight select"));
        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholderStartsWithOneFourSeven), AssertMessages.entityNotExpected("Default state of the Start from position select", expectedPlaceholderStartsWithOneFourSeven, blocky.startFromPositionTextField().getPlaceholder()));
        Assertions.assertEquals("The number of players to show in the most decorated players", blocky.showTextField().getPlaceholder(), AssertMessages.entityNotExpected("Default state of the Show select"));

        var firstTournamentResponse = blocky.getFirstTournamentResponse();

        Assertions.assertEquals(firstTournamentResponse.getName(), blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Default state of the Tournament select"));
        Assertions.assertEquals(firstTournamentResponse.getSeasons().get(0).getName(), blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Default state of the Season select"));
    }

    @Test
    public void SMP_T707_expectedOptionsLoaded_when_addMostDecoratedPlayersBlocky_and_checkTournament() {
        var expectedOption = blocky.getFirstTournamentResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Tournament selected option"));
        blocky.asserts().assertExpectedTournamentOptions();
    }

    @Test
    public void SMP_T707_expectedOptionsLoaded_when_addMostDecoratedPlayersBlocky_and_checkSeason() {
        var expectedOption = blocky.getFirstTournamentResponse().getSeasons().get(0).getName();

        Assertions.assertEquals(expectedOption, blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedSeasonOptions();
    }

    @Test
    public void SMP_T707_expectedOptionsLoaded_when_addMostDecoratedPlayersBlocky_and_checkColumnsToShow() {
        List<String> expectedOptions = Arrays.asList(
                MostDecoratedPlayerColumnsEnum.FIRST_YELLOW_CARD.getDisplayValue(),
                MostDecoratedPlayerColumnsEnum.YELLOW_CARDS.getDisplayValue(),
                MostDecoratedPlayerColumnsEnum.RED_CARDS.getDisplayValue(),
                MostDecoratedPlayerColumnsEnum.TOTAL_CARDS.getDisplayValue());
        List<String> columnsToShow = blocky.columnsToShowSelect().getOptionsValues();

        Assertions.assertEquals(String.join("\n", expectedOptions), blocky.tabsSelect().getText(), AssertMessages.entityNotExpected("Columns to show options"));
        Assertions.assertLinesMatch(List.of(StringConstants.NO_OPTIONS_STRING), columnsToShow, AssertMessages.entityNotExpected("Sort Direction default option"));
    }

    @Test
    public void SMP_T707_expectedControlsDisplayed_when_addMostDecoratedPlayersBlocky_and_checkPlayersToHighlight() {
        blocky.asserts().assertExpectedSeasonCardsOptions();
    }

    @Test
    public void SMP_T707_expectedControlsDisplayed_when_addMostDecoratedPlayersBlocky_and_checkStartFromPosition() {
        var expectedPlaceholderStartsWith = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();
        var expectedFieldType = "number";
        var incorrectValue = "Test";
        var correctValue = "5";

        blockyData.setStartFromPosition(incorrectValue);
        blocky.fillForm(blockyData);

        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholderStartsWith), AssertMessages.entityNotExpected("Start from position field placeholder", expectedPlaceholderStartsWith, blocky.startFromPositionTextField().getPlaceholder()));
        Assertions.assertEquals(expectedFieldType, blocky.startFromPositionTextField().getAttribute("type"), AssertMessages.entityNotExpected("Start from position field type"));

        blockyData.setStartFromPosition(correctValue);
        blocky.fillForm(blockyData);

        Assertions.assertNotEquals(blocky.startFromPositionTextField().getText(), incorrectValue, AssertMessages.entityNotExpected("Field typed text"));
        Assertions.assertEquals(blocky.startFromPositionTextField().getText(), correctValue, AssertMessages.entityNotExpected("Field typed text"));
    }

    @Test
    public void SMP_T707_expectedControlsDisplayed_when_addMostDecoratedPlayersBlocky_and_checkShow() {
        String expectedPlaceholder = PlaceholderField.THE_NUMBER_OF_PLAYERS_TO_SHOW_IN_THE_MOST_DECORATED_PLAYERS.getValue();
        String expectedPlaceholderStartsWithOneFourSeven = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();
        String expectedFieldType = "number";
        String unexpectedString = "Test";
        String expectedNumber = "5";

        Assertions.assertEquals(expectedPlaceholder, blocky.showTextField().getPlaceholder(), AssertMessages.entityNotExpected("Show field placeholder"));
        Assertions.assertEquals(expectedFieldType, blocky.showTextField().getAttribute("type"), AssertMessages.entityNotExpected("Show field type"));
        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholderStartsWithOneFourSeven), AssertMessages.entityNotExpected("Start from position field placeholder", expectedPlaceholderStartsWithOneFourSeven, blocky.startFromPositionTextField().getPlaceholder()));
        blockyData.setShow(unexpectedString);
        blocky.selectShowSelect(blockyData);
        Assertions.assertNotEquals(blocky.showTextField().getText(), unexpectedString, AssertMessages.entityNotExpected("Field typed text"));

        blockyData.setShow(expectedNumber);
        blocky.selectShowSelect(blockyData);
        Assertions.assertEquals(blocky.showTextField().getText(), expectedNumber, AssertMessages.entityNotExpected("Field typed text"));
    }

    @Test
    public void SMP_T707_expectedControlsDisplayed_when_saveMostDecoratedPlayersBlocky() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T707_blockyPreviewDisplayed_when_saveMostDecoratedPlayersBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T707_editModeControlsDisplayed_when_saveMostDecoratedPlayersBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T707_blockRemoved_when_saveMostDecoratedPlayersBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.removeResult();

        blocky.validateBlockyNotExist();
    }
}