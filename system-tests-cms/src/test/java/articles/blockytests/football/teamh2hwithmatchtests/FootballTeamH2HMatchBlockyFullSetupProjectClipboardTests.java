package articles.blockytests.football.teamh2hwithmatchtests;

import blockies.football.teamh2hwithmatchblocky.TeamH2hWithMatchBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.api.queryparamenums.StatusType;
import data.models.blockymodels.football.FootballTeamH2HMatchesBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamH2HMatchesBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_H2H_WITH_MATCHES)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.H2H_BLOCKY)
public class FootballTeamH2HMatchBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private TeamH2hWithMatchBlocky blocky;
    private FootballTeamH2HMatchesBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.map().paragraphList().get(0).addBlock(WidgetBlock.FOOTBALL_TEAM_H2H_MATCH);
        blocky = articleFormPage.mainSection().getTeamH2hWithMatchBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamH2HMatchesBlockySportData(StatusType.FINISHED);
        blockyData = FootballTeamH2HMatchesBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void htmlCodeCopied_when_saveTeamH2hBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void saveTeamH2hBlocky_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void saveTeamH2hBlocky_when_saveArticle_with_translatedTeamName() {
        blockyData.getTeamOne().setName(blockyData.getTeamOne().getName());
        blockyData.getTeamTwo().setName(blockyData.getTeamTwo().getName());
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blockyData.getTeamOne().setName(blockyData.getTeamOne().getName());
        blockyData.getTeamTwo().setName(blockyData.getTeamTwo().getName());
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}