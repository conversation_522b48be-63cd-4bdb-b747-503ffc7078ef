package articles.blockytests.football.tournamentprogrammetests;

import blockies.football.tournamentprogrammeblocky.TournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TOURNAMENT_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class FootballTournamentProgrammeBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private TournamentProgrammeBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOURNAMENT_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Football tournament programme blocky and select upcoming game and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addFootballTournamentProgrammeBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}