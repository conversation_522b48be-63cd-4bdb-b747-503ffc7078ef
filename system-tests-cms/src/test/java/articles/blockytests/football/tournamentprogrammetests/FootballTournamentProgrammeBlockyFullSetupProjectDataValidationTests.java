package articles.blockytests.football.tournamentprogrammetests;

import blockies.football.tournamentprogrammeblocky.TournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.WidgetBlock;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.blockymodels.football.FootballTournamentProgrammeBlockyFieldsModel;
import factories.blockies.football.FootballTournamentProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TOURNAMENT_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class FootballTournamentProgrammeBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final String EXPECTED_TOURNAMENTS_API_URL = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
    private TournamentProgrammeBlocky blocky;
    private FootballTournamentProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOURNAMENT_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTournamentProgrammeBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T693_expectedControlsDisplayed_when_addTournamentProgrammeBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T693_expectedRequestsCreated_when_addTournamentProgrammeBlocky() {
        var tournamentGetResponses = blocky.getTournamentsResponse();
        var firstTournamentResponse = blocky.getFirstTournamentResponse();
        var seasonResponse = blocky.getSeasonsResponse();
        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + tournamentGetResponses.get(0).getId();
        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + firstTournamentResponse.getSeasons().get(0).getId();

        ProxyServer.assertRequestMade(EXPECTED_TOURNAMENTS_API_URL);
        ProxyServer.assertRequestMade(tournamentApiUrl);
        ProxyServer.assertRequestMade(seasonApiUrl);
        Assertions.assertFalse(tournamentGetResponses.isEmpty(), AssertMessages.responseNotContains("Tournaments List"));
        Assertions.assertEquals(tournamentGetResponses.get(0).getName(), firstTournamentResponse.getName(), AssertMessages.responseNotContains("First Tournament Name"));
        Assertions.assertEquals(firstTournamentResponse.getSeasons().get(0).getName(), seasonResponse.getName(), AssertMessages.responseNotContains("First Season of the Tournament"));
    }

    @Test
    public void SMP_T693_expectedDefaultStateLoaded_when_addTournamentProgrammeBlocky() {
        blocky.asserts().assertFieldsDefaultState();
    }

    @Test
    public void SMP_T693_expectedOptionsLoaded_when_addTournamentProgrammeBlocky_and_checkTournament() {
        var expectedOption = blocky.getFirstTournamentResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Tournament selected option"));
        blocky.asserts().assertExpectedTournamentOptions();
    }

    @Test
    public void SMP_T693_expectedOptionsLoaded_when_addTournamentProgrammeBlocky_and_checkSeason() {
        var expectedOption = blocky.getSeasonsResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedSeasonOptions();
    }

    @Test
    public void SMP_T693_expectedOptionsLoaded_when_addTournamentProgrammeBlocky_and_checkStage() {
        blocky.asserts().assertExpectedStageOptions();
    }

    @Test
    public void SMP_T693_expectedOptionsLoaded_when_addTournamentProgrammeBlocky_and_checkRound() {
        blocky.selectTournament(blockyData);
        blocky.selectStage(blockyData);
        blocky.asserts().assertExpectedRoundOptions(blockyData);
    }

    @Test
    public void SMP_T693_expectedOptionsLoaded_when_addTournamentProgrammeBlocky_and_checkSortDirection() {
        var expectedOptions = Arrays.asList("Ascending", "Descending");
        var sortOptionsResults = blocky.sortDirectionFixturesSelect().getOptionsValues();
        var sortOptionsFixtures = blocky.sortDirectionFixturesSelect().getOptionsValues();

        Assertions.assertEquals("Select order direction", blocky.sortDirectionResultsSelect().getText(), AssertMessages.entityNotExpected("Sort Direction default option"));
        Assertions.assertLinesMatch(expectedOptions, sortOptionsResults, AssertMessages.entityNotExpected("Sort Direction options"));

        Assertions.assertEquals("Select order direction", blocky.sortDirectionFixturesSelect().getText(), AssertMessages.entityNotExpected("Sort Direction default option"));
        Assertions.assertLinesMatch(expectedOptions, sortOptionsFixtures, AssertMessages.entityNotExpected("Sort Direction options"));
    }

    @Test
    public void SMP_T693_expectedControlsDisplayed_when_saveTournamentProgrammeBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T693_blockyPreviewDisplayed_when_saveTournamentProgrammeBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertFootballApiGetRequest();
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T693_editModeControlsDisplayed_when_saveTournamentProgrammeBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T693_blockRemoved_when_saveTournamentProgrammeBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }
}