package articles.blockytests.football.livescoretests;

import blockies.football.livescoreblocky.FootballLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_LIVESCORE)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class FootballLivescoreBlockyFullSetupApiProjectDataValidationTests extends CmsWebTest {

    private FootballLivescoreBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LIVESCORE_WIDGET);
        blocky = articleFormPage.mainSection().getFootballLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is not checked when add Football livescore blocky and feature 'auto_check_display_odds' is set to false")
    public void displayOddCheckboxNotChecked_when_addFootballLivescoreBlocky() {
        blocky.displayOddsCheckbox().validateIsUnchecked();
    }
}