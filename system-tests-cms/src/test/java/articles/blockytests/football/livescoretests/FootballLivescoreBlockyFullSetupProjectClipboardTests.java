package articles.blockytests.football.livescoretests;

import blockies.football.livescoreblocky.FootballLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballLivescoreBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_LIVESCORE)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class FootballLivescoreBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private FootballLivescoreBlocky blocky;
    private LivescoreBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LIVESCORE_WIDGET);
        blocky = articleFormPage.mainSection().getFootballLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = FootballLivescoreBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T633_htmlCodeCopied_when_saveLivescoreBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T633_livescoreBlockySaved_when_saveArticle() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}