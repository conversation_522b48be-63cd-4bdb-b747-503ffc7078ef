package articles.blockytests.football.livescoretests;

import blockies.football.livescoreblocky.FootballLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.WidgetBlock;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import factories.blockies.football.FootballLivescoreBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Heading;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_LIVESCORE)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class FootballLivescoreBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private FootballLivescoreBlocky blocky;
    private LivescoreBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LIVESCORE_WIDGET);
        blocky = articleFormPage.mainSection().getFootballLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = FootballLivescoreBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T633_expectedControlsDisplayed_when_addLivescoreBlocky() {
        blocky.validateEditSectionIsVisible();
    }

    @Test
    public void SMP_T633_requiredMessageDisplayed_when_addLivescoreBlocky_and_saveWithEmptyDateTime() {
        var expectedValidationMessage = PlaceholderField.NO_DATE_SELECTED.getValue();
        blocky.saveBlock();
        var actualMessages = blocky.validationMessagesList().stream().map(Heading::getText).toList();

        Assertions.assertTrue(actualMessages.contains(expectedValidationMessage),
                AssertMessages.entityNotExpected("Date validation message", expectedValidationMessage, String.join("; ", actualMessages)));
    }

    @Test
    public void SMP_T633_oddsDataSectionDisplayed_when_addLivescoreBlocky_checkDisplayOddsCheckbox() {
        blocky.displayOddsCheckbox().check();

        blocky.asserts().assertBookmakersList();
        Assertions.assertTrue(blocky.displayOddsCheckbox().isChecked(), AssertMessages.entityNotExpected("state of Display Odds"));
        Assertions.assertTrue(blocky.bookmakerSelect().isVisible(), AssertMessages.entityNotVisible("Bookmaker select"));
    }

    @Test
    public void SMP_T633_blockySaved_when_fillRequiredFields_and_clickSaveButton() {
        blockyData = FootballLivescoreBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.validatePreviewControlsAreVisible(blockyData);
    }

    @Test
    public void SMP_T633_expectedControlsDisplayed_when_saveLivescoreBlocky() {
        blockyData = FootballLivescoreBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.validatePreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T633_previewDisplayed_when_saveLivescoreBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.waitPreviewScreenToLoad();

        blocky.previewResult();

        blocky.asserts().assertFootballApiGetRequest();
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T633_editModeControlsDisplayed_when_saveLivescoreBlocky_and_clickEditButton() {
        var date = LocalDate.now();
        blocky.dateTimePicker().setDate(date);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.validateEditSectionIsVisible();
    }

    @Test
    public void SMP_T633_blockyRemoved_when_saveLivescoreBlocky_and_clickRemoveButton() {
        var date = LocalDate.now();
        blocky.dateTimePicker().setDate(date);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.removeResult();

        blocky.validateBlockyNotExist();
    }
}