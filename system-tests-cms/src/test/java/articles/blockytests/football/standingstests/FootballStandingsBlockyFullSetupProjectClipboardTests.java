package articles.blockytests.football.standingstests;

import blockies.football.standingsblocky.StandingsBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.blockymodels.football.FootballStandingsBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballStandingsBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_STANDINGS)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.STANDINGS_BLOCKY)
public class FootballStandingsBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private StandingsBlocky blocky;
    private FootballStandingsBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_STANDINGS_WIDGET);
        blocky = articleFormPage.mainSection().getStandingsBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getStandingsBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballStandingsBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T635_htmlCodeCopied_when_saveStandingsBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T635_standingsBlockySaved_when_saveArticle() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void saveFootballStandingsBlockyWithAllFieldsFilled_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}