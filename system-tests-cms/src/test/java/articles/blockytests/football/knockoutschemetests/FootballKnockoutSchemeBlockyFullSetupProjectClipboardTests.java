package articles.blockytests.football.knockoutschemetests;

import blockies.football.knockoutschemeblocky.KnockoutSchemeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.blockymodels.football.FootballKnockoutBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballKnockoutBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_KNOCKOUT_SCHEME)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.KNOCKOUT_BLOCKY)
public class FootballKnockoutSchemeBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private KnockoutSchemeBlocky blocky;
    private FootballKnockoutBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_KNOCKOUT_SCHEME_WIDGET);
        blocky = articleFormPage.mainSection().getKnockoutSchemeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getKnockoutBlockySportData(FootballTournamentEnum.LA_LIGA);
        blockyData = FootballKnockoutBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T630_htmlCodeCopied_when_saveKnockoutSchemeBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T630_knockoutSchemeBlockySaved_when_saveArticle() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}