package articles.blockytests.football.singleroundtests;

import blockies.football.singleroundblocky.FootballSingleRoundBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_SINGLE_ROUND)
@Story(CMSStories.SINGLE_ROUND_BLOCKY)
public class FootballSingleRoundBlockyFullSetupApiProjectDataValidationTests extends CmsWebTest {

    private FootballSingleRoundBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.openCreatePage();
        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_SINGLE_ROUND);

        blocky = articleFormPage.mainSection().getFootballSingleRoundBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is not checked when add Football single round blocky and feature 'auto_check_display_odds' is set to false")
    public void displayOddCheckboxNotChecked_when_addFootballSingleRoundBlocky() {
        blocky.displayOddsCheckbox().validateIsUnchecked();
    }
}