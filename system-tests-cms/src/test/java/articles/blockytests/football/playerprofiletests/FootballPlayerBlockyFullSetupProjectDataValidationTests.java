package articles.blockytests.football.playerprofiletests;

import blockies.football.playerprofileblocky.PlayerProfileBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.*;
import data.constants.enums.football.FootballPlayerProfileStatisticsEnum;
import data.models.blockymodels.football.FootballPlayerProfileBlockyFieldsModel;
import data.widgets.options.enums.FootballPlayerEnum;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballPlayerProfileBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;
import java.util.stream.Collectors;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_PLAYER_PROFILE)
@Story(CMSStories.PROFILE_BLOCKY)
public class FootballPlayerBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final FootballPlayerEnum PLAYER = FootballPlayerEnum.ERLING_HOLLAND;
    private PlayerProfileBlocky blocky;
    private FootballPlayerProfileBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_PLAYER_WIDGET);
        blocky = articleFormPage.mainSection().getPlayerProfileBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getPlayerProfileBlockySportData(PLAYER);
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T634_expectedControlsDisplayed_when_addPlayerProfileBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void expectedControlsDisplayedAllBLockElement_when_addPlayerProfileBlocky() {
        blocky.selectPlayer(blockyData);
        blocky.selectSeason(blockyData);

        blocky.asserts().assertAllBlockElements();
    }

    @Test
    public void SMP_T634_expectedDefaultStateLoaded_when_addPlayerProfileBlocky() {
        Assertions.assertEquals("Search", blocky.playerSelect().getText(), AssertMessages.entityNotExpected("Default state of the Player select"));
        Assertions.assertEquals(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING, blocky.validationMessagesList().get(0).getText(), AssertMessages.entityNotExpected("Empty Player Select validation message"));
    }

    @Test
    public void SMP_T634_expectedOptionsLoaded_when_addPlayerProfileBlocky_and_checkTournamentSeasons() {
        blocky.selectPlayer(blockyData);
        blocky.selectSeason(blockyData);

        blocky.asserts().assertSelectedSeasonOption(PLAYER.getId());
        blocky.asserts().assertExpectedSeasonOptions(PLAYER.getNameEn());
    }

    @Test
    public void SMP_T634_expectedOptionsLoaded_when_addPlayerProfileBlocky_and_checkMatch() {
        blocky.selectPlayer(blockyData);
        blocky.selectSeason(blockyData);
        blocky.selectMatch(blockyData);

        blocky.asserts().assertSelectedMatchOption(blockyData);
    }

    @Test
    public void SMP_T634_expectedOptionsLoaded_when_addPlayerProfileBlocky_and_checkDefaultTab() {
        blocky.selectPlayer(blockyData);

        var expectedDefaultOptions = "Matches played\nGoals\nShots\nAssists\nMinutes\nYellow cards";
        var actualOptions = blocky.tabsSelect().getOptionsValues();

        Assertions.assertEquals(expectedDefaultOptions, blocky.tabsSelect().getText(), AssertMessages.entityNotExpected("Tabs Select default option"));
        Assertions.assertLinesMatch(Arrays.stream(FootballPlayerProfileStatisticsEnum.values())
                .filter(value -> value != FootballPlayerProfileStatisticsEnum.EMPTY)
                .map(FootballPlayerProfileStatisticsEnum::getDisplayValue)
                .collect(Collectors.toList()), actualOptions, AssertMessages.entityNotExpected("Tabs Select options"));
    }

    @Test
    // @Issue("SFE-5734")
    public void SMP_T634_expectedControlsDisplayed_when_fillMandatoryFields_and_savePlayerProfileBlocky() {
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void expectedControlsDisplayed_when_fillAllFields_and_savePlayerProfileBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    //@Issue("SFE-4374")
    //@Issue("SFE-5850")
    public void SMP_T634_blockyPreviewDisplayed_when_savePlayerProfileBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertFootballApiGetRequests(blockyData);
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void playerProfileBlockyHided_when_blockyNotSaved_and_clickCancelButton() {
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
    }

    @Test
    public void playerProfileBlockyHidden_when_blockySaved_and_clickCancelButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult()
                .cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void editModeControlsDisplayed_when_savePlayerProfileBlocky_and_clickEditButton() {
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void playerProfileBlockyRemoved_when_savePlayerProfileBlocky_and_clickRemoveButton() {
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData)
                .saveBlock()
                .getBrowserService().waitForAjax();
        blocky.removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Football player profile blocky and select not started game and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addFootballPlayerProfileBlocky_and_selectNotStartedMatch() {
        blocky.selectPlayer(blockyData);
        blocky.selectMatch(blockyData);

        blocky.displayOddsCheckbox().validateIsChecked();
    }

    @Test
    //@Issue("SFE-4374")
    //@Issue("SFE-5850")
    public void SMP_T634_playerProfileBlocky_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.fillForm(blockyData)
                .saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    //@Issue("SFE-5850")
    public void SMP_T634_playerProfileBlocky_when_saveArticle_with_translatedTeamName() {
        blockyData.getPlayer().setName(PLAYER.getNameBg());
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        blockyData.getPlayer().setName(PLAYER.getNameEn());
        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void errorMessageDisplayed_when_tryToSavePlayerProfileBlocky_and_leaveEmptyPlayerNameField() {
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildAllFields(blockyData);
        blockyData.getPlayer().setName(StringConstants.EMPTY_STRING);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().errorMessageDisplayedForFootballPlayerName(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING);
    }

    @Test
    public void errorMessageDisplayed_when_tryToSavePlayerProfileBlocky_and_leaveEmptyFieldsForm() {
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blockyData.getPlayer().setName(StringConstants.EMPTY_STRING);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().errorMessageDisplayedForFootballPlayerName(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING);
    }
}