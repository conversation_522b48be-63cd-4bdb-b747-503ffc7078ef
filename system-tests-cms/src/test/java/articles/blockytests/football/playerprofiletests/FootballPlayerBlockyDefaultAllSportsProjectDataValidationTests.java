package articles.blockytests.football.playerprofiletests;

import blockies.football.playerprofileblocky.PlayerProfileBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.football.FootballPlayerProfileBlockyFieldsModel;
import data.widgets.options.enums.FootballPlayerEnum;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballPlayerProfileBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_PLAYER_PROFILE)
@Story(CMSStories.PROFILE_BLOCKY)
public class FootballPlayerBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private static final FootballPlayerEnum PLAYER = FootballPlayerEnum.ERLING_HOLLAND;
    private PlayerProfileBlocky blocky;
    private FootballPlayerProfileBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_PLAYER_WIDGET);
        blocky = articleFormPage.mainSection().getPlayerProfileBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getPlayerProfileBlockySportData(PLAYER);
        blockyData = FootballPlayerProfileBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Football player blocky and select upcoming game and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addFootballPlayerProfileBlocky_and_selectNotStartedMatch() {
        blocky.selectPlayer(blockyData);
        blocky.selectMatch(blockyData);
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}