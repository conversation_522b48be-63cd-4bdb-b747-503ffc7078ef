package articles.blockytests.football.teamformtests;

import blockies.football.teamformblocky.TeamFormBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballFormBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamFormBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_FORM)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.STANDINGS_BLOCKY)
@Disabled("Missing v2 widget and clients do not use Team Form v2 blocky yet.")
// TODO: ivan.petkov 06.03.2024 - Remove @Disabled annotation once widget implemented
public class FootballTeamFormBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private TeamFormBlocky blocky;
    private FootballFormBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.TEAM_FORM_WIDGET);
        blocky = articleFormPage.mainSection().getTeamFormBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamFormBlockySportData(FootballTeamEnum.LIVERPOOL);
        blockyData = FootballTeamFormBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T705_htmlCodeCopied_when_saveTeamFormBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T705_TeamFormBlockySaved_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}