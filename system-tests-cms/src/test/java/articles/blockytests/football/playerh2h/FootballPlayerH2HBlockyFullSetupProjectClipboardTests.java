package articles.blockytests.football.playerh2h;

import blockies.football.playerh2hblocky.PlayerH2hBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.football.FootballPlayerH2HBlockyFieldsModel;
import data.widgets.options.enums.FootballPlayerEnum;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballPlayerH2HBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_PLAYER_H2H)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.H2H_BLOCKY)
public class FootballPlayerH2HBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private static final FootballPlayerEnum FIRST_PLAYER = FootballPlayerEnum.KARIM_BENZEMA;
    private static final FootballPlayerEnum SECOND_PLAYER = FootballPlayerEnum.ERLING_HOLLAND;
    private PlayerH2hBlocky blocky;
    private FootballPlayerH2HBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.PLAYER_H2H);
        blocky = articleFormPage.mainSection().getPlayerH2hBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getPlayerH2HBlockySportData(FIRST_PLAYER, SECOND_PLAYER);
        blockyData = FootballPlayerH2HBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T703_htmlCodeCopied_when_savePlayerH2hBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T703_savePlayerH2hBlocky_when_saveArticle() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void SMP_T703_savePlayerH2hBlocky_when_saveArticle_with_translatedTeamName() {
        blockyData.getPlayerOne().setName(FIRST_PLAYER.getNameBg());
        blockyData.getPlayerTwo().setName(SECOND_PLAYER.getNameBg());

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        blockyData.getPlayerOne().setName(FIRST_PLAYER.getNameEn());
        blockyData.getPlayerTwo().setName(SECOND_PLAYER.getNameEn());

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}