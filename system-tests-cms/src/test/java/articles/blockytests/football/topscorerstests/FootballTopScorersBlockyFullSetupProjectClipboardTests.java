package articles.blockytests.football.topscorerstests;

import blockies.football.topscorersblocky.TopScorersBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.football.FootballTopScorersBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTopScorersBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TOP_SCORERS)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.STANDINGS_BLOCKY)
public class FootballTopScorersBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private TopScorersBlocky blocky;
    private FootballTopScorersBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOP_SCORERS);
        blocky = articleFormPage.mainSection().getTopScorersBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTopScorersBlockySportData();
        blockyData = FootballTopScorersBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T706_htmlCodeCopied_when_saveTopScorersBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T706_topScorersBlockySaved_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}