package articles.blockytests.football.topscorerstests;

import blockies.football.topscorersblocky.TopScorersBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.WidgetBlock;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.blockymodels.football.FootballTopScorersBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.topscorer.TopScorerModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTopScorersBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TOP_SCORERS)
@Story(CMSStories.STANDINGS_BLOCKY)
public class FootballTopScorersBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final String EXPECTED_TOURNAMENTS_API_URL = FootballApiUrl.TOURNAMENTS.getUrl().replace("tournaments/", "tournaments") + "?client_order=sportalios";
    private TopScorersBlocky blocky;
    private FootballTopScorersBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TOP_SCORERS);
        blocky = articleFormPage.mainSection().getTopScorersBlocks().get(0);
        blocky.waitEditScreenToLoad();
        app().browser().waitUntil(e -> !blocky.seasonSelect().getText().equals(PlaceholderField.ONE_FOUR_SEVEN_ETC.getValue()));

        blockyData = footballApiFacade.getTopScorersBlockySportData();
        blockyData = FootballTopScorersBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T706_expectedControlsDisplayed_when_addTopScorersBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T706_expectedRequestsCreated_when_addTopScorersBlocky() {
        List<CommonResultModel> tournamentsResponse = blocky.getTournamentsResponse();
        CommonResultModel firstTournamentResponse = blocky.getFirstTournamentResponse();
        String tournamentApiUrl = FootballApiUrl.TOURNAMENTS.getUrl() + tournamentsResponse.get(0).getId();
        String topScorerUrl = FootballApiUrl.TOURNAMENTS_SEASON.getUrl() + firstTournamentResponse.getSeasons().get(0).getId() + "/topscorer";

        ProxyServer.assertRequestMade(EXPECTED_TOURNAMENTS_API_URL);
        ProxyServer.assertRequestMade(tournamentApiUrl);
        ProxyServer.assertRequestMade(topScorerUrl);
        Assertions.assertFalse(tournamentsResponse.isEmpty(), AssertMessages.responseNotContains("Tournaments List"));
        Assertions.assertEquals(tournamentsResponse.get(0).getName(), firstTournamentResponse.getName(), AssertMessages.responseNotContains("First Tournament Name"));
    }

    @Test
    public void SMP_T706_expectedDefaultStateLoaded_when_addTopScorersBlocky() {
        String expectedPlaceholderStartsWith = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();
        blocky.selectTournament(blockyData);
        blocky.selectSeason(blockyData);
        blocky.filterStatisticsByTeamCheckBox().validateIsUnchecked();

        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), blocky.playersToHighlightSelect().getPlaceholder(), AssertMessages.entityNotExpected("Default state of the Players to highlight select"));
        blocky.statisticsParametersSelect().clearSelection();
        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), blocky.statisticsParametersSelect().getPlaceholder(), AssertMessages.entityNotExpected("Default state of the Statistics parameters select"));
        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholderStartsWith), AssertMessages.entityNotExpected("Default state of the Start from position select", expectedPlaceholderStartsWith, blocky.startFromPositionTextField().getPlaceholder()));
        Assertions.assertEquals(PlaceholderField.THE_NUMBER_OF_PLAYERS_TO_SHOW_IN_THE_MOST_DECORATED_PLAYERS.getValue(), blocky.showTextField().getPlaceholder(), AssertMessages.entityNotExpected("Default state of the Show select"));

        //TODO: hhristov 08/12/2025 - I commented out this line and added lines 81 and 82 because the widget's default state always selects the Bulgarian League and the current season (e.g., 2025/2026), which currently has no top scorers or statistics available.
        // This should be commented back in once the league starts or if the widget's default state changes.
        //CommonResultModel firstTournamentResponse = blocky.getFirstTournamentResponse();
        Assertions.assertEquals(blockyData.getTournament().getName(), blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Default state of the Tournament select"));
        Assertions.assertEquals(blockyData.getTournament().getSeasons().get(0).getName(), blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Default state of the Season select"));
    }

    @Test
    public void SMP_T706_expectedOptionsLoaded_when_addTopScorersBlocky_and_checkTournament() {
        String expectedOption = blocky.getFirstTournamentResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Tournament selected option"));
        blocky.asserts().assertExpectedTournamentOptions();
    }

    @Test
    public void SMP_T706_expectedOptionsLoaded_when_addTopScorersBlocky_and_checkSeason() {
        String expectedOption = blocky.getFirstTournamentResponse().getSeasons().get(0).getName();

        Assertions.assertEquals(expectedOption, blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedSeasonOptions();
    }

    @Test
    public void SMP_T706_expectedOptionsLoaded_when_addTopScorersBlocky_and_checkTeams() {
        blocky.filterStatisticsByTeamCheckBox().check();
        blocky.asserts().assertExpectedSeasonsTeamsOptions();
    }

    @Test
    public void SMP_T706_expectedPlayerOptionsLoaded_when_addTopScorersBlocky_and_selectTeams() {
        List<TopScorerModel> topScorerResponse = blocky.selectFirstSeasonWithTopScorers();
        blocky.filterStatisticsByTeamCheckBox().check();
        blocky.teamsSelect().selectOptionByIndex(0);

        blocky.asserts().assertExpectedTopScorersOptionsAfterTeamSelect(topScorerResponse);
    }

    @Test
    public void SMP_T706_expectedControlsDisplayed_when_addTopScorersBlocky_and_checkPlayersToHighlight() {
        blocky.asserts().assertExpectedTopScorersOptions();
    }

    @Test
    public void SMP_T706_expectedControlsDisplayed_when_addTopScorersBlocky_and_checkStartFromPosition() {
        String expectedPlaceholder = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();
        String expectedFieldType = "number";
        String unexpectedString = "Test";
        int expectedNumber = 5;
        // TODO hhristov 08/12/2025 - I added lines 141, 142 and 143 because the widget's default state always selects the Bulgarian League and the current season (e.g., 2025/2026), which currently has no top scorers or statistics available.
        // This should be commented back in once the league starts or if the widget's default state changes.
        blocky.selectTournament(blockyData);
        blocky.selectSeason(blockyData);
        blocky.selectPlayerHighlight(blockyData);

        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholder), AssertMessages.entityNotExpected("Start from position field placeholder"));
        Assertions.assertEquals(expectedFieldType, blocky.startFromPositionTextField().getAttribute("type"), AssertMessages.entityNotExpected("Start from position field type"));
        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholder), AssertMessages.entityNotExpected("Default state of the Start from position select", expectedPlaceholder, blocky.startFromPositionTextField().getPlaceholder()));

        blocky.startFromPositionTextField().setText(unexpectedString);
        Assertions.assertNotEquals(blocky.startFromPositionTextField().getText(), unexpectedString, AssertMessages.entityNotExpected("Field typed text"));

        blocky.startFromPositionTextField().setText(String.valueOf(expectedNumber));
        Assertions.assertEquals(blocky.startFromPositionTextField().getText(), String.valueOf(expectedNumber), AssertMessages.entityNotExpected("Field typed text"));
    }

    @Test
    public void SMP_T706_expectedControlsDisplayed_when_addTopScorersBlocky_and_checkShow() {
        String expectedPlaceholderStartsWith = PlaceholderField.FROM_ONE_TO_NUMBER.getValue();
        String expectedPlaceholder = "The number of players to show in the most decorated players";
        String expectedFieldType = "number";
        String unexpectedString = "Test";
        int expectedNumber = 5;

        // TODO hhristov 08/12/2025 - I added lines 166, 167 and 168 because the widget's default state always selects the Bulgarian League and the current season (e.g., 2025/2026), which currently has no top scorers or statistics available.
        // This should be commented back in once the league starts or if the widget's default state changes.
        blocky.selectTournament(blockyData);
        blocky.selectSeason(blockyData);
        blocky.selectPlayerHighlight(blockyData);

        Assertions.assertEquals(expectedPlaceholder, blocky.showTextField().getPlaceholder(), AssertMessages.entityNotExpected("Show field placeholder"));
        Assertions.assertTrue(blocky.startFromPositionTextField().getPlaceholder().contains(expectedPlaceholderStartsWith), AssertMessages.entityNotExpected("Default state of the Start from position select", expectedPlaceholderStartsWith, blocky.startFromPositionTextField().getPlaceholder()));
        Assertions.assertEquals(expectedFieldType, blocky.showTextField().getAttribute("type"), AssertMessages.entityNotExpected("Show field type"));

        blocky.showTextField().setText(unexpectedString);
        Assertions.assertNotEquals(blocky.showTextField().getText(), unexpectedString, AssertMessages.entityNotExpected("Field typed text"));

        blocky.showTextField().setText(String.valueOf(expectedNumber));
        Assertions.assertEquals(blocky.showTextField().getText(), String.valueOf(expectedNumber), AssertMessages.entityNotExpected("Field typed text"));
    }

    @Test
    public void SMP_T706_expectedControlsDisplayed_when_saveTopScorersBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T706_blockyPreviewDisplayed_when_saveTopScorersBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T706_editModeControlsDisplayed_when_saveTopScorersBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T706_blockRemoved_when_saveTopScorersBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    //Issue("SFE-5879")
    public void allStatisticsPreSelected_when_addTopScorersBlocky_and_selectTeam() {
        blocky.asserts().assertAllStatisticsPreSelected(blocky);
    }
}