package articles.blockytests.football.lineupstests;

import blockies.football.lineupsblocky.LineupsBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.EventStatusType;
import data.constants.Language;
import data.constants.WidgetBlock;
import data.models.blockymodels.football.FootballLineupsBlockyFieldsModel;
import data.models.searchapi.Translation;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballLineupsBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(browser = Browser.CHROME, lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_LINEUPS)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.LINEUPS_BLOCKY)
public class FootballLineupsBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private LineupsBlocky blocky;
    private FootballLineupsBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        handleDraftDetectedStartFromScratch();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LINEUPS);
        blocky = articleFormPage.mainSection().getLineupsBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getLineupsBlockySportData(EventStatusType.FINISHED);
        blockyData = FootballLineupsBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void lineupsBlockySaved_when_fillRequiredFields_and_saveArticle() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void lineupsBlockySaved_when_fillAllFields_and_saveArticle() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void lineupsBlockySaved_when_fillAllFields_and_saveArticle_with_translatedTeamName() {
        String bgTeamName = blockyData.getTeam().getTranslations()
                .stream()
                .filter(translation -> Language.BULGARIAN.getCode().equals(translation.getLanguage()))
                .map(Translation::getName)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Football team does not have bulgarian translation"));

        blockyData.getTeam().setName(bgTeamName);
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        blockyData.getTeam().setName(bgTeamName);
        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void htmlCodeCopied_when_lineupsBlocky_and_clickCopyEmbedCodeButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void htmlCodeCopied_when_lineupsBlocky_and_clickAllToggleButtons_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }
}