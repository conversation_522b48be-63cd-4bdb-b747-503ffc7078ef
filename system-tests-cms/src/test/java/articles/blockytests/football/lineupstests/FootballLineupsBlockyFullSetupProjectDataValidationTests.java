package articles.blockytests.football.lineupstests;

import blockies.football.lineupsblocky.LineupsBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.*;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.football.FootballTeamEnum;
import data.customelements.MatchResult;
import data.models.blockymodels.football.FootballLineupsBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballLineupsBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;
import static data.constants.PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_LINEUPS)
@Story(CMSStories.LINEUPS_BLOCKY)
public class FootballLineupsBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private LineupsBlocky blocky;
    private FootballLineupsBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        handleDraftDetectedStartFromScratch();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_LINEUPS);
        blocky = articleFormPage.mainSection().getLineupsBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getLineupsBlockySportData(EventStatusType.FINISHED);
        blockyData = FootballLineupsBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void expectedControlsDisplayed_when_addLineupsBlocky() {
        blocky.asserts().assertDefaultSectionIsVisible();
    }

    @Test
    public void requiredMessageDisplayed_when_addLineupsBlocky_and_saveWithEmptyTeam() {
        String expectedTeamFieldValidationMessage = StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING;
        String expectedMatchResultsValidationMessage = StringConstants.NO_MATCHES_ARE_CURRENTLY_SELECTED;
        blocky.saveBlockWithoutWait();

        blocky.asserts().assertValidationMessageDisplayed(expectedTeamFieldValidationMessage);
        blocky.asserts().assertValidationMessageDisplayed(expectedMatchResultsValidationMessage);
    }

    @Test
    public void teamFieldCanHoldMultipleEntities_when_addLineupsBlocky_and_addMoreThanOneTeam() {
        String firstTeam = blockyData.getTeam().getName();
        String secondTeam = FootballTeamEnum.MANCHESTER_UNITED.getName();
        blocky.filterMultipleEntities(blockyData.getTeam().getName(), secondTeam);

        Assertions.assertTrue(blocky.getSelectedEntities().contains(firstTeam),
                AssertMessages.entityNotExpected("Selected First Team", firstTeam, blocky.getSelectedEntities()));
        Assertions.assertTrue(blocky.getSelectedEntities().contains(secondTeam),
                AssertMessages.entityNotExpected("Selected Second Team", secondTeam, blocky.getSelectedEntities()));
    }

    @Test
    public void upcomingMatchesDisplayed_when_addLineupsBlocky_and_selectTeam() {
        blockyData = footballApiFacade.getLineupsBlockySportData(EventStatusType.NOT_STARTED);
        blocky.selectTeam(blockyData.getTeam().getName());

        ProxyServer.assertRequestMade(FootballApiUrl.TEAMS_BY_NAME.getUrl() + blockyData.getTeam().getName().replace(" ", "%20"));
        List<MatchResult> matches = blocky.eventsList().getUpcomingMatches();

        Assertions.assertFalse(matches.isEmpty(), AssertMessages.entityNotVisible("Upcoming matches"));
    }

    @Test
    public void pastMatchesDisplayed_when_addLineupsBlocky_and_selectTeam() {
        blocky.selectTeam(blockyData.getTeam().getName());
        List<MatchResult> matches = blocky.eventsList().getPastMatches();

        Assertions.assertFalse(matches.isEmpty(), AssertMessages.entityNotVisible("Upcoming matches"));
    }

    @Test
    public void showToggleButtonsDisplayed_when_addLineupsBlocky_and_checkToggleButtons() {
        blocky.showCoachToggleButton().click();
        blocky.showFirstTeamToggleButton().click();
        blocky.showSubstitutesToggleButton().click();
        blocky.toggleFirstTeamOpenToggleButton().click();
        blocky.toggleSubstitutesOpenToggleButton().click();

        Assertions.assertTrue(blocky.showCoachToggleButton().isToggleOn(), "Show Coaches toggle is not on");
        Assertions.assertTrue(blocky.showFirstTeamToggleButton().isToggleOn(), "Show First Team toggle is not on");
        Assertions.assertTrue(blocky.showSubstitutesToggleButton().isToggleOn(), "Show Substitutes toggle is not on");
        Assertions.assertTrue(blocky.toggleFirstTeamOpenToggleButton().isToggleOn(), "Toggle First Team Open toggle is not on");
        Assertions.assertTrue(blocky.toggleSubstitutesOpenToggleButton().isToggleOn(), "Toggle Substitutes Open toggle is not on");
    }

    @Test
    public void expectedOptionsDisplayed_when_addLineupsBlocky_and_checkRefreshTimeSelect() {
        String expectedPlaceholder = LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue();

        Assertions.assertEquals(expectedPlaceholder, blocky.refreshTimeSelect().getText(), AssertMessages.entityNotExpected("Refresh Time Placeholder"));
        Assertions.assertLinesMatch(RefreshTime.getEnumValues(), blocky.refreshTimeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Refresh Time Options"));
    }

    @Test
    public void lineupsBlockySaved_when_fillRequiredFields_and_clickSaveButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void lineupsBlockyHidden_when_blockyNotSaved_and_clickCancelButton() {
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
    }

    @Test
    public void lineupsBlockyHided_when_blockySaved_and_clickCancelButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
        blocky.editResult();

        blocky.cancelEdit();
        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void previewDisplayed_when_saveLineupsBlocky_and_clickPreviewButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.waitPreviewScreenToLoad();

        blocky.previewResult();

        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), FootballApiUrlV2.MATCHES.getUrl(), HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(FootballApiUrlV2.MATCHES.getUrl());
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void editModeControlsDisplayed_when_saveLineupsBlocky_and_clickEditButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void editModeControlsWithClickedToggleButtonsDisplayed_when_saveLineupsBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertEditSectionIsVisibleWithClickToggleButtons(blockyData);
    }

    @Test
    public void lineupsBlockyRemoved_when_saveLineupsBlocky_and_clickRemoveButton() {
        blockyData = FootballLineupsBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.removeResult();
        blocky.validateBlockyNotExist();
    }
}