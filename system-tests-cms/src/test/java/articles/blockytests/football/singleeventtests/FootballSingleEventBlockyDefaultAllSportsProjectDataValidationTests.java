package articles.blockytests.football.singleeventtests;

import blockies.football.singleeventblocky.FootballSingleEventBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.EventStatusType;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballSingleEventBlockyFieldsModel;
import factories.blockies.football.FootballSingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_SINGLE_EVENT)
@Tag(CMSStories.SINGLEEVENT_BLOCKY)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class FootballSingleEventBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private FootballSingleEventBlocky blocky;
    private FootballSingleEventBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_SINGLE_EVENT);
        blocky = articleFormPage.mainSection().getFootballSingleEventBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getSingleEventBlockySportData(FootballTeamEnum.LIVERPOOL.getName(), EventStatusType.NOT_STARTED);
        blockyData = FootballSingleEventBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Football single event blocky and select upcoming game and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addFootballSingleEventBlocky_and_selectUpcomingGame() {
        blocky.filterEntity(blockyData.getTeam().getName());
        blocky.selectEvent(blockyData.getEvent());

        blocky.displayOddsCheckbox().validateIsChecked();
    }
}