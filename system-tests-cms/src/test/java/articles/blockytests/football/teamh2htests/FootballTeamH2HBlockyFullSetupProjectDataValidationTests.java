package articles.blockytests.football.teamh2htests;

import blockies.football.teamh2hblocky.TeamH2hBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.football.FootballStatisticsEnum;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballTeamH2HBlockyFieldsModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.teams.TeamModel;
import data.utils.StringUtils;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamH2HBlockyFieldsFactory;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_H2H)
@Story(CMSStories.H2H_BLOCKY)
public class FootballTeamH2HBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private TeamH2hBlocky blocky;
    private FootballTeamH2HBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_H2H);
        blocky = articleFormPage.mainSection().getTeamH2hBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamH2HBlockySportData(FootballTeamEnum.LIVERPOOL, FootballTeamEnum.REAL_MADRID);
        blockyData = FootballTeamH2HBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T704_expectedControlsDisplayed_when_addTeamH2hBlocky() {
        blocky.selectTeamOne(blockyData);
        blocky.selectTeamTwo(blockyData);
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T704_expectedRequestsCreated_when_addTeamH2hBlocky_and_searchTeams() {
        blocky.selectTeamOne(blockyData);
        blocky.selectTeamTwo(blockyData);

        List<TeamModel> teamGetResponses = blocky.getTeamsGetResponse();
        String teamsApiUrl = FootballApiUrl.TEAMS_BY_NAME.getUrl() + blockyData.getTeamTwo().getName().replace(" ", "%20");
        String seasonApiUrl = FootballApiUrl.TEAMS_SEASONS.getUrl().formatted(teamGetResponses.get(0).getId());

        TeamModel secondTeamGetResponses = blocky.getSecondTeamResponse(blockyData.getTeamTwo().getName());
        String secondTeamsApiUrl = FootballApiUrl.TEAMS_BY_NAME.getUrl() + StringUtils.replaceSpacesWithSymbol(blockyData.getTeamTwo().getName(), "%20");
        String secondSeasonApiUrl = FootballApiUrl.TEAMS_SEASONS.getUrl().formatted(secondTeamGetResponses.getId());

        ProxyServer.assertRequestMade(teamsApiUrl);
        ProxyServer.assertRequestMade(seasonApiUrl);
        ProxyServer.assertRequestMade(secondTeamsApiUrl);
        ProxyServer.assertRequestMade(secondSeasonApiUrl);
        Assertions.assertEquals(blockyData.getTeamOne().getName(), teamGetResponses.get(0).getName(), AssertMessages.responseNotContains("First Team Name"));
        Assertions.assertEquals(blockyData.getTeamTwo().getName(), secondTeamGetResponses.getName(), AssertMessages.responseNotContains("Second Team Name"));
    }

    @Test
    public void SMP_T704_expectedDefaultStateLoaded_when_addTeamH2hBlocky() {
        blocky.saveBlock();
        Assertions.assertEquals("", blocky.firstTeamSelect().getText(), AssertMessages.entityNotExpected("Default state of the Team select"));
        Assertions.assertEquals(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING, blocky.validationMessage().get(0).getText(), AssertMessages.entityNotExpected("Validation message of the Team select"));
        Assertions.assertEquals("", blocky.secondTeamSelect().getText(), AssertMessages.entityNotExpected("Default state of the Team select"));
        Assertions.assertEquals(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING, blocky.validationMessage().get(1).getText(), AssertMessages.entityNotExpected("Validation message of the Team select"));
    }

    @Test
    public void SMP_T704_expectedOptionsLoaded_when_addTeamH2hBlocky_and_checkTeam() {
        blocky.selectTeamOne(blockyData);
        List<TeamModel> teamGetResponses = blocky.getTeamsGetResponse();

        Assertions.assertEquals(teamGetResponses.get(0).getName(), blocky.firstTeamSelect().getText(), AssertMessages.entityNotExpected("Team selected option"));
    }

    @Test
    public void SMP_T704_expectedOptionsLoaded_when_addTeamH2hBlocky_and_checkFirstTeamSeasons() {
        blocky.selectTeamOne(blockyData);
        Season expectedSeason = blocky.getSeasonsResponse().get(0);
        String expectedOption = String.format("%s-%s", expectedSeason.getTournament().getName(), expectedSeason.getName());

        Assertions.assertEquals(expectedOption, blocky.firstSeasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedFirstTeamSeasonOptions();
    }

    @Test
    public void SMP_T704_expectedOptionsLoaded_when_addTeamH2hBlocky_and_checkSecondTeamSeasons() {
        blocky.selectTeamTwo(blockyData);

        String expectedOption = "%s-%s".formatted(blockyData.getTeamTwoTournamentSeasons().getTournament().getName(),
                blockyData.getTeamTwoTournamentSeasons().getName());

        Assertions.assertEquals(expectedOption, blocky.secondSeasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedSecondTeamSeasonOptions(blockyData.getTeamTwo().getName());
    }

    @Test
    public void SMP_T704_expectedOptionsLoaded_when_addTeamH2hBlocky_and_checkStatisticsParameters() {
        List<String> expectedSelectedOptions = Arrays.asList("Matches played", "Goals scored", "Wins", "Draws", "Goals conceded", "Defeats", "Points", "Rank");
        List<String> expectedAvailableOptions = List.of(StringConstants.NO_OPTIONS_STRING);

        blocky.selectTeamOne(blockyData);
        blocky.selectTeamOneTournamentSeason(blockyData);
        blocky.selectTeamTwo(blockyData);
        blocky.selectTeamTwoTournamentSeason(blockyData);
        List<String> actualOptions = blocky.statisticsParametersSelect().getOptionsValues();

        Assertions.assertEquals(String.join("\n", expectedSelectedOptions), blocky.statisticsParametersSelect().getText(), AssertMessages.entityNotExpected("Select View default option"));
        Assertions.assertLinesMatch(expectedAvailableOptions, actualOptions, AssertMessages.entityNotExpected("Select View options"));
    }

    @Test
    @Issue("PLT-428")
    public void SMP_T704_expectedControlsDisplayed_when_saveTeamH2hBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(
                blockyData.getTeamOne().getName(),
                blockyData.getTeamTwo().getName(),
                FootballStatisticsEnum.getTeamH2HBlockyStatistics());
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T704_blockyPreviewDisplayed_when_saveTeamH2hBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T704_editModeControlsDisplayed_when_saveTeamH2hBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T704_blockRemoved_when_saveTeamH2hBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    @Issue("PLT-428")
    public void allStatisticsPreSelected_when_addTeamH2hBlocky_and_selectTeams() {
        blocky.selectTeamOne(blockyData);
        blocky.selectTeamTwo(blockyData);

        blocky.asserts().assertAllStatisticsPreSelected(blocky);
    }

    @Test
    public void blockyNotSaved_when_addFootballTeamH2HBlocky_and_saveBlock() {
        blocky.saveBlock();

        blocky.asserts()
                .assertErrorMessageForFirstTeamSelectDisplayed()
                .assertErrorMessageForSecondTeamSelectFieldDisplayed();
    }

    @Test
    public void blockyNotSaved_when_fillMandatoryFirstTeamFieldOnly_and_saveBlock() {
        blocky.selectTeamOne(blockyData);
        blocky.saveBlock();

        blocky.asserts()
                .assertErrorMessageForSecondTeamSelectFieldDisplayed()
                .assertErrorMessageForFirstTeamSelectNotDisplayed();
    }

    @Test
    public void blockyNotSaved_when_fillMandatorySecondTeamFieldOnly_and_saveBlock() {
        blocky.selectTeamTwo(blockyData);
        blocky.saveBlock();

        blocky.asserts()
                .assertErrorMessageForFirstTeamSelectDisplayed()
                .assertErrorMessageForSecondTeamSelectFieldNotDisplayed();
    }

    @Test
    @Issue("PLT-428")
    public void footballTeamH2HBlockyHidden_when_blockySaved_and_clickCancelButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
        blocky.editResult();
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void editSectionControlsWithClickedToggleButtonsDisplayed_when_saveTeamH2HBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertEditSectionIsVisible(blockyData);
    }
}