package articles.blockytests.football.teamh2htests;

import blockies.football.teamh2hblocky.TeamH2hBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballTeamH2HBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamH2HBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_H2H)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.H2H_BLOCKY)
public class FootballTeamH2HBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private static final FootballTeamEnum FIRST_TEAM = FootballTeamEnum.LIVERPOOL;
    private static final FootballTeamEnum SECOND_TEAM = FootballTeamEnum.REAL_MADRID;
    private TeamH2hBlocky blocky;
    private FootballTeamH2HBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_H2H);
        blocky = articleFormPage.mainSection().getTeamH2hBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamH2HBlockySportData(FIRST_TEAM, SECOND_TEAM);
        blockyData = FootballTeamH2HBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T704_htmlCodeCopied_when_saveTeamH2hBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T704_saveTeamH2hBlocky_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void saveTeamH2HBlocky_when_fillMandatoryFields_and_saveArticle() {
        blockyData = FootballTeamH2HBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void SMP_T704_saveTeamH2hBlocky_when_saveArticle_with_translatedTeamName() {
        blockyData.getTeamOne().setName(FIRST_TEAM.getBgName());
        blockyData.getTeamTwo().setName(SECOND_TEAM.getBgName());
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blockyData.getTeamOne().setName(FIRST_TEAM.getName());
        blockyData.getTeamTwo().setName(SECOND_TEAM.getName());
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}