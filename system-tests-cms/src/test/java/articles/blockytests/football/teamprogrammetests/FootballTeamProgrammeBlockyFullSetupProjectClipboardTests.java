package articles.blockytests.football.teamprogrammetests;

import blockies.football.teamprogrammeblocky.TeamProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballTeamProgrammeBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_PROGRAMME)
@Tag(CMSTags.CLIPBOARD)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class FootballTeamProgrammeBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private static final FootballTeamEnum TEAM_NAME = FootballTeamEnum.LIVERPOOL;
    private TeamProgrammeBlocky blocky;
    private FootballTeamProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getTeamProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamProgrammeBlockySportData(TEAM_NAME);
        blockyData = FootballTeamProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T631_htmlCodeCopied_when_saveTeamProgrammeBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T631_teamProgrammeBlockySaved_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}