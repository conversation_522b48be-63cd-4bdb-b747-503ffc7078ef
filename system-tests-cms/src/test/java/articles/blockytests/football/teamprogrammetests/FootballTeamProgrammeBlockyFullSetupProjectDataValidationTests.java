package articles.blockytests.football.teamprogrammetests;

import blockies.football.teamprogrammeblocky.TeamProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.WidgetBlock;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.football.FootballTeamEnum;
import data.models.blockymodels.football.FootballTeamProgrammeBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.football.FootballTeamProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_TEAM_PROGRAMME)
@Tag(CMSTags.CLIPBOARD)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class FootballTeamProgrammeBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final String EXPECTED_TOURNAMENTS_URL = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
    private TeamProgrammeBlocky blocky;
    private FootballTeamProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_TEAM_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getTeamProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getTeamProgrammeBlockySportData(FootballTeamEnum.LIVERPOOL);
        blockyData = FootballTeamProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T631_expectedControlsDisplayed_when_addTeamProgrammeBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T631_expectedRequestsCreated_when_addTeamProgrammeBlocky() {
        var tournamentGetResponses = blocky.getTournamentGetResponses();
        var firstTournamentResponse = blocky.getTournamentResponse();
        var seasonResponse = blocky.getSeasonResponse();
        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + tournamentGetResponses.get(0).getId();
        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + firstTournamentResponse.getSeasons().get(0).getId();

        ProxyServer.assertRequestMade(EXPECTED_TOURNAMENTS_URL);
        ProxyServer.assertRequestMade(tournamentApiUrl);
        ProxyServer.assertRequestMade(seasonApiUrl);
        Assertions.assertFalse(tournamentGetResponses.isEmpty(), AssertMessages.responseNotContains("Tournaments List"));
        Assertions.assertEquals(firstTournamentResponse.getName(), tournamentGetResponses.get(0).getName(), AssertMessages.responseNotContains("First Tournament Name"));
        Assertions.assertEquals(firstTournamentResponse.getSeasons().get(0).getName(), seasonResponse.getName(), AssertMessages.responseNotContains("First Season of the Tournament"));
    }

    @Test
    public void SMP_T631_expectedDefaultStateLoaded_when_addTeamProgrammeBlocky() {
        blocky.asserts().assertFieldsDefaultState();
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkTournament() {
        var expectedOption = blocky.getTournamentResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.tournamentSelect().getText(), AssertMessages.entityNotExpected("Tournament selected option"));
        blocky.asserts().assertExpectedTournamentOptions();
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkSeason() {
        var expectedOption = blocky.getSeasonResponse().getName();

        Assertions.assertEquals(expectedOption, blocky.seasonSelect().getText(), AssertMessages.entityNotExpected("Season selected option"));
        blocky.asserts().assertExpectedSeasonOptions();
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkStage() {
        blocky.asserts().assertExpectedStageOptions();
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkRound() {
        blocky.stageSelect().selectOptionByIndex(0);
        blocky.asserts().assertExpectedRoundOptions();
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkTeam() {
        blockyData = FootballTeamProgrammeBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);

        Assertions.assertEquals(blockyData.getTeam().getName(), blocky.teamSelect().getText(), AssertMessages.entityNotExpected("Team selected option"));
        blocky.asserts().assertExpectedTeamsOptions(blockyData);
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkMatchType() {
        var expectedOptions = Arrays.asList("Home", "Away", "Both");
        blocky.stageSelect().selectOptionByIndex(0);

        Assertions.assertEquals(expectedOptions.get(2), blocky.matchTypeSelect().getText(), AssertMessages.entityNotExpected("Match Type default option"));
        Assertions.assertLinesMatch(expectedOptions, blocky.matchTypeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Match Type options"));
    }

    @Test
    public void SMP_T631_expectedOptionsLoaded_when_addTeamProgrammeBlocky_and_checkSortDirection() {
        var expectedOptions = Arrays.asList("Ascending", "Descending");
        var sortOptionsFixtures = blocky.sortDirectionFixturesSelect().getOptionsValues();
        var sortOptionsResults = blocky.sortDirectionResultsSelect().getOptionsValues();

        Assertions.assertEquals("Select order direction", blocky.sortDirectionFixturesSelect().getText(), AssertMessages.entityNotExpected("Sort Direction default option"));
        Assertions.assertLinesMatch(expectedOptions, sortOptionsFixtures, AssertMessages.entityNotExpected("Sort Direction options"));

        Assertions.assertEquals("Select order direction", blocky.sortDirectionResultsSelect().getText(), AssertMessages.entityNotExpected("Sort Direction default option"));
        Assertions.assertLinesMatch(expectedOptions, sortOptionsResults, AssertMessages.entityNotExpected("Sort Direction options"));
    }

    @Test
    public void SMP_T631_expectedControlsDisplayed_when_saveTeamProgrammeBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T631_blockyPreviewDisplayed_when_saveTeamProgrammeBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertFootballApiGetRequest();
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T631_editModeControlsDisplayed_when_saveTeamProgrammeBlocky_and_clickEditButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T631_blockRemoved_when_saveTeamProgrammeBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Football team programme blocky and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addFootballTeamProgrammeBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}