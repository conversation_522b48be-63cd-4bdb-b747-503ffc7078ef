package articles.blockytests.football.odds;

import blockies.football.oddsblocky.FootballOddsBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.models.blockymodels.football.FootballOddsBlockyFieldsModel;
import factories.blockies.football.FootballOddsBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.FOOTBALL)
@Tag(SportBlocky.FOOTBALL_ODDS)
@Tag(CMSStories.ODDS_BLOCKY)
@Story(CMSStories.ODDS_BLOCKY)
public class FootballOddsBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private FootballOddsBlocky blocky;
    private FootballOddsBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.FOOTBALL_ODDS);
        blocky = articleFormPage.mainSection().getOddsBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = footballApiFacade.getOddsBlockySportData();
        blockyData = FootballOddsBlockyFieldsFactory.buildDynamicFieldsOnly(blockyData);
    }

    @Test
    public void expectedControlsDisplayed_when_addOddsBlocky() {
        blocky.asserts().assertDefaultEditSectionIsVisible();
    }

    @Test
    public void requiredMessageDisplayed_when_addOddsBlocky_and_saveWithEmptyTeam() {
        var expectedTeamFieldValidationMessage = StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING;

        blocky.validateValidationMessageDisplayed(expectedTeamFieldValidationMessage);
    }

    @Test
    public void expectedControlsDisplayed_when_addOddsBlocky_and_selectTeam() {
        blocky.selectTeam(blockyData.getTeam());

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void expectedMarketValueTypesDisplayed_when_addOddsBlocky_and_selectTeam() {
        blocky.selectTeam(blockyData.getTeam());

        blocky.asserts().assertExpectedMarketValueTypeOptions();
    }
}