package articles.blockytests.basketball.tournamentprogrammetests;

import blockies.basketball.tournamentprogrammeblocky.BasketballTournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT_ALL_SPORTS)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.BASKETBALL)
@Tag(SportBlocky.BASKETBALL_TOURNAMENT_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class BasketballTournamentProgrammeBlockyDefaultAllSportsProjectDataValidationTests extends CmsWebTest {

    private BasketballTournamentProgrammeBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getBasketballTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Basketball tournament programme blocky and feature 'auto_check_display_odds' is not set")
    public void displayOddCheckboxChecked_when_addBasketballTournamentProgrammeBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}