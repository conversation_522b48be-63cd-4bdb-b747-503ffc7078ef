package articles.blockytests.basketball.tournamentprogrammetests;

import blockies.basketball.tournamentprogrammeblocky.BasketballTournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.RefreshTime;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.models.basketball.competition.CompetitionModel;
import data.models.basketball.season.SeasonModel;
import data.models.blockymodels.basketball.BasketballTournamentProgrammeBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.basketball.BasketballTournamentProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.BASKETBALL)
@Tag(SportBlocky.BASKETBALL_TOURNAMENT_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class BasketballTournamentProgrammeBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private BasketballTournamentProgrammeBlocky blocky;
    private BasketballTournamentProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getBasketballTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = basketballApiFacade.getTournamentProgrammeSportData(BasketballCompetitionName.NBA);
        blockyData = BasketballTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void expectedControlsDisplayed_when_addBasketballTournamentProgrammeBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void expectedRequestsExecuted_when_addBasketballTournamentProgrammeBlocky() {
        CompetitionModel firstCompetition = blocky.getFirstCompetitionFromResponse();
        SeasonModel firstSeason = blocky.getFirstSeasonFromResponseBy();

        ProxyServer.assertRequestMade(blocky.getCompetitionsApiUrl());
        ProxyServer.assertRequestMade(blocky.getSeasonsApiUrl().formatted(firstCompetition.getId()));
        ProxyServer.assertRequestMade(blocky.getSeasonsDetailsApiUrl().formatted(firstSeason.getId()));
    }

    @Test
//    @Issue("SBE-3065")
    public void expectedDefaultStateLoaded_when_addBasketballTournamentProgrammeBlocky() {
        blocky.asserts().assertFieldsDefaultState();
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkCompetition() {
        blocky.asserts().assertExpectedCompetitionOptions();
    }

    @Test
//    @Issue("SBE-3065")
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkSeason() {
        blocky.asserts().assertExpectedSeasonOptions();
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkStage() {
        blocky.asserts().assertExpectedStageOptions();
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkRound() {
        blocky.stageSelect().selectOptionByIndex(0);
        blocky.asserts().assertExpectedRoundOptions();
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkSortDirection() {
        List<String> expectedSortDirectionOptions = List.of("Ascending", "Descending");
        List<String> actualSortDirectionFixturesOptions = blocky.sortDirectionFixturesSelect().getOptionsValues();
        List<String> actualSortDirectionResultsOptions = blocky.sortDirectionResultsSelect().getOptionsValues();

        Assertions.assertLinesMatch(expectedSortDirectionOptions, actualSortDirectionFixturesOptions,
                AssertMessages.entityNotExpected("Sort Direction options"));
        Assertions.assertLinesMatch(expectedSortDirectionOptions, actualSortDirectionResultsOptions,
                AssertMessages.entityNotExpected("Sort Direction options"));
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballTournamentProgrammeBlocky_and_checkRefreshTime() {
        List<String> expectedRefreshTimeValues = RefreshTime.getEnumValues();
        List<String> actualRefreshTimeValues = blocky.refreshTimeSelect().getOptionsValues();

        Assertions.assertLinesMatch(expectedRefreshTimeValues, actualRefreshTimeValues,
                AssertMessages.entityNotExpected("Refresh Time options"));
    }

    @Test
    public void expectedControlsDisplayed_when_saveBasketballTournamentProgrammeBlocky() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void blockyPreviewDisplayed_when_saveBasketballTournamentProgrammeBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
        blocky.asserts().assertMultiSportApiGetEventsSearchRequest();
    }

    @Test
    public void editModeControlsDisplayed_when_saveBasketballTournamentProgrammeBlocky_and_clickEditButton() {
        blockyData = BasketballTournamentProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void blockRemoved_when_saveBasketballTournamentProgrammeBlocky_and_clickRemoveButton() {
        blockyData = BasketballTournamentProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    //@Issue("SFE-4836")
    public void errorMessageDisplayedForCompetitionField_when_saveBasketballTournamentProgrammeBlocky_and_competitionEmpty() {
        var expectedValidationMessage = StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING;
        blocky.competitionSelect().clearSelection();

        blocky.saveBlock();

        Assertions.assertTrue(blocky.validationMessagesList()
                        .stream()
                        .anyMatch(e -> e.getText().contains(expectedValidationMessage)),
                AssertMessages.entityNotVisible("Field empty validation message"));
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Basketball tournament programme blocky and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addBasketballTournamentProgrammeBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}