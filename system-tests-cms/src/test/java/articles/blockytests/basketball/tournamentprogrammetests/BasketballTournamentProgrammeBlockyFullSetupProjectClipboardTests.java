package articles.blockytests.basketball.tournamentprogrammetests;

import blockies.basketball.tournamentprogrammeblocky.BasketballTournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.models.blockymodels.basketball.BasketballTournamentProgrammeBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.basketball.BasketballTournamentProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.BASKETBALL)
@Tag(SportBlocky.BASKETBALL_TOURNAMENT_PROGRAMME)
@Tag(CMSTags.CLIPBOARD)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class BasketballTournamentProgrammeBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private BasketballTournamentProgrammeBlocky blocky;
    private BasketballTournamentProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET);
        blocky = articleFormPage.mainSection().getBasketballTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = basketballApiFacade.getTournamentProgrammeSportData(BasketballCompetitionName.NBA);
        blockyData = BasketballTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void htmlCodeCopied_when_saveBasketballTournamentProgrammeBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void basketballTournamentProgrammeBlockySaved_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData);
        blocky.saveBlock();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}