package articles.blockytests.basketball.singleeventtests;

import blockies.basketball.singleeventblocky.BasketballSingleEventBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.*;
import data.constants.apiurls.MultiSportApiUrl;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.customelements.MatchResult;
import data.models.blockymodels.basketball.BasketballSingleEventBlockyFieldsModel;
import data.models.searchapi.ResultModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.basketball.BasketballSingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.BASKETBALL)
@Tag(SportBlocky.BASKETBALL_SINGLE_EVENT)
@Tag(CMSStories.SINGLEEVENT_BLOCKY)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class BasketballSingleEventBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private static final BasketballTeamEnum BASKETBALL_TEAM = BasketballTeamEnum.CHICAGO_BULLS;
    private BasketballSingleEventBlocky blocky;
    private BasketballSingleEventBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_SINGLE_EVENT);
        blocky = articleFormPage.mainSection().getBasketballSingleEventBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = basketballApiFacade.getSingleEventBlockySportData(EventStatusType.FINISHED);
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void expectedControlsDisplayed_when_addBasketballSingleEventBlocky() {
        blocky.asserts().assertBlockyElements();
        blocky.asserts().validateEditSectionDefaultState();
    }

    @Test
    public void requiredMessageDisplayed_when_addBasketballSingleEventBlocky_and_saveWithEmptyTeam() {
        blocky.saveBlock();
        blocky.validateValidationMessageDisplayed(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING);
        blocky.validateValidationMessageDisplayed("No games selected");
    }

    @Test
    public void teamFieldCanHoldMultipleEntities_when_addBasketballSingleEventBlocky_and_addMoreThanOneTeam() {
        var secondTeam = BasketballTeamEnum.MIAMI_HEAT.getName();
        blocky.filterMultipleTeams(BASKETBALL_TEAM.getName(), secondTeam);

        Assertions.assertTrue(blocky.getSelectedTeams().contains(BASKETBALL_TEAM.getName()), AssertMessages.entityNotVisible("Selected First Team", BASKETBALL_TEAM.getName(), blocky.getSelectedTeams()));
        Assertions.assertTrue(blocky.getSelectedTeams().contains(secondTeam), AssertMessages.entityNotVisible("Selected Second Team", secondTeam, blocky.getSelectedTeams()));
    }

    @Test
    public void upcomingMatchesDisplayed_when_addBasketballSingleEventBlocky_and_selectTeam() {
        ResultModel notStartedEvent = searchApiV2Facade.getNotStartedEventFor(SupportedSports.BASKETBALL);
        String teamName = notStartedEvent.getParticipantDetails().get(0).getParticipant().getName();
        String teamId = notStartedEvent.getParticipantDetails().get(0).getParticipant().getId();

        blocky.selectTeam(teamName, teamId);
        List<MatchResult> upcomingMatches = blocky.eventsList().getUpcomingMatches();

        Assertions.assertFalse(upcomingMatches.isEmpty(), AssertMessages.entityNotVisible("Upcoming matches"));
    }

    @Test
    public void pastMatchesDisplayed_when_addBasketballSingleEventBlocky_and_selectTeam() {
        blocky.selectTeam(BASKETBALL_TEAM.getName(), BASKETBALL_TEAM.getId());
        List<MatchResult> pastMatches = blocky.eventsList().getPastMatches();

        Assertions.assertFalse(pastMatches.isEmpty(), AssertMessages.entityNotVisible("Past matches"));
    }

    @Test
    public void blockySaved_when_addBasketballSingleEventBlocky_and_saveWithEmptyBookmaker() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.waitPreviewScreenToLoad();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void expectedOptionsLoaded_when_addBasketballSingleEventBlocky_and_checkRefreshTime() {
        List<String> expectedRefreshTimeValues = RefreshTime.getEnumValues();
        List<String> actualRefreshTimeValues = blocky.refreshTimeSelect().getOptionsValues();

        Assertions.assertLinesMatch(expectedRefreshTimeValues, actualRefreshTimeValues, AssertMessages.entityNotExpected("Refresh Time options"));
    }

    @Test
    public void singleEventBlockySaved_when_fillRequiredFields_and_clickSaveButton() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void singleEventBlockyHided_when_blockyNotSaved_and_clickCancelButton() {
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
    }

    @Test
    public void singleEventBlockyHidden_when_blockySaved_and_clickCancelButton() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
        blocky.editResult();

        blocky.cancelEdit();
        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    public void SMP_T632_expectedControlsDisplayed_when_saveSingleEventBlocky() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible();
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void previewDisplayed_when_saveSingleEventBlocky_and_clickPreviewButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();
        blocky.waitPreviewScreenToLoad();

        blocky.previewResult();

        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), MultiSportApiUrl.BASKETBALL_EVENTS.getUrl(), HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(MultiSportApiUrl.EVENTS.getUrl());
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void editModeControlsDisplayed_when_saveSingleEventBlocky_and_clickEditButton() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void singleEventBlockyRemoved_when_saveSingleEventBlocky_and_clickRemoveButton() {
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.removeResult();
        blocky.validateBlockyNotExist();
    }

    @Test
    public void oddsDataSectionDisplayed_when_addSingleEventBlocky_and_checkDisplayOddsCheckbox() {
        blockyData = basketballApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blockyData.setDisplayOdds(true);
        blocky.fillForm(blockyData);

        Assertions.assertTrue(blocky.displayOddsCheckbox().isChecked(), AssertMessages.entityNotExpected("state of Display Odds"));
        Assertions.assertTrue(blocky.bookmakerSelect().isVisible(), AssertMessages.entityNotVisible("Bookmaker select"));
        blocky.bookmakerSelect().validateNoOptions();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Basketball single event blocky and select upcoming game and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addBasketballSingleEventBlocky_and_selectUpcomingGame() {
        blockyData = basketballApiFacade.getSingleEventBlockySportData(EventStatusType.NOT_STARTED);

        blocky.selectTeam(blockyData.getTeam().getName(), blockyData.getTeam().getId());
        blocky.selectEvent(blockyData.getEvent());

        blocky.displayOddsCheckbox().validateIsChecked();
    }
}