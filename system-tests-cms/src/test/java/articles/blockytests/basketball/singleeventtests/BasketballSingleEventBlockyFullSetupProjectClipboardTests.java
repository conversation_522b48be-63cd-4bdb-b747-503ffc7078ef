package articles.blockytests.basketball.singleeventtests;

import blockies.basketball.singleeventblocky.BasketballSingleEventBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.EventStatusType;
import data.constants.WidgetBlock;
import data.models.blockymodels.basketball.BasketballSingleEventBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.basketball.BasketballSingleEventBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.BASKETBALL)
@Tag(CMSStories.SINGLEEVENT_BLOCKY)
@Tag(SportBlocky.BASKETBALL_SINGLE_EVENT)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class BasketballSingleEventBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private BasketballSingleEventBlocky blocky;
    private BasketballSingleEventBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.BASKETBALL_SINGLE_EVENT);
        blocky = articleFormPage.mainSection().getBasketballSingleEventBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = basketballApiFacade.getSingleEventBlockySportData(EventStatusType.FINISHED);
        blockyData = BasketballSingleEventBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void singleEventBlockySaved_when_saveArticle() {
        blocky.fillForm(blockyData);
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void singleEventBlockySaved_when_saveArticle_with_translatedTeamName() {
        String enName = blockyData.getTeam().getName();
        String translatedName = blockyData.getTeam().getNameTranslation("");
        blockyData.getTeam().setName(translatedName);

        blocky.fillForm(blockyData);
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.saveBlock().copyEmbedCode();
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        blockyData.getTeam().setName(enName);
        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void htmlCodeCopied_when_saveSingleEventBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData);
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }
}