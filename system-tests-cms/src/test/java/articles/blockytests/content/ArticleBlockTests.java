package articles.blockytests.content;

import blockies.content.articlesblocky.ArticleBlocky;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.models.articles.ArticleResponseModel;
import data.models.articles.BodyItem;
import data.models.articles.Data;
import data.models.uimodels.ArticleFormModel;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import io.qameta.allure.TmsLink;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import static data.constants.StringConstants.ARTICLE_STRING;
import static data.constants.StringConstants.TYPE_STRING;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(CMSTags.CONTENT)
@Tag(CMSStories.ARTICLE_BLOCKY)
@Story(CMSStories.ARTICLE_BLOCKY)
@TmsLink("SMP-T291")
public class ArticleBlockTests extends CmsWebTest {

    private ArticleBlocky articleBlock;
    private ArticleResponseModel foundArticleViaAPI;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.ARTICLE_WIDGET);
        articleBlock = articleFormPage.mainSection().getArticleBlocks().get(0);
        articleBlock.waitEditScreenToLoad();
    }

    @Test
    public void SMP_T291_expectedControlsDisplayed_when_addArticleBlocky() {
        articleBlock.asserts().validateEditSectionIsVisible();
    }

    @Test
    public void SMP_T291_requiredMessageDisplayed_when_addArticleBlocky_and_saveEmpty() {
        articleBlock.saveBlock();

        Assertions.assertTrue(articleBlock.validationMessagesList()
                        .stream()
                        .anyMatch(e -> e.getText().contains(StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING)),
                AssertMessages.entityNotVisible("Field empty validation message"));
    }

    @Test
    public void SMP_T291_getArticlesByIdRequestTriggered_when_addArticleBlocky_and_chooseExistingArticle() {
        foundArticleViaAPI = contentApiFacade.getArticle();

        articleBlock.chooseArticle(foundArticleViaAPI.getTitle());
        articleBlock.saveBlock();

        articleBlock.asserts().validateArticlesByIdCallExecuted(foundArticleViaAPI.getId());
    }

    @Test
    public void SMP_T291_expectedPreviewBlockControlsDisplayed_when_chooseExistingArticle_and_saveBlock() {
        foundArticleViaAPI = contentApiFacade.getArticle();

        articleBlock.chooseArticle(foundArticleViaAPI.getTitle());
        articleBlock.saveBlock();

        articleBlock.asserts().validatePreviewBlockControlsFor(foundArticleViaAPI);
    }

    @Test
    public void SMP_T291_editModeControlsDisplayed_when_saveArticleBlock_and_clickEditButton() {
        foundArticleViaAPI = contentApiFacade.getArticle();

        articleBlock.chooseArticle(foundArticleViaAPI.getTitle());
        articleBlock.saveBlock();

        articleBlock.waitPreviewScreenToLoad();
        articleBlock.editResult();

        articleBlock.asserts()
                .validateEditSectionIsVisible()
                .validateChooseArticleTitleIs(foundArticleViaAPI.getTitle());
    }

    @Test
    public void SMP_T291_blockRemoved_when_saveArticleBlock_and_clickRemoveButton() {
        foundArticleViaAPI = contentApiFacade.getArticle();

        articleBlock.chooseArticle(foundArticleViaAPI.getTitle());
        articleBlock.saveBlock();

        articleBlock.waitPreviewScreenToLoad();
        articleBlock.removeResult();

        articleBlock.validateBlockyNotExist();
    }

    @Test
    public void SMP_T291_articleBlockSaved_when_saveArticle() {
        ArticleFormModel articleRequiredFields = ArticleFieldsFactory.buildArticleRequiredFields();
        foundArticleViaAPI = contentApiFacade.getArticle();

        articleBlock.chooseArticle(foundArticleViaAPI.getTitle());
        articleBlock.saveBlock();

        articleFormPage.setRequiredFields(articleRequiredFields.getTitle(), articleRequiredFields.getMainCategory());
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

        BodyItem bodyObject = createdArticle.getBody().get(0);
        Data dataObject = bodyObject.getData();

        Assertions.assertEquals(ARTICLE_STRING, bodyObject.getType(), AssertMessages.entityNotExpected(TYPE_STRING));
        Assertions.assertEquals(foundArticleViaAPI.getId(), dataObject.getId(), AssertMessages.entityNotExpected("%s id".formatted(ARTICLE_STRING), foundArticleViaAPI.getId(), dataObject.getId()));
    }
}