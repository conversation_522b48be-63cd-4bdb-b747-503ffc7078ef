package articles.blockytests.tennis.tournamentprogrammetests;

import blockies.tennis.tournamentprogrammeblocky.TennisTournamentProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.tennis.TennisCompetitionName;
import data.models.blockymodels.tennis.TennisTournamentProgrammeBlockyFieldsModel;
import factories.blockies.tennis.TennisTournamentProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_TOURNAMENT_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class TennisTournamentProgrammeBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private TennisTournamentProgrammeBlocky blocky;
    private TennisTournamentProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.TENNIS_TOURNAMENT_PROGRAMME);
        blocky = articleFormPage.mainSection().getTennisTournamentProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = tennisApiFacade.getTournamentProgrammeSportData(TennisCompetitionName.US_OPEN);
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void expectedElementsOfBlockyDisplayed_when_addTennisTournamentProgrammeBlocky() {
        blocky.asserts().assertBlockyElements();
    }

    @Test
//    @Issue("SFE-4668")
    public void expectedDefaultStateOfElementsLoaded_when_addTennisTournamentProgrammeBlocky() {
        blocky.asserts().assertFieldsDefaultState();
    }

    @Test
    public void expectedRequestsTriggered_when_addTennisTournamentProgrammeBlocky() {
        blocky.asserts().assertRequestsTriggeredAfterBlockyAdded();
    }

    @Test
    public void expectedCompetitionsOptionsLoadedInCompetitionField_when_checkCompetitionField() {
        blocky.asserts().assertCompetitionOptions();
    }

    @Test
    public void expectedSeasonsOptionsLoadedInSeasonField_when_checkSeasonField() {
        blocky.asserts().assertSeasonOptions();
    }

    @Test
    public void expectedTournamentOptionsLoadedInTournamentField_when_checkTournamentField() {
        blocky.asserts().assertTournamentOptions();
    }

    @Test
    public void expectedRoundsOptionsFilledInRoundsField_when_selectTournament_and_checkRoundsField() {
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildDynamicFieldsOnly(blockyData);

        blocky.fillForm(blockyData);

        blocky.asserts().assertRoundsOptionsForTournament(blockyData.getTournament());
    }

    @Test
    public void expectedSortDirectionOptionsLoadedInSortDirectionField_when_checkSortDirectionField() {
        blocky.asserts().assertSortDirectionOptions();
    }

    @Test
    public void expectedMatchTypeOptionsLoadedInMatchTypeField_when_checkMatchTypeField() {
        blocky.asserts().assertMatchTypeOptions();
    }

    @Test
    public void expectedGenderOptionsLoadedInGenderField_when_checkGenderField() {
        blocky.asserts().assertGenderOptions();
    }

    @Test
    public void expectedBookmakerOptionsLoadedInSelectBookmakerField_when_checkSelectBookmakerField() {
        blocky.asserts().assertBookmakerOptions();
    }

    @Test
    public void expectedRefreshTimeOptionsLoadedInRefreshTimeField_when_checkRefreshTimeField() {
        blocky.asserts().assertRefreshTimeOptions();
    }

    @Test
    public void expectedPreviewOptionsDisplayed_when_allFieldFilled_and_saveTennisTournamentProgrammeBlocky() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .waitPreviewScreenToLoad();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void expectedPreviewOptionsDisplayed_when_onlyCompetitionMandatoryFieldFilled_and_saveTennisTournamentProgrammeBlocky() {
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .waitPreviewScreenToLoad();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void blockRemoved_when_saveTennisTournamentProgrammeBlocky_and_clickRemoveButton() {
        blocky.saveBlock()
                .removeResult();

        blocky.asserts().assertBlockyNotExist();
    }

    @Test
    public void editModeControlsDisplayed_when_saveTennisTournamentProgrammeBlockyWithAllFieldsFilled_and_clickEditButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertEditSectionIsVisible(blockyData);
    }

    @Test
    public void editModeControlsDisplayed_when_saveTennisTournamentProgrammeBlockyWithMandatoryFieldsOnly_and_clickEditButton() {
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertEditSectionIsVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void blockyPreviewDisplayed_when_saveTennisTournamentProgrammeBlockyWithAllFieldsFilled_and_clickPreviewButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void blockyPreviewDisplayed_when_saveTennisTournamentProgrammeBlockyWithMandatoryFieldsOnly_and_clickPreviewButton() {
        blockyData = TennisTournamentProgrammeBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Tennis tournament programme blocky and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addTennisTournamentProgrammeBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}