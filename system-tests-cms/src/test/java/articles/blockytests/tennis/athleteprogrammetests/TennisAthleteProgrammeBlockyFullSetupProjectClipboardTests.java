package articles.blockytests.tennis.athleteprogrammetests;

import blockies.tennis.athleteprogrammeblocky.TennisAthleteProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.constants.enums.tennis.TennisPlayerName;
import data.models.blockymodels.tennis.TennisAthleteProgrammeBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.tennis.TennisAthleteProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_ATHLETE_PROGRAMME)
@Tag(CMSTags.CLIPBOARD)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class TennisAthleteProgrammeBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private static final TennisPlayerName PLAYER = TennisPlayerName.NOVAK_DJOKOVIC;
    private TennisAthleteProgrammeBlocky blocky;
    private TennisAthleteProgrammeBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        articleFormPage.addBlocky(WidgetBlock.TENNIS_ATHLETE_PROGRAMME);

        blocky = articleFormPage.mainSection().getTennisAthleteProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = tennisApiFacade.getAthleteProgrammeBlockySportData();
        blockyData = TennisAthleteProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void htmlCodeCopied_when_saveTennisAthleteProgrammeBlockyWithAllFieldFilled_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

//        articleFormPage.map().alertMessage().validateMessageIs(ToastMessageEnum.WIDGET_EMBED_CODE_COPIED);
        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void htmlCodeCopied_when_saveTennisAthleteProgrammeBlockyWithPlayerMandatoryFieldFilled_and_clickCopyEmbedCodeButton() {
        blockyData = TennisAthleteProgrammeBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

//        articleFormPage.map().alertMessage().validateMessageIs(ToastMessageEnum.WIDGET_EMBED_CODE_COPIED);
        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void saveTennisAthleteProgrammeBlockyWithAllFieldsFilled_when_saveArticle() {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void saveTennisAthleteProgrammeBlockyWithAllFieldsFilled_when_saveArticle_with_translatedTeamName() {
        blockyData = tennisApiFacade.getAthleteProgrammeBlockySportData(PLAYER.getBgName());

        blockyData.getPlayer().setName(PLAYER.getBgName());
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));

        blocky.fillForm(blockyData)
                .saveBlock();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blockyData.getPlayer().setName(PLAYER.getName());
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }

    @Test
    public void htmlCodeCopied_when_saveTennisAthleteProgrammeBlocky_and_withoutDisplayOddsCheckbox() {
        blockyData = TennisAthleteProgrammeBlockyFieldsFactory.buildAllFields(blockyData);
        blockyData.setDisplayOdds(false);

        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }
}