package articles.blockytests.tennis.athleteprogrammetests;

import blockies.tennis.athleteprogrammeblocky.TennisAthleteProgrammeBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.EventStatusType;
import data.constants.WidgetBlock;
import data.models.blockymodels.tennis.TennisAthleteProgrammeBlockyFieldsModel;
import factories.blockies.tennis.TennisAthleteProgrammeBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_ATHLETE_PROGRAMME)
@Tag(CMSStories.PROGRAMME_BLOCKY)
@Story(CMSStories.PROGRAMME_BLOCKY)
public class TennisAthleteProgrammeBlockyFullSetupApiProjectDataValidationTests extends CmsWebTest {

    private TennisAthleteProgrammeBlocky blocky;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.TENNIS_ATHLETE_PROGRAMME);
        blocky = articleFormPage.mainSection().getTennisAthleteProgrammeBlocks().get(0);
        blocky.waitEditScreenToLoad();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is not checked when add Tennis athlete programme blocky and feature 'auto_check_display_odds' is set to false")
    public void displayOddCheckboxNotChecked_when_addTennisAthleteProgrammeBlocky() {
        var blockyData = tennisApiFacade.getAthleteProgrammeBlockySportData(EventStatusType.NOT_STARTED);
        blocky.selectPlayer(blockyData);
        blocky.displayOddsCheckbox().validateIsUnchecked();
    }
}