package articles.blockytests.tennis.livescoretests;

import blockies.tennis.livescoreblocky.TennisLivescoreBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.*;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.widgets.options.enums.DataRefreshTimeEnum;
import factories.blockies.tennis.TennisLivescoreBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Heading;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_LIVESCORE)
@Story(CMSStories.LIVESCORE_BLOCKY)
public class TennisLivescoreBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private TennisLivescoreBlocky blocky;
    private LivescoreBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.TENNIS_LIVESCORE);
        blocky = articleFormPage.mainSection().getTennisLivescoreBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = tennisApiFacade.getLivescoreBlockySportData(LocalDate.now());
        blockyData = TennisLivescoreBlockyFieldsFactory.buildMandatoryFields(blockyData);
    }

    @Test
    public void SMP_T666_expectedControlsDisplayed_when_addLivescoreBlocky() {
        blocky.validateEditSectionIsVisible();
    }

    @Test
    public void SMP_T666_requiredMessageDisplayed_when_addLivescoreBlocky_and_saveWithEmptyDateTime() {
        var expectedValidationMessage = PlaceholderField.NO_DATE_SELECTED.getValue();
        blocky.saveBlock();
        var actualMessages = blocky.validationMessagesList().stream().map(Heading::getText).toList();

        Assertions.assertTrue(actualMessages.contains(expectedValidationMessage),
                AssertMessages.entityNotExpected("Date validation message", expectedValidationMessage, String.join("; ", actualMessages)));
    }

    @Test
    public void SMP_T666_oddsDataSectionDisplayed_when_addLivescoreBlocky_and_checkDisplayOddsCheckbox() {
        blocky.displayOddsCheckbox().check();

        Assertions.assertTrue(blocky.displayOddsCheckbox().isChecked(), AssertMessages.entityNotExpected("state of Display Odds"));
        Assertions.assertTrue(blocky.bookmakerSelect().isVisible(), AssertMessages.entityNotVisible("Bookmaker select"));
    }

    @Test
    public void SMP_T666_requiredMessageDisplayed_when_addLivescoreBlocky_and_setEmptyBookmaker() {
        var expectedValidationMessage = StringConstants.NO_DATE_SELECTED_STRING;
        blocky.displayOddsCheckbox().check();
        blocky.bookmakerSelect().clearSelection();
        blocky.saveBlock();
        var actualMessages = blocky.validationMessagesList().stream().map(Heading::getText).toList();

        Assertions.assertTrue(actualMessages.contains(expectedValidationMessage),
                AssertMessages.entityNotExpected("Date validation message", expectedValidationMessage, String.join("; ", actualMessages)));
    }

    @Test
    public void SMP_T673_expectedOptionsDisplayed_when_addSingleEventBlocky_and_checkRefreshTimeSelect() {
        var expectedPlaceholder = PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue();

        Assertions.assertEquals(expectedPlaceholder, blocky.refreshTimeSelect().getText(), AssertMessages.entityNotExpected("Refresh Time Placeholder"));
        Assertions.assertLinesMatch(RefreshTime.getEnumValues(), blocky.refreshTimeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Refresh Time Options"));
    }

    @Test
    public void SMP_T666_blockySaved_when_fillRequiredFields_and_clickSaveButton() {
        blockyData = TennisLivescoreBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.validatePreviewControlsAreVisible(blockyData);
    }

    @Test
    public void SMP_T666_expectedControlsDisplayed_when_saveLivescoreBlocky() {
        blockyData.setDisplayOdds(false);
        blocky.fillForm(blockyData);
        // TODO: fix it when Odds GET request returns results.
//        livescoreSection.map().displayOddsCheckbox().check();
//        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), OddsApiUrl.TENNIS_CONFIGURATION.url, HttpMethod.GET, 1);
//        var bookmaker = livescoreSection.map().bookmakerSelect().getText();
        blocky.saveBlock();

        blocky.waitPreviewScreenToLoad();
        blocky.validatePreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T666_previewDisplayed_when_saveLivescoreBlocky_and_clickPreviewButton() {
        blockyData.setRefreshTime(DataRefreshTimeEnum.SUPER_SLOW);

        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertTennisApiGetRequest(blockyData);
        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T666_editModeControlsDisplayed_when_saveLivescoreBlocky_and_clickEditButton() {
        blocky.dateTimePicker().setDate(LocalDate.now());

        blocky.saveBlock()
                .editResult();

        blocky.validateEditSectionIsVisible();
    }

    @Test
    public void SMP_T666_blockyRemoved_when_saveLivescoreBlocky_and_clickRemoveButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }

    @Test
    @DisplayName("Verify Display odds checkbox is checked when add Tennis livescore blocky and feature 'auto_check_display_odds' is set to true")
    public void displayOddCheckboxChecked_when_addTennisLivescoreBlocky() {
        blocky.displayOddsCheckbox().validateIsChecked();
    }
}