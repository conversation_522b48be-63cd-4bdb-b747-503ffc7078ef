package articles.blockytests.tennis.rankingtests;

import blockies.tennis.rankingblocky.TennisRankingBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.RefreshTime;
import data.constants.StringConstants;
import data.constants.WidgetBlock;
import data.models.blockymodels.tennis.TennisRankingBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.tennis.TennisRankingBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_RANKING)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class TennisRankingBlockyFullSetupProjectDataValidationTests extends CmsWebTest {

    private TennisRankingBlocky blocky;
    private TennisRankingBlockyFieldsModel blockyData;
    private static final String LIMIT = "2";

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();

        articleFormPage.addBlocky(WidgetBlock.TENNIS_RANKING);
        blocky = articleFormPage.mainSection().getTennisRankingBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = tennisApiFacade.getRankingBlockySportData();
        blockyData = TennisRankingBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T673_expectedControlsDisplayed_when_addTennisRankingBlocky() {
        blocky.asserts().assertBlockyElements();
        blocky.asserts().assertDefaultStateEditSection();
    }

    @Test
    public void SMP_T705_limitTextFieldDisplayed_when_addTennisRankingBlocky() {
        blocky.limitTextField().validateIsVisible();
        Assertions.assertEquals("number", blocky.limitTextField().getAttribute("type"), AssertMessages.entityNotExpected("Limit field type"));
        blocky.limitTextField().setText("TEST");
        blocky.limitTextField().validateTextContains("");

        blocky.limitTextField().setText(LIMIT);
        blocky.limitTextField().validateTextContains(LIMIT);
    }

    @Test
    public void SMP_T705_offsetTextFieldDisplayed_when_addTennisRankingBlocky() {
        blocky.offsetTextField().validateIsVisible();
        Assertions.assertEquals("number", blocky.offsetTextField().getAttribute("type"), AssertMessages.entityNotExpected("Offset field type"));
        blocky.offsetTextField().setText("TEST");
        blocky.offsetTextField().validateTextContains("");

        blocky.offsetTextField().setText(LIMIT);
        blocky.offsetTextField().validateTextContains(LIMIT);
    }

    @Test
    public void SMP_T673_expectedOptionsDisplayed_when_addTennisRankingBlocky_and_checkStandingType() {
        var expectedStandingTypes = blocky.getStandingTypesAsStrings();
        expectedStandingTypes.add(0, "ALL");

        Assertions.assertLinesMatch(expectedStandingTypes, blocky.standingTypeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Standing types"));
    }

    @Test
    public void SMP_T673_expectedOptionsDisplayed_when_addTennisRankingBlocky_and_checkHighlightPlayers() {
        var rankingOption = blocky.getRankingOptions().get(0);
        blocky.standingTypeSelect().selectOptionByText(rankingOption.getStandingType().getName());
        blocky.highlightPlayersCheckbox().check();

        var expectedPlayers = blocky.getPlayersNamesForStandingType(rankingOption.getId());
        var actualPlayers = blocky.highlightPlayersSelect().getOptionsValues();

        Assertions.assertLinesMatch(expectedPlayers, actualPlayers, AssertMessages.entityNotExpected("Highlighted players"));
    }

    @Test
    public void SMP_T673_expectedOptionsDisplayed_when_addTennisRankingBlocky_and_checkRefreshTimeSelect() {
        var expectedPlaceholder = "Leave empty if you want to use global settings";

        Assertions.assertEquals(expectedPlaceholder, blocky.refreshTimeSelect().getText(), AssertMessages.entityNotExpected("Refresh Time Placeholder"));
        Assertions.assertLinesMatch(RefreshTime.getEnumValues(), blocky.refreshTimeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Refresh Time Options"));
    }

    @Test
    public void SMP_T673_requiredMessageDisplayed_when_addTennisRankingBlocky_and_saveWithEmptyFields() {
        var expectedEmptyFieldValidationMessage = StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING;
        var expectedLimitValidationMessage = "No limit selected";
        blocky.saveBlockWithoutWait();

        blocky.asserts().assertValidationMessageDisplayed(expectedEmptyFieldValidationMessage);
        blocky.asserts().assertValidationMessageDisplayed(expectedLimitValidationMessage);
    }

    @Test
    public void SMP_T673_tennisRankingBlockySaved_when_fillRequiredFields_and_save() {
        blockyData = TennisRankingBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blockyData.setOffset("1");

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void SMP_T673_tennisRankingBlockySaved_when_fillAllFields_and_save() {
        blockyData = TennisRankingBlockyFieldsFactory.buildAllFields(blockyData);
        blockyData.setOffset("1");

        blocky.fillForm(blockyData)
                .saveBlock();

        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    public void SMP_T673_tennisRankingBlockyHided_when_blockyNotSaved_and_clickCancelButton() {
        blocky.cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
    }

    @Test
    public void SMP_T673_tennisRankingBlockyHided_when_blockySaved_and_clickCancelButton() {
        blockyData = TennisRankingBlockyFieldsFactory.buildMandatoryFields(blockyData);
        blockyData.setOffset("1");

        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult()
                .cancelEdit();

        blocky.asserts().assertEditSectionNotExist();
        blocky.asserts().assertPreviewControlsAreVisible(blockyData);
    }

    @Test
    @Tag(CMSTags.BLOCKY_PREVIEW)
    public void SMP_T673_previewDisplayed_when_saveSingleEventBlocky_and_clickPreviewButton() {
        blockyData = TennisRankingBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .previewResult();

        blocky.asserts().assertPreviewHtml(blockyData);
    }

    @Test
    public void SMP_T673_editModeControlsDisplayed_when_saveTennisRankingBlocky_and_clickEditButton() {
        blockyData = TennisRankingBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .editResult();

        blocky.asserts().assertBlockyElements();
    }

    @Test
    public void SMP_T673_blockyRemoved_when_saveTennisRankingBlocky_and_clickRemoveButton() {
        blockyData = TennisRankingBlockyFieldsFactory.buildMandatoryFields(blockyData);

        blocky.fillForm(blockyData)
                .saveBlock()
                .removeResult();

        blocky.validateBlockyNotExist();
    }
}