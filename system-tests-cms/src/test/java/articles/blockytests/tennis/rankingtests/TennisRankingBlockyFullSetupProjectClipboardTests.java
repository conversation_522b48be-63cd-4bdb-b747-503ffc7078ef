package articles.blockytests.tennis.rankingtests;

import blockies.tennis.rankingblocky.TennisRankingBlocky;
import categories.*;
import core.CmsWebTest;
import data.constants.WidgetBlock;
import data.models.blockymodels.tennis.TennisRankingBlockyFieldsModel;
import factories.articles.ArticleFieldsFactory;
import factories.blockies.tennis.TennisRankingBlockyFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED, browser = Browser.CHROME)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.BLOCKIES)
@Tag(SMPSports.TENNIS)
@Tag(SportBlocky.TENNIS_RANKING)
@Tag(CMSTags.CLIPBOARD)
@Story(CMSStories.SINGLEEVENT_BLOCKY)
public class TennisRankingBlockyFullSetupProjectClipboardTests extends CmsWebTest {

    private TennisRankingBlocky blocky;
    private TennisRankingBlockyFieldsModel blockyData;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.openCreatePage();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.addBlocky(WidgetBlock.TENNIS_RANKING);

        blocky = articleFormPage.mainSection().getTennisRankingBlocks().get(0);
        blocky.waitEditScreenToLoad();

        blockyData = tennisApiFacade.getRankingBlockySportData();
        blockyData = TennisRankingBlockyFieldsFactory.buildAllFields(blockyData);
    }

    @Test
    public void SMP_T673_htmlCodeCopied_when_saveTennisRankingBlocky_and_clickCopyEmbedCodeButton() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        blocky.asserts().assertCopiedEmbedCode(blockyData);
    }

    @Test
    public void SMP_T694_validateTennisRankingBody_when_saveArticle() {
        blocky.fillForm(blockyData)
                .saveBlock()
                .copyEmbedCode();

        articleFormPage.setRequiredFields(articleFormModel)
                .saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        blocky.asserts().assertBodyObjectInArticleCreateRequest(blockyData, articleFormPage.getArticleCreateRequest().getBody().get(0));
    }
}