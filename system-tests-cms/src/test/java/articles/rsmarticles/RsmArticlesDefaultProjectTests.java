package articles.rsmarticles;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.RSM_ARTICLES)
@Story(CMSStories.RSM_ARTICLES)
public class RsmArticlesDefaultProjectTests extends CmsWebTest {

    @Test
    public void rsmArticlesAreHiddenInNavigationBar() {
        rsmArticlesPage.asserts().validateRsmArticleIsNotDisplayedInNavigationBar();
    }

    @Test
    @DisplayName("Verify RSM Articles are hidden when external origin is not set for Articles")
    public void rsmArticlesAreHidden_when_externalOriginIsNotSet() {
        articleFormPage.open();

        Assertions.assertNotNull(rsmArticlesPage.getArticlesWithoutFilters());
    }
}