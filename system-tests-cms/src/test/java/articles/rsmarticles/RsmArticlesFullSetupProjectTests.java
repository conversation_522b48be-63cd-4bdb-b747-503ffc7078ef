package articles.rsmarticles;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.CategoryEnum;
import data.constants.ContentTypeEnum;
import data.constants.StringConstants;
import data.models.articles.ArticleResponseModel;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.utils.StringUtils;
import data.widgets.options.enums.StatusTypeEnum;
import facades.ContentApiFacade;
import factories.articles.ArticlesHttpFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.advanced.grid.GridRow;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.RSM_ARTICLES)
@Story(CMSStories.RSM_ARTICLES)
public class RsmArticlesFullSetupProjectTests extends CmsWebTest {

    private static final String ORIGIN = StringUtils.capitalizeFirstLetter(StringConstants.ORIGIN_STRING);
    private final String rsmArticlesOriginId = new ContentApiFacade().getOrigin(StringConstants.RSM_ARTICLES_STRING, ContentTypeEnum.ARTICLES).getId();
    private final List<String> articleOriginIds = new ContentApiFacade().getOriginIds(ContentTypeEnum.ARTICLES,
            StringConstants.AI_ARTICLES_GENERATION_STRING, StringConstants.RSM_ARTICLES_STRING);

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        Category activeCategory = contentApiFacade.getActiveCategory();
        AuthorModel defaultAuthor = contentApiFacade.getDefaultAuthor();

        articleToBeCreated = ArticlesHttpFactory.buildDefaultArticle();
        articleToBeCreated.setTitle("[API] RSM Articles Title");
        articleToBeCreated.setStatus(StatusTypeEnum.ACTIVE.name().toLowerCase());
        articleToBeCreated.setCategoryId(activeCategory.getId());
        articleToBeCreated.setAuthors(List.of(defaultAuthor.getId()));
        articleToBeCreated.setOriginId(rsmArticlesOriginId);
        articleToBeCreated.setOriginalSourceUrl("https://sportal.bg/");
        articleToBeCreated.setSource("source");
        createdArticle = articlesHttpRepo.create(articleToBeCreated).getResult();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteArticle(createdArticle);
    }

    @Test
    public void rsmArticlesDisplayedNavigationBar() {
        rsmArticlesPage.asserts().validateRsmArticleDisplayedInNavigationBar();
    }

    @Test
    //@Issue("SFE-5864")
    public void rsmArticlesSuccessfullyDisplayedInNavigationBar_when_rsmArticleFeatureSetToTrue_and_correctRequestExecutedInRsmArticlesScreen() {
        rsmArticlesPage.browser().refresh();
        rsmArticlesPage.open();

        rsmArticlesPage.asserts().pageLoaded();
        Assertions.assertNotNull(rsmArticlesPage.getArticlesByRsmOrigin(rsmArticlesOriginId));
    }

    @Test
    //@Issue("SFE-5864")
    public void allArticlesSuccessfullyRequestExecuted_when_rsmArticleFeatureSetToTrue_and_correctRequestExecutedInAllArticlesScreen() {
        rsmArticlesPage.open();

        rsmArticlesPage.asserts().pageLoaded();

        articleFormPage.open();

        Assertions.assertNotNull(rsmArticlesPage.getArticlesExcludingOrigin(articleOriginIds));
    }

    @Test
    public void createButtonNotDisplay_when_openRsmArticlesPage() {
        rsmArticlesPage.open();

        rsmArticlesPage.asserts().validateCreateNewArticleButtonNotDisplayed();
    }

    @Test
    public void counterDisplaysCorrectNumberOfNewRsmArticles_when_rsmArticleCreated() {
        app().browser().waitForAjax();
        Wait.forMilliseconds(1000);
        app().browser().refresh();

        int actualCount = Integer.parseInt(rsmArticlesPage.map().rsmArticlesCounter().getText());
        long expectedCount = articlesHttpRepo.getAll().getResult().stream()
                .filter(article -> article.getOrigin() != null
                        && article.getOrigin().getName() != null
                        && article.getOrigin().getName().equals(StringConstants.RSM_ARTICLES_STRING))
                .count();

        Assertions.assertEquals(expectedCount, actualCount, "RSM Articles counter shows incorrect number of articles");
        Assertions.assertTrue(actualCount > 0, "RSM Articles counter should show at least one article");
    }

    @Test
    public void newLabelDisplayed_when_createdNewRsmArticle() {
        app().browser().refresh();
        rsmArticlesPage.open();
        app().browser().refresh();

        rsmArticlesPage.asserts().validateNewLabelDisplayed();
    }

    @Test
    public void rsmArticleEditedSuccessfully_when_editArticle() {
        String expectedUpdatedTitle = "%s %s".formatted(StringConstants.UPDATED_STRING, createdArticle.getTitle());
        String expectedUpdatedCategory = CategoryEnum.BASKETBALL.getName();

        app().browser().refresh();
        rsmArticlesPage.open();
        rsmArticlesPage.editArticle(createdArticle.getId());
        app().browser().waitForAjax();
        articleFormPage.setRequiredFields(expectedUpdatedTitle, expectedUpdatedCategory);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateUpdateSuccessful();
        ArticleResponseModel updatedArticle = articleFormPage.getArticleUpdateResponse();

        Assertions.assertNotNull(updatedArticle, AssertMessages.requestNotCreated(StringConstants.ARTICLE_STRING));
        Assertions.assertEquals(expectedUpdatedTitle, updatedArticle.getTitle(), AssertMessages.entityNotExpected(StringConstants.TITLE_STRING));
        Assertions.assertEquals(expectedUpdatedCategory, updatedArticle.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(createdArticle.getStrapline(), updatedArticle.getStrapline(), AssertMessages.entityNotExpected("strap line"));
        Assertions.assertEquals(StringConstants.ARTICLE_STRING, updatedArticle.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));
    }

    @Test
    public void rsmArticleDeletedSuccessfully_when_deleteArticle() {
        app().browser().refresh();
        rsmArticlesPage.open();
        app().browser().refresh();
        rsmArticlesPage.deleteArticle(createdArticle.getId());

//        rsmArticlesPage.map().alertMessage().validateDeleteSuccessfulFor("Article");
        rsmArticlesPage.asserts().validateDeleteSuccessful(createdArticle.getTitle());
        createdArticle = null;
    }

    @Test
    public void originValueRedirectToRespectiveUrl_when_clickOriginValue_and_sourceIsSet() {
        rsmArticlesPage.browser().refresh();
        rsmArticlesPage.open();
        rsmArticlesPage.browser().refresh();

        GridRow row = rsmArticlesPage.map().articlesRsmListGrid().getRow(0);

        row.getCell(ORIGIN).validateTextIs(articleToBeCreated.getSource());
        row.getCell(ORIGIN).click();
        rsmArticlesPage.browser().switchToLastTab();
        String expectedUrl = rsmArticlesPage.browser().getUrl();
        rsmArticlesPage.browser().getWrappedDriver().close();
        rsmArticlesPage.browser().switchToFirstBrowserTab();

        Assertions.assertEquals(expectedUrl, articleToBeCreated.getOriginalSourceUrl(),
                "User not redirected to correct url");
    }

    @Test
    public void originValueNotClickable_when_clickOriginValue_and_sourceNotSet() {
        int expectedTabCount = 1;

        articleToBeCreated.setOriginalSourceUrl(null);
        createdArticle = articlesHttpRepo.create(articleToBeCreated).getResult();
        Wait.forMilliseconds(1000);

        rsmArticlesPage.browser().refresh();
        rsmArticlesPage.open();
        rsmArticlesPage.browser().refresh();

        rsmArticlesPage.map().articlesRsmListGrid().getRows()
                .stream()
                .filter(r -> r.getTitle().equals(createdArticle.getTitle()))
                .findFirst()
                .ifPresent(r -> {
                    r.getCell(ORIGIN).validateTextIs(articleToBeCreated.getSource());
                    r.getCell(ORIGIN).click();

                    Assertions.assertEquals(expectedTabCount, rsmArticlesPage.browser().getWrappedDriver().getWindowHandles().size(),
                            "Original source URL is clickable");
                });
    }

    @Test
    public void originalValueNotDisplayed_when_originalSource_and_sourceNotSet() {
        int expectedTabCount = 1;

        articleToBeCreated.setOriginalSourceUrl(null);
        articleToBeCreated.setSource(null);
        createdArticle = articlesHttpRepo.create(articleToBeCreated).getResult();
        Wait.forMilliseconds(1000);

        rsmArticlesPage.open();
        rsmArticlesPage.browser().refresh();

        rsmArticlesPage.map().articlesRsmListGrid().getRows()
                .stream()
                .filter(r -> r.getTitle().equals(createdArticle.getTitle()))
                .findFirst()
                .ifPresent(r -> {
                    r.getCell(ORIGIN).validateTextIs(StringConstants.EMPTY_STRING);
                    r.getCell(ORIGIN).click();

                    Assertions.assertEquals(expectedTabCount, rsmArticlesPage.browser().getWrappedDriver().getWindowHandles().size(),
                            "Original source URL is clickable");
                });
    }
}