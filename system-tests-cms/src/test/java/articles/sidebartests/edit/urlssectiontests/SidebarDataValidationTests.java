package articles.sidebartests.edit.urlssectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.ContentApiUrl;
import data.constants.PlaceholderField;
import data.constants.enums.football.FootballEntity;
import data.models.articles.ArticleModel;
import data.models.articles.ArticleResponseModel;
import facades.ContentApiFacade;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.DEFAULT, project = Project.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.URLS_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.URLS)
public class SidebarDataValidationTests extends BaseSidebarTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        CREATED_ARTICLE.set(new ContentApiFacade().createArticle(FootballEntity.NONE));
        articleFormPage.openEditPageForArticle(CREATED_ARTICLE.get().getId());
        articleFormPage.collapseOpenedSections();
        articleFormPage.urlsSection().expand();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteArticle(CREATED_ARTICLE.get());
    }

    @Test
    public void articleSaved_when_notFilledUrls() {
        articleFormPage.urlsSection().map().externalUrlField().validateTextContains("");
        articleFormPage.urlsSection().map().statusSelect().validateTextContains("");
        articleFormPage.urlsSection().map().canonicalUrlField().validateTextContains("");

        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateUpdateSuccessful();
    }

    @Test
    public void expectedPlaceholdersDisplayed_when_openUrlsSection() {
        var expectedExternalUrlPlaceholder = "URL to external resource";
        var expectedTypeRedirectPlaceholder = PlaceholderField.SELECT.getValue();
        var expectedCanonicalUrlPlaceholder = "URL to the primary resource";
        var expectedUrlDesktopPlaceholder = "URL for desktop";
        var expectedUrlMobilePlaceholder = "URL for mobile";
        var expectedUrlAmpPlaceholder = "URL for AMP";
        var externalUrlPlaceholder = articleFormPage.urlsSection().map().externalUrlField().getPlaceholder();
        var typeRedirectPlaceholder = articleFormPage.urlsSection().map().statusSelect().getText();
        var canonicalUrlPlaceholder = articleFormPage.urlsSection().map().canonicalUrlField().getPlaceholder();
        var UrlDesktopPlaceholder = articleFormPage.urlsSection().map().desktopUrlField().getPlaceholder();
        var UrlMobilePlaceholder = articleFormPage.urlsSection().map().mobileUrlField().getPlaceholder();
        var UrlAmpPlaceholder = articleFormPage.urlsSection().map().ampUrlField().getPlaceholder();

        Assertions.assertEquals(expectedExternalUrlPlaceholder, externalUrlPlaceholder, "The expected placeholder is not equal to the actual one!");
        Assertions.assertEquals(expectedTypeRedirectPlaceholder, typeRedirectPlaceholder, "The expected placeholder is not equal to the actual one!");
        Assertions.assertEquals(expectedCanonicalUrlPlaceholder, canonicalUrlPlaceholder, "The expected placeholder is not equal to the actual one!");
        Assertions.assertEquals(expectedUrlDesktopPlaceholder, UrlDesktopPlaceholder, "The expected placeholder is not equal to the actual one!");
        Assertions.assertEquals(expectedUrlMobilePlaceholder, UrlMobilePlaceholder, "The expected placeholder is not equal to the actual one!");
        Assertions.assertEquals(expectedUrlAmpPlaceholder, UrlAmpPlaceholder, "The expected placeholder is not equal to the actual one!");
    }

    @Test
    public void expectedControlsDisplayed_when_expandUrlsSection() {
        articleFormPage.urlsSection().map().externalUrlField().validateIsVisible();
        articleFormPage.urlsSection().map().statusSelect().validateIsVisible();
        articleFormPage.urlsSection().map().canonicalUrlField().validateIsVisible();
        articleFormPage.urlsSection().map().desktopUrlField().validateIsVisible();
        articleFormPage.urlsSection().map().facebookDesktop().validateIsVisible();
        articleFormPage.urlsSection().map().twitterDesktop().validateIsVisible();
        articleFormPage.urlsSection().map().mobileUrlField().validateIsVisible();
        articleFormPage.urlsSection().map().facebookMobile().validateIsVisible();
        articleFormPage.urlsSection().map().twitterMobile().validateIsVisible();
        articleFormPage.urlsSection().map().ampUrlField().validateIsVisible();
        articleFormPage.urlsSection().map().facebookAmp().validateIsVisible();
        articleFormPage.urlsSection().map().twitterAmp().validateIsVisible();
    }

    @Test
    public void properOptionExist_when_openStatusList() {
        var expectedOptionsList = Arrays.asList("Permanent", "Temporary");
        var statusOptions = articleFormPage.urlsSection().map().statusSelect().getOptionsValues();

        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedOptionsList, statusOptions), "The actual options are not equal to the expected!");
    }

    @Test
    public void properDataExistsInContentApiRequest_when_populateEditableFields_and_save() {
        var expectedExternalUrl = "https://cmstest.com";
        var expectedRedirectType = "Permanent";
        var expectedCanonicalUrl = "https://cmssecondtest.com";

        articleFormPage.urlsSection().map().externalUrlField().setText(expectedExternalUrl);
        articleFormPage.urlsSection().map().statusSelect().selectOptionByText(expectedRedirectType);
        articleFormPage.urlsSection().map().canonicalUrlField().setText(expectedCanonicalUrl);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateUpdateSuccessful();

        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), ContentApiUrl.ARTICLES.url, HttpMethod.PATCH, 0);
        var request = ProxyServer.getRequestByUrl(ContentApiUrl.ARTICLES.url, HttpMethod.PATCH.toString(), ArticleModel.class);
        Assertions.assertEquals(expectedExternalUrl, request.getExternalUrl(), "The External Url is not equal to the expected one!");
        Assertions.assertEquals(expectedRedirectType.toLowerCase(), request.getSeo().getRedirectType(), "The Redirect Type is not equal to the expected one!");
        Assertions.assertEquals(expectedCanonicalUrl, request.getCanonicalUrl(), "The Canonical Url is not equal to the expected one!");
    }

    @Test
    public void properDataExistsInContentApiResponse_when_populateEditableFields_and_save() {
        var expectedExternalUrl = "https://cmstest.com";
        var expectedRedirectType = "Permanent";
        var expectedCanonicalUrl = "https://cmssecondtest.com";

        articleFormPage.urlsSection().map().externalUrlField().setText(expectedExternalUrl);
        articleFormPage.urlsSection().map().statusSelect().selectOptionByText(expectedRedirectType);
        articleFormPage.urlsSection().map().canonicalUrlField().setText(expectedCanonicalUrl);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateUpdateSuccessful();

        var expectedUrl = ContentApiUrl.ARTICLES.url + "/" + getContentIdFromUrl();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), expectedUrl, HttpMethod.PATCH, 0);
        var response = ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.PATCH.toString(), ArticleResponseModel.class);
        Assertions.assertEquals(expectedExternalUrl, response.getUrls().getExternalUrl(), "The External Url is not equal to the expected one!");
        Assertions.assertEquals(expectedRedirectType.toLowerCase(), response.getSeo().getRedirectType(), "The Redirect Type is not equal to the expected one!");
        Assertions.assertEquals(expectedCanonicalUrl, response.getUrls().getCanonicalUrl(), "The Canonical Url is not equal to the expected one!");
    }

    @Test
    public void socialMediaUrlFieldsNotEditable_when_openUrlSection() {
        articleFormPage.urlsSection().map().desktopUrlField().validateIsDisabled();
        articleFormPage.urlsSection().map().mobileUrlField().validateIsDisabled();
        articleFormPage.urlsSection().map().ampUrlField().validateIsDisabled();
    }
}
