package articles.sidebartests.edit.tagssectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.AssertMessages;
import data.constants.CleanupBehaviorEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.football.FootballEntity;
import data.constants.enums.tennis.TennisCompetitionName;
import data.widgets.options.enums.DataGenderEnum;
import data.widgets.options.enums.DataPlayerEnum;
import facades.ContentApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.cleanupservice.DataCleanupBehavior;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@DataCleanupBehavior(cleanupBehavior = CleanupBehaviorEnum.AFTER_EACH)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSStories.RELATED)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.TAGS)
public class EditArticleTennisRelatedRequestValidationTests extends BaseSidebarTests {

    private static final String PLAYER_NAME = DataPlayerEnum.NOVAK_DJOKOVIC.getNameEn();
    private static final String COMPETITION_NAME = TennisCompetitionName.US_OPEN.getName();
    private static final String EXPECTED_SPORT = SupportedSports.TENNIS.getValue();
    private static final String EXPECTED_PROVIDER = StringConstants.SPORTS_SEARCH_API_STRING;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        CREATED_ARTICLE.set(new ContentApiFacade().createArticle(FootballEntity.NONE));
        articleFormPage.openEditPageForArticle(CREATED_ARTICLE.get().getId());
        articleFormPage.generalSection().collapse();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().map().sportSelect().searchSelectByText(SupportedSports.TENNIS.getTitle());
    }

    @Test
    public void SMP_T554_properRelatedPostResponseReceived_when_selectSportConnectionPlayer() {
        articleFormPage.tagsSection().filterSportsConnections(PLAYER_NAME);
        articleFormPage.saveArticle();

        articleRelated = articleFormPage.getArticleRelatedRequest().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(EXPECTED_PROVIDER, articleRelated.getProvider(),
                        AssertMessages.entityNotExpected("provider", EXPECTED_PROVIDER, articleRelated.getProvider())),
                () -> Assertions.assertEquals(StringConstants.PLAYER_STRING, articleRelated.getType(),
                        AssertMessages.entityNotExpected("type", StringConstants.PLAYER_STRING, articleRelated.getType())),
                () -> Assertions.assertEquals(StringConstants.PLAYER_STRING, articleRelated.getData().getEntityType(),
                        AssertMessages.entityNotExpected("entity type", StringConstants.PLAYER_STRING, articleRelated.getData().getEntityType())),
                () -> Assertions.assertEquals(PLAYER_NAME, articleRelated.getData().getName(),
                        AssertMessages.entityNotExpected("name", PLAYER_NAME, articleRelated.getData().getName())),
                () -> Assertions.assertEquals(EXPECTED_SPORT, articleRelated.getData().getSport(),
                        AssertMessages.entityNotExpected("sport", EXPECTED_SPORT, articleRelated.getData().getSport())),
                () -> Assertions.assertEquals(DataGenderEnum.MALE.name(), articleRelated.getData().getGender(),
                        AssertMessages.entityNotExpected("gender", DataGenderEnum.MALE.name(), articleRelated.getData().getGender()))
        );
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectSportConnectionTeam() {
        String expectedName = "Australia Green";
        String expectedGender = "MIXED";

        articleFormPage.tagsSection().filterSportsConnections(expectedName);
        articleFormPage.saveArticle();

        articleRelated = articleFormPage.getArticleRelatedRequest().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(EXPECTED_PROVIDER, articleRelated.getProvider(),
                        AssertMessages.entityNotExpected("provider", EXPECTED_PROVIDER, articleRelated.getProvider())),
                () -> Assertions.assertEquals(StringConstants.TEAM_STRING, articleRelated.getType(),
                        AssertMessages.entityNotExpected("type", StringConstants.TEAM_STRING, articleRelated.getType())),
                () -> Assertions.assertEquals(StringConstants.TEAM_STRING, articleRelated.getData().getEntityType(),
                        AssertMessages.entityNotExpected("entity type", StringConstants.TEAM_STRING, articleRelated.getData().getEntityType())),
                () -> Assertions.assertEquals(expectedName, articleRelated.getData().getName(),
                        AssertMessages.entityNotExpected("name", expectedName, articleRelated.getData().getName())),
                () -> Assertions.assertEquals(EXPECTED_SPORT, articleRelated.getData().getSport(),
                        AssertMessages.entityNotExpected("sport", EXPECTED_SPORT, articleRelated.getData().getSport())),
                () -> Assertions.assertEquals(expectedGender, articleRelated.getData().getGender(),
                        AssertMessages.entityNotExpected("gender", expectedGender, articleRelated.getData().getGender()))
        );
    }

    @Test
    public void SMP_T554_properRelatedPostResponseReceived_when_selectSportConnectionCompetition() {
        articleFormPage.tagsSection().filterSportsConnections(COMPETITION_NAME);
        articleFormPage.saveArticle();

        articleRelated = articleFormPage.getArticleRelatedRequest().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(EXPECTED_PROVIDER, articleRelated.getProvider(),
                        AssertMessages.entityNotExpected("provider", EXPECTED_PROVIDER, articleRelated.getProvider())),
                () -> Assertions.assertEquals(StringConstants.COMPETITION_STRING, articleRelated.getType(),
                        AssertMessages.entityNotExpected("type", StringConstants.COMPETITION_STRING, articleRelated.getType())),
                () -> Assertions.assertEquals(StringConstants.COMPETITION_STRING, articleRelated.getData().getEntityType(),
                        AssertMessages.entityNotExpected("entity type", StringConstants.COMPETITION_STRING, articleRelated.getData().getEntityType())),
                () -> Assertions.assertEquals(COMPETITION_NAME, articleRelated.getData().getName(),
                        AssertMessages.entityNotExpected("name", COMPETITION_NAME, articleRelated.getData().getName())),
                () -> Assertions.assertEquals(EXPECTED_SPORT, articleRelated.getData().getSport(),
                        AssertMessages.entityNotExpected("sport", EXPECTED_SPORT, articleRelated.getData().getSport()))
        );
    }
}