package articles.sidebartests.edit.tagssectiontests;

import articles.sidebartests.BaseCustomEntitiesSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.AssertMessages;
import data.constants.CleanupBehaviorEnum;
import data.constants.StringConstants;
import data.constants.enums.CustomEntityEnum;
import data.constants.enums.football.FootballEntity;
import facades.ContentApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.cleanupservice.DataCleanupBehavior;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSStories.RELATED)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.TAGS)
public class EditArticleCustomEntitiesRelatedRequestValidationTests extends BaseCustomEntitiesSidebarTests {

    @Override
    protected void beforeEach() {
        CREATED_ARTICLE.set(new ContentApiFacade().createArticle(FootballEntity.NONE));
        ProxyServer.newHar();
        articleFormPage.openEditPageForArticle(CREATED_ARTICLE.get().getId());
        articleFormPage.collapseOpenedSections();
        tagsSection = articleFormPage.tagsSection();
        tagsSection.expand();
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectPersonEntityType() {
        tagsSection.selectDomain(domain.getName())
                .selectEntityType(CustomEntityEnum.PERSON.getValue())
                .selectCustomEntitiesConnections(personAllFields.getName())
                .asserts()
                .assertValuesInCustomEntityConnectionsSelect(CustomEntityEnum.PERSON, personAllFields.getName());

        articleFormPage.setRequiredFields(ARTICLE_REQUIRED_FIELDS)
                .saveArticle();

        articleRelated = articleFormPage.getArticleRelatedResponse().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(CustomEntityEnum.PERSON.getValue().toLowerCase(), articleRelated.getType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.TYPE_STRING)),
                () -> Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelated.getProvider(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.PROVIDER_STRING)),
                () -> Assertions.assertEquals(personAllFields.getId(), articleRelated.getData().getId(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ID_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(personAllFields.getName(), articleRelated.getData().getName(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.NAME_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(personAllFields.getEntityType(), articleRelated.getData().getEntityType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ENTITY_TYPE_STRING, StringConstants.DATA_STRING))
        );
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectOrganizationEntityType() {
        tagsSection.selectDomain(domain.getName())
                .selectEntityType(CustomEntityEnum.ORGANIZATION.getValue())
                .selectCustomEntitiesConnections(organizationRequiredFields.getName())
                .asserts()
                .assertValuesInCustomEntityConnectionsSelect(CustomEntityEnum.ORGANIZATION, organizationRequiredFields.getName());

        articleFormPage.setRequiredFields(ARTICLE_REQUIRED_FIELDS)
                .saveArticle();

        articleRelated = articleFormPage.getArticleRelatedResponse().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(CustomEntityEnum.ORGANIZATION.getValue().toLowerCase(), articleRelated.getType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.TYPE_STRING)),
                () -> Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelated.getProvider(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.PROVIDER_STRING)),
                () -> Assertions.assertEquals(organizationRequiredFields.getId(), articleRelated.getData().getId(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ID_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(organizationRequiredFields.getName(), articleRelated.getData().getName(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.NAME_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(organizationRequiredFields.getEntityType(), articleRelated.getData().getEntityType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ENTITY_TYPE_STRING, StringConstants.DATA_STRING))
        );
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectPlaceEntityType() {
        tagsSection.selectDomain(domain.getName())
                .selectEntityType(CustomEntityEnum.PLACE.getValue())
                .selectCustomEntitiesConnections(placeRequiredFields.getName())
                .asserts()
                .assertValuesInCustomEntityConnectionsSelect(CustomEntityEnum.PLACE, placeRequiredFields.getName());

        articleFormPage.setRequiredFields(ARTICLE_REQUIRED_FIELDS)
                .saveArticle();

        articleRelated = articleFormPage.getArticleRelatedResponse().get(0);

        Assertions.assertAll(
                () -> Assertions.assertEquals(CustomEntityEnum.PLACE.getValue().toLowerCase(), articleRelated.getType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.TYPE_STRING)),
                () -> Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelated.getProvider(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.PROVIDER_STRING)),
                () -> Assertions.assertEquals(placeRequiredFields.getId(), articleRelated.getData().getId(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ID_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(placeRequiredFields.getName(), articleRelated.getData().getName(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.NAME_STRING, StringConstants.DATA_STRING)),
                () -> Assertions.assertEquals(placeRequiredFields.getEntityType(), articleRelated.getData().getEntityType(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.ENTITY_TYPE_STRING, StringConstants.DATA_STRING))
        );
    }
}