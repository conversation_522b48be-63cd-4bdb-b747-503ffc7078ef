package articles.sidebartests.create.tagssectiontests;

import articles.sidebartests.BaseSidebarTests;
import blockies.text.ParagraphBlock;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.google.common.reflect.TypeToken;
import data.constants.AssertMessages;
import data.constants.CmsPage;
import data.constants.SupportedSports;
import data.models.autotaging.TagResponseObject;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static data.constants.enums.basketball.BasketballTeamEnum.MIAMI_HEAT;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.E2E)
@Tag(CMSTags.CRITICAL_FUNCTIONALITY)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSTags.CRITICAL_FUNCTIONALITY)
public class AutoTaggingBasketballTests extends BaseSidebarTests {

    private static final String EXPECTED_BASKETBALL_TEAM = MIAMI_HEAT.getName();
    private static final String PARAGRAPH_TEXT = "Miami Heat brilliant technocrats micromanaged their way to the NBA title This Boston Celtics team, " +
            "while not quite as luminously talented as peak Stephen Curry Golden State Warriors, have much of their predecessors' " +
            "machine-like air of inevitability But the peaks of Stephen Curry, Kevin Durant, and even LeBron James have passed; " +
            "the NBA's old generation is finally, seemingly, drifting into history, its work done, the legends assured. " +
            "The new generation is here, and the technocrats of Boston Celtics look set to master its performance model for years to come.";
    private static final List<String> EXPECTED_TAGS = List.of(
            "Golden State Warriors", "The new", "Stephen Curry", "LeBron James", "Boston Celtics", "NBA", "Miami Heat", "Kevin Durant");
    private static final List<String> EXPECTED_TAGS_EXTENDED = List.of(
            "Golden State Warriors", "The New Oval", "Stephen Curry", "LeBron James", "Boston Celtics", "NBA", "Miami Heat", "Kevin Durant");

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.sidebar().navigateTo(CmsPage.ARTICLES);
        articleListPage.createNewArticle();
        articleFormPage.handleDraftDetectedStartFromScratch();
        articleFormPage.generalSection().collapse();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().selectSport(SupportedSports.BASKETBALL.getTitle());
    }

    @Test
    public void tagsSuggestionsDisplayedParagraphList_when_typeTextInParagraph() {
        int expectedLabels = 8;
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());
        List<String> firstTagsLabels = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();

        Assertions.assertEquals(expectedLabels, firstTagsLabels.size(), AssertMessages.lessExpectedTags());
        Assertions.assertTrue(CollectionUtils.isEqualCollection(firstTagsLabels, EXPECTED_TAGS),
                AssertMessages.tagsSuggestionsNotIncludingExpected(EXPECTED_TAGS_EXTENDED, firstTagsLabels));
    }

    @Test
    public void tagSuggestionsIncludesAtLeastOneOptionParagraphList_when_typeTextInParagraph() {
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        Assertions.assertAll(
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(0)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(0))),
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(1)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(1))),
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(2)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(2)))
        );
    }

    @Test
    public void properApiRequestCreated_when_typeTextInParagraph() {
        int expectedEntitiesCount = 1;
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);

        Type responseTypeToken = new TypeToken<Map<String, List<TagResponseObject>>>() {
        }.getType();
        Map<String, List<TagResponseObject>> response = Objects.requireNonNull(
                ProxyServer.getResponseByUrl(AUTO_TAGGING_API_URL, HttpMethod.POST.toString(), responseTypeToken));

        List<String> actualTagStrings = response.values()
                .stream()
                .toList()
                .get(0)
                .stream().map(x -> x.getEntities().get(0).getName())
                .toList();

        var actualBasketballTags = response.values()
                .stream()
                .toList()
                .get(0)
                .get(response.values().stream().toList().get(0).size() - 1)
                .getEntities();

        Assertions.assertAll(
                () -> Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTagStrings),
                        AssertMessages.tagsNotProperlyDisplayed(EXPECTED_TAGS_EXTENDED, actualTagStrings)),
                () -> Assertions.assertTrue(actualBasketballTags.stream().anyMatch(tag -> tag.getName().equals(DataPlayerEnum.KEVIN_DURANT.getNameEn())),
                        AssertMessages.tagNotFound(actualBasketballTags.stream().toList())),
                () -> Assertions.assertTrue(actualBasketballTags.stream().anyMatch(tag -> tag.getEntityType().equals("player")),
                        AssertMessages.expectedEntityTypeNotFound("Miami Heats", "team", actualBasketballTags.stream().toList())),
                () -> Assertions.assertTrue(actualBasketballTags.stream().anyMatch(tag -> tag.getSport().equals("basketball")),
                        AssertMessages.expectedSportNotFound("basketball", "venue", actualBasketballTags.stream().toList())),
                () -> Assertions.assertEquals(expectedEntitiesCount, actualBasketballTags.size(),
                        AssertMessages.unexpectedNumberOfSuggestions("Kevin Durant"))
        );
    }

    @Test
    public void properEntitiesFromApiResponseDisplayedParagraphList_when_typeTextInParagraph() {
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        Type responseTypeToken = new TypeToken<Map<String, List<TagResponseObject>>>() {
        }.getType();
        Map<String, List<TagResponseObject>> response = Objects.requireNonNull(
                ProxyServer.getResponseByUrl(AUTO_TAGGING_API_URL, HttpMethod.POST.toString(), responseTypeToken));
        List<String> actualTags = response.values().stream().toList().get(0).stream().map(x -> x.getEntities().get(0).getName()).toList();

        Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTags),
                AssertMessages.apiTagsDisplayMismatch(EXPECTED_TAGS_EXTENDED, actualTags));
    }

    @Test
    public void textLinked_when_clickTagSuggestionFromParagraphList() {
        String expectedLinkedText = EXPECTED_BASKETBALL_TEAM;
        String expectedId = MIAMI_HEAT.getId();
        String expectedHref = "https://temp-team.com/basketball/team-%s".formatted(expectedId);
        String expectedDataType = "team";
        String expectedClass = "autolink-enabled";

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();
        paragraph.insertSuggestion(expectedLinkedText);

        app().browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        Anchor linkedAnchor = paragraph.linkedTextList()
                .stream()
                .filter(e -> e.getText().contains(expectedLinkedText))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Linked text not found"));

        Assertions.assertAll(
                () -> Assertions.assertNotNull(linkedAnchor, AssertMessages.noLinkedTextFound()),
                () -> Assertions.assertEquals(expectedHref, linkedAnchor.getHref(),
                        AssertMessages.anchorHrefNotExpected(expectedHref, linkedAnchor.getHref())),
                () -> Assertions.assertEquals(expectedLinkedText, linkedAnchor.getText(),
                        AssertMessages.anchorTextNotExpected(expectedLinkedText, linkedAnchor.getText())),
                () -> Assertions.assertEquals(expectedDataType, linkedAnchor.getAttribute("data-resource-type"),
                        AssertMessages.anchorDataTypeNotExpected(expectedDataType, linkedAnchor.getAttribute("data-resource-type"))),
                () -> Assertions.assertEquals(expectedId, linkedAnchor.getAttribute("data-resource-id"),
                        AssertMessages.anchorIdNotExpected(expectedId, linkedAnchor.getAttribute("data-resource-id"))),
                () -> Assertions.assertTrue(linkedAnchor.getHtmlClass().contains(expectedClass),
                        AssertMessages.anchorClassNotExpected(expectedClass)));
    }

    @Test
    public void allKeywordsLinkedInText_when_clickAddAllFromParagraphList() {
        String expectedLinkedTeam = EXPECTED_BASKETBALL_TEAM;
        int expectedLinkedWordsCount = 1;
        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();

        paragraph.textEditor().setText(PARAGRAPH_TEXT);
        paragraph.refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !paragraph.firstTagsSuggestionsButtons().isEmpty());

        int suggestionsCount = paragraph.firstTagsSuggestionsButtons().size();
        paragraph.addAllTagsButton().click();
        app().browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        List<String> linkedAnchors = paragraph.getLinkedTextLabels();

        Assertions.assertEquals(suggestionsCount, new HashSet<>(linkedAnchors).size(),
                AssertMessages.tagsNotAppliedAsExpected(suggestionsCount, new HashSet<>(linkedAnchors).size()));
        Assertions.assertEquals(expectedLinkedWordsCount, linkedAnchors.stream().filter(e -> e.contains(expectedLinkedTeam)).count(),
                AssertMessages.entityNotExpected("Expected tag was not present.", expectedLinkedTeam, paragraph.getTagsSuggestions()));
    }

    @Test
    public void onlyFirstOccurrencesLinkedInText_when_clickAddFirstOccurrencesFromParagraphList() {
        String expectedLinkedTeam = EXPECTED_BASKETBALL_TEAM;
        int expectedLinkedWordsCount = 2;
        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();

        paragraph.textEditor().setText(PARAGRAPH_TEXT);
        paragraph.refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !paragraph.firstTagsSuggestionsButtons().isEmpty());

        int suggestionsCount = paragraph.firstTagsSuggestionsButtons().size();
        paragraph.addFirstTagsButton().click();
        app().browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        List<String> linkedAnchors = paragraph.getLinkedTextLabels();

        Assertions.assertEquals(suggestionsCount, new HashSet<>(linkedAnchors).size(),
                AssertMessages.tagsNotAppliedAsExpected(suggestionsCount, new HashSet<>(linkedAnchors).size()));
        Assertions.assertNotEquals(expectedLinkedWordsCount, linkedAnchors.stream().filter(e -> e.contains(expectedLinkedTeam)).count(),
                AssertMessages.entityNotExpected("Expected tag was not present.", expectedLinkedTeam, paragraph.getTagsSuggestions()));
    }

    @Test
    public void tagsSuggestionsParagraphListUpdated_when_clickRefreshButton() {
        String expectedLinkedPlayer = DataPlayerEnum.STEPHEN_CURRY.getNameEn();

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        app().browser().waitForRequest(AUTO_TAGGING_API_URL);
        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        List<String> suggestionTags = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();

        articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons()
                .stream()
                .filter(e -> e.getText().contains(expectedLinkedPlayer))
                .findFirst()
                .ifPresent(Button::click);

        articleFormPage.map().activeParagraph().additionalTagsDropDownName(expectedLinkedPlayer)
                .stream()
                .filter(e -> e.getText().contains(expectedLinkedPlayer))
                .findFirst()
                .ifPresent(Button::click);

        app().browser().waitUntil(f -> !articleFormPage.map().activeParagraph().linkedTextList().isEmpty());
        List<String> suggestionTagsAfterLink = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();

        Assertions.assertNotEquals(suggestionTags.size(), suggestionTagsAfterLink.size(),
                AssertMessages.tagNotRemovedFromSuggestions(suggestionTags.size(), suggestionTagsAfterLink.size()));
        Assertions.assertTrue(suggestionTagsAfterLink.stream().noneMatch(e -> e.contains(expectedLinkedPlayer)),
                AssertMessages.tagStillInSuggestions(expectedLinkedPlayer, articleFormPage.map().activeParagraph().getTagsSuggestions()));
    }
}