package articles.sidebartests.create.tagssectiontests;

import articles.sidebartests.BaseSidebarTests;
import blockies.text.ParagraphBlock;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.google.common.reflect.TypeToken;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.tennis.TennisCompetitionName;
import data.models.autotaging.TagEntity;
import data.models.autotaging.TagResponseObject;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.TAGS)
public class AutoTaggingTennisTests extends BaseSidebarTests {

    private static final String TENNIS_PLAYER = DataPlayerEnum.GRIGOR_DIMITROV.getNameEn();
    private static final String EXPECTED_TENNIS_PLAYER = DataPlayerEnum.ROGER_FEDERER.getNameEn();
    private static final String PARAGRAPH_TEXT = "It was never going to be easy for Andy Murray. In 2012, entering his " +
            "fifth final, Andy Murray had seen only two opponents across those championship matches: Novak Djokovic and " +
            "Roger Federer, twice each. It was Novak Djokovic again across the net in his second US Open final. " +
            "Andy Murray had won just one set in his previous four major finals, when he took the opener against Roger Federer " +
            "at in 2012 to lead for a fleeting moment. After that match, he addressed his home crowd in his " +
            "famous getting closer speech through tears on the field. We look back at the 2012 US Open men's singles final " +
            "between Andy Murray, Grigor Dimitrov and Novak Djokovic.";
    private static final List<String> EXPECTED_TAGS = Arrays.asList(
            "US Open", "Andy Murray", "Grigor Dimitrov", "Novak Djokovic", "Roger Federer");
    private static final List<String> EXPECTED_TAGS_EXTENDED = List.of(
            "US Open Cup", "Andy Murray", "Grigor Dimitrov", "Novak Djokovic", "Roger Federer");

    @Override
    protected void beforeEach() {
        articleFormPage.openCreatePage();
        ProxyServer.newHar();
        articleFormPage.generalSection().collapse();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().selectSport(SupportedSports.TENNIS.getTitle());
    }

    @Test
    public void tagsSuggestionsDisplayedParagraphList_when_typeTextInParagraph() {
        int expectedLabels = 5;

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());
        List<String> firstTagsLabels = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();

        Assertions.assertEquals(expectedLabels, firstTagsLabels.size(), AssertMessages.lessExpectedTags());
        Assertions.assertTrue(CollectionUtils.isEqualCollection(firstTagsLabels, EXPECTED_TAGS),
                AssertMessages.tagsSuggestionsNotIncludingExpected(EXPECTED_TAGS_EXTENDED, firstTagsLabels));
    }

    @Test
    public void tagSuggestionsIncludesAtLeastOneOptionParagraphList_when_typeTextInParagraph() {
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        Assertions.assertAll(
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(0)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(0))),
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(1)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(1))),
                () -> Assertions.assertFalse(articleFormPage.map().activeParagraph().additionalTagsSuggestionsButtons(EXPECTED_TAGS.get(2)).isEmpty(),
                        AssertMessages.specificSuggestionNotFound(EXPECTED_TAGS.get(2))));
    }

    @Test
    public void properApiRequestCreated_when_typeTextInParagraph() {
        int expectedEntitiesCount = 1;

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);

        Type responseTypeToken = new TypeToken<Map<String, List<TagResponseObject>>>() {
        }.getType();
        Map<String, List<TagResponseObject>> response = Objects.requireNonNull(
                ProxyServer.getResponseByUrl(AUTO_TAGGING_API_URL, HttpMethod.POST.toString(), responseTypeToken));
        List<String> actualTagStrings = response.values().stream().toList().get(0)
                .stream().map(x -> x.getEntities().get(0).getName()).toList();
        List<TagEntity> actualTennisTags = response.values().stream().toList().get(0)
                .get(response.values().stream().toList().get(0).size() - 1).getEntities();

        Assertions.assertAll(
                () -> Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTagStrings),
                        AssertMessages.tagsNotProperlyDisplayed(EXPECTED_TAGS_EXTENDED, actualTagStrings)),
                () -> Assertions.assertTrue(actualTennisTags.stream().anyMatch(tag -> tag.getName().equals(EXPECTED_TENNIS_PLAYER)),
                        AssertMessages.tagNotFound(actualTennisTags.stream().toList())),
                () -> Assertions.assertTrue(actualTennisTags.stream().anyMatch(tag -> tag.getEntityType().equals(StringConstants.PLAYER_STRING)),
                        AssertMessages.expectedEntityTypeNotFound(EXPECTED_TENNIS_PLAYER, StringConstants.TEAM_STRING, actualTennisTags.stream().toList())),
                () -> Assertions.assertTrue(actualTennisTags.stream().anyMatch(tag -> tag.getSport().equals(SupportedSports.TENNIS.getValue())),
                        AssertMessages.expectedSportNotFound(EXPECTED_TENNIS_PLAYER, StringConstants.VENUE_STRING, actualTennisTags.stream().toList())),
                () -> Assertions.assertEquals(expectedEntitiesCount, actualTennisTags.size(),
                        AssertMessages.unexpectedNumberOfSuggestions(EXPECTED_TENNIS_PLAYER)));
    }

    @Test
    public void properEntitiesFromApiResponseDisplayedParagraphList_when_typeTextInParagraph() {
        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        Type responseTypeToken = new TypeToken<Map<String, List<TagResponseObject>>>() {
        }.getType();
        Map<String, List<TagResponseObject>> response = Objects.requireNonNull(
                ProxyServer.getResponseByUrl(AUTO_TAGGING_API_URL, HttpMethod.POST.toString(), responseTypeToken));
        var actualTags = response.values().stream().toList().get(0)
                .stream().map(x -> x.getEntities().get(0).getName()).toList();

        Assertions.assertTrue(CollectionUtils.isEqualCollection(EXPECTED_TAGS_EXTENDED, actualTags),
                AssertMessages.apiTagsDisplayMismatch(EXPECTED_TAGS_EXTENDED, actualTags));
    }

    @Test
    public void textLinked_when_clickTagSuggestionFromParagraphList() {
        String expectedLinkedText = EXPECTED_TENNIS_PLAYER;
        String expectedId = DataPlayerEnum.ROGER_FEDERER.getId();
        String expectedHref = "https://temp-player.com/tennis/player-%s".formatted(expectedId);
        String expectedDataType = StringConstants.PLAYER_STRING;
        var expectedClass = "autolink-enabled";

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();
        paragraph.insertSuggestion(expectedLinkedText);

        articleFormPage.browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        Anchor linkedAnchor = paragraph.linkedTextList().stream()
                .filter(e -> e.getText().contains(expectedLinkedText))
                .findFirst()
                .orElseThrow(() -> new AssertionError(AssertMessages.noLinkedTextFound()));

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedHref, linkedAnchor.getHref(),
                        AssertMessages.anchorHrefNotExpected(expectedHref, linkedAnchor.getHref())),
                () -> Assertions.assertEquals(expectedLinkedText, linkedAnchor.getText(),
                        AssertMessages.anchorTextNotExpected(expectedLinkedText, linkedAnchor.getText())),
                () -> Assertions.assertEquals(expectedDataType, linkedAnchor.getAttribute("data-resource-type"),
                        AssertMessages.anchorDataTypeNotExpected(expectedDataType, linkedAnchor.getAttribute("data-resource-type"))),
                () -> Assertions.assertEquals(expectedId, linkedAnchor.getAttribute("data-resource-id"),
                        AssertMessages.anchorIdNotExpected(expectedId, linkedAnchor.getAttribute("data-resource-id"))),
                () -> Assertions.assertTrue(linkedAnchor.getHtmlClass().contains(expectedClass),
                        AssertMessages.anchorClassNotExpected(expectedClass)));
    }

    @Test
    public void allKeywordsLinkedInText_when_clickAddAllFromParagraphList() {
        String expectedLinkedTeam = TENNIS_PLAYER;
        int expectedLinkedWordsCount = 1;
        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();

        paragraph.textEditor().setText(PARAGRAPH_TEXT);
        paragraph.refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !paragraph.firstTagsSuggestionsButtons().isEmpty());

        int suggestionsCount = paragraph.firstTagsSuggestionsButtons().size();
        paragraph.addAllTagsButton().click();
        articleFormPage.browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        List<String> linkedAnchors = paragraph.getLinkedTextLabels();

        Assertions.assertEquals(suggestionsCount, new HashSet<>(linkedAnchors).size(),
                AssertMessages.tagsNotAppliedAsExpected(suggestionsCount, new HashSet<>(linkedAnchors).size()));
        Assertions.assertEquals(expectedLinkedWordsCount, linkedAnchors.stream().filter(e -> e.contains(expectedLinkedTeam)).count(),
                AssertMessages.entityNotExpected("Expected tag was not present.", expectedLinkedTeam, paragraph.getTagsSuggestions()));
    }

    @Test
    public void onlyFirstOccurrencesLinkedInText_when_clickAddFirstOccurrencesFromParagraphList() {
        String expectedLinkedTeam = TENNIS_PLAYER;
        int expectedLinkedWordsCount = 2;
        ParagraphBlock paragraph = articleFormPage.map().activeParagraph();

        paragraph.textEditor().setText(PARAGRAPH_TEXT);
        paragraph.refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !paragraph.firstTagsSuggestionsButtons().isEmpty());

        int suggestionsCount = paragraph.firstTagsSuggestionsButtons().size();
        paragraph.addFirstTagsButton().click();
        articleFormPage.browser().waitUntil(f -> !paragraph.linkedTextList().isEmpty());
        List<String> linkedAnchors = paragraph.getLinkedTextLabels();

        Assertions.assertEquals(suggestionsCount, new HashSet<>(linkedAnchors).size(),
                AssertMessages.tagsNotAppliedAsExpected(suggestionsCount, new HashSet<>(linkedAnchors).size()));
        Assertions.assertNotEquals(expectedLinkedWordsCount, linkedAnchors.stream().filter(e -> e.contains(expectedLinkedTeam)).count(),
                AssertMessages.entityNotExpected("Expected tag was not present.", expectedLinkedTeam, paragraph.getTagsSuggestions()));
    }

    @Test
    public void tagsSuggestionsParagraphListUpdated_when_clickRefreshButton() {
        String expectedLinkedTeam = TennisCompetitionName.US_OPEN.getName();

        articleFormPage.map().activeParagraph().textEditor().setText(PARAGRAPH_TEXT);
        articleFormPage.map().activeParagraph().refreshTags();
        articleFormPage.browser().waitForRequest(AUTO_TAGGING_API_URL);
        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().isEmpty());

        List<String> suggestionTags = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();
        articleFormPage.map().activeParagraph().firstTagsSuggestionsButtons().stream()
                .filter(e -> e.getText().contains(expectedLinkedTeam))
                .findFirst()
                .ifPresent(Button::click);

        articleFormPage.map().activeParagraph().insertAllOccurrencesDropDownButtons(expectedLinkedTeam).stream()
                .filter(e -> e.getText().contains(expectedLinkedTeam))
                .findFirst()
                .ifPresent(Button::click);

        articleFormPage.browser().waitUntil(f -> !articleFormPage.map().activeParagraph().linkedTextList().isEmpty());
        List<String> suggestionTagsAfterLink = articleFormPage.map().activeParagraph().getFirstTagsSuggestionsLabels();

        Assertions.assertNotEquals(suggestionTags.size(), suggestionTagsAfterLink.size(),
                AssertMessages.tagNotRemovedFromSuggestions(suggestionTags.size(), suggestionTagsAfterLink.size()));
        Assertions.assertTrue(suggestionTagsAfterLink.stream().noneMatch(e -> e.contains(expectedLinkedTeam)),
                AssertMessages.tagStillInSuggestions(expectedLinkedTeam, articleFormPage.map().activeParagraph().getTagsSuggestions()));
    }
}