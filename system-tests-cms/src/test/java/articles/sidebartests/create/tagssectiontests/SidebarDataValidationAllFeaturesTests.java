package articles.sidebartests.create.tagssectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.StringConstants;
import data.constants.enums.football.FootballTournamentEnum;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import sections.sidebarsections.tagssection.Map;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.TAGS)
public class SidebarDataValidationAllFeaturesTests extends BaseSidebarTests {

    private Map tagsSectionMap;
    private static final String EXPECTED_TOURNAMENTS = FootballTournamentEnum.LA_LIGA.getName();

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage.openCreatePage();
        articleFormPage.generalSection().collapse();
        articleFormPage.tagsSection().expand();
        tagsSectionMap = articleFormPage.tagsSection().map();
    }

    @Test
    public void footballSportSelectedByDefault_when_openTagsSection() {
        Assertions.assertEquals(StringConstants.SUPPORTED_SPORTS_LIST.get(0), articleFormPage.tagsSection().map().sportSelect().getText(),
                "The Football sport is not default value");
    }

    @Test
    public void expectedSportsDisplayed_when_openTagsSection_and_clickSportSelect() {
        List<String> expectedSports = StringConstants.SUPPORTED_SPORTS_LIST;
        List<String> listedSports = articleFormPage.tagsSection().map().sportSelect().getOptionsValues();

        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedSports, listedSports), "Not all expected sports are listed!");
    }

    @Test
    public void tagsSelectDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.tagsSelect().isVisible(), "Tags Select is not visible!");
        Assertions.assertEquals("Tags", tagsSectionMap.tagsSelect().getLabel().getText(), "Tags Select label is not the proper one!");
    }

    @Test
    public void sportSelectDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.sportSelect().isVisible(), "Sport Select is not visible!");
        Assertions.assertEquals("Sport", tagsSectionMap.sportSelect().getLabel().getText());
    }

    @Test
    public void footballConnectionsSelectDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.footballConnectionsSelect().isVisible(), StringConstants.FOOTBALL_CONNECTIONS + " Select is not visible!");
        Assertions.assertEquals(StringConstants.FOOTBALL_CONNECTIONS, tagsSectionMap.footballConnectionsSelect().getLabel().getText(),
                StringConstants.FOOTBALL_CONNECTIONS + " Select label is not the proper one!");
    }

    @Test
    public void tournamentSelectDisplayed_when_openTagsSection() {
        articleFormPage.browser().waitUntil(e -> tagsSectionMap.tournamentSelect().isVisible());
        Assertions.assertTrue(tagsSectionMap.tournamentSelect().isVisible(), "Tournament Select is not visible!");
        Assertions.assertEquals("Tournament", tagsSectionMap.tournamentSelect().getLabel().getText(),
                "Tournament Select label is not the proper one!");
    }

    @Test
    public void tournamentSeasonSelectDisplayed_when_openTagsSection() {
        tagsSectionMap.tournamentSelect().searchSelectByText(EXPECTED_TOURNAMENTS);
        articleFormPage.browser().waitUntil(e -> tagsSectionMap.seasonSelect().isVisible());
        Assertions.assertTrue(tagsSectionMap.seasonSelect().isVisible(), "Season Select is not visible!");
        Assertions.assertEquals("Season", tagsSectionMap.seasonSelect().getLabel().getText(),
                "Season Select label is not the proper one!");
    }

    @Test
    public void suggestedTagsListDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.suggestedTagsLabel().isVisible(), "Suggested entities Select is not visible!");
        Assertions.assertEquals("No suggested entities found(auto generated)", tagsSectionMap.suggestedTagsLabel().getText(),
                "Suggested entities Select label is not the proper one!");
    }

    @Test
    public void filterMatchesDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.filterMatchesExpandButton().isVisible(), "Filter Expand Button is not visible!");
    }

    @Test
    public void filterTournamentSelectDisplayed_when_openTagsSection() {
        tagsSectionMap.filterMatchesExpandButton().click();

        Assertions.assertTrue(tagsSectionMap.filterTournamentSelect().isVisible(), "Tournament Filter Select is not visible!");
        Assertions.assertEquals("Tournament", tagsSectionMap.filterTournamentSelect().getLabel().getText(),
                "Tournament Filter Select label is not the proper one!");
    }

    @Test
    public void filterTournamentSeasonSelectDisplayed_when_openTagsSection() {
        tagsSectionMap.filterMatchesExpandButton().click();

        tagsSectionMap.filterTournamentSelect().searchSelectByText(EXPECTED_TOURNAMENTS);
        articleFormPage.browser().waitUntil(e -> tagsSectionMap.filterTournamentSeasonSelect().isVisible());
        Assertions.assertEquals("Season", tagsSectionMap.filterTournamentSeasonSelect().getLabel().getText(),
                "Season Select label is not the proper one!");
    }

    @Test
    public void filterDatePickerDisplayed_when_openTagsSection() {
        tagsSectionMap.filterMatchesExpandButton().click();

        Assertions.assertTrue(tagsSectionMap.filterDatePickerFrom().isVisible(), "Date From Picker is not visible!");
        Assertions.assertTrue(tagsSectionMap.filterDatePickerTo().isVisible(), "Date To Picker is not visible!");
    }

    @Test
    public void filterClearButtonDisplayed_when_openTagsSection() {
        tagsSectionMap.filterMatchesExpandButton().click();

        Assertions.assertTrue(tagsSectionMap.filterClearButton().isVisible(), "Clear Filter Button is not visible!");
    }

    @Test
    public void matchResultsDisplayed_when_openTagsSection() {
        Assertions.assertTrue(tagsSectionMap.upcomingMatchesTabButton().isVisible(), "Upcoming Matches Tab is not visible!");
        Assertions.assertTrue(tagsSectionMap.pastMatchesTabButton().isVisible(), "Past Matches Tab is not visible!");
        Assertions.assertTrue(tagsSectionMap.interruptedMatchesTabButton().isVisible(), "Interrupted Matches Tab is not visible!");
    }
}