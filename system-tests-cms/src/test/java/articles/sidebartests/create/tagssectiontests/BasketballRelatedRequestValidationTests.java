package articles.sidebartests.create.tagssectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.models.related.DataListObject;
import data.widgets.options.enums.DataGenderEnum;
import data.widgets.options.enums.DataPlayerEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.TAGS_SECTION)
@Story(CMSStories.RELATED)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.TAGS)
public class BasketballRelatedRequestValidationTests extends BaseSidebarTests {

    private static final String PLAYER_NAME = DataPlayerEnum.LEBRON_JAMES.getNameEn();
    private static final String TEAM_NAME = DataTeamEnum.BOSTON_CELTICS.getFullName();
    private static final String EXPECTED_SPORT = DataWidgetSportEnum.BASKETBALL.getValue();
    private List<DataListObject> articleRelatedResponse;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();
        articleFormPage.tagsSection().expand();
        articleFormPage.tagsSection().map().sportSelect().searchSelectByText(SupportedSports.BASKETBALL.getTitle());
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectSportConnectionPlayer() {
        articleFormPage.tagsSection().filterSportsConnections(PLAYER_NAME);
        articleFormPage.setRequiredFields(DEFAULT_TITLE, DEFAULT_MAIN_CATEGORY);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        articleRelatedResponse = articleFormPage.getArticleRelatedResponse();

        Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelatedResponse.get(0).getProvider(),
                "The provider is not the expected one!");
        Assertions.assertEquals(StringConstants.PLAYER_STRING, articleRelatedResponse.get(0).getType(),
                "The type is not the expected one!");
        Assertions.assertEquals(StringConstants.PLAYER_STRING, articleRelatedResponse.get(0).getData().getEntityType(),
                "The entity type is not the expected one!");
        Assertions.assertEquals(PLAYER_NAME, articleRelatedResponse.get(0).getData().getName(),
                "The name is not the expected one!");
        Assertions.assertEquals(EXPECTED_SPORT, articleRelatedResponse.get(0).getData().getSport(),
                "The sport is not the expected one!");
        Assertions.assertEquals(DataGenderEnum.MALE.name(), articleRelatedResponse.get(0).getData().getGender(),
                "The name is not the expected one!");
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectSportConnectionTeam() {
        String expectedObjectType = StringConstants.CLUB_STRING.toUpperCase();

        articleFormPage.tagsSection().filterSportsConnections(TEAM_NAME);
        articleFormPage.setRequiredFields(DEFAULT_TITLE, DEFAULT_MAIN_CATEGORY);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        articleRelatedResponse = articleFormPage.getArticleRelatedResponse();

        Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelatedResponse.get(0).getProvider(),
                "The provider is not the expected one!");
        Assertions.assertEquals(StringConstants.TEAM_STRING, articleRelatedResponse.get(0).getType(),
                "The type is not the expected one!");
        Assertions.assertEquals(expectedObjectType, articleRelatedResponse.get(0).getData().getType(),
                "The type is not the expected one!");
        Assertions.assertEquals(StringConstants.TEAM_STRING, articleRelatedResponse.get(0).getData().getEntityType(),
                "The entity type is not the expected one!");
        Assertions.assertEquals(TEAM_NAME, articleRelatedResponse.get(0).getData().getName(),
                "The name is not the expected one!");
        Assertions.assertEquals(EXPECTED_SPORT, articleRelatedResponse.get(0).getData().getSport(),
                "The sport is not the expected one!");
        Assertions.assertEquals(DataGenderEnum.MALE.name(), articleRelatedResponse.get(0).getData().getGender(),
                "The name is not the expected one!");
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectSportConnectionVenue() {
        String expectedName = "Emirates Arena";

        articleFormPage.tagsSection().filterSportsConnections(expectedName);
        articleFormPage.setRequiredFields(DEFAULT_TITLE, DEFAULT_MAIN_CATEGORY);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        articleRelatedResponse = articleFormPage.getArticleRelatedResponse();

        Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelatedResponse.get(0).getProvider(),
                "The provider is not the expected one!");
        Assertions.assertEquals(StringConstants.ARENA_STRING, articleRelatedResponse.get(0).getType(),
                "The type is not the expected one!");
        Assertions.assertEquals(StringConstants.ARENA_STRING, articleRelatedResponse.get(0).getData().getEntityType(),
                "The entity type is not the expected one!");
        Assertions.assertEquals(expectedName, articleRelatedResponse.get(0).getData().getName(),
                "The name is not the expected one!");
        Assertions.assertEquals(EXPECTED_SPORT, articleRelatedResponse.get(0).getData().getSport(),
                "The sport is not the expected one!");
    }

    @Test
    public void properRelatedPostResponseReceived_when_selectSportConnectionCompetition() {
        String expectedName = BasketballCompetitionName.NBA.name();

        articleFormPage.tagsSection().filterSportsConnections(expectedName);
        articleFormPage.setRequiredFields(DEFAULT_TITLE, DEFAULT_MAIN_CATEGORY);
        articleFormPage.saveArticle();

        createdArticle = articleFormPage.getArticleCreateResponse();
        articleRelatedResponse = articleFormPage.getArticleRelatedResponse();

        Assertions.assertEquals(StringConstants.SPORTS_SEARCH_API_STRING, articleRelatedResponse.get(0).getProvider(),
                "The provider is not the expected one!");
        Assertions.assertEquals(StringConstants.COMPETITION_STRING, articleRelatedResponse.get(0).getType(),
                "The type is not the expected one!");
        Assertions.assertEquals(StringConstants.COMPETITION_STRING, articleRelatedResponse.get(0).getData().getEntityType(),
                "The entity type is not the expected one!");
        Assertions.assertEquals(expectedName, articleRelatedResponse.get(0).getData().getName(),
                "The name is not the expected one!");
        Assertions.assertEquals(EXPECTED_SPORT, articleRelatedResponse.get(0).getData().getSport(),
                "The sport is not the expected one!");
    }
}