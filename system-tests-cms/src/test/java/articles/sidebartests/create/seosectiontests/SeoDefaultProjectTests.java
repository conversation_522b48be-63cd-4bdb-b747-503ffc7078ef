package articles.sidebartests.create.seosectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.github.javafaker.Faker;
import data.constants.AssertMessages;
import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.articles.ArticleResponseModel;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.DEFAULT, project = Project.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.SEO_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.CONFIGURATION)
public class SeoDefaultProjectTests extends BaseSidebarTests {

    @Override
    protected void beforeEach() {
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        articleFormPage.generalSection().expand();
        ProxyServer.newHar();
    }

    // SEO Title
    @Test
    //@Issue("SFE-5770")
    public void articleSaved_when_copySeoCheckboxUnchecked_and_emptySeoTitle() {
        var expectedSeoTitle = "";
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoTitle, articleRequest.getSeo().getTitle(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", expectedSeoTitle));
    }

    @Test
    public void SMP_T597_seoTitleFilled_when_fillTitleField_and_copySeoCheckboxChecked() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().seoTitleTextField().validateTextContains(articleFormModel.getTitle());
    }

    @Test
    public void SMP_T597_seoTitleCanBeEdited_when_copySeoCheckboxUnchecked() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().seoTitleTextField().validateTextContains(articleFormModel.getTitle());
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();

        var modifiedSeoTitle = articleFormPage.seoSection().map().seoTitleTextField().getText() + "_modified";
        articleFormPage.seoSection().map().seoTitleTextField().setText(modifiedSeoTitle);

        articleFormPage.seoSection().map().seoTitleTextField().validateTextContains(modifiedSeoTitle);
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoTitle_and_save() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();

        var modifiedSeoTitle = articleFormPage.seoSection().map().seoTitleTextField().getText() + "_modified";
        articleFormPage.seoSection().map().seoTitleTextField().setText(modifiedSeoTitle);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(modifiedSeoTitle, articleRequest.getSeo().getTitle(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", modifiedSeoTitle));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoTitleWithUnicodeCharacters_and_save() {
        var expectedSeoTitle = "SEO Заглавие :) … ☀";

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();

        articleFormPage.seoSection().map().seoTitleTextField().setText(expectedSeoTitle);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoTitle, articleRequest.getSeo().getTitle(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", expectedSeoTitle));
    }

    @Test
    public void properDataExistsInContentApiResponse_when_changeSeoTitle_and_reopenArticle() {
        var expectedSeoTitle = "SEO Заглавие :) … ☀";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();

        articleFormPage.seoSection().map().seoTitleTextField().setText(expectedSeoTitle);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        String articleId = getContentIdFromUrl();
        var expectedUrl = ContentApiUrl.ARTICLES.url + "/" + articleId;
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);
        createdArticle = ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), ArticleResponseModel.class);
        Assertions.assertEquals(expectedSeoTitle, createdArticle.getSeo().getTitle(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", expectedSeoTitle));
    }

    @Test
    public void properTextDisplayed_when_changeSeoTitle_and_reopenArticle() {
        var expectedSeoTitle = "SEO Заглавие :) … ☀";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();

        articleFormPage.seoSection().map().seoTitleTextField().setText(expectedSeoTitle);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.seoSection().expand();
        var actualSeoTitle = articleFormPage.seoSection().map().seoTitleTextField().getText();
        Assertions.assertEquals(expectedSeoTitle, actualSeoTitle);
    }

    @Test
    public void seoTitleNotUpdated_when_uncheckSeoTitle_and_createArticle_and_updateTitle() {
        String unexpectedUpdatedTitle = StringConstants.EDIT_STRING;
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();

        createdArticle = articleFormPage.getArticleCreateResponse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();
        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.setTitle(unexpectedUpdatedTitle);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().expand();
        var actualSeoTitle = articleFormPage.seoSection().map().seoTitleTextField().getText();
        var actualSeoSlug = articleFormPage.seoSection().map().slugTextField().getText();
        Assertions.assertNotEquals(unexpectedUpdatedTitle, actualSeoTitle, "SEO Title incorrectly changed when Title is changed");
        Assertions.assertNotEquals(unexpectedUpdatedTitle, actualSeoSlug, "SEO slug incorrectly changed when Title is changed");
        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsUnchecked();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsUnchecked();
    }

    // SEO Description
    @Test
    public void articleSaved_when_emptySeoDescription() {
        var expectedSeoDescription = "";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().uncheck();
        articleFormPage.seoSection().map().seoDescriptionTextField().validateTextContains(expectedSeoDescription);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoDescription, articleRequest.getSeo().getDescription(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", expectedSeoDescription));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoDescription_and_save() {
        var expectedSeoDescription = "SEO Test Text";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoDescriptionTextField().setText(expectedSeoDescription);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoDescription, articleRequest.getSeo().getDescription(), String.format("The Post request doesn't contain the expected SEO title '%1$s'!", expectedSeoDescription));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoDescriptionWithUnicodeCharacters_and_save() {
        var expectedSeoDescription = "SEO Текст :) … ☀";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoDescriptionTextField().setText(expectedSeoDescription);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoDescription, articleRequest.getSeo().getDescription(), String.format("The Post request doesn't contain the expected SEO description '%1$s'!", expectedSeoDescription));
    }

    @Test
    public void properDataExistsInContentApiResponse_when_changeSeoDescription_and_reopenArticle() {
        var expectedSeoDescription = "SEO Заглавие :) … ☀";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoDescriptionTextField().setText(expectedSeoDescription);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();

        var expectedUrl = ContentApiUrl.ARTICLES.url + "/" + getContentIdFromUrl();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);

        createdArticle = ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), ArticleResponseModel.class);
        Assertions.assertEquals(expectedSeoDescription, createdArticle.getSeo().getDescription(), String.format("The Post request doesn't contain the expected SEO description '%1$s'!", expectedSeoDescription));
    }

    @Test
    public void SMP_T597_properTextDisplayed_when_changeSeoDescription_and_reopenArticle() {
        var expectedSeoDescription = "SEO Заглавие :) … ☀";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoDescriptionTextField().setText(expectedSeoDescription);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().expand();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var actualSeoDescription = articleFormPage.seoSection().map().seoDescriptionTextField().getText();
        Assertions.assertEquals(expectedSeoDescription, actualSeoDescription);
    }

    // SEO Keywords
    @Test
    public void articleSaved_when_emptySeoKeywords() {
        var expectedSeoKeyword = "";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().seoKeyWordsTextField().validateListContains(expectedSeoKeyword);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(articleRequest.getSeo().getKeywords().isEmpty(), String.format("The Post request doesn't contain the expected SEO keyword '%1$s'!", expectedSeoKeyword));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoKeywords_and_save() {
        var expectedSeoKeyword = "SEOTest";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoKeyWordsTextField().setText(expectedSeoKeyword);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(articleRequest.getSeo().getKeywords().contains(expectedSeoKeyword), String.format("The Post request doesn't contain the expected SEO keyword '%1$s'!", expectedSeoKeyword));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSeoKeywordsWithUnicodeCharacters_and_save() {
        var expectedSeoKeyword = "SEO Заглавие :) … ☀";
        var secondExpectedSeoKeyword = "SEOTest";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoKeyWordsTextField().setText(expectedSeoKeyword);
        articleFormPage.seoSection().map().seoKeyWordsTextField().setText(secondExpectedSeoKeyword);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var currentUrl = app().browser().getUrl();
        String articleId = currentUrl.substring(currentUrl.lastIndexOf("/") + 1);
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.ARTICLES.url + "/" + articleId, HttpMethod.GET, 2);
        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(createdArticle.getSeo().getKeywords().contains(expectedSeoKeyword), String.format("The Post request doesn't contain the expected SEO keyword '%1$s'!", expectedSeoKeyword));
        Assertions.assertTrue(createdArticle.getSeo().getKeywords().contains(secondExpectedSeoKeyword), String.format("The Post request doesn't contain the expected SEO keyword '%1$s'!", expectedSeoKeyword));
    }

    @Test
    public void SMP_T597_properTextDisplayed_when_changeSeoKeywords_and_reopenArticle() {
        var expectedSeoKeyword = "SEO Заглавие :) … ☀";
        var secondExpectedSeoKeyword = "SEOTest";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoKeyWordsTextField().setText(expectedSeoKeyword);
        articleFormPage.seoSection().map().seoKeyWordsTextField().setText(secondExpectedSeoKeyword);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().seoKeyWordsTextField().validateListContains(expectedSeoKeyword);
        articleFormPage.seoSection().map().seoKeyWordsTextField().validateListContains(secondExpectedSeoKeyword);
    }

    // SEO slug
    @Test
    public void SMP_T593_articleSaved_when_useAutoGeneratedSeoSlug() {
        var expectedSeoSlug = articleFormModel.getTitle().toLowerCase().replaceAll("['!,.]", "").replace(" ", "-");
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().slugTextField().validateTextContains(expectedSeoSlug);
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoSlug, articleRequest.getSeo().getSlug(), String.format("The Post request doesn't contain the expected SEO slug '%1$s'!", expectedSeoSlug));
    }

    @Test
    public void generateAutomaticallyFromSeoTitleCheckboxIsCheckedByDefault_when_open_and_saveArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();

        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSlug(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SLUG_STRING));
    }

    @Test
    public void copySeoTitleCheckboxIsCheckedByDefault_when_open_and_saveArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();

        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSlug(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SEO_TITLE_STRING));
    }

    @Test
    public void copySeoTitleAndGenerateAutomaticallyFromSeoTitleAreCheckedByDefault_when_savingArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();

        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSlug(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SLUG_STRING));
        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSeoTitle(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SEO_TITLE_STRING));
    }

    @Test
    public void copyArticleTitleToSeoTitle_when_CopySeoTitleCheckboxIsChecked_and_saveArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();

        createdArticle = articleFormPage.getArticleCreateResponse();

        var expectedSeoTitle = createdArticle.getTitle();

        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSeoTitle(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SEO_TITLE_STRING));
        Assertions.assertEquals(expectedSeoTitle, createdArticle.getSeo().getTitle(), AssertMessages.seoTitleDoesNotMatchArticleTitle(expectedSeoTitle, createdArticle.getSeo().getTitle()));
    }

    @Test
    public void generateSlugAutomaticallyBasedOnSeoTitle_when_generateAutomaticallyFromSeoTitleIsChecked_and_saveArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().check();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        createdArticle = articleFormPage.getArticleCreateResponse();

        String expectedSlug = createdArticle.getTitle().toLowerCase().replaceAll("[^a-z0-9]+", "-").replaceAll("^-|-$", "");

        Assertions.assertEquals(expectedSlug, createdArticle.getSeo().getSlug(), AssertMessages.slugDoesNotMatch(expectedSlug, createdArticle.getSeo().getSlug()));
        Assertions.assertTrue(createdArticle.getSeo().getAutomaticSlug(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SLUG_STRING));
    }

    @Test
    public void SMP_T597_slugCanBeEdited_when_generateSeoTitleCheckboxUnchecked() {
        var expectedSeoSlug = "new-slug";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.seoSection().map().slugTextField().setText(expectedSeoSlug);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(expectedSeoSlug, articleRequest.getSeo().getSlug(), String.format("The Post request doesn't contain the expected SEO slug '%1$s'!", expectedSeoSlug));
    }

    @Test
    public void properDataExistsInContentApiRequest_when_fillSlugWithUnicodeCharacters_and_save() {
        var testSeoSlug = "нов-линк";
        var expectedSeoSlug = "nov-link";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.seoSection().map().slugTextField().setText(testSeoSlug);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertNotSame(testSeoSlug, expectedSeoSlug, "The Cyrillic SEO slug should be transformed, but it is not!");
        Assertions.assertEquals(expectedSeoSlug, articleRequest.getSeo().getSlug(), String.format("The Post request doesn't contain the expected SEO slug '%1$s'!", expectedSeoSlug));
    }

    @Test
    @Disabled
    @Tag(SMPCategories.DISABLED)
    public void properDataExistsInContentApiResponse_when_changeSlug_and_reopenArticle() {
        var expectedSeoSlug = "new-slug";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.seoSection().map().slugTextField().setText(expectedSeoSlug);
        articleFormPage.saveArticle();
        app().browser().waitForRequest("related");

        var expectedUrl = ContentApiUrl.ARTICLES.url + "/" + getContentIdFromUrl();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);
        createdArticle = ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), ArticleResponseModel.class);

        Assertions.assertTrue(createdArticle.getSeo().getSlug().contains(expectedSeoSlug), String.format("The Get Response doesn't contain the expected SEO slug '%1$s'!", expectedSeoSlug));
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().slugTextField().validateTextContains(expectedSeoSlug);
    }

    @Test
    public void slugTrimmed_when_typeNotAllowedSpecialCharactersInSlug_and_save() {
        var testSeoSlug = "Special[^,?{}[]///«/\\ characters test"; //`¬
        var expectedSeoSlug = "special-characters-test";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.seoSection().map().slugTextField().setText(testSeoSlug);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertNotSame(testSeoSlug, expectedSeoSlug, "The Cyrillic SEO slug should be transformed, but it is not!");
        Assertions.assertEquals(expectedSeoSlug, articleRequest.getSeo().getSlug(), String.format("The Post request doesn't contain the expected SEO slug '%1$s'!", expectedSeoSlug));
    }

    // SEO checkboxes
    @Test
    public void SMP_T593_articleSaved_when_uncheckAllSeoCheckboxes() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.seoSection().map().allowIndexingCheckbox().uncheck();
        articleFormPage.seoSection().map().allowFollowingCheckbox().uncheck();
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertFalse(articleRequest.getSeo().getIndex(), "The Post request doesn't contain the expected state of the checkboxes!");
        Assertions.assertFalse(articleRequest.getSeo().getFollow(), "The Post request doesn't contain the expected state of the checkboxes!");
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsUnchecked();
        articleFormPage.seoSection().map().allowIndexingCheckbox().validateIsUnchecked();
        articleFormPage.seoSection().map().allowFollowingCheckbox().validateIsUnchecked();
    }

    @Test
    public void properDataExistsInContentApiRequest_when_checkAllSeoCheckboxes_and_save() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().allowIndexingCheckbox().check();
        articleFormPage.seoSection().map().allowFollowingCheckbox().check();
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(articleRequest.getSeo().getIndex(), "The Post request doesn't contain the expected state of the checkboxes!");
        Assertions.assertTrue(articleRequest.getSeo().getFollow(), "The Post request doesn't contain the expected state of the checkboxes!");
    }

    @Test
    public void properOptionDisplayed_when_checkAllSeoCheckboxes_and_reopenArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().allowIndexingCheckbox().check();
        articleFormPage.seoSection().map().allowFollowingCheckbox().check();
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().allowIndexingCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().allowFollowingCheckbox().validateIsChecked();
    }

    // SEO JSONLD
    @Test
    public void articleSaved_when_emptyJsonLdSchema() {
        var expectedJsonLd = "";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().jsonLdTextField().validateTextContains(expectedJsonLd);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(articleRequest.getSeo().getJsonld().isEmpty(), String.format("The Post request doesn't contain the expected SEO JSONLD '%1$s'!", expectedJsonLd));
    }

    @Test
    public void errorMessageDisplayed_when_fillInvalidJsonSchema() {
        var invalidJsonLd = "test";
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().jsonLdTextField().setText(invalidJsonLd);
        articleFormPage.saveArticle(false);

//        articleFormPage.map().alertMessage().validateSaveUnsuccessful();
        createdArticle = articleFormPage.getArticleCreateResponse();
        Assertions.assertEquals("VALIDATION_ERROR", createdArticle.getType(), String.format("The Post request doesn't contain the expected SEO JSONLD '%1$s'!", invalidJsonLd));
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().jsonLdTextField().setText("");
        articleFormPage.map().alertMessage().toNotBeVisible();
    }

    @Test
    public void SMP_T597_properDataExistsInContentApiRequest_when_fillValidJsonSchema_and_save() {
        var firstString = new Faker().harryPotter().character();
        var secondString = new Faker().harryPotter().book();
        var validJsonLd = String.format("{\"@character\": \"1%s\", \"book\": \"2%s\"}", firstString, secondString);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().jsonLdTextField().setText(validJsonLd);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(articleRequest.getSeo().getJsonld().contains(validJsonLd), "The Post request doesn't contain the expected state of the checkboxes!");
    }

    @Test
    public void properDataExistsInContentApiResponse_when_fillValidJsonSchema_and_reopenArticle() {
        var firstString = new Faker().harryPotter().character();
        var secondString = new Faker().harryPotter().book();
        var validJsonLd = String.format("{\"@character\": \"1%s\", \"book\": \"2%s\"}", firstString, secondString);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().jsonLdTextField().setText(validJsonLd);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().jsonLdTextField().validateTextContains(validJsonLd);
    }

    @Test
    public void slugChangedBasedOnSeoTitle_when_articleTitleChanged_and_generateSlugCheckboxIsCheck() {
        String updatedTitle = StringConstants.EDIT_STRING;

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.setTitle(updatedTitle);
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().expand();
        String actualSeoSlug = articleFormPage.seoSection().map().slugTextField().getText();

        Assertions.assertEquals(updatedTitle, actualSeoSlug, "SEO slug incorrectly changed when Title is changed");
    }

    @Test
    public void slugNotChangedBasedOnSeoTitle_when_articleTitleChanged_and_generateSlugCheckboxIsUnCheck() {
        String updatedTitle = StringConstants.EDIT_STRING;

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.setTitle(updatedTitle);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        articleFormPage.seoSection().expand();
        String actualSeoSlug = articleFormPage.seoSection().map().slugTextField().getText();

        Assertions.assertEquals(articleFormModel.getTitle().toLowerCase().replace(" ", "-"), actualSeoSlug, "SEO slug incorrectly changed when Title is changed");
    }

    @Test
    //@Issue("SFE-5770")
    public void generateSeoSlugCheckboxRemainsUnchecked_after_saveArticleWithEditedTitle_and_reopenArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.setTitle(StringConstants.EDIT_STRING);
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsUnchecked();

        Assertions.assertFalse(articleFormPage.getArticleCreateResponse().getSeo().getAutomaticSlug(),
                AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SLUG_STRING));
    }

    @Test
    public void generateSeoSlugCheckboxUnchecked_after_saveArticleWithNothingEdited_and_reopenArticle() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().generateSlugCheckbox().uncheck();
        articleFormPage.saveArticle();
//        articleFormPage.map().alertMessage().validateSaveSuccessful();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.generalSection().collapse();
        articleFormPage.seoSection().expand();

        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsUnchecked();
        Assertions.assertFalse(articleFormPage.getArticleCreateResponse().getSeo().getAutomaticSlug(),
                AssertMessages.valueOfPropertyNotCorrect(StringConstants.AUTOMATIC_SLUG_STRING));
    }

    @Test
    public void seoTitleAndSlugCheckboxesAreChecked_when_createArticleAfterStartFromScratch_and_saveArticle() {
        articleFormPage.mainSection().getParagraphBlocks().get(0)
                .textEditor()
                .setText(EXISTING_LIST_1)
                .enter();

        articleFormPage.browser().refresh();
        articleFormPage.map().draftDetectedMessage().startFromScratch();

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();
        articleFormPage.waitForArticleCreationRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        articleFormPage.seoSection().expand();
        articleFormPage.seoSection().map().copySeoTitleCheckbox().validateIsChecked();
        articleFormPage.seoSection().map().generateSlugCheckbox().validateIsChecked();

        Assertions.assertAll(
                () -> Assertions.assertTrue(articleFormPage.seoSection().map().copySeoTitleCheckbox().isChecked(),
                        "SEO title checkbox should be checked"),
                () -> Assertions.assertTrue(articleFormPage.seoSection().map().generateSlugCheckbox().isChecked(),
                        "Generate slug checkbox should be checked")
        );
    }
}