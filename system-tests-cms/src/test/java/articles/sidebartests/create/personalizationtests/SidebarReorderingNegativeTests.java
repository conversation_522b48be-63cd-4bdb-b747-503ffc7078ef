package articles.sidebartests.create.personalizationtests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.CmsPage;
import data.constants.SidebarApiUrl;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.DEFAULT, project = Project.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Story(CMSStories.PERSONALIZATION)
public class SidebarReorderingNegativeTests extends BaseSidebarTests {

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        ProxyServer.clearblockRequestList();
        articleFormPage.openCreatePage();
        articleFormPage.waitPageToLoad();
        handleSaveSidebarSettingsModal_cancel();
    }

    @Test
    public void SMP_T691_sidebarNotDisplayed_when_notSuccessfulGetRequest() {
        ProxyServer.newHar();
        var expectedUrl = SidebarApiUrl.CONFIGURATIONS_ADMIN.url;
        var patternUrl = ".*/sidebar/configurations/admin.*";
        ProxyServer.blockRequestByUrl(patternUrl, HttpMethod.GET);
        app().browser().refresh();
        app().browser().waitForPartialUrl(CmsPage.ARTICLES.href);
        articleFormPage.waitForPageLoad();

        articleFormPage.waitForSpinners();

        articleFormPage.map().alertMessage().toBeVisible();
        articleFormPage.map().alertMessage().validateTextContains("There was a problem fetching sidebar configuration.");

        ProxyServer.assertRequestNotMade(expectedUrl, HttpMethod.GET);

        var element = app().create().allByXPath(WebComponent.class, articleFormPage.generalSection().map().sectionDiv().getFindStrategy().getValue()).stream().findAny();
        Assertions.assertTrue(element.isEmpty(), "Sections are rendered, but it is not expected");
    }

    @Override
    public void afterEach() {
        // Overridden on purpose to skip the check for console logs in the base test class.
    }
}