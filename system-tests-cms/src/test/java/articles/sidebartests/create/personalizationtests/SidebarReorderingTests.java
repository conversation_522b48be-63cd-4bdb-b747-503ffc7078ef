package articles.sidebartests.create.personalizationtests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.configuration.SportalSettings;
import data.models.customizationapi.sidebar.SidebarRequestModel;
import data.models.customizationapi.sidebar.SidebarResponseModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.authentication.loginpage.LoginPage;
import plugins.authentication.projectselectpage.ProjectSelectPage;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.DEFAULT, project = Project.DEFAULT)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Story(CMSStories.PERSONALIZATION)
public class SidebarReorderingTests extends BaseSidebarTests {
    private final String toggleOffClass = "fa-toggle-off";
    private final String toggleOnClass = "fa-toggle-on";

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        ProxyServer.clearblockRequestList();
        articleFormPage.openCreatePage();
        articleFormPage.waitPageToLoad();
        handleSaveSidebarSettingsModal_cancel();
        articleFormPage.collapseOpenedSections();
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openGeneralSection() {
        var toggleStateClosed = articleFormPage.generalSection().map().toggle().getHtmlClass();

        articleFormPage.generalSection().expand();
        var toggleStateOpened = articleFormPage.generalSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openMediaSection() {
        var toggleStateClosed = articleFormPage.mediaSection().map().toggle().getHtmlClass();

        articleFormPage.mediaSection().expand();
        var toggleStateOpened = articleFormPage.mediaSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openTagsSection() {
        var toggleStateClosed = articleFormPage.tagsSection().map().toggle().getHtmlClass();

        articleFormPage.tagsSection().expand();
        var toggleStateOpened = articleFormPage.tagsSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openRelatedContentSection() {
        var toggleStateClosed = articleFormPage.relatedContentSection().map().toggle().getHtmlClass();

        articleFormPage.relatedContentSection().expand();
        var toggleStateOpened = articleFormPage.relatedContentSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openSeoSection() {
        var toggleStateClosed = articleFormPage.seoSection().map().toggle().getHtmlClass();

        articleFormPage.seoSection().expand();
        var toggleStateOpened = articleFormPage.seoSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openUrlsSection() {
        var toggleStateClosed = articleFormPage.urlsSection().map().toggle().getHtmlClass();

        articleFormPage.urlsSection().expand();
        var toggleStateOpened = articleFormPage.urlsSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openListSection() {
        var toggleStateClosed = articleFormPage.listSection().map().toggle().getHtmlClass();

        articleFormPage.listSection().expand();
        var toggleStateOpened = articleFormPage.listSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_openCustomDataSection() {
        var toggleStateClosed = articleFormPage.customDataSection().map().toggle().getHtmlClass();

        articleFormPage.customDataSection().expand();
        var toggleStateOpened = articleFormPage.customDataSection().map().toggle().getHtmlClass();

        Assertions.assertTrue(toggleStateClosed.contains(toggleOffClass), "The toggle class is not the expected one!");
        Assertions.assertTrue(toggleStateOpened.contains(toggleOnClass), "The toggle class is not the expected one!");
    }

    @Test
    public void SMP_T691_toggleButtonChangeStates_when_activateSideBarEditMode_and_deactivate() {
        var toggleStateDefault = articleFormPage.map().sidebarEditModeToggle().isActivated();

        articleFormPage.map().sidebarEditModeToggle().activate();
        var toggleStateActivated = articleFormPage.map().sidebarEditModeToggle().isActivated();

        articleFormPage.map().sidebarEditModeToggle().deactivate();
        var toggleStateDeactivated = articleFormPage.map().sidebarEditModeToggle().isActivated();

        Assertions.assertFalse(toggleStateDefault, "The Edit Mode toggle is not deactivated as expected!");
        Assertions.assertTrue(toggleStateActivated, "The Edit Mode toggle is not activated as expected!");
        Assertions.assertFalse(toggleStateDeactivated, "The Edit Mode toggle is not deactivated as expected!");
    }

    @Test
    public void SMP_T691_allSectionsSwitchedEditMode_when_activateSideBarEditMode() {
        articleFormPage.map().sidebarEditModeToggle().activate();

        articleFormPage.generalSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.generalSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.generalSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.mediaSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.mediaSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.mediaSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.tagsSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.tagsSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.tagsSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.relatedContentSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.relatedContentSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.relatedContentSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.seoSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.seoSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.seoSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.urlsSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.urlsSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.urlsSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.listSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.listSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.listSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");

        articleFormPage.customDataSection().map().sectionToggleButton().validateIsVisible();
        articleFormPage.customDataSection().map().editModeSectionDragIcon().validateIsVisible();
        Assertions.assertTrue(articleFormPage.customDataSection().map().sectionDiv().getHtmlClass().contains("opacity-06"), "The Section doesn't contain the expected class!");
    }

    @Test
    public void popupMessageDisplayed_when_hoverActivatedSideBarEditModeSwitch() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        articleFormPage.map().sidebarEditModeToggle().hover();

        Assertions.assertEquals("Sidebar edit mode on", articleFormPage.map().sidebarEditModeToggle().getTooltipText(), "The Sidebar Edit mode switch hover message is not the expected one!");
    }

    @Test
    public void popupMessageDisplayed_when_hoverDeactivatedSideBarEditModeSwitch() {
        articleFormPage.map().sidebarEditModeToggle().hover();

        Assertions.assertEquals("Sidebar edit mode off", articleFormPage.map().sidebarEditModeToggle().getTooltipText(), "The Sidebar Edit mode switch hover message is not the expected one!");
    }

    @Test
    public void SMP_T691_saveSettingsPopupDisplayed_when_repositionSection_and_deactivateSideBarEditMode() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        articleFormPage.generalSection().changePosition(2, true);
        articleFormPage.map().sidebarEditModeToggle().deactivate();

        Assertions.assertTrue(articleFormPage.map().sidebarChangesMessage().isDisplayed(), "The Sidebar save changes message is not displayed!");
    }

    @Disabled
    @Tag(SMPCategories.DISABLED)
    @Test
    public void SMP_T691_putRequestSend_when_saveSideBarChanges() {
        var expectedSectionsNumber = 9;
        var expectedResourcePage = "article";
        var expectedUrl = ConfigurationService.get(SportalSettings.class).getCustomizationApiUrl() + "sidebar/configurations/admin";
        articleFormPage.collapseOpenedSections();

        articleFormPage.map().sidebarEditModeToggle().activate();
        articleFormPage.generalSection().changePosition(2, true);
        var generalSectionMovedPosition = articleFormPage.generalSection().getCurrentPosition();
        articleFormPage.map().sidebarEditModeToggle().deactivate();
        articleFormPage.map().sidebarChangesMessage().saveChanges();
        articleFormPage.map().alertMessage().toBeVisible();

        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), expectedUrl, HttpMethod.PUT, 0);
        var expectedRequest = ProxyServer.getRequestByUrl(expectedUrl, HttpMethod.PUT.toString(), SidebarRequestModel.class);

        Assertions.assertEquals(ConfigurationService.get(SportalSettings.class).getApiDefaultProject(), expectedRequest.getKey().getProject());
        Assertions.assertEquals(expectedResourcePage, expectedRequest.getKey().getResource());
        Assertions.assertEquals(expectedSectionsNumber, expectedRequest.getSections().size());
        Assertions.assertEquals(generalSectionMovedPosition, expectedRequest.getSections().stream().filter(e -> e.getKey().contains("general")).findFirst().get().getWeight());
    }

    @Test
    public void toggleMessageDisplayed_when_saveSideBarChangesInPopup() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        articleFormPage.generalSection().changePosition(1, false);
        articleFormPage.map().sidebarEditModeToggle().deactivate();

        articleFormPage.map().sidebarChangesMessage().saveChanges();
        app().browser().waitUntil(e -> articleFormPage.map().alertMessage().isVisible());

        Assertions.assertTrue(articleFormPage.map().alertMessage().isVisible(), "The Sidebar save changes message is not displayed!");
        Assertions.assertEquals("Successfully saved sidebar settings", articleFormPage.map().alertMessage().getText(), "The alert message doesn't contain the expected message!");
    }

    @Disabled
    @Tag(SMPCategories.DISABLED)
    @Test
    public void SMP_T691_newSectionPositionSaved_when_changeSectionPosition_and_deactivateSideBarEditMode() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        var generalSectionFirstPosition = articleFormPage.generalSection().getCurrentPosition();
        articleFormPage.generalSection().changePosition(1, true);
        articleFormPage.map().sidebarEditModeToggle().deactivate();
        articleFormPage.map().sidebarChangesMessage().saveChanges();

        articleFormPage.map().sidebarEditModeToggle().activate();
        var generalSectionMovedPosition = articleFormPage.generalSection().getCurrentPosition();

        Assertions.assertNotSame(generalSectionFirstPosition, generalSectionMovedPosition, "The General Section is not moved!");
    }

    @Test
    public void SMP_T691_getRequestCustomizationApiSend_when_openContentPage() {
        app().browser().refresh();
        var expectedSectionsNumber = 9;
        var expectedResourcePage = "article";
        var expectedUrl = ConfigurationService.get(SportalSettings.class).getCustomizationApiUrl() + "sidebar/configurations/admin?";

        var actualResponse = ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), SidebarResponseModel.class);

        Assertions.assertEquals(ConfigurationService.get(SportalSettings.class).getApiDefaultProject(), actualResponse.getKey().getProject());
        Assertions.assertEquals(expectedResourcePage, actualResponse.getKey().getResource());
        Assertions.assertEquals(expectedSectionsNumber, actualResponse.getSections().size());
    }

    @Disabled
    @Tag(SMPCategories.DISABLED)
    @Test
    public void SMP_T628_newSectionPositionSaved_when_changeSectionPosition_and_refreshThePage() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        var generalSectionFirstPosition = articleFormPage.generalSection().getCurrentPosition();
        articleFormPage.generalSection().changePosition(1, false);
        var generalSectionMovedPosition = articleFormPage.generalSection().getCurrentPosition();
        articleFormPage.map().sidebarEditModeToggle().deactivate();
        articleFormPage.map().sidebarChangesMessage().saveChanges();
        articleFormPage.map().alertMessage().toBeVisible();

        app().browser().refresh();
        articleFormPage.map().sidebarEditModeToggle().activate();
        var generalSectionLastPosition = articleFormPage.generalSection().getCurrentPosition();

        Assertions.assertNotSame(generalSectionFirstPosition, generalSectionLastPosition, "The section's position is not changed, but it is expected to be!");
        Assertions.assertEquals(generalSectionMovedPosition, generalSectionLastPosition, "The section's position is not the expected one");
    }

    @Test
    @Disabled("Cannot fix the test - needs further investigation")
    @Tag(SMPCategories.DISABLED)
    //TODO: navramov 16/06/2023 fix test
    public void properSectionsPositionDisplayed_when_changeSectionPosition_and_changeUser() {
        articleFormPage.map().sidebarEditModeToggle().activate();
        int movePositions = 2;
        var generalSectionInitialPosition = articleFormPage.generalSection().getCurrentPosition();
        articleFormPage.generalSection().changePosition(movePositions, true);
        if (articleFormPage.generalSection().getCurrentPosition() == generalSectionInitialPosition) {
            app().browser().waitForAjax();
            articleFormPage.generalSection().changePosition(movePositions, true);
        }
        var generalSectionChangedPosition = articleFormPage.generalSection().getCurrentPosition();
        Assertions.assertNotEquals(generalSectionInitialPosition, generalSectionChangedPosition, "Position did not change after reordering!");

        articleFormPage.map().sidebarEditModeToggle().deactivate();
        articleFormPage.map().sidebarChangesMessage().saveChanges();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), "/sidebar/configurations/admin", HttpMethod.PUT, 0);
        articleFormPage.map().alertMessage().toBeVisible();

        app().browser().clearLocalStorage();
        app().browser().clearSessionStorage();
        app().browser().refresh();

        var loginPage = app().goTo(LoginPage.class);
        loginPage.login(User.FULLSETUP);
        var projectSelectPage = app().createPage(ProjectSelectPage.class);
        projectSelectPage.selectProject(getCurrentTestProject());
        articleFormPage.openCreatePage();
        articleFormPage.map().sidebarEditModeToggle().activate();
        var generalSectionOtherUserPosition = articleFormPage.generalSection().getCurrentPosition();

        Assertions.assertEquals((int)generalSectionChangedPosition, (int)generalSectionOtherUserPosition, "The section's position is not changed, but it is expected to be!");
    }
}
