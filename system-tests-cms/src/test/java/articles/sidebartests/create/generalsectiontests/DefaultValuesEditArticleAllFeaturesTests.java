package articles.sidebartests.create.generalsectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.google.gson.reflect.TypeToken;
import data.constants.ContentApiUrl;
import data.constants.enums.football.FootballEntity;
import data.models.authors.AuthorModel;
import data.models.common.CommonModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.GENERAL_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.CONFIGURATION)
public class DefaultValuesEditArticleAllFeaturesTests extends BaseSidebarTests {

    private volatile CommonModel commentPolicy;
    private volatile CommonModel articleOrigin;
    private volatile CommonModel distributionChannels;
    private volatile CommonModel distributionRegions;
    private volatile CommonModel articleTypes;
    private volatile AuthorModel firstAuthor;
    private volatile CommonModel articleStatuses;

    @Override
    protected void beforeEach() {
        createdArticle = contentApiFacade.createArticle(FootballEntity.NONE);
        articleFormPage.openEditPageForArticle(createdArticle.getId());
        articleFormPage.generalSection().expand();

        var responseListType = new TypeToken<ArrayList<CommonModel>>() {}.getType();
        ArrayList<CommonModel> articleContentPoliciesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.ARTICLES_COMMENTS_POLICIES.url, HttpMethod.GET.toString(), responseListType);
        commentPolicy = articleContentPoliciesResponse.stream().filter(CommonModel::getDefaultBoolean).findFirst().orElse(null);

        ArrayList<CommonModel> articleOriginsResponse = ProxyServer.getResponseByUrl(ContentApiUrl.ARTICLES_ORIGINS.url, HttpMethod.GET.toString(), responseListType);
        articleOrigin = articleOriginsResponse.stream().filter(CommonModel::getDefaultBoolean).findFirst().orElse(null);

        ArrayList<CommonModel> distributionChannelsResponse = ProxyServer.getResponseByUrl(ContentApiUrl.DISTRIBUTION_CHANNELS.url, HttpMethod.GET.toString(), responseListType);
        distributionChannels = distributionChannelsResponse.stream().filter(e -> e.getName().contains(EXPECTED_DEFAULT_CHANNEL)).findFirst().orElse(null);

        ArrayList<CommonModel> distributionRegionsResponse = ProxyServer.getResponseByUrl(ContentApiUrl.DISTRIBUTION_REGIONS.url, HttpMethod.GET.toString(), responseListType);
        distributionRegions = distributionRegionsResponse.stream().filter(CommonModel::getDefaultBoolean).findFirst().orElse(null);

        ArrayList<CommonModel> articleTypesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.ARTICLES_TYPES.url, HttpMethod.GET.toString(), responseListType);
        articleTypes = articleTypesResponse.stream().filter(e -> e.getName().contains(EXPECTED_DEFAULT_TYPE)).findFirst().orElse(null);

        var authorResponseListType = new TypeToken<ArrayList<AuthorModel>>() {}.getType();
        ArrayList<AuthorModel> authorResponse = ProxyServer.getResponseByUrl(ContentApiUrl.AUTHORS.url, HttpMethod.GET.toString(), authorResponseListType);
        firstAuthor = authorResponse.stream().filter(AuthorModel::getDefaultBoolean).findFirst().orElse(null);

        ArrayList<CommonModel> articleStatusesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.ARTICLES_STATUSES.url, HttpMethod.GET.toString(), responseListType);
        articleStatuses = articleStatusesResponse.stream().filter(CommonModel::getDefaultBoolean).findFirst().orElse(null);
    }

    @Test
    public void SMP_T528_defaultAuthorExistsInContentApiResponse_when_startNewArticle() {
        Assertions.assertTrue(firstAuthor != null && firstAuthor.getDefaultBoolean(), "There is no default Author!");
        Assertions.assertEquals(EXPECTED_DEFAULT_AUTHOR, firstAuthor.getName(), "The expected default Author is not equal to the actual one!");
    }

    @Test
    public void SMP_T528_defaultStatusExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(articleStatuses, "A default Status is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_STATUS, articleStatuses.getName(), "The expected default Status is not equal to the actual one!");
        Assertions.assertTrue(articleStatuses.getDefaultBoolean(), "There is no default Status!");
    }

    @Test
    public void SMP_T528_defaultTypeExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(articleTypes, "A default Type is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_TYPE, articleTypes.getName(), "The expected default Type is not equal to the actual one!");
        Assertions.assertEquals(true, articleTypes.getDefaultBoolean(), "There is no default Type!");
    }

    @Test
    public void SMP_T528_defaultDistributionRegionsExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(distributionRegions, "A default Distribution Region is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_REGION, distributionRegions.getName(), "The expected default Distribution Region is not equal to the actual one!");
        Assertions.assertTrue(distributionRegions.getDefaultBoolean(), "There is no default Distribution Region!");
    }

    @Test
    public void SMP_T528_defaultDistributionChannelsExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(distributionChannels, "A default Distribution Channel is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_CHANNEL, distributionChannels.getName(), "The expected default Distribution Channel is not equal to the actual one!");
        Assertions.assertTrue(distributionChannels.getDefaultBoolean(), "There is no default Distribution Channel!");
    }

    @Test
    public void SMP_T528_defaultOriginExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(articleOrigin, "A default Origin is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_ORIGIN, articleOrigin.getName(), "The expected default Origin is not equal to the actual one!");
        Assertions.assertTrue(articleOrigin.getDefaultBoolean(), "There is no default Origin!");
    }

    @Test
    public void SMP_T528_defaultCommentsPoliciesExistsInContentApiResponse_when_startNewArticle_and_displayed() {
        Assertions.assertNotNull(commentPolicy, "A default Comment Policy is not found!");

        Assertions.assertEquals(EXPECTED_DEFAULT_COMMENT_POLICY, commentPolicy.getName(), "The expected default Comment Policy is not equal to the actual one!");
        Assertions.assertTrue(commentPolicy.getDefaultBoolean(), "There is no default Comment Policy!");
    }
}