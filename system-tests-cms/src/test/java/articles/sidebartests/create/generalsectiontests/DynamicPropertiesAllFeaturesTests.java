package articles.sidebartests.create.generalsectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.google.gson.reflect.TypeToken;
import data.constants.ContentApiUrl;
import data.models.common.CommonModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.GENERAL_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.PERSONALIZATION)
public class DynamicPropertiesAllFeaturesTests extends BaseSidebarTests {

    private static final ThreadLocal<List<CommonModel>> articleProperties = new ThreadLocal<>();
    private static final ThreadLocal<List<CommonModel>> videosProperties = new ThreadLocal<>();
    private static final ThreadLocal<List<CommonModel>> galleryProperties = new ThreadLocal<>();
    private static final ThreadLocal<List<CommonModel>> wikiPagesProperties = new ThreadLocal<>();
    private static final ThreadLocal<List<CommonModel>> bannersProperties = new ThreadLocal<>();
    private static final ThreadLocal<List<CommonModel>> categoriesProperties = new ThreadLocal<>();

    @Override
    protected void beforeAll() {
        var responseListType = new TypeToken<ArrayList<CommonModel>>() {}.getType();
        dashboardPage.waitForPageLoad();
        ArrayList<CommonModel> articlePropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.ARTICLES_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        articleProperties.set(articlePropertiesResponse);

        ArrayList<CommonModel> videosPropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.VIDEOS_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        videosProperties.set(videosPropertiesResponse);

        ArrayList<CommonModel> galleryPropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.GALLERIES_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        galleryProperties.set(galleryPropertiesResponse);

        ArrayList<CommonModel> bannersPropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.BANNERS_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        bannersProperties.set(bannersPropertiesResponse);

        ArrayList<CommonModel> wikiPagesPropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.WIKI_PAGES_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        wikiPagesProperties.set(wikiPagesPropertiesResponse);

        ArrayList<CommonModel> categoriesPropertiesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.CATEGORIES_PROPERTIES.url, HttpMethod.GET.toString(), responseListType);
        categoriesProperties.set(categoriesPropertiesResponse);
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openArticleGeneralSection() {
        articleFormPage.openCreatePage();
        articleFormPage.generalSection().expand();

        Assertions.assertNotNull(articleProperties.get(), "Article Dynamic Properties not found!");
        for (CommonModel property : articleProperties.get()) {
            articleFormPage.generalSection().map().propertyCheckbox(property.getName()).validateIsVisible();
            articleFormPage.generalSection().map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openVideosGeneralSection() {
        videosListPage.openCreatePage();
        videosListPage.generalSection().expand();

        Assertions.assertNotNull(videosProperties.get(), "Videos Dynamic Properties not found!");
        for (CommonModel property : videosProperties.get()) {
            videosListPage.generalSection().map().propertyCheckbox(property.getName()).validateIsVisible();
            videosListPage.generalSection().map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openGalleriesGeneralSection() {
        galleriesPage.openCreatePage();
        galleriesPage.generalSection().expand();

        Assertions.assertNotNull(galleryProperties.get(), "Galleries Dynamic Properties not found!");
        for (CommonModel property : galleryProperties.get()) {
            galleriesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsVisible();
            galleriesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openBannersGeneralSection() {
        bannersPage.openCreatePage();

        Assertions.assertNotNull(bannersProperties.get(), "Banners Dynamic Properties not found!");
        for (CommonModel property : bannersProperties.get()) {
            bannersPage.map().propertyCheckbox(property.getName()).validateIsVisible();
            bannersPage.map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openWikiPagesGeneralSection() {
        wikiPagesPage.openCreatePage();
        wikiPagesPage.generalSection().expand();

        Assertions.assertNotNull(wikiPagesProperties, "WikiPages Dynamic Properties not found!");
        for (CommonModel property : wikiPagesProperties.get()) {
            wikiPagesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsVisible();
            wikiPagesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }

    @Test
    public void dynamicPropertiesCorrectlyApplied_when_openCategoriesGeneralSection() {
        categoriesPage.openCreatePage();
        categoriesPage.generalSection().expand();

        Assertions.assertNotNull(categoriesProperties, "Categories Dynamic Properties not found!");
        for (CommonModel property : categoriesProperties.get()) {
            categoriesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsVisible();
            categoriesPage.generalSection().map().propertyCheckbox(property.getName()).validateIsChecked(property.getDefaultBoolean());
        }
    }
}