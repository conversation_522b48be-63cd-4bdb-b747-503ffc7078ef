package articles.sidebartests.create.mediasectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import com.github.javafaker.Faker;
import data.constants.*;
import data.constants.video.VideoConstants;
import data.models.common.CommonModel;
import data.models.images.ImagesModel;
import data.models.images.UrlsModel;
import data.models.images.watermarks.WatermarkModel;
import data.models.related.DataListObject;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.json.TypeToken;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.articleformpage.ArticleFormPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.MEDIA_SECTION)
@Story(CMSStories.MEDIA)
public class SidebarDataValidationAllFeaturesTests extends BaseSidebarTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        createdVideo = null;
        createdGallery = null;
        articleFormModel = new ArticleFieldsFactory().buildArticleRequiredFields();
        articleFormPage = app().createPage(ArticleFormPage.class);
        articleFormPage.openCreatePage();
        articleFormPage.collapseOpenedSections();
        articleFormPage.generalSection().expand();
        articleFormPage.mediaSection().expand();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteGallery(createdGallery);
        contentApiFacade.deleteVideo(createdVideo);
    }

    @Test
    public void articleSaved_when_selectMainImage() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

//        articleFormPage.map().alertMessage().validateSaveSuccessful();
    }

    @Test
    public void SMP_T533_allExpectedControlsDisplayed_when_openMediaSection() {
        articleFormPage.mediaSection().map().emptyMainImage().validateIsVisible();
        articleFormPage.mediaSection().map().imageDescriptionTextField().validateIsVisible();
        articleFormPage.mediaSection().map().addDescriptionCheckBox().validateIsVisible();
        articleFormPage.mediaSection().map().uploadNewImagesButton().validateIsVisible();
        articleFormPage.mediaSection().map().editImageButton().validateIsVisible();
        articleFormPage.mediaSection().map().cropImageButton().validateIsVisible();
        articleFormPage.mediaSection().map().toggleUploadedImagesButton().validateIsVisible();
        articleFormPage.mediaSection().map().chooseMainMediaSelect().validateIsVisible();
    }

    @Test
    public void SMP_T545_imageDataUpdated_when_editImageDescription_and_save() {
        var expectedDescription = new Faker().chuckNorris().fact();
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().map().imageDescriptionTextField().setText(expectedDescription);
        articleFormPage.mediaSection().checkAddDescriptionCheckbox();
        ProxyServer.newHar();
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.PATCH, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), CommonModel.class);

        Assertions.assertEquals(expectedDescription, updateImageRequest.getDescription(), AssertMessages.entityNotExpected("request description"));
        Assertions.assertEquals(expectedDescription, updateImageResponse.getDescription(), AssertMessages.entityNotExpected("response description"));
    }

    @Test
    public void errorMessageDisplayed_when_fillDescription_and_exceedsMaximumLength() {
        var expectedDescription = new Faker().lorem().characters(256);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().map().imageDescriptionTextField().setText(expectedDescription);
        articleFormPage.mediaSection().checkAddDescriptionCheckbox();
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

//        articleFormPage.map().alertMessage().validateSaveUnsuccessful();
        articleFormPage.mediaSection().map().allertMessageDiv().validateIsVisible();
        articleFormPage.mediaSection().map().allertMessageDiv().validateTextIs("Exceeds maximum length limit of 255 characters");
    }

    @Test
    public void SMP_T545_imageDataUpdated_when_addQuickWatermarkToImage_and_save() {
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.mediaSection().map().applyWatermarkCheckBox().check();
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ImageApiUrl.WATERMARKS.getUrl(), HttpMethod.GET, 0);
        var watermarkModel = new TypeToken<List<WatermarkModel>>() {
        }.getType();
        List<WatermarkModel> watermarksResponse = ProxyServer.getResponseByUrl(ImageApiUrl.WATERMARKS.getUrl(), HttpMethod.GET.toString(), watermarkModel);

        Assertions.assertFalse(watermarksResponse.isEmpty(), AssertMessages.entityNotExpected("Watermark checkbox state"));
    }

    @Test
    public void SMP_T534_editImageButtonsNotActive_when_notSelectedImage() {
        articleFormPage.mediaSection().map().imageDescriptionTextField().validateIsDisabled();
        articleFormPage.mediaSection().map().editImageButton().validateIsDisabled();
        articleFormPage.mediaSection().map().cropImageButton().validateIsDisabled();
    }

    @Test
    public void SMP_T545_imageUpdatePopUpDisplayed_when_selectImage_and_clickEditImage() {
        var expectedHeading = "Image update";
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        Assertions.assertEquals(expectedHeading, articleFormPage.mediaSection().map().imageEditPopUp().popUpHeading().getText(), AssertMessages.entityNotExpected("pop-up heading"));
    }

    @Test
    public void SMP_T541_allExpectedControlsDisplayed_when_openImageUpdate() {
        List<String> expectedSports = Arrays.asList(SupportedSports.FOOTBALL.getTitle(), SupportedSports.BASKETBALL.getTitle(), SupportedSports.TENNIS.getTitle(), SupportedSports.ICE_HOCKEY.getTitle(), SupportedSports.HANDBALL.getTitle(), SupportedSports.CYCLING.getTitle(), SupportedSports.CRICKET.getTitle(), SupportedSports.FUTSAL.getTitle(), SupportedSports.TABLE_TENNIS.getTitle(), SupportedSports.HORSE_RACING.getTitle(), SupportedSports.ROLLER_HOCKEY.getTitle(), SupportedSports.RUGBY_LEAGUE.getTitle(), SupportedSports.RUGBY_UNION.getTitle(), SupportedSports.MOTORSPORTS.getTitle());
        List<String> expectedTypes = Arrays.asList("Content", "Press", "Avatar", "Thumbnail");

        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().popUpHeading().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().imageDescriptionPopUpTextField().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().imageDescriptionPopUpTextField().validatePlaceholderContains("Image Description");
        articleFormPage.mediaSection().map().imageEditPopUp().originPopUpSelect().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().sportPopUpSelect().validateIsVisible();
        var actualSports = articleFormPage.mediaSection().map().imageEditPopUp().sportPopUpSelect().getOptionsValues();
        articleFormPage.mediaSection().map().imageEditPopUp().sportPopUpSelect().selectOptionByIndex(0);
        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedSports, actualSports), AssertMessages.entityNotExpected("Sport options"));

        articleFormPage.mediaSection().map().imageEditPopUp().sportConnectionsPopUpSelect().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().focus();
        var actualTypes = articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().getOptionsValues();
        articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().selectOptionByIndex(0);
        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedTypes, actualTypes), AssertMessages.entityNotExpected("Type options"));

        articleFormPage.mediaSection().map().imageEditPopUp().authorPopUpSelect().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().imageAltPopUpTextField().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().imageAltPopUpTextField().validatePlaceholderContains("Enter image alt for SEO purposes");
        articleFormPage.mediaSection().map().imageEditPopUp().imageCaptionPopUpTextField().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().imageCaptionPopUpTextField().validatePlaceholderContains("Enter image caption for SEO purposes");
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().cancelPopUpButton().validateIsVisible();
    }

    @Test
    public void errorMessageDisplayed_when_openImageUpdate_and_exceedsDescriptionMaximumLength() {
        var expectedDescription = new Faker().lorem().characters(256);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageDescriptionPopUpTextField().setText(expectedDescription);
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();

//        articleFormPage.map().alertMessage().validateImageUpdateFail();
        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpDiv().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpDiv().validateTextIs("Exceeds maximum length limit of 255 characters");
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_emptyFields() {
        String fileName = "testImage.png";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageUploadSuccessful();
        articleFormPage.mediaSection().openImageEditPopUp();

        Assertions.assertFalse(articleFormPage.mediaSection().map().imageEditPopUp().imageDescriptionPopUpTextField().getText().isEmpty(), AssertMessages.entityNotExpected("element text"));
        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), articleFormPage.mediaSection().map().imageEditPopUp().originPopUpSelect().getText(), AssertMessages.entityNotExpected("element text"));
        articleFormPage.mediaSection().map().imageEditPopUp().sportPopUpSelect().clearSelection();
        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().getText(), AssertMessages.entityNotExpected("element text"));
        Assertions.assertEquals(PlaceholderField.SELECT.getValue(), articleFormPage.mediaSection().map().imageEditPopUp().authorPopUpSelect().getText(), AssertMessages.entityNotExpected("element text"));
        Assertions.assertTrue(articleFormPage.mediaSection().map().imageEditPopUp().imageAltPopUpTextField().getText().isEmpty(), AssertMessages.entityNotExpected("element text"));
        Assertions.assertTrue(articleFormPage.mediaSection().map().imageEditPopUp().imageCaptionPopUpTextField().getText().isEmpty(), AssertMessages.entityNotExpected("element text"));
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();

//        articleFormPage.map().alertMessage().validateImageUpdateSuccessful();
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_fillImageDescription() {
        var expectedDescription = new Faker().chuckNorris().fact();
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageDescriptionPopUpTextField().setText(expectedDescription);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.GET.toString(), CommonModel.class);

        Assertions.assertEquals(expectedDescription, updateImageRequest.getDescription(), AssertMessages.entityNotExpected("request image description"));
        Assertions.assertEquals(expectedDescription, updateImageResponse.getDescription(), AssertMessages.entityNotExpected("response image description"));
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_selectOrigin() {
        var expectedOrigin = "Getty";
        var expectedOriginId = "1";
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().originPopUpSelect().selectOptionByText(expectedOrigin);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.GET.toString(), CommonModel.class);

        Assertions.assertEquals(expectedOriginId, updateImageRequest.getOriginId(), AssertMessages.entityNotExpected("request Origin Id"));
        Assertions.assertEquals(expectedOrigin, updateImageResponse.getOrigin().getName(), AssertMessages.entityNotExpected("response Origin Name"));
        Assertions.assertEquals(expectedOriginId, updateImageResponse.getOrigin().getId(), AssertMessages.entityNotExpected("response Origin Id"));
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_selectFootballConnections() {
        var expectedEntity = "Ronaldo";
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().sportConnectionsPopUpSelect().filter(expectedEntity, true);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();

        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(ContentApiUrl.IMAGES.url);
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_selectAuthor() {
        var expectedEntity = StringConstants.SPORTAL_365_STRING;
        var expectedEntityId = StringConstants.SPORTAL_365_ID;
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().authorPopUpSelect().searchSelectByText(expectedEntity);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        ImagesModel updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        CommonModel updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.GET.toString(), CommonModel.class);

        Assertions.assertEquals(expectedEntityId, updateImageRequest.getAuthors().get(0), AssertMessages.entityNotExpected("request Author Id"));
        Assertions.assertEquals(expectedEntity, updateImageResponse.getAuthors().get(0).getName(), AssertMessages.entityNotExpected("response Author name"));
        Assertions.assertEquals(expectedEntityId, updateImageResponse.getAuthors().get(0).getId(), AssertMessages.entityNotExpected("response Author Id"));
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_selectSport() {
        var expectedSport = "Basketball";
        var expectedProvider = "sports-search-api";
        var expectedConnection = "Chicago Bulls";
        var fileName = "testImage.png";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageUploadSuccessful();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().sportPopUpSelect().searchSelectByText(expectedSport);
        articleFormPage.mediaSection().map().imageEditPopUp().sportConnectionsPopUpSelect().filter(expectedConnection, true);
        articleFormPage.mediaSection().map().imageEditPopUp().sportConnectionsPopUpSelect().selectOptionByIndex(0);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        var requestType = new TypeToken<ArrayList<DataListObject>>() {
        }.getType();
        ProxyServer.waitForRequest(app().browser().getWrappedDriver(), "/related", HttpMethod.POST, 0);
        Wait.forMilliseconds(1000);
        List<DataListObject> imageRelatedRequest = ProxyServer.getRequestByUrl("/related", HttpMethod.POST.toString(), requestType);

        Assertions.assertEquals(expectedProvider, imageRelatedRequest.get(0).getProvider(), AssertMessages.entityNotExpected("request Provider"));
        Assertions.assertEquals(expectedConnection, imageRelatedRequest.get(0).getData().getName(), AssertMessages.entityNotExpected("request Sport Connection name"));
        Assertions.assertEquals(expectedSport.toLowerCase(), imageRelatedRequest.get(0).getData().getSport(), AssertMessages.entityNotExpected("request Sport"));
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_selectType() {
        var expectedEntity = "Content";
        var fileName = "testImage.png";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageUploadSuccessful();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().typePopUpSelect().selectOptionByText(expectedEntity);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.PATCH, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), CommonModel.class);

        Assertions.assertEquals(expectedEntity.toLowerCase(), updateImageRequest.getType(), AssertMessages.entityNotExpected("request Type"));
        Assertions.assertEquals(expectedEntity.toLowerCase(), updateImageResponse.getType(), AssertMessages.entityNotExpected("response Type"));
    }

    @Test
    public void errorMessageDisplayed_when_openImageUpdate_and_exceedsImageAltMaximumLength() {
        var expectedDescription = new Faker().lorem().characters(256);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageAltPopUpTextField().setText(expectedDescription);

        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpLabel().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpLabel().validateTextIs("Image alt must be less than 255 symbols");
        Assertions.assertTrue(articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().isDisabled(), "The pop-up save button is active, but it is not expected to be.");
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_fillImageAlt() {
        var expectedDescription = new Faker().app().author();
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageAltPopUpTextField().setText(expectedDescription);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.GET.toString(), CommonModel.class);

        Assertions.assertEquals(expectedDescription, updateImageRequest.getAlt(), AssertMessages.entityNotExpected("request image description"));
        Assertions.assertEquals(expectedDescription, updateImageResponse.getAlt(), AssertMessages.entityNotExpected("response image description"));
    }

    @Test
    public void errorMessageDisplayed_when_openImageUpdate_and_exceedsImageCaptionMaximumLength() {
        var expectedDescription = new Faker().lorem().characters(256);
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageCaptionPopUpTextField().setText(expectedDescription);

        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpLabel().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().alertMessagePopUpLabel().validateTextIs("Image caption must be less than 255 symbols");
        Assertions.assertTrue(articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().isDisabled(), "The pop-up save button is active, but it is not expected to be.");
    }

    @Test
    public void SMP_T545_imageUpdate_when_openImageUpdate_and_fillImageCaption() {
        var expectedDescription = new Faker().app().author();
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageEditPopUp();

        articleFormPage.mediaSection().map().imageEditPopUp().imageCaptionPopUpTextField().setText(expectedDescription);
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.GET, 0);
        var updateImageRequest = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), ImagesModel.class);
        var updateImageResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES.url, HttpMethod.GET.toString(), CommonModel.class);

        Assertions.assertEquals(expectedDescription, updateImageRequest.getCaption(), AssertMessages.entityNotExpected("request image description"));
        Assertions.assertEquals(expectedDescription, updateImageResponse.getCaption(), AssertMessages.entityNotExpected("response image description"));
    }

    @Test
    public void SMP_T544_properDataExistsInContentApiRequest_when_changeImageRatio_and_manuallyCropped() {
        var expectedEntity = "9x16";
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageCropPopUp();
        articleFormPage.mediaSection().map().imageCropPopUp().imageCropContainer().toExist().waitToBe();

        articleFormPage.mediaSection().map().imageCropPopUp().imageRatioPopUpSelect().selectOptionByText(expectedEntity);
        articleFormPage.waitForSpinners();
        articleFormPage.mediaSection().map().imageCropPopUp().moveImageCropContainer();
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
//        articleFormPage.map().alertMessage().validateImageUpdateSuccessful();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.PATCH, 0);
        var updateImageResponse = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), UrlsModel.class);

        Assertions.assertNotNull(updateImageResponse.getUrls().getRes9x16(), "The expected resolution 9x16 is empty, but it is not expected");
        Assertions.assertNull(updateImageResponse.getUrls().getRes16x9(), "The resolution 16x9 is not empty, but it is expected to be");
        Assertions.assertNotNull(updateImageResponse.getUrls().getRes9x16().getBottom(), "The expected bottom point from resolution 9x16 is empty");
    }

    @Test
    public void SMP_T544_properDataExistsInContentApiRequest_when_changeImageRatio() {
        var expectedEntity = "9x16";
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageCropPopUp();
        articleFormPage.mediaSection().map().imageCropPopUp().imageCropContainer().toExist().waitToBe();

        articleFormPage.mediaSection().map().imageCropPopUp().imageRatioPopUpSelect().selectOptionByText(expectedEntity);
        articleFormPage.waitForSpinners();
        ProxyServer.newHar();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().click();
//        articleFormPage.map().alertMessage().validateImageUpdateSuccessful();

        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), ContentApiUrl.IMAGES.url, HttpMethod.PATCH, 0);
        var updateImageResponse = ProxyServer.getRequestByUrl(ContentApiUrl.IMAGES.url, HttpMethod.PATCH.toString(), UrlsModel.class);
        Assertions.assertNotNull(updateImageResponse.getUrls().getRes9x16(), "The expected resolution 9x16 is empty, but it is not expected");
    }

    @Test
    public void SMP_T542_allExpectedControlsDisplayed_when_openImageCropMenu() {
        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageCropPopUp();
        articleFormPage.waitForSpinners();
        articleFormPage.mediaSection().map().imageCropPopUp().imageCropContainer().waitToBe();

        articleFormPage.mediaSection().map().imageCropPopUp().imageRatioPopUpSelect().validateIsVisible();
        articleFormPage.mediaSection().map().imageCropPopUp().imageDownRightCropButton().validateIsVisible();
        articleFormPage.mediaSection().map().imageCropPopUp().imageDownMiddleCropButton().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().savePopUpButton().validateIsVisible();
        articleFormPage.mediaSection().map().imageEditPopUp().cancelPopUpButton().validateIsVisible();
    }

    @Test
    public void SMP_T544_expectedCropRatiosDisplayed_when_openImageCropMenu() {
        var expectedCropOptions = Arrays.asList("23x9", "16x9", "9x16", "4x3", "3x4", "2x3", "3x2", "1x1");

        articleFormPage.mediaSection().selectFirstLoadedImage();
        articleFormPage.mediaSection().openImageCropPopUp();
        articleFormPage.mediaSection().map().imageCropPopUp().imageCropContainer().toExist().waitToBe();

        var actualCropOptions = articleFormPage.mediaSection().map().imageCropPopUp().imageRatioPopUpSelect().getOptionsValues();
        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedCropOptions, actualCropOptions), AssertMessages.entityNotExpected("Crop options list"));
    }

    @Test
    public void SMP_T579_searchVideoDisplayed_when_selectVideoFromChooseMainMedia() {
        var expectedMedia = "Video";
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);

        articleFormPage.mediaSection().map().searchVideoLabel().validateIsVisible();
        articleFormPage.mediaSection().map().searchVideoSelect().validateIsVisible();
    }

    @Test
    public void SMP_T579_searchGalleryDisplayed_when_selectGalleryFromChooseMainMedia() {
        var expectedMedia = "Gallery";
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);

        articleFormPage.mediaSection().map().searchGalleryLabel().validateIsVisible();
        articleFormPage.mediaSection().map().searchGallerySelect().validateIsVisible();
    }

    @Test
    public void SMP_T579_videoEmbedDisplayed_when_selectVideoEmbedFromChooseMainMedia() {
        var expectedMedia = "Embed video";
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);

        articleFormPage.mediaSection().map().videoEmbedLabel().validateIsVisible();
        articleFormPage.mediaSection().map().videoEmbedTextField().validateIsVisible();
        articleFormPage.mediaSection().map().videoEmbedRemoveButton().validateIsVisible();
    }

    @Test
    public void SMP_T579_properApiRequestCreated_when_selectVideoFromChooseMainMedia_and_selectOption() {
        createdVideo = contentApiFacade.createVideo();
        var expectedMedia = createdVideo.getEntityType();
        var expectedEntity = createdVideo.getTitle();
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);
        articleFormPage.mediaSection().map().searchVideoSelect().selectOptionByText(expectedEntity);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleRequest = articleFormPage.getArticleCreateRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(articleRequest.getMainMedia().stream().anyMatch(e -> e.getResourceType().equals(expectedMedia.toLowerCase())), AssertMessages.requestNotContains("video media"));
        Assertions.assertEquals(expectedEntity, articleRequest.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedMedia.toLowerCase())).findFirst().get().getData().getTitle(), AssertMessages.requestNotContains("video media"));
        Assertions.assertEquals(expectedEntity, createdArticle.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedMedia.toLowerCase())).findFirst().get().getData().getTitle(), AssertMessages.responseNotContains("video media"));
    }

    @Test
    public void SMP_T579_properApiRequestCreated_when_selectGalleryFromChooseMainMedia_and_selectOption() {
        createdGallery = contentApiFacade.createGallery();
        var expectedMedia = createdGallery.getEntityType();
        var expectedEntity = createdGallery.getTitle();
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);
        articleFormPage.mediaSection().map().searchGallerySelect().selectOptionByText(expectedEntity);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleRequest = articleFormPage.getArticleCreateRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(articleRequest.getMainMedia().stream().anyMatch(e -> e.getResourceType().equals(expectedMedia.toLowerCase())), AssertMessages.requestNotContains("gallery media"));
        Assertions.assertEquals(expectedEntity, articleRequest.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedMedia.toLowerCase())).findFirst().get().getData().getTitle(), AssertMessages.requestNotContains("gallery media"));
        Assertions.assertEquals(expectedEntity, createdArticle.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedMedia.toLowerCase())).findFirst().get().getData().getTitle(), AssertMessages.responseNotContains("gallery media"));
    }

    @Test
    public void SMP_T579_properApiRequestCreated_when_selectEmbedVideoFromChooseMainMedia_and_selectOption() {
        var expectedMedia = "Embed video";
        var expectedResourceType = "embed";
        var expectedResourceSubtype = "video";
        var expectedEntity = VideoConstants.EMBED_CODE;
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedMedia);
        articleFormPage.mediaSection().map().videoEmbedTextField().setText(expectedEntity);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleRequest = articleFormPage.getArticleCreateRequest();
        createdArticle = articleFormPage.getArticleCreateResponse();

        Assertions.assertTrue(articleRequest.getMainMedia().stream().anyMatch(e -> e.getResourceType().equals(expectedResourceType) && e.getResourceSubtype().equals(expectedResourceSubtype)), AssertMessages.requestNotContains("embed video"));
        Assertions.assertEquals(expectedEntity, articleRequest.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedResourceType)).findFirst().get().getData().getEmbedCode(), AssertMessages.requestNotContains("embed video"));
        Assertions.assertEquals(expectedEntity, createdArticle.getMainMedia().stream().filter(e -> e.getResourceType().equals(expectedResourceType)).findFirst().get().getData().getEmbedCode(), AssertMessages.responseNotContains("embed video"));
    }

    @Test
    public void requiredMessageDisplayed_when_selectAllMediaFromChooseMainMedia_and_noMediaSelected() {
        var expectedVideoMedia = "Video";
        var expectedGalleryMedia = "Gallery";
        var expectedEmbedVideoMedia = "Embed video";
        var expectedAlertMessage = StringConstants.FIELD_MUST_NOT_BE_EMPTY_STRING;
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedVideoMedia);
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedGalleryMedia);
        articleFormPage.mediaSection().map().chooseMainMediaSelect().selectOptionByText(expectedEmbedVideoMedia);

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();
        createdArticle = articleFormPage.getArticleCreateResponse();

//        articleFormPage.map().alertMessage().validateSaveUnsuccessful();
        Assertions.assertEquals(expectedAlertMessage, articleFormPage.mediaSection().map().allertMessageDiv().getText(), AssertMessages.entityNotExpected("Alert message"));
    }

    @Test
    public void SMP_T540_imageSuccessfullyUploaded_when_uploadLocalImage() {
        var fileName = "testImage.png";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageUploadSuccessful();
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), URL, HttpMethod.POST, 0);
        var imageResponse = ProxyServer.getResponseByUrl(URL, "POST", ImagesModel.class);

        Assertions.assertEquals(fileName, imageResponse.getOriginalFilename(), AssertMessages.responseNotContains("Original file name"));
    }

    @Test
    public void SMP_T540_imageDisplayed_when_uploadNewImage() {
        var fileName = "testImage.png";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
        ProxyServer.waitForResponse(app().browser().getWrappedDriver(), URL, HttpMethod.POST, 0);
        var imageResponse = ProxyServer.getResponseByUrl(URL, "POST", ImagesModel.class);
        var imageCmsPath = imageResponse.getPath();

        Assertions.assertTrue(articleFormPage.mediaSection().map().mediaMainImage().getSrc().contains(imageCmsPath), "Main Media image does not contain the expected file path");
    }

    @Test
    public void SMP_T540_errorMessageDisplayed_when_tryUploadImageExceedsMaxResolution() {
        var fileName = "BigResolutionImage.jpg";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageExceedsMaxResolution();
    }

    @Test
    public void SMP_T540_errorMessageDisplayed_when_tryUploadImageExceedsMaxSize() {
        var fileName = "BigFileSizeImage.jpg";

        articleFormPage.mediaSection().uploadPicture(getTestFilePath(fileName));
//        articleFormPage.map().alertMessage().validateImageExceedsMaxFileSize();
    }

    @Test
    public void SMP_T533_allExpectedControlsDisplayed_when_toggleUploadedImages() {
        var expectedImagesCount = 20;
        articleFormPage.mediaSection().map().toggleUploadedImagesButton().toBeVisible().waitToBe();
        articleFormPage.mediaSection().map().toggleUploadedImagesButton().click();
        articleFormPage.mediaSection().waitForImagesToLoad();

        articleFormPage.mediaSection().map().uploadedImagesGallery().searchTextField().validateIsVisible();
        articleFormPage.mediaSection().map().uploadedImagesGallery().searchTextField().validatePlaceholderContains("Input title or part of content ...");
        articleFormPage.mediaSection().map().uploadedImagesGallery().searchButton().validateIsVisible();
        Assertions.assertEquals(expectedImagesCount, articleFormPage.mediaSection().map().uploadedImagesGallery().imageList().size(), AssertMessages.entityNotExpected("count of the images"));
        Assertions.assertFalse(articleFormPage.mediaSection().map().uploadedImagesGallery().paginationButtonsList().isEmpty(), "The pagination buttons are not displayed!");
    }

    @Test
    //@Issue("SFE-5716")
    //@Issue("PLT-292")
    public void SMP_T580_advancedFiltersDisplayed_when_toggleUploadedImages() {
        articleFormPage.mediaSection().map().toggleUploadedImagesButton().toBeVisible().waitToBe();
        articleFormPage.mediaSection().map().toggleUploadedImagesButton().click();
        articleFormPage.mediaSection().map().toggleImagoImagesButton().validateIsVisible();
        articleFormPage.mediaSection().map().uploadedImagesGallery().openAdvancedFilters();

        articleFormPage.mediaSection().asserts().assertAdvanceFiltersDisplayed();
    }
}