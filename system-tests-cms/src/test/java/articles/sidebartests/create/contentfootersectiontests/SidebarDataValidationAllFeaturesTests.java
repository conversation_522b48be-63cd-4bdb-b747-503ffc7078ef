package articles.sidebartests.create.contentfootersectiontests;

import articles.sidebartests.BaseSidebarTests;
import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import categories.SidebarSection;
import factories.articles.ArticleFieldsFactory;
import io.qameta.allure.Story;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.Arrays;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.ARTICLES)
@Tag(CMSTags.SIDEBAR)
@Tag(SidebarSection.CONTENT_FOOTER_SECTION)
@Story(CMSStories.SIDEBAR)
@Story(CMSStories.FOOTER)
public class SidebarDataValidationAllFeaturesTests extends BaseSidebarTests {

    private final String expectedFooter = "Снимки: Иван Раднев, Sportal365";
    private final List<String> expectedFooters = Arrays.asList(expectedFooter, "Снимки: Sportal.bg", "Снимки: Владимир Иванов, Sportal.bg", "Снимки: Иван Иванов, Sportal.bg");

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        articleFormModel = ArticleFieldsFactory.buildArticleRequiredFields();
        articleFormPage.openCreatePage();
        articleFormPage.contentFooterSection().expand();
        articleFormPage.contentFooterSection().map().valueSelect().clearSelection();
    }

    @Test
    public void articleSaved_when_emptyContentFooter() {
        if (articleFormPage.contentFooterSection().map().valueSelect().getText().isEmpty()) {
            articleFormPage.contentFooterSection().map().valueSelect().clearSelection();
        }

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

//        articleFormPage.map().alertMessage().validateMessageIs(ToastMessageEnum.ARTICLE_SUCCESSFULLY_CREATED);
    }

    @Test
    public void allExpectedOptionsListed_when_checkContentFooterOptions() {
        var contentFooterOptions = articleFormPage.contentFooterSection().map().valueSelect().getOptionsValues();

        Assertions.assertTrue(CollectionUtils.isEqualCollection(expectedFooters, contentFooterOptions), "The expected options are not listed!");
    }

    @Test
    public void alreadySelectedOptionNotListed_when_selectContentFooter() {
        articleFormPage.contentFooterSection().map().valueSelect().selectOptionByText(expectedFooter);

        articleFormPage.contentFooterSection().map().valueSelect().validateOptionNotExists(expectedFooter);
    }

    @Test
    public void properDataExistsInContentApiRequest_when_selectContentFooter_and_save() {
        articleFormPage.contentFooterSection().map().valueSelect().filter(expectedFooter.substring(0, 18), false);
        articleFormPage.contentFooterSection().map().valueSelect().selectOptionByIndex(0);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertEquals(articleRequest.getFooter(), expectedFooter, "The expected footer is not equal to the actual one!");
    }

    @Test
    public void properDataExistsInContentApiRequest_when_selectAllContentFooterOptions_and_save() {
        var optionsNumber = articleFormPage.contentFooterSection().map().valueSelect().getOptionsCollection().size();

        for (int i = 0; i < optionsNumber; i++) {
            articleFormPage.contentFooterSection().map().valueSelect().selectOptionByIndex(0);
        }

        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleRequest = articleFormPage.getArticleCreateRequest();
        Assertions.assertTrue(expectedFooters.stream().allMatch(articleRequest.getFooter()::contains), "The expected footer options are not existing in the request!");
    }

    @Test
    public void properDataExistsInContentApiResponse_when_selectContentFooter_and_save() {
        articleFormPage.contentFooterSection().map().valueSelect().filter(expectedFooter.substring(0, 18), false);
        articleFormPage.contentFooterSection().map().valueSelect().selectOptionByIndex(0);
        articleFormPage.setRequiredFields(articleFormModel);
        articleFormPage.saveArticle();

        articleFormPage.waitForArticleCreationRequest();
        var articleResponse = articleFormPage.getArticleCreateResponse();
        Assertions.assertEquals(articleResponse.getFooter(), expectedFooter, "The expected footer is not equal to the actual one!");
    }
}