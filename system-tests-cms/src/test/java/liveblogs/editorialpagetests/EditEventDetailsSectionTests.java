package liveblogs.editorialpagetests;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.liveblog.LiveBlogTypeEnum;
import data.models.footballapi.v2.MatchV2Model;
import data.models.liveblogapi.LiveBlogResponseModel;
import data.models.liveblogapi.SeoModel;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import pages.liveblogspages.liveblogspage.LiveBlogsPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.LIVEBLOG)
@Tag(CMSTags.LIVEBLOG_POSTS)
@Story(CMSStories.LIVEBLOG)
public class EditEventDetailsSectionTests extends BaseLiveBlogTests {

    private MatchV2Model unusedSportEvent;
    private LiveBlogResponseModel liveBlogResponse;
    private SeoModel seoConfig;

    @Override
    protected void beforeEach() {
        super.beforeEach();

        liveBlogsPage = app().goTo(LiveBlogsPage.class);

        if (unusedSportEvent == null) {
            unusedSportEvent = footballApiFacade.getNotStartedMatch();
        }

        seoConfig = SeoModel.builder()
                .automaticSlug(false)
                .automaticSeoTitle(false)
                .title(StringConstants.EMPTY_STRING)
                .build();

        ProxyServer.newHar();
        liveBlogResponse = liveBlogApiFacade.createLiveBlogConfiguration(seoConfig, unusedSportEvent);
        editorialPage.openEditorLiveBlogConfiguration(liveBlogResponse.getId());
    }

    @Override
    public void afterEach() {
        liveBlogApiFacade.deleteLiveBlogs(liveBlogResponse);
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void expectedGenerateSlugCheckboxIsAlreadyCheck_onDefaultState(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);

        eventDetailsSection.asserts().assertGenerateSlugCheckboxEditLiveBlogDefaultState();
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void expectedCopySeoTitleFromTheMainCheckboxIsAlreadyCheck_onDefaultState(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);

        eventDetailsSection.asserts().assertCopySeoTitleFromMainTitleCheckboxEditLiveBlogDefaultState();
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void expectedGenerateSlugAndCopySeoTitleFromTheMainCheckboxIsAlreadyCheck_onDefaultState(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);

        eventDetailsSection.asserts().assertGenerateSlugCheckboxEditLiveBlogDefaultState();
        eventDetailsSection.asserts().assertCopySeoTitleFromMainTitleCheckboxEditLiveBlogDefaultState();
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void generateCorrectSlug_when_generateAutomaticallyfromLiveblogTitleIsCheck_and_eventTitleIsFilled(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);
        var eventTitle = faker.harryPotter().character();

        eventDetailsSection.fillEventTitleField(eventTitle);
        eventDetailsSection.map().generateSlugAutomaticallyCheckbox().check();

        String expectedSlug = eventTitle.toLowerCase().replaceAll("[^a-z0-9]+", "-").replaceAll("^-|-$", "");

        Assertions.assertEquals(expectedSlug, eventDetailsSection.map().liveBlogSlugTextField().getText(), AssertMessages.slugDoesNotMatch());
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void copySeoTitleFromMainTitle_when_copySeoTitleFromMainTitleIsCheck_and_eventTitleIsFilled(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);
        var eventTitle = faker.harryPotter().character();
        eventDetailsSection.fillEventTitleField(eventTitle);

        eventDetailsSection.map().copySeoTitleFromMainTitleCheckbox().check();

        Assertions.assertEquals(eventTitle, eventDetailsSection.map().seoTitleTextField().getText(), AssertMessages.seoTitleDoesNotMatch());
    }

    @ParameterizedTest
    @EnumSource(LiveBlogTypeEnum.class)
    public void generateCorrectSlugAndCopySeoTitleFromMainTitle_when_copySeoTitleFromMainTitleIsCheck_and_eventTitleIsFilled(LiveBlogTypeEnum liveBlogType) {
        eventDetailsSection.selectType(liveBlogType);
        var eventTitle = faker.harryPotter().character();
        eventDetailsSection.fillEventTitleField(eventTitle);

        eventDetailsSection.map().generateSlugAutomaticallyCheckbox().check();
        eventDetailsSection.map().copySeoTitleFromMainTitleCheckbox().check();

        String expectedSlug = eventTitle.toLowerCase().replaceAll("[^a-z0-9]+", "-").replaceAll("^-|-$", "");

        Assertions.assertEquals(expectedSlug, eventDetailsSection.map().liveBlogSlugTextField().getText(), AssertMessages.slugDoesNotMatch());
        Assertions.assertEquals(eventTitle, eventDetailsSection.map().seoTitleTextField().getText(), AssertMessages.seoTitleDoesNotMatch());
    }
}