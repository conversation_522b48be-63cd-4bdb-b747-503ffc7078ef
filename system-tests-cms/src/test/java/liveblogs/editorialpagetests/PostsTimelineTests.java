package liveblogs.editorialpagetests;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.liveblog.LiveBlogNewPostToggleButtonEnum;
import data.constants.liveblog.LiveBlogPostsTimelinePostType;
import data.customelements.LiveBlogPost;
import data.models.footballapi.v2.MatchV2Model;
import data.models.liveblogapi.LiveBlogResponseModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.liveblogspages.liveblogspage.LiveBlogsPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.List;
import java.util.Map;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.LIVEBLOG)
@Tag(CMSTags.LIVEBLOG_POSTS)
@Story(CMSStories.LIVEBLOG)
public class PostsTimelineTests extends BaseLiveBlogTests {

    private static final String EXPECTED_MINUTES = "45";
    private MatchV2Model unusedSportEvent;
    private LiveBlogResponseModel liveBlogResponse;
    private String postText;

    @Override
    protected void beforeEach() {
        super.beforeEach();

        postText = faker.lorem().paragraph(3);
        liveBlogsPage = app().goTo(LiveBlogsPage.class);
        if (unusedSportEvent == null) {
            unusedSportEvent = footballApiFacade.getNotStartedMatch();
        }
        ProxyServer.newHar();
        liveBlogResponse = liveBlogApiFacade.createLiveBlogConfiguration(unusedSportEvent);
        editorialPage.openEditorialPageForLiveBlog(liveBlogResponse.getId());
    }

    @Override
    public void afterEach() {
        liveBlogApiFacade.deleteLiveBlogs(liveBlogResponse);
    }

    @Test
    public void newPublishedPostAdded_when_publishPost() {
        Map<LiveBlogPostsTimelinePostType, Integer> expectedCountOfPosts = Map.of(
                LiveBlogPostsTimelinePostType.PUBLISHED, 1,
                LiveBlogPostsTimelinePostType.PINNED, 0,
                LiveBlogPostsTimelinePostType.HIGHLIGHTS, 0,
                LiveBlogPostsTimelinePostType.DRAFTS, 0
        );

        newPostSection.postEditor().setText(postText);
        newPostSection.publishPost();
        editorialPage.map().alertMessage().validatePostCreated();

        List<LiveBlogPost> publishedPosts = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED);
        String actualPostText = publishedPosts.get(0).postEditor().getHtml();

        Assertions.assertEquals("<p>%s</p>".formatted(postText), actualPostText, AssertMessages.incorrectContentOfLiveBlogPost(LiveBlogPostsTimelinePostType.PUBLISHED));
        postsTimelineSection.asserts().assertPostsCount(expectedCountOfPosts);
    }

    @Test
    public void newPinnedPostAdded_when_publishPost_and_pinIt() {
        Map<LiveBlogPostsTimelinePostType, Integer> expectedCountOfPosts = Map.of(
                LiveBlogPostsTimelinePostType.PUBLISHED, 1,
                LiveBlogPostsTimelinePostType.PINNED, 1,
                LiveBlogPostsTimelinePostType.HIGHLIGHTS, 0,
                LiveBlogPostsTimelinePostType.DRAFTS, 0
        );

        newPostSection.postEditor().setText(postText);
        newPostSection.publishPost();
//        editorialPage.map().alertMessage().validateMessageIs(ToastMessageEnum.YOUR_POST_HAS_BEEN_SUCCESSFULLY_CREATED);

        postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED).get(0).pinPost();
        List<LiveBlogPost> pinnedPosts = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PINNED);

        String actualPostText = pinnedPosts.get(0).postEditor().getHtml();

        Assertions.assertEquals("<p>%s</p>".formatted(postText), actualPostText, AssertMessages.incorrectContentOfLiveBlogPost(LiveBlogPostsTimelinePostType.PINNED));
        postsTimelineSection.asserts().assertPostsCount(expectedCountOfPosts);
    }

    @Test
    public void newHighlightPostAdded_when_publishPost_and_highlightIt() {
        Map<LiveBlogPostsTimelinePostType, Integer> expectedCountOfPosts = Map.of(
                LiveBlogPostsTimelinePostType.PUBLISHED, 1,
                LiveBlogPostsTimelinePostType.PINNED, 0,
                LiveBlogPostsTimelinePostType.HIGHLIGHTS, 1,
                LiveBlogPostsTimelinePostType.DRAFTS, 0
        );

        newPostSection.postEditor().setText(postText);
        newPostSection.publishPost();
        editorialPage.map().alertMessage().validatePostCreated();

        postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED).get(0).highlightPost();
        List<LiveBlogPost> highlightsPosts = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.HIGHLIGHTS);
        String actualPostText = highlightsPosts.get(0).postEditor().getHtml();

        Assertions.assertEquals("<p>%s</p>".formatted(postText), actualPostText, AssertMessages.incorrectContentOfLiveBlogPost(LiveBlogPostsTimelinePostType.HIGHLIGHTS));
        postsTimelineSection.asserts().assertPostsCount(expectedCountOfPosts);
    }

    @Test
    public void newDraftPostAdded_when_savePostAsDraft() {
        Map<LiveBlogPostsTimelinePostType, Integer> expectedCountOfPosts = Map.of(
                LiveBlogPostsTimelinePostType.PUBLISHED, 0,
                LiveBlogPostsTimelinePostType.PINNED, 0,
                LiveBlogPostsTimelinePostType.HIGHLIGHTS, 0,
                LiveBlogPostsTimelinePostType.DRAFTS, 1
        );

        newPostSection.postEditor().setText(postText);
        newPostSection.saveAsDraft();
        editorialPage.map().alertMessage().validatePostCreated();

        List<LiveBlogPost> draftsPosts = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.DRAFTS);
        String actualPostText = draftsPosts.get(0).postEditor().getHtml();

        Assertions.assertEquals("<p>%s</p>".formatted(postText), actualPostText, AssertMessages.incorrectContentOfLiveBlogPost(LiveBlogPostsTimelinePostType.DRAFTS));
        postsTimelineSection.asserts().assertPostsCount(expectedCountOfPosts);
    }

    @Test
    public void minuteAddedToPost_when_addMinute_and_publishPost() {
        newPostSection.postEditor().setText(postText);
        newPostSection.addMinute(EXPECTED_MINUTES);
        newPostSection.publishPost();
        editorialPage.map().alertMessage().validatePostCreated();
        ProxyServer.newHar();

        LiveBlogPost liveBlogPost = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED).get(0);
        liveBlogPost.validateMinuteDisplayed(EXPECTED_MINUTES);

        LiveBlogPostModel publishedPost = editorialPage.getPublishedPostsResponse(liveBlogResponse.getId()).get(0);

        Assertions.assertEquals(EXPECTED_MINUTES, publishedPost.getMinute(), AssertMessages.liveBlogPostIncorrectProperty(StringConstants.MINUTE_STRING));
        Assertions.assertNull(publishedPost.getInjuryMinute(), AssertMessages.liveBlogPostIncorrectProperty(StringConstants.INJURY_MINUTE_STRING));

        liveBlogPost.editPost();
        editorialPage.editPostSection().asserts()
                .assertToggleButtonOn(LiveBlogNewPostToggleButtonEnum.ADD_MINUTE)
                .assertToggleButtonOff(LiveBlogNewPostToggleButtonEnum.ADD_INJURY_MINUTE);

        editorialPage.editPostSection().map().addMinuteTextField().validateTextIs(EXPECTED_MINUTES);
        editorialPage.editPostSection().asserts().assertAddInjuryMinuteTextFieldIsNotDisplayed();
    }

    @Test
    public void minuteAndInjuryMinuteAddedToPost_when_addMinuteAndInjuryMinute_and_publishPost() {
        String expectedInjuryMinutes = "3";
        String expectedMinutesAtPost = "%s+%s".formatted(EXPECTED_MINUTES, expectedInjuryMinutes);

        newPostSection.postEditor().setText(postText);
        newPostSection.addMinute(EXPECTED_MINUTES);
        newPostSection.addInjuryMinute(expectedInjuryMinutes);
        newPostSection.publishPost();
        editorialPage.map().alertMessage().validatePostCreated();
        ProxyServer.newHar();

        LiveBlogPost liveBlogPost = postsTimelineSection.getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED).get(0);
        liveBlogPost.validateMinuteDisplayed(expectedMinutesAtPost);

        LiveBlogPostModel publishedPostResponse = editorialPage.getPublishedPostsResponse(liveBlogResponse.getId()).get(0);

        Assertions.assertEquals(EXPECTED_MINUTES, publishedPostResponse.getMinute(), AssertMessages.liveBlogPostIncorrectProperty(StringConstants.MINUTE_STRING));
        Assertions.assertEquals(expectedInjuryMinutes, publishedPostResponse.getInjuryMinute(), AssertMessages.liveBlogPostIncorrectProperty(StringConstants.INJURY_MINUTE_STRING));

        liveBlogPost.editPost();
        editorialPage.editPostSection().asserts()
                .assertToggleButtonOn(LiveBlogNewPostToggleButtonEnum.ADD_MINUTE)
                .assertToggleButtonOn(LiveBlogNewPostToggleButtonEnum.ADD_INJURY_MINUTE);

        editorialPage.editPostSection().map().addMinuteTextField().validateTextIs(EXPECTED_MINUTES);
        editorialPage.editPostSection().map().addInjuryMinuteTextField().validateTextIs(expectedInjuryMinutes);
    }

    @Test
    public void addSingleEventToPost_when_createNewLiveBlogAddSportEvent_and_toggleSportEventButtonIsHidden() {
        var postText = faker.lorem().paragraph(2);
        newPostSection.postEditor().setText(postText);
        newPostSection.publishPost();
        ProxyServer.newHar();

        var postResponse = getLiveBlogPostsResponse(liveBlogResponse);
        var firstPost = postResponse.get(0);

        editorialPage.asserts().assertSportTagEventButtonNotDisplayed();
        Assertions.assertAll(
                () -> Assertions.assertFalse(firstPost.getSportEvent().getId().isEmpty(), AssertMessages.entityNotExpected("Sport event id")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getUuid().isEmpty(), AssertMessages.entityNotExpected("Sport event UUID")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getHomeTeam().getName().isEmpty(), AssertMessages.entityNotExpected("Home team name")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getAwayTeam().getName().isEmpty(), AssertMessages.entityNotExpected("Away team name")));
    }
}