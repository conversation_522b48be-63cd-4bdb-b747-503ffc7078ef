package liveblogs.configurationtests;

import categories.CMSStories;
import categories.CMSTags;
import categories.LiveBlogTag;
import categories.SMPCategories;
import com.google.gson.reflect.TypeToken;
import data.constants.*;
import data.models.lists.ListModel;
import data.models.lists.ListModelWithoutConfiguration;
import data.models.liveblogapi.LiveBlogResponseModel;
import facades.ContentApiFacade;
import factories.liveblog.LiveBlogSportConfigurationSectionFieldsFactory;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.http.HttpMethod;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.LIVEBLOG)
@Tag(LiveBlogTag.ADDITIONAL_SETTINGS_SECTION)
@Story(CMSStories.LIVEBLOG)
public class AdditionalSettingsAllFeaturesTests extends BaseLiveBlogTests {

    private LiveBlogResponseModel createdLiveBlog;
    protected ListModel listCreatedViaAPI;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ProxyServer.newHar();
        configurationPage.openCreatePage();
        configurationPage.browser().refresh();
        liveBlogsPage.waitPageToLoad();
        createdLiveBlog = null;

        listCreatedViaAPI = new ContentApiFacade().createList(ListContentType.EDITORIAL);
        var sportConfigurationSectionData = LiveBlogSportConfigurationSectionFieldsFactory.buildAllFields(footballApiFacade.getNotStartedMatch());
        configurationPage.completeEventDetailsSection(eventDetailsSectionData);
        configurationPage.fillSportConfigurationSectionAndProceed(sportConfigurationSectionData);
        configurationPage.waitPageToLoad();
    }

    @Override
    public void afterEach() {
        liveBlogApiFacade.deleteLiveBlogs(createdLiveBlog);
    }

    @Test
    public void activeHeadingDisplayed_when_navigateToAdditionalSettingsSection() {
        additionalSettingsSection.asserts().assertSectionLoaded();
    }

    @Test
    public void expectedComponentLoaded_when_navigateToAdditionalSettingsSection_and_checkDefaultLanguage() {
        additionalSettingsSection.map().languageSelect().validateIsVisible();
        Assertions.assertEquals(Language.ENGLISH.getValue(), additionalSettingsSection.map().languageSelect().getText(), AssertMessages.entityNotExpected("Language default option"));
    }

    @Test
    public void expectedComponentLoaded_when_navigateToAdditionalSettingsSection_and_checkPagination() {
        var expectedDefaultValue = "25";
        var expectedPlaceholder = PlaceholderField.SELECT.getValue();
        var expectedType = "number";
        var expectedLabel = "Pagination";
        var expectedClass = "form-control";

        additionalSettingsSection.map().paginationTextField().validateIsVisible();
        Assertions.assertEquals(expectedPlaceholder, additionalSettingsSection.map().paginationTextField().getPlaceholder(), AssertMessages.entityNotExpected("Placeholder"));
        Assertions.assertTrue(additionalSettingsSection.map().paginationTextField().getHtmlClass().contains(expectedClass), AssertMessages.entityNotExpected("Class"));
        Assertions.assertEquals(expectedType, additionalSettingsSection.map().paginationTextField().getAttribute("type"), AssertMessages.entityNotExpected("Type"));
        Assertions.assertEquals(expectedDefaultValue, additionalSettingsSection.map().paginationTextField().getAttribute("value"), AssertMessages.entityNotExpected("Value"));
        Assertions.assertEquals(expectedLabel, additionalSettingsSection.map().paginationTextField().getWrappedElement().findElement(By.xpath("./..//preceding-sibling::label")).getText(), AssertMessages.entityNotExpected("Label"));
    }

    @Test
    public void expectedComponentLoaded_when_navigateToAdditionalSettingsSection_and_checkAuthors() {
        var expectedOptions = Arrays.asList("sportal bg", "sportal365", "John Doe");
        var expectedPlaceholder = PlaceholderField.SELECT.getValue();

        additionalSettingsSection.map().authorsSelect().validateIsVisible();
        expectedOptions.stream()
                .forEach((item) -> {
                    additionalSettingsSection.map().authorsSelect().clearSelection();
                    additionalSettingsSection.map().authorsSelect().filter(item, false);
                    var values = additionalSettingsSection.map().authorsSelect().getOptionsValues();
                    Assertions.assertTrue(values.contains(item), AssertMessages.entityNotExpected(item, values, "Authors options"));
                });

        Assertions.assertEquals(expectedPlaceholder, additionalSettingsSection.map().authorsSelect().getText(), AssertMessages.entityNotExpected("Author placeholder"));
    }

    @Test
    public void expectedComponentLoaded_when_navigateToAdditionalSettingsSection_and_checkCollaborators() {
        var expectedOptions = Arrays.asList("sportal bg", "sportal365", "John Doe");
        var expectedPlaceholder = PlaceholderField.SELECT.getValue();

        additionalSettingsSection.map().addCollaboratorButton().click();
        additionalSettingsSection.map().collaboratorAuthorSelect().validateIsVisible();
        additionalSettingsSection.map().collaboratorAdditionalInfoSelect().validateIsVisible();
        additionalSettingsSection.map().collaboratorAddButton().validateIsVisible();
        additionalSettingsSection.map().collaboratorAddButton().validateIsDisabled();
        additionalSettingsSection.map().collaboratorCloseButton().validateIsVisible();

        expectedOptions.stream()
                .forEach((item) -> {
                    additionalSettingsSection.map().collaboratorAuthorSelect().clearSelection();
                    additionalSettingsSection.map().collaboratorAuthorSelect().filter(item, false);
                    var values = additionalSettingsSection.map().collaboratorAuthorSelect().getOptionsValues();
                    Assertions.assertTrue(values.contains(item), AssertMessages.entityNotExpected(item, values, "Collaborators options"));
                });

        Assertions.assertEquals(expectedPlaceholder, additionalSettingsSection.map().collaboratorAdditionalInfoSelect().getPlaceholder(), AssertMessages.entityNotExpected("Collaborators placeholder"));

        additionalSettingsSection.map().collaboratorAuthorSelect().selectOptionByIndex(0);
        additionalSettingsSection.map().collaboratorAddButton().click();
        additionalSettingsSection.map().collaboratorDiv().validateIsVisible();
    }

    @Test
    //@Issue("SBE-3533")
    public void expectedComponentLoaded_when_navigateToAdditionalSettingsSection_and_checkList() {
        additionalSettingsSection.map().addToListButton().click();
        additionalSettingsSection.map().listSelect().validateIsVisible();
        additionalSettingsSection.map().listAddButton().validateIsVisible();
        additionalSettingsSection.map().listAddButton().validateIsDisabled();
        additionalSettingsSection.map().listCloseButton().validateIsVisible();

        String expectedSearchUrl = ContentApiUrl.EDITORIAL_LISTS_LIST_ALL.getUrl();
        ProxyServer.waitForResponse(additionalSettingsSection.browser().getWrappedDriver(), expectedSearchUrl, HttpMethod.GET, 0);
        Type responseListType = new TypeToken<ArrayList<ListModelWithoutConfiguration>>() {
        }.getType();
        Object listsResponse = Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedSearchUrl, HttpMethod.GET.toString(), responseListType));

        Assertions.assertFalse(((ArrayList<ListModel>)listsResponse).isEmpty(), AssertMessages.responseNotContains("List"));
    }

    @Test
    public void warningMessageLoaded_when_navigateToAdditionalSettingsSection_and_tryToAddToList() {
        var expectedSearchUrl = ContentApiUrl.EDITORIAL_LISTS_LIST_ALL.url;
        var expectedAlertText = "Create the live blog to update the list with it";

        additionalSettingsSection.map().addToListButton().click();
        ProxyServer.waitForResponse(additionalSettingsSection.browser().getWrappedDriver(), expectedSearchUrl, HttpMethod.GET, 0);

        additionalSettingsSection.map().listSelect().selectOptionByIndex(0);
        additionalSettingsSection.map().listAddButton().click();

        additionalSettingsSection.map().listAlertDiv().validateIsVisible();
        additionalSettingsSection.map().listAlertDiv().validateTextIs(expectedAlertText);
    }

    @Test
    public void addLiveBlogToListSuccessfully_when_createNewLiveBlog_and_verifyLiveBlogIsAddedToCorrespondingList() {
        liveBlogsPage.configurationPage().clickCreateButton();
//        liveBlogsPage.map().alertMessage().validateLiveBlogCreateSuccessful();

        configurationPage.openEditPageForLiveBlog(liveBlogsPage.getCreatedLiveBlogResponse().getId());
        configurationPage.clickAdditionalSettingsSection();
        additionalSettingsSection.addToList(listCreatedViaAPI.getTitle());
        createdLiveBlog = liveBlogsPage.getCreatedLiveBlogResponse();

//        liveBlogsPage.map().alertMessage().validateListUpdateSuccessful();
        configurationPage.asserts().assertExpectedListIsAddedToTheLiveBlog(listCreatedViaAPI.getTitle());
    }

    @Test
    public void errorMessageDisplayed_when_tryToAddOverTheMinimumItemsLiveBlogToList_and_verifyLiveBlogIsNotAddedToList() {
        listCreatedViaAPI = contentApiFacade.createList(ListContentType.EDITORIAL, 0, 0);

        liveBlogsPage.configurationPage().clickCreateButton();
//        liveBlogsPage.map().alertMessage().validateLiveBlogCreateSuccessful();

        configurationPage.openEditPageForLiveBlog(liveBlogsPage.getCreatedLiveBlogResponse().getId());
        configurationPage.clickAdditionalSettingsSection();
        additionalSettingsSection.selectList(listCreatedViaAPI.getTitle());
        additionalSettingsSection.addCurrentLiveBlogToList(listCreatedViaAPI.getTitle());
        createdLiveBlog = liveBlogsPage.getCreatedLiveBlogResponse();

        additionalSettingsSection.asserts().assertSaveButtonIsDisabled();
        additionalSettingsSection.asserts().assertErrorMessageDoNotMeetRequirementsOfTheList();
    }
}