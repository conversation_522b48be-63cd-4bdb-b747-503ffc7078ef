package liveblogs.configurationtests;

import categories.CMSStories;
import categories.CMSTags;
import categories.LiveBlogTag;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.footballapi.v2.MatchV2Model;
import data.models.liveblogapi.ui.LiveBlogSportConfigurationSectionFieldsModel;
import data.utils.StringUtils;
import factories.liveblog.LiveBlogEventDetailsSectionFieldsFactory;
import factories.liveblog.LiveBlogSportConfigurationSectionFieldsFactory;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.LIVEBLOG)
@Tag(LiveBlogTag.SPORT_CONFIGURATION_SECTION)
@Story(CMSStories.LIVEBLOG)
public class SportConfigurationSectionTests extends BaseLiveBlogTests {

    private LiveBlogSportConfigurationSectionFieldsModel sectionData;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        MatchV2Model notStartedMatch = footballApiFacade.getNotStartedMatch();
        sectionData = LiveBlogSportConfigurationSectionFieldsFactory.buildAllFields(notStartedMatch);
        ProxyServer.newHar();
        configurationPage.openCreatePage();
        configurationPage.browser().refresh();
        liveBlogsPage.waitPageToLoad();

        eventDetailsSectionData = LiveBlogEventDetailsSectionFieldsFactory.buildMandatoryFields(eventDetailsSectionData);
        configurationPage.completeEventDetailsSection(eventDetailsSectionData);
        configurationPage.waitPageToLoad();
    }

    @Test
    public void activeHeadingDisplayed_when_navigateToSportConfigurationSection() {
        sportConfigurationSection.asserts().assertSectionLoaded();
    }

    @Test
    public void expectedComponentLoaded_when_navigateToSportConfiguration_and_checkSport() {
        var expectedOptionsNumber = 1;
        var expectedOption = "Football";
        var expectedText = "At the moment we support only football live blog events.";

        sportConfigurationSection.map().sportSelect().validateIsVisible();
        Assertions.assertEquals(expectedOptionsNumber, sportConfigurationSection.map().sportSelect().getOptionsCollection().size(), AssertMessages.entityNotExpected("Options number"));
        Assertions.assertEquals(expectedOption, sportConfigurationSection.map().sportSelect().getText(), AssertMessages.entityNotExpected("Sport Selected Option"));
        Assertions.assertEquals(expectedText, sportConfigurationSection.map().sportSelect().getWrappedElement().findElement(By.xpath(".//following-sibling::h6")).getText(), AssertMessages.entityNotExpected("Following test"));
    }

    @Test
    public void expectedComponentLoaded_when_navigateToSportConfiguration_and_checkTournament() {
        String expectedPlaceholder = PlaceholderField.SELECT.getValue();
        String expectedText = "We will use the current season of the selected tournament.";

        sportConfigurationSection.map().tournamentSelect().validateIsVisible();
        Assertions.assertEquals(expectedPlaceholder, sportConfigurationSection.map().tournamentSelect().getText(), AssertMessages.entityNotExpected("Sport Selected Placeholder"));
        Assertions.assertEquals(expectedText, sportConfigurationSection.map().tournamentSelect().getWrappedElement().findElement(By.xpath(".//following-sibling::h6")).getText(), AssertMessages.entityNotExpected("Following test"));

        sportConfigurationSection.map().tournamentSelect().filter(sectionData.getTournament().getName(), true);
        String expectedRequestUrl = "%s&entity_type=tournament".formatted(FootballApiUrl.SEARCH.url + StringUtils.replaceSpacesWithSymbol(sectionData.getTournament().getName(), "%20")).replace("'", "%27");
        ProxyServer.assertRequestMade(expectedRequestUrl);
    }

    @Test
    public void teamComponentLoaded_when_navigateToSportConfiguration_and_selectTournament() {
        var expectedPlaceholder = PlaceholderField.SELECT.getValue();
        var expectedText = "We will use teams of the current season of the selected tournament.";

        sportConfigurationSection.map().tournamentSelect().searchSelectByText(sectionData.getTournament().getName());

        sportConfigurationSection.map().teamsSelect().validateIsVisible();
        Assertions.assertEquals(expectedPlaceholder, sportConfigurationSection.map().teamsSelect().getText(), AssertMessages.entityNotExpected("Team Selected Placeholder"));
        Assertions.assertEquals(expectedText, sportConfigurationSection.map().teamsSelect().getWrappedElement().findElement(By.xpath(".//following-sibling::h6")).getText(), AssertMessages.entityNotExpected("Following test"));
    }

    @Test
    public void eventsListLoaded_when_navigateToSportConfiguration_and_checkEvents() {
        sportConfigurationSection.map().addEventsButton().click();
        sportConfigurationSection.map().noMatchResultsDiv().get(0).validateIsVisible();
        sportConfigurationSection.map().addEventsModalCloseButton().click();

        sportConfigurationSection.map().tournamentSelect().searchSelectByText(sectionData.getTournament().getName());
        sportConfigurationSection.map().teamsSelect().selectOptionByIndex(0);

        sportConfigurationSection.map().addEventsButton().click();
        sportConfigurationSection.browser().waitForAjax();
        Assertions.assertFalse(sportConfigurationSection.map().upcomingMatchResults().isEmpty(), AssertMessages.entityNotExpected("Upcoming match results"));
    }
}