package liveblogs.createtests;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.enums.football.FootballTournamentEnum;
import data.constants.liveblog.LiveBlogPostsTimelinePostType;
import io.qameta.allure.Story;
import liveblogs.BaseLiveBlogTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import pages.liveblogspages.liveblogspage.LiveBlogsPage;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.STATIC)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.LIVEBLOG)
@Story(CMSStories.LIVEBLOG)
public class NewLiveBlogValidationsStaticProjectTests extends BaseLiveBlogTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();

        liveBlogsPage = app().goTo(LiveBlogsPage.class);
        if (unusedSportEvent == null) {
            unusedSportEvent = footballApiFacade.getNotStartedMatch(FootballTournamentEnum.BRAZIL_SERIE_A);
        }
        ProxyServer.newHar();
        createdLiveBlog = liveBlogApiFacade.createLiveBlogConfiguration(unusedSportEvent);
        editorialPage.openEditorialPageForLiveBlog(createdLiveBlog.getId());
        editorialPage.waitForPageLoad();
    }

    @Override
    public void afterEach() {
        liveBlogApiFacade.deleteLiveBlogs(createdLiveBlog);
    }

    @Test
    public void addSportTagToPost_when_creatingNewLiveBlogWithSportEntities_and_allSportTagsDisplayedInPost() {
        var postText = faker.lorem().paragraph(3);
        var teamTag = unusedSportEvent.getHomeTeam().getName();
        var teamUuId = unusedSportEvent.getHomeTeam().getId();
        var teamId = footballTeamsV2HttpRepo.getById(teamUuId);
        var coachTag = teamId.getResult().getCoach().getName();
        var playersList = footballTeamsHttpRepo.getAllPlayersByTeamId(teamUuId);
        var firstPlayer = playersList.getResult().get(0).getPlayer().getName();

        editorialPage.selectSportTag(teamTag, coachTag, firstPlayer);
        newPostSection.postEditor().setText(postText);
        newPostSection.publishPost();

        ProxyServer.newHar();
        var postResponse = getLiveBlogPostsResponse(createdLiveBlog);
        var firstPost = postResponse.get(0);

        Assertions.assertAll(
                () -> Assertions.assertFalse(firstPost.getSportEvent().getId().isEmpty(), AssertMessages.entityNotExpected("Sport event id")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getUuid().isEmpty(), AssertMessages.entityNotExpected("Sport event UUID")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getHomeTeam().getName().isEmpty(), AssertMessages.entityNotExpected("Home team name")),
                () -> Assertions.assertFalse(firstPost.getSportEvent().getAwayTeam().getName().isEmpty(), AssertMessages.entityNotExpected("Away team name")));

        var liveBlogPost = editorialPage.postsTimelineSection().getPostsType(LiveBlogPostsTimelinePostType.PUBLISHED).get(0);
        liveBlogPost.editPost();

        editorialPage.asserts().assertSportTagDisplayedInTimelinePost(firstPost.getSportEvent().getHomeTeam().getName());
    }
}