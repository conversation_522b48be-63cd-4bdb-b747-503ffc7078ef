package galleries.crud;

import categories.CMSStories;
import categories.CMSTags;
import categories.SMPCategories;
import core.CmsWebTest;
import data.constants.AssertMessages;
import data.constants.CategoryEnum;
import data.constants.ContentApiUrl;
import data.constants.StringConstants;
import data.models.galleries.GalleryResponseModel;
import data.models.galleries.Item;
import data.models.uimodels.GalleryFormModel;
import factories.galleries.GalleryFieldsFactory;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import solutions.bellatrix.web.infrastructure.ProxyServer;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.CMS)
@Tag(CMSTags.CRUD)
@Tag(CMSStories.GALLERIES)
@Story(CMSStories.GALLERIES)
public class GalleriesCRUDTests extends CmsWebTest {

    private static final String TEST_IMAGE_PNG = "testImage.png";
    private GalleryFormModel galleryRequiredFields;
    private GalleryResponseModel createdGallery;

    @Override
    protected void beforeEach() {
        ProxyServer.newHar();
        createdGallery = null;
        galleryRequiredFields = new GalleryFieldsFactory().buildGalleryRequiredFields();
    }

    @Override
    public void afterEach() {
        contentApiFacade.deleteGallery(createdGallery);
    }

    @Test
    public void errorMessageDisplayed_when_createGalleryFromContentTab_and_setMandatoryFieldsEmpty() {
        galleriesPage.openCreatePage();
        galleriesPage.setRequiredFields(null, null);
        galleriesPage.saveGalleryFromContentTab();

//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_CREATE_FAILED);
        galleriesPage.asserts().validateErrorMessageDisplayedForTitleField();
        galleriesPage.asserts().validateErrorMessageDisplayedForMainCategoryField();
    }

    @Test
    public void errorMessageDisplayed_when_createGalleryFromItemsTab_and_setMandatoryFieldsEmpty() {
        galleriesPage.openCreatePage();
        galleriesPage.clickItemsTab();
        galleriesPage.saveGalleryFromItemsTab();
//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_CREATE_FAILED);

        galleriesPage.clickContentTab();
        galleriesPage.asserts().validateErrorMessageDisplayedForTitleField();
        galleriesPage.asserts().validateErrorMessageDisplayedForMainCategoryField();
    }

    @Test
    public void galleryCreatedSuccessfully_when_createGalleryFromItemsTab_and_uploadNewPicture() {
        galleriesPage.openCreatePage();
        galleriesPage.setRequiredFields(galleryRequiredFields.getTitle(), galleryRequiredFields.getMainCategory());
        galleriesPage.clickItemsTab();
        galleriesPage.uploadNewImage(getTestFilePath(TEST_IMAGE_PNG));
        galleriesPage.saveGalleryFromItemsTab();

//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_CREATED);
        createdGallery = galleriesPage.getCreateGalleryResponse();

        validateGalleryCreatedSuccessfully(createdGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_createGalleryFromItemsTab_and_setAlreadyUploadedImage() {
        galleriesPage.openCreatePage();
        galleriesPage.setRequiredFields(galleryRequiredFields.getTitle(), galleryRequiredFields.getMainCategory());
        galleriesPage.clickItemsTab();
        galleriesPage.selectAlreadyUploadedImage();
        galleriesPage.saveGalleryFromItemsTab();

//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_CREATED);
        createdGallery = galleriesPage.getCreateGalleryResponse();

        validateGalleryCreatedSuccessfully(createdGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_createGalleryFromContentTab_and_uploadNewPicture() {
        galleriesPage.openCreatePage();
        galleriesPage.setRequiredFields(galleryRequiredFields.getTitle(), galleryRequiredFields.getMainCategory());
        galleriesPage.clickItemsTab();
        galleriesPage.uploadNewImage(getTestFilePath(TEST_IMAGE_PNG));
        galleriesPage.clickContentTab();
        galleriesPage.saveGalleryFromContentTab();
//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_CREATED);
        createdGallery = galleriesPage.getCreateGalleryResponse();

        validateGalleryCreatedSuccessfully(createdGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_createGalleryFromContentTab_and_setAlreadyUploadedImage() {
        galleriesPage.openCreatePage();
        galleriesPage.setRequiredFields(galleryRequiredFields.getTitle(), galleryRequiredFields.getMainCategory());
        galleriesPage.clickItemsTab();
        galleriesPage.selectAlreadyUploadedImage();
        galleriesPage.clickContentTab();
        galleriesPage.saveGalleryFromContentTab();

//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_CREATED);
        createdGallery = galleriesPage.getCreateGalleryResponse();

        validateGalleryCreatedSuccessfully(createdGallery);
    }

    @Test
    public void galleryUpdatedSuccessfully_when_updateGallery() {
        createdGallery = createGalleryViaAPI();

        galleryRequiredFields.setTitle("%s %s".formatted(StringConstants.UPDATED_STRING, galleryRequiredFields.getTitle()));
        galleryRequiredFields.setMainCategory(CategoryEnum.BASKETBALL.getName());

        galleriesPage.openEditPage(createdGallery.getId());
        galleriesPage.setRequiredFields(galleryRequiredFields.getTitle(), galleryRequiredFields.getMainCategory());
        galleriesPage.saveGalleryFromContentTab();
        //galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_SUCCESSFULLY_UPDATED);
        galleriesPage.browser().tryWaitForResponse("statistics?galleries=%s".formatted(createdGallery.getId()));

        GalleryResponseModel updatedGallery = galleriesPage.getUpdateGalleryResponse(createdGallery.getId());
        Assertions.assertNotNull(updatedGallery, AssertMessages.requestNotCreated(StringConstants.GALLERY_FIRST_CASE_UPPERCASED_STRING));

        Assertions.assertEquals(createdGallery.getId(), updatedGallery.getId(), AssertMessages.entityNotExpected(StringConstants.ID_STRING));
        Assertions.assertEquals(galleryRequiredFields.getTitle(), updatedGallery.getTitle(), AssertMessages.entityNotExpected(StringConstants.TITLE_STRING));
        Assertions.assertEquals(galleryRequiredFields.getMainCategory(), updatedGallery.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(StringConstants.GALLERY_STRING, updatedGallery.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));

        Item expectedItem = createdGallery.getItems().get(0);
        Item actualItem = updatedGallery.getItems().get(0);

        Assertions.assertEquals(expectedItem.getId(), actualItem.getId(), AssertMessages.responseNotContains("item id"));
        Assertions.assertEquals(expectedItem.getEntityType(), actualItem.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));
    }

    @Test
    public void galleryDeletedSuccessfully_when_deleteGallery() {
        createdGallery = createGalleryViaAPI();

        galleriesPage.open();
        galleriesPage.deleteGallery(createdGallery.getId());
        galleriesPage.browser().waitForRequest(ContentApiUrl.GALLERIES_ID.getUrl().formatted(createdGallery.getId()));
//        galleriesPage.map().alertMessage().validateMessageIs(ToastMessageEnum.GALLERY_DELETED_SUCCESSFULLY);
        createdGallery = null;
    }

    private void validateGalleryCreatedSuccessfully(GalleryResponseModel createdGallery) {
        Assertions.assertNotNull(createdGallery, AssertMessages.requestNotCreated(StringConstants.GALLERY_STRING));
        Assertions.assertEquals(galleryRequiredFields.getTitle(), createdGallery.getTitle(), AssertMessages.entityNotExpected(StringConstants.TITLE_STRING));
        Assertions.assertEquals(galleryRequiredFields.getMainCategory(), createdGallery.getCategory().getTitle(), AssertMessages.entityNotExpected("main category"));
        Assertions.assertEquals(StringConstants.GALLERY_STRING, createdGallery.getEntityType(), AssertMessages.responseNotContains(StringConstants.ENTITY_TYPE_STRING));
    }

    private GalleryResponseModel createGalleryViaAPI() {
        return contentApiFacade.createGallery();
    }
}