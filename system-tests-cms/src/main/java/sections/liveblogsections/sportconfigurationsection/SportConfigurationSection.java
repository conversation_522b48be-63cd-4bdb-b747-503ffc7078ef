package sections.liveblogsections.sportconfigurationsection;

import com.google.common.reflect.TypeToken;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.articles.NestedMatch;
import data.models.liveblogapi.SportEvents;
import data.models.liveblogapi.ui.LiveBlogSportConfigurationSectionFieldsModel;
import data.models.related.Tournament;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.liveblogspages.configurationpage.ConfigurationPage;
import pages.liveblogspages.liveblogspage.LiveBlogsPage;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.infrastructure.ProxyServer;
import solutions.bellatrix.web.pages.WebSection;

import java.util.ArrayList;
import java.util.List;

public class SportConfigurationSection extends WebSection<Map, Asserts> {

    public void waitSectionToLoad() {
        map().sportSelect().toExist().toBeVisible().waitToBe();
        new ConfigurationPage().asserts().validateSportConfigurationHeadingActive();
    }

    public List<NestedMatch> getUpcomingEvents(LiveBlogSportConfigurationSectionFieldsModel dataSection) {
        var tournamentId = dataSection.getTournament().getId();
        var seasonId = dataSection.getSeason().getId();
        var teamId = dataSection.getTeams()
                .stream()
                .filter(t -> t.getName().equals(dataSection.getTeams().get(0).getName()))
                .findFirst()
                .orElseThrow()
                .getId();

        browser().waitForAjax();
        var expectedSearchUrl = FootballApiUrl.MATCHES.url + "?sort_direction=asc&limit=10&tournament_ids=%s&season_ids=%s&status_types=notstarted,inprogress&team_ids=%s&team_ids_operator=OR".formatted(tournamentId, seasonId, teamId).replace(",", "%2C");
        ProxyServer.waitForResponse(browser().getWrappedDriver(), expectedSearchUrl, HttpMethod.GET, 0);
        var teamResponseType = new TypeToken<ArrayList<NestedMatch>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(expectedSearchUrl, HttpMethod.GET.toString(), teamResponseType);
    }

    public void fillTournament(String expectedTournament) {
        map().tournamentSelect().searchSelectByText(expectedTournament);
    }

    public void fillTournament(Tournament expectedTournament) {
        map().tournamentSelect().searchSelectById(expectedTournament.getName(), expectedTournament.getId().toString());
    }

    public void selectTeam(String teamName) {
        map().teamsSelect().searchSelectByText(teamName);
    }

    public void populateRequiredFields(LiveBlogSportConfigurationSectionFieldsModel dataSection, int numberOfAdditionalEvents) {
        map().addEventsButton().click();
        map().noMatchResultsDiv().get(0).validateIsVisible();
        map().addEventsModalCloseButton().click();

        fillTournament(dataSection.getTournament());
        selectTeam(dataSection.getTeams().get(0).getName());

        map().addEventsButton().click();
        selectNotSelectedEvent(dataSection);
        map().addEventsModalAddButton().click();

        for (int i = 0; i < numberOfAdditionalEvents; i++) {
            map().addEventsButton().click();
            selectNotSelectedEvent(dataSection);
            map().addEventsModalAddButton().click();
        }
    }

    private void selectNotSelectedEvent(LiveBlogSportConfigurationSectionFieldsModel dataSection) {
        List<SportEvents> alreadyUsedEvents = new LiveBlogsPage().collectUsedSportEvents();

        while (map().loadMoreItemsEventModalButton() != null) {
            Log.info("Load more button found! Clicking...");
            map().loadMoreItemsEventModalButton().click();
            browser().waitForAjax();
            new LiveBlogsPage().waitForSpinners();
        }

        if (dataSection.getMatch() != null) {
            map().upcomingMatchResults()
                    .stream()
                    .filter(x -> x.getText().contains(dataSection.getMatch().getMatchTitleWithoutStatus()))
                    .findFirst().orElseThrow(() -> new RuntimeException("Event with title '%s' from date %s not found.".formatted(dataSection.getMatch().getMatchTitleWithoutStatus(), dataSection.getMatch().getKickoffTime())))
                    .select();
        } else {
            List<NestedMatch> upcomingEvents = getUpcomingEvents(dataSection);
            List<String> usedEventsIds = alreadyUsedEvents.stream().map(SportEvents::getId).toList();

            for (NestedMatch event : upcomingEvents) {
                if (!usedEventsIds.contains(event.getId().toString())) {
                    map().upcomingMatchResults().get(upcomingEvents.indexOf(event)).select();
                    break;
                }
            }
        }
    }

    public void selectAlreadySelectedEvent(LiveBlogSportConfigurationSectionFieldsModel dataSection) {
        var alreadyUsedEvents = new LiveBlogsPage().collectUsedSportEvents();
        var upcomingEvents = getUpcomingEvents(dataSection);

        List<String> usedEventsIds = alreadyUsedEvents.stream().map(SportEvents::getId).toList();
        for (NestedMatch event : upcomingEvents) {
            if (usedEventsIds.contains(event.getId().toString())) {
                map().upcomingMatchResults().get(upcomingEvents.indexOf(event)).select();
                break;
            }
        }
    }
}