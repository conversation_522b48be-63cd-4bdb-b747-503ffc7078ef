package sections.liveblogsections.editorialsections.linkpopupsection;

import solutions.bellatrix.web.pages.PageAsserts;

public class Asserts extends PageAsserts<Map> {
    public void validateExpectedControlsExist() {
        map().blockyEdit().linkTextField().validateIsVisible();
        map().blockyEdit().textTextField().validateIsVisible();
        map().blockyEdit().openInNewWindowRadioButton().validateIsVisible();
        map().blockyEdit().openInSameWindowRadioButton().validateIsVisible();
        map().blockyEdit().linkRadioButton().validateIsVisible();
        map().blockyEdit().articleRadioButton().validateIsVisible();
        map().blockyEdit().videoRadioButton().validateIsVisible();
        map().blockyEdit().galleryRadioButton().validateIsVisible();
        map().blockyEdit().audioRadioButton().validateIsVisible();
        map().blockyEdit().documentRadioButton().validateIsVisible();
    }
}
