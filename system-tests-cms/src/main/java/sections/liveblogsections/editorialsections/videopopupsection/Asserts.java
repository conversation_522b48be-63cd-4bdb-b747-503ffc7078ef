package sections.liveblogsections.editorialsections.videopopupsection;

import solutions.bellatrix.web.pages.PageAsserts;

public class Asserts extends PageAsserts<Map> {
    public void validateExpectedControlsExist() {
        map().blockyEdit().chooseVideoSelect().validateIsVisible();
        map().blockyEdit().startAtField().validateIsVisible();
        map().blockyEdit().imageWidthOptions().get(0).validateIsVisible();
        map().blockyEdit().imageAlignmentOptions().get(0).validateIsVisible();
    }
}
