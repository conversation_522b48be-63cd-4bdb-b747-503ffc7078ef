package sections.liveblogsections.eventdetailssection;

import com.google.common.reflect.TypeToken;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.MainMediaEnum;
import data.constants.liveblog.LiveBlogTypeEnum;
import data.customelements.DateTimePicker;
import data.models.categories.Category;
import data.models.footballapi.odd_providers.OddProviderModel;
import data.models.liveblogapi.ui.LiveBlogEventDetailsSectionFieldsModel;
import data.utils.StringUtils;
import facades.ContentApiFacade;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.liveblogspages.configurationpage.ConfigurationPage;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Label;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.infrastructure.ProxyServer;
import solutions.bellatrix.web.pages.PageAsserts;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class Asserts extends PageAsserts<Map> {

    private final EventDetailsSection eventDetailsSection = new ConfigurationPage().eventDetailsSection();

    public Asserts assertElementsPageFor(LiveBlogTypeEnum liveBlogType) {
        assertEventTitleDefaultState();
        assertDescriptionDefaultState();
        assertLiveBlogSlugDefaultState();
        assertGenerateSlugCheckboxDefaultState();
        assertSeoTitleDefaultState();
        assertSeoDescriptionDefaultState();
        assertStartTimeDefaultState();
        assertEndTimeDefaultState();
        assertMainCategoryDefaultState();
        assertAdditionalCategoriesDefaultState();
        assertSponsorsDefaultState();
        assertTypeDefaultStateFor(liveBlogType);
        assertMatchHeaderDefaultStateFor(liveBlogType);
        assertStatusFieldDefaultState();
        assertMainMediaDefaultState();
        return this;
    }

    public Asserts assertEventTitleDefaultState() {
        String expectedLabel = "Event title";
        map().eventTitleTextField().validateIsVisible();
        map().eventTitleTextField().validatePlaceholderIs(PlaceholderField.THE_TITLE_OF_YOUR_CONTENT.getValue());
        assertLabelIs(expectedLabel, map().eventTitleTextField());
        assertFieldRequired(map().eventTitleTextField());
        return this;
    }

    public Asserts assertDescriptionDefaultState() {
        String expectedLabel = "Description";
        map().descriptionTextField().validateIsVisible();
        map().descriptionTextField().validatePlaceholderIs(PlaceholderField.DESCRIPTION_OF_YOUR_CONTENT.getValue());
        assertLabelIs(expectedLabel, map().descriptionTextField());
        return this;
    }

    public Asserts assertLiveBlogSlugDefaultState() {
        String expectedLabel = "Live Blog slug";
        String expectedPlaceholder = PlaceholderField.SEO_FRIENDLY_URL.getValue();
        map().liveBlogSlugTextField().validateIsVisible();
        map().liveBlogSlugTextField().validatePlaceholderIs(expectedPlaceholder);
        assertLabelIs(expectedLabel, map().liveBlogSlugTextField());
        assertSlugFieldEnablementState();
        return this;
    }

    public Asserts assertSeoTitleDefaultState() {
        String expectedLabel = "SEO Title";
        String expectedPlaceholder = map().seoTitleTextField().isDisabled() ? StringConstants.EMPTY_STRING : PlaceholderField.TITLE_USED_FOR_SEO_PURPOSES.getValue();
        map().seoTitleTextField().validateIsVisible();
        map().seoTitleTextField().validatePlaceholderIs(expectedPlaceholder);
        assertLabelIs(expectedLabel, map().seoTitleTextField());
        assertSeoTitleFieldEnablementState();
        return this;
    }

    public Asserts assertSeoDescriptionDefaultState() {
        String expectedLabel = "SEO Description";
        map().seoDescriptionTextField().validateIsVisible();
        map().seoDescriptionTextField().validatePlaceholderIs(PlaceholderField.DESCRIPTION_USED_FOR_SEO_PURPOSES.getValue());
        assertLabelIs(expectedLabel, map().seoDescriptionTextField());
        return this;
    }

    public Asserts assertStartTimeDefaultState() {
        assertTimeTypeElement("Start time");
        return this;
    }

    public Asserts assertEndTimeDefaultState() {
        assertTimeTypeElement("End time");
        return this;
    }

    public Asserts assertMainCategoryDefaultState() {
        String expectedLabel = "Main Category";
        Set<String> expectedCategories = new LinkedHashSet<>(new ContentApiFacade().getTitlesCategories());
        Set<String> actualCategories = new LinkedHashSet<>(map().mainCategorySelect().getOptionsValues());

        map().mainCategorySelect().validateIsVisible();
        map().mainCategorySelect().validateTextIs(PlaceholderField.SELECT.getValue());
        assertLabelIs(expectedLabel, map().mainCategorySelect());
        assertFieldRequired(map().mainCategorySelect());
        Assertions.assertEquals(expectedCategories, actualCategories, AssertMessages.entityNotExpected("%s options".formatted(expectedLabel)));
        return this;
    }

    public Asserts assertAdditionalCategoriesDefaultState() {
        String expectedLabel = "Additional Categories";
        Set<String> expectedCategories = new LinkedHashSet<>(new ContentApiFacade().getTitlesCategories());
        Set<String> actualCategories = new LinkedHashSet<>(map().additionalCategoriesSelect().getOptionsValues());

        map().additionalCategoriesSelect().validateIsVisible();
        map().additionalCategoriesSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        assertLabelIs(expectedLabel, map().additionalCategoriesSelect());
        Assertions.assertEquals(expectedCategories, actualCategories, AssertMessages.entityNotExpected("%s options".formatted(expectedLabel)));
        return this;
    }

    public void assertSponsorsDefaultState() {
        String expectedLabel = "Sponsors";
        map().sponsorsSelect().validateIsVisible();
        map().sponsorsSelect().validatePlaceholderIs(PlaceholderField.SELECT.getValue());
        assertLabelIs(expectedLabel, map().sponsorsSelect());
        assertSponsors();
    }

    public Asserts assertSponsors() {
        var expectedOptions = Arrays.asList("Efbet", "Bet365", "BWin", "Winbet", "BetanoBG", "Inbet", "Superbet.ro");
        var responseOptions = new ArrayList<String>();

        var responseListType = new TypeToken<ArrayList<OddProviderModel>>() {
        }.getType();
        ProxyServer.waitForResponse(eventDetailsSection.browser().getWrappedDriver(), FootballApiUrl.ODD_PROVIDERS.url, HttpMethod.GET, 0);
        ArrayList<OddProviderModel> oddsResponse = Objects.requireNonNull(ProxyServer.getResponseByUrl(FootballApiUrl.ODD_PROVIDERS.url, HttpMethod.GET.toString(), responseListType));

        for (OddProviderModel entity : oddsResponse) {
            responseOptions.add(entity.getName());
        }

        Assertions.assertLinesMatch(expectedOptions, eventDetailsSection.map().sponsorsSelect().getOptionsValues());
        Assertions.assertLinesMatch(expectedOptions, responseOptions);
        return this;
    }

    public Asserts assertTypeDefaultStateFor(LiveBlogTypeEnum liveBlogType) {
        List<String> expectedOptions = Arrays.asList(LiveBlogTypeEnum.FOOTBALL_MATCH_BLOG.getValue(), LiveBlogTypeEnum.LIVE_NEWS_BLOG.getValue());
        String expectedLabel = "Type";
        map().typeSelect().validateIsVisible();
        map().typeSelect().validateTextIs(liveBlogType.getValue());
        assertLabelIs(expectedLabel, map().typeSelect());
        Assertions.assertLinesMatch(expectedOptions, eventDetailsSection.map().typeSelect().getOptionsValues(), AssertMessages.entityNotExpected("Type options"));
        return this;
    }

    public Asserts assertMatchHeaderDefaultStateFor(LiveBlogTypeEnum liveBlogType) {
        if (liveBlogType == LiveBlogTypeEnum.LIVE_NEWS_BLOG) {
            Assertions.assertNull(eventDetailsSection.map().showMatchHeaderButton(), "Match header is displayed but it should not be");
        } else {
            String expectedLabel = "Match Header";
            String expectedText = "Show the match header";
            String expectedClass = "cursor-pointer";
            map().showMatchHeaderButton().validateIsVisible();
            assertLabelIs(expectedLabel, map().showMatchHeaderButton(), "./..//preceding-sibling::label");
            Assertions.assertTrue(eventDetailsSection.map().showMatchHeaderButton().getHtmlClass().contains(expectedClass), AssertMessages.entityNotExpected("Class"));
            Assertions.assertEquals(expectedText, eventDetailsSection.map().showMatchHeaderButton().getWrappedElement().findElement(By.xpath(".//span")).getText(), AssertMessages.entityNotExpected("Text"));
        }
        return this;
    }

    public Asserts assertStatusFieldDefaultState() {
        String expectedLabel = "Status";
        List<String> expectedOptions = Arrays.asList("Active", "Archived", "Inactive");
        map().statusSelect().validateIsVisible();
        map().statusSelect().validateTextIs(expectedOptions.get(0));
        assertLabelIs(expectedLabel, map().statusSelect(), "./..//preceding-sibling::label");
        return this;
    }

    public Asserts assertMainMediaDefaultState() {
        String expectedLabel = "Main media";
        List<String> expectedOptions = MainMediaEnum.getValues();
        map().mainMediaSelect().validateIsVisible();
        map().mainMediaSelect().validateTextIs(expectedOptions.get(0));
        assertLabelIs(expectedLabel, map().mainMediaSelect(), ".//preceding::label[@for='live-blog-main-media']");
        Assertions.assertLinesMatch(expectedOptions, eventDetailsSection.map().mainMediaSelect().getOptionsValues(), AssertMessages.entityNotExpected("Main Media options"));
        assertFieldRequired(eventDetailsSection.map().mainMediaSelect(), "./../..//preceding-sibling::span");
        return this;
    }

    public void assertFieldsData(LiveBlogEventDetailsSectionFieldsModel sectionData) {
        if (sectionData.getEventTitle() != null) {
            map().eventTitleTextField().validateTextIs(sectionData.getEventTitle());
            String expectedSlug = StringUtils.replaceSpacesWithSymbol(sectionData.getEventTitle(), "-").toLowerCase();
            map().liveBlogSlugTextField().validateTextIs(expectedSlug);
        }
        if (sectionData.getDescription() != null) {
            map().descriptionTextField().validateTextIs(sectionData.getDescription());
        }
        if (sectionData.getStartTime() != null) {
            LocalDate localDate = sectionData.getStartTime().toLocalDate();
            String formattedDate = localDate.format(DateTimeFormatter.ofPattern("M/d/yyyy"));
            map().startTimeDateTimePicker().validateTextIs(formattedDate);
        }
        if (sectionData.getEndTime() != null) {
            LocalDate localDate = sectionData.getEndTime().toLocalDate();
            String formattedDate = localDate.format(DateTimeFormatter.ofPattern("M/d/yyyy"));
            map().endTimeDateTimePicker().validateTextIs(formattedDate);
        }
        if (sectionData.getMainCategory() != null) {
            map().mainCategorySelect().validateTextIs(sectionData.getMainCategory().getTitle());
        }
        if (sectionData.getAdditionalCategories() != null) {
            String expectedAdditionalCategories = sectionData.getAdditionalCategories().stream().map(Category::getTitle).toList().toString();
            String actualAdditionalCategories = Arrays.stream(map().additionalCategoriesSelect().getText().split("\n")).toList().toString();
            Assertions.assertEquals(expectedAdditionalCategories, actualAdditionalCategories, AssertMessages.entityNotExpected("Additional category"));
        }
        if (sectionData.getSponsors() != null) {
            String expectedSponsorNames = sectionData.getSponsors().stream().map(OddProviderModel::getName).toList().toString();
            String actualSponsorNames = Arrays.stream(map().sponsorsSelect().getText().split("\n")).toList().toString();
            Assertions.assertEquals(expectedSponsorNames, actualSponsorNames, AssertMessages.entityNotExpected("Sponsors"));
        }
        if (sectionData.getType() != null) {
            map().typeSelect().validateTextIs(sectionData.getType().getValue());
        }
        if (sectionData.isMatchHeader()) {
            Assertions.assertTrue(map().showMatchHeaderButton().getHtmlClass().contains("active"), AssertMessages.entityNotExpected("Match header"));
        }
        if (sectionData.getStatus() != null) {
            map().statusSelect().validateTextIs(sectionData.getStatus().getValue());
        }
        if (sectionData.getMainMedia() != null) {
            map().mainMediaSelect().validateTextIs(sectionData.getMainMedia().getValue());
        }
    }

    private void assertLabelIs(String expectedLabel, WebComponent webComponent) {
        assertLabelIs(expectedLabel, webComponent, ".//preceding-sibling::label");
    }

    private void assertLabelIs(String expectedLabel, WebComponent webComponent, String xpathExpression) {
        Assertions.assertEquals(expectedLabel, webComponent.getWrappedElement().findElement(By.xpath(xpathExpression)).getText(), AssertMessages.entityNotExpected("Label"));
    }

    private void assertFieldRequired(WebComponent webComponent) {
        assertFieldRequired(webComponent, ".//preceding-sibling::span");
    }

    private void assertFieldRequired(WebComponent webComponent, String xpathExpression) {
        Assertions.assertEquals("*", webComponent.getWrappedElement().findElement(By.xpath(xpathExpression)).getText(), AssertMessages.entityNotExpected("Required *"));
    }

    public void assertSlug(String liveBlogSlug) {
        Assertions.assertEquals(liveBlogSlug, eventDetailsSection.map().liveBlogSlugTextField().getValue(), AssertMessages.entityNotExpected("Live Blog slug"));
    }

    public void assertSeoTitle(LiveBlogEventDetailsSectionFieldsModel sectionData) {
        TextInput seoTitleField = eventDetailsSection.map().seoTitleTextField();

        if (sectionData.isCopySeoTitleFromMainTitle()) {
            seoTitleField.validateIsDisabled();
        } else {
            seoTitleField.validateNotDisabled();
        }

        Assertions.assertEquals(sectionData.getSeoTitle(), seoTitleField.getValue(), AssertMessages.entityNotExpected("SEO Title"));
    }

    private void assertSlugFieldEnablementState() {
        if (map().generateSlugAutomaticallyCheckbox().isChecked()) {
            map().liveBlogSlugTextField().validateIsDisabled();
        } else {
            map().liveBlogSlugTextField().validateNotDisabled();
        }
    }

    private void assertSeoTitleFieldEnablementState() {
        if (map().copySeoTitleFromMainTitleCheckbox().isChecked()) {
            map().seoTitleTextField().validateIsDisabled();
        } else {
            map().seoTitleTextField().validateNotDisabled();
        }
    }

    private void assertTimeTypeElement(String timeType) {
        DateTimePicker element = timeType.equals("Start time") ? map().startTimeDateTimePicker() : map().endTimeDateTimePicker();
        element.validateIsVisible();
        element.validatePlaceholderIs(PlaceholderField.YYYY_MM_DD_HH_MM.getValue());
        assertLabelIs(timeType, element, "./..//preceding-sibling::label");
        if (timeType.equals("Start time")) {
            assertFieldRequired(element, "./..//preceding-sibling::span");
        }
    }

    private Asserts assertCheckboxDefaultState(CheckBox checkbox, String expectedLabelText, boolean isChecked) {
        checkbox.validateIsVisible();
        if (isChecked) {
            checkbox.validateIsChecked();
        } else {
            checkbox.validateIsUnchecked();
        }
        checkbox.createByXPath(Label.class, ".//following-sibling::label").validateTextIs(expectedLabelText);
        return this;
    }

    public Asserts assertGenerateSlugCheckboxDefaultState() {
        return assertCheckboxDefaultState(map().generateSlugAutomaticallyCheckbox(), StringConstants.GENERATE_AUTOMATICALLY_FROM_LIVEBLOG_TITLE, true);
    }

    public Asserts assertCopySeoTitleFromMainTitleCheckboxDefaultState() {
        return assertCheckboxDefaultState(map().copySeoTitleFromMainTitleCheckbox(), StringConstants.COPY_SEO_TITLE_FROM_THE_MAIN_TITLE, true);
    }

    public Asserts assertGenerateSlugCheckboxEditLiveBlogDefaultState() {
        return assertCheckboxDefaultState(map().generateSlugAutomaticallyCheckbox(), StringConstants.GENERATE_AUTOMATICALLY_FROM_LIVEBLOG_TITLE, false);
    }

    public Asserts assertCopySeoTitleFromMainTitleCheckboxEditLiveBlogDefaultState() {
        return assertCheckboxDefaultState(map().copySeoTitleFromMainTitleCheckbox(), StringConstants.COPY_SEO_TITLE_FROM_THE_MAIN_TITLE, false);
    }
}