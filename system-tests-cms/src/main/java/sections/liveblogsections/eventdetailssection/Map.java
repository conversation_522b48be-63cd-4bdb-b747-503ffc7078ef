package sections.liveblogsections.eventdetailssection;

import data.customelements.DateTimePicker;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.*;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {

    public TextInput eventTitleTextField() {
        return create().byId(TextInput.class, "title");
    }

    public TextInput descriptionTextField() {
        return create().byId(TextInput.class, "description");
    }

    public TextInput liveBlogSlugTextField() {
        return create().byId(TextInput.class, "slug");
    }

    public CheckBox generateSlugAutomaticallyCheckbox() {
        return create().byId(CheckBox.class, "generate-slug-automatically");
    }

    public TextInput seoTitleTextField() {
        return create().byId(TextInput.class, "seo-title");
    }

    public TextInput seoDescriptionTextField() {
        return create().byId(TextInput.class, "seo-description");
    }

    public CheckBox copySeoTitleFromMainTitleCheckbox() {
        return create().byId(CheckBox.class, "generate-seo-title-automatically");
    }

    public DateTimePicker startTimeDateTimePicker() {
        return create().byId(DateTimePicker.class, "live-blog-start-time-select");
    }

    public DateTimePicker endTimeDateTimePicker() {
        return create().byId(DateTimePicker.class, "live-blog-end-time-select");
    }

    public SingleValueSelect mainCategorySelect() {
        return create().byId(SingleValueSelect.class, "live-blog-main-category-select");
    }

    public MultiValueSelect additionalCategoriesSelect() {
        return create().byId(MultiValueSelect.class, "live-blog-additional-category-select");
    }

    public MultiValueSelect sponsorsSelect() {
        return create().byId(MultiValueSelect.class, "live-blog-sponsors-select");
    }

    public SingleValueSelect typeSelect() {
        return create().byId(SingleValueSelect.class, "live-blog-type-select");
    }

    public RadioButton showMatchHeaderButton() {
        List<RadioButton> radioButtons = create().allById(RadioButton.class, "live-blog-match-header-toggle");
        if (radioButtons.isEmpty()) {
            return null;
        }
        return radioButtons.get(0);
    }

    public SingleValueSelect statusSelect() {
        return create().byId(SingleValueSelect.class, "live-blog-status-select");
    }

    public SingleValueSelect mainMediaSelect() {
        return create().byId(SingleValueSelect.class, "live-blog-main-media-type-select");
    }

    public TextInput uploadContainerInput() {
        return create().byId(TextInput.class, "live-blog-image-upload-container");
    }

    public Button uploadedImagesToggleButton() {
        return create().byId(Button.class, "live-blog-toggle-images");
    }

    public SingleValueSelect searchGallerySelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Search gallery']//following-sibling::div");
    }

    public SingleValueSelect searchVideoSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Search video']//following-sibling::div");
    }

    public TextInput embedCodeTextField() {
        return create().byXPath(TextInput.class, "//textarea[contains(@placeholder,'Embed code')]");
    }

    public Image firstUploadedImage() {
        return create().byXPath(Image.class, "(//div[@class='live-blog-masonry-grid']//img)[1]");
    }

    public Button uploadedImagesSaveButton() {
        return create().byId(Button.class, "live-blog-main-image-listing-modal-save-button");
    }

    public Button uploadedImageButton() {
        return create().byId(Button.class, "live-blog-image-upload-container-operations");
    }

    public Button editImageButton() {
        return create().byId(Button.class, "sidebar-main-media-content-edit-button");
    }

    public Button cropImageButton() {
        return create().byId(Button.class, "sidebar-main-media-content-crop-button");
    }
}
