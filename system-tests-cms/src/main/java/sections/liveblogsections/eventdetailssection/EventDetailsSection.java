package sections.liveblogsections.eventdetailssection;

import com.github.javafaker.Faker;
import data.constants.enums.CheckboxStateEnum;
import data.constants.enums.MainMediaEnum;
import data.constants.liveblog.LiveBlogStatusEnum;
import data.constants.liveblog.LiveBlogTypeEnum;
import data.models.categories.Category;
import data.models.footballapi.odd_providers.OddProviderModel;
import data.models.liveblogapi.ui.LiveBlogEventDetailsSectionFieldsModel;
import org.openqa.selenium.Keys;
import services.KeyboardShortcutsService;
import solutions.bellatrix.web.pages.WebSection;

import java.time.LocalDateTime;
import java.util.List;

public class EventDetailsSection extends WebSection<Map, Asserts> {

    public void selectAlreadyUploadedImage() {
        map().uploadedImagesToggleButton().click();
        map().firstUploadedImage().toExist().toBeClickable().waitToBe();
        map().firstUploadedImage().click();
        map().uploadedImagesSaveButton().click();
        map().editImageButton().toExist().toBeClickable().waitToBe();
    }

    public void fillSection(LiveBlogEventDetailsSectionFieldsModel data) {
        fillEventTitleField(data.getEventTitle());
        fillDescriptionField(data.getDescription());
        selectGenerateAutomaticallyFromLiveBlogTitleCheckbox(data.getGenerateSlugAutomatically());
        fillLiveBlogSlugField(data.getLiveBlogSlug());
        if (!data.isCopySeoTitleFromMainTitle() || data.getSeoTitle() != null) {
            uncheckCopySeoTitleFromMainTitle();
        }
        fillSeoTitleField(data.getSeoTitle());
        fillSeoDescriptionField(data.getSeoDescription());
        fillStartTimeField(data.getStartTime());
        fillEndTimeField(data.getEndTime());
        fillMainCategoryField(data.getMainCategory() != null ? data.getMainCategory().getTitle() : null);
        fillAdditionalCategoriesField(data.getAdditionalCategories());
        fillSponsorsField(data.getSponsors());
        selectType(data.getType());
        toggleMatchHeader(data.isMatchHeader());
        selectStatus(data.getStatus());
        selectMainMedia(data.getMainMedia());
    }

    public EventDetailsSection fillEventTitleField(String eventTitle) {
        if (eventTitle != null) {
            map().eventTitleTextField().setText(eventTitle);
        }
        return this;
    }

    public EventDetailsSection fillDescriptionField(String description) {
        if (description != null) {
            map().descriptionTextField().setText(description);
        }
        return this;
    }

    public EventDetailsSection selectGenerateAutomaticallyFromLiveBlogTitleCheckbox(CheckboxStateEnum state) {
        map().generateSlugAutomaticallyCheckbox().check(state.isChecked());
        return this;
    }

    public EventDetailsSection fillLiveBlogSlugField(String liveBlogSlug) {
        if (liveBlogSlug != null && !map().liveBlogSlugTextField().isDisabled()) {
            //ToDo remove code at 84 lines when setText method is fixed (getWrappedElement().clear() in defaultSetText method working accordingly)
            map().liveBlogSlugTextField().getWrappedElement().sendKeys(Keys.chord(KeyboardShortcutsService.getModifierKey(), "a", Keys.BACK_SPACE));
            map().liveBlogSlugTextField().setText(liveBlogSlug);
        }
        return this;
    }

    public EventDetailsSection fillSeoTitleField(String seoTitle) {
        if (seoTitle != null) {
            map().seoTitleTextField().setText(seoTitle);
        }
        return this;
    }

    public EventDetailsSection fillSeoDescriptionField(String seoDescription) {
        if (seoDescription != null) {
            map().seoDescriptionTextField().setText(seoDescription);
        }
        return this;
    }

    public EventDetailsSection checkCopySeoTitleFromMainTitle() {
        map().copySeoTitleFromMainTitleCheckbox().check();
        return this;
    }

    public EventDetailsSection uncheckCopySeoTitleFromMainTitle() {
        map().copySeoTitleFromMainTitleCheckbox().uncheck();
        return this;
    }


    public EventDetailsSection fillStartTimeField(LocalDateTime startTime) {
        if (startTime != null) {
            map().startTimeDateTimePicker().setDateTime(startTime);
        }
        return this;
    }

    public EventDetailsSection fillEndTimeField(LocalDateTime endTime) {
        if (endTime != null) {
            map().endTimeDateTimePicker().setDateTime(endTime);
        }
        return this;
    }

    public EventDetailsSection fillMainCategoryField(String mainCategory) {
        if (mainCategory != null) {
            map().mainCategorySelect().searchSelectByText(mainCategory);
        }
        return this;
    }

    public EventDetailsSection fillAdditionalCategoriesField(List<Category> additionalCategories) {
        if (additionalCategories != null) {
            for (Category additionalCategory : additionalCategories) {
                map().additionalCategoriesSelect().searchSelectByText(additionalCategory.getTitle());
            }
        }
        return this;
    }

    public EventDetailsSection fillSponsorsField(List<OddProviderModel> sponsors) {
        if (sponsors != null) {
            for (OddProviderModel sponsor : sponsors) {
                map().sponsorsSelect().selectOptionByExactText(sponsor.getName());
            }
        }
        return this;
    }

    public EventDetailsSection selectType(LiveBlogTypeEnum type) {
        if (type != null) {
            map().typeSelect().selectOptionByText(type.getValue());
        }
        return this;
    }

    public EventDetailsSection toggleMatchHeader(boolean matchHeader) {
        return this;
    }

    public EventDetailsSection selectStatus(LiveBlogStatusEnum liveBlogStatus) {
        if (liveBlogStatus != null) {
            map().statusSelect().selectOptionByText(liveBlogStatus.getValue());
        }
        return this;
    }

    public EventDetailsSection selectMainMedia(MainMediaEnum mainMedia) {
        if (mainMedia != null) {
            map().mainMediaSelect().selectOptionByText(mainMedia.getValue());
            switch (mainMedia) {
                case IMAGE:
                    selectAlreadyUploadedImage();
                    break;
                default:
                    throw new IllegalArgumentException("Main media type not supported: " + mainMedia);
            }
        }
        return this;
    }
}