package sections.sidebarsections.generalsection;

import data.constants.ContentPageSection;
import data.customelements.DateTimePicker;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    private static final ContentPageSection section = ContentPageSection.GENERAL;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", section.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", section.getPartialId()));
    }

    public Button toggle() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//i[contains(@class,'fa-toggle')]", section.getPartialId()));
    }

    public Button editModeSectionNumber() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'font')]", section.getPartialId()));
    }

    public Button editModeSectionDragIcon() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'cursor-draggable')]", section.getPartialId()));
    }

    public SingleValueSelect mainCategorySelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Main Category']//parent::div");
    }

    public DateTimePicker publishedFrom() {
        return create().byId(DateTimePicker.class, "sidebar-general-content-published-from-date");
    }

    public DateTimePicker publishedUntil() {
        return create().byId(DateTimePicker.class, "sidebar-general-content-published-to-date");
    }

    public DateTimePicker contentUpdate() {
        return create().allById(DateTimePicker.class, "sidebar-general-content-content-updated-at-date")
                .stream()
                .findFirst()
                .map(element -> element.isPeriodSupported(true))
                .orElse(null);
    }

    public MultiValueSelect additionalCategoriesSelect() {
        return create().byXPath(MultiValueSelect.class, "//label[text()='Additional Categories']//parent::div");
    }

    public MultiValueSelect authorSelect() {
        return create().byXPath(MultiValueSelect.class, "//label[text()='Author']//parent::div");
    }

    public TextInput customAuthorTextField() {
        return create().byId(TextInput.class, "sidebar-general-content-custom-author-text");
    }

    public SingleValueSelect statusSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Status']//parent::div");
    }

    public SingleValueSelect typeSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Type']//parent::div");
    }

    public CheckBox propertyCheckbox(String propertyLabel) {
        return create().byXPath(CheckBox.class,
                String.format("//label[text()='Properties']/..//label[text()='%1$s']/../input[@type='checkbox']", propertyLabel));
    }

    public MultiValueSelect distributionRegionsSelect() {
        return create().byXPath(MultiValueSelect.class, "//label[text()='Distribution Regions']//parent::div");
    }

    public MultiValueSelect distributionChannelsSelect() {
        return create().byXPath(MultiValueSelect.class, "//label[text()='Distribution Channels']//parent::div");
    }

    public SingleValueSelect originSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Origin']//parent::div");
    }

    public SingleValueSelect commentsPoliciesSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Comments policies']//parent::div");
    }

    public Div alertMessage() {
        return create().byClassContaining(Div.class, "alert");
    }
}