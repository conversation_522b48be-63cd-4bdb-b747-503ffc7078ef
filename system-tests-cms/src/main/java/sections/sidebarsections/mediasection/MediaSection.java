package sections.sidebarsections.mediasection;

import lombok.SneakyThrows;
import org.openqa.selenium.ElementNotInteractableException;
import org.openqa.selenium.NotFoundException;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.TimeoutException;
import sections.BaseSidebarSection;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.WebComponent;

public class MediaSection extends BaseSidebarSection<Map, Asserts> {
    @SneakyThrows
    public void waitForImagesToLoad() {
        Wait.retry(() -> {
                    int lastImageActualHeight = map().lastUploadedImage().getHeight();
                    int firstImageActualHeight = map().firstUploadedImage().getHeight();
                    if (lastImageActualHeight > 0 && firstImageActualHeight > 0) {
                    } else {
                        browser().injectInfoNotificationToast("Waiting for image load. Current Height: " + lastImageActualHeight);
                        throw new TimeoutException("Waiting for image load. Current Height: " + lastImageActualHeight);
                    }
                },
                5,
                1,
                TimeoutException.class, NotFoundException.class, StaleElementReferenceException.class, NumberFormatException.class, ElementNotInteractableException.class);
    }

    @Override
    protected WebComponent toggleElement() {
        return map().toggle();
    }

    @Override
    protected WebComponent toggleButtonElement() {
        return map().sectionToggleButton();
    }

    @Override
    protected WebComponent elementToWait() {
        return map().emptyMainImage();
    }

    public void uploadPicture(String path) {
        map().uploadNewImagesInput().getWrappedElement().sendKeys(path);
        browser().waitUntil(e -> !map().uploadNewImageSpinner().getHtmlClass().contains("fa-spinner"));
    }

    public void checkAddDescriptionCheckbox() {
        waitForSpinners();
        map().addDescriptionCheckBox().check();

        if (!map().addDescriptionCheckBox().isChecked()) {
            map().addDescriptionCheckBox().check();
        }
    }

    public void applyWatermark() {
        map().applyWatermarkCheckBox().check();
    }

    public void selectFirstLoadedImage() {
        map().toggleUploadedImagesButton().toBeVisible().waitToBe();
        map().toggleUploadedImagesButton().click();
        waitForImagesToLoad();
        map().firstUploadedImage().toBeVisible().waitToBe();
        map().firstUploadedImage().getWrappedElement().click();
    }

    public void openImageEditPopUp() {
        map().editImageButton().toExist().waitToBe();
        map().editImageButton().click();

        map().imageCropPopUp().toExist().waitToBe();
        map().imageEditPopUp().popUpHeading().toBeVisible().waitToBe();
        map().imageEditPopUp().authorPopUpSelect().toBeClickable().waitToBe();
    }

    public void openImageCropPopUp() {
        map().cropImageButton().toExist().waitToBe();
        map().cropImageButton().click();

        map().imageCropPopUp().toExist().waitToBe();
        map().imageEditPopUp().popUpHeading().toBeVisible().waitToBe();
    }
}
