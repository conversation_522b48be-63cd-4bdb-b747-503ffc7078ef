package sections.sidebarsections.urlssection;

import data.constants.ContentPageSection;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    private static final ContentPageSection section = ContentPageSection.URLS;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", section.getPartialId()));
    }

    public Button editModeSectionDragIcon() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'cursor-draggable')]", section.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", section.getPartialId()));
    }

    public Button toggle() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//i[contains(@class,'fa-toggle')]", section.getPartialId()));
    }

    public TextInput externalUrlField() {
        return create().byId(TextInput.class, "sidebar-urls-external-url-input");
    }

    public SingleValueSelect statusSelect() {
        return sectionDiv().createByXPath(SingleValueSelect.class, ".//label[text()='Status']//parent::div/div");
    }

    public TextInput canonicalUrlField() {
        return create().byId(TextInput.class, "sidebar-urls-canonical-url-input");
    }

    public TextInput desktopUrlField() {
        return create().byXPath(TextInput.class, "//label[@for='publicUrlDesktop']/..//input");
    }

    public Button facebookDesktop() {
        return create().byId(Button.class, "facebook-share-button-publicUrlDesktop");
    }

    public Button twitterDesktop() {
        return create().byId(Button.class, "twitter-share-button-publicUrlDesktop");
    }

    public TextInput mobileUrlField() {
        return create().byXPath(TextInput.class, "//label[@for='publicUrlMobile']/..//input");
    }

    public Button facebookMobile() {
        return create().byId(Button.class, "facebook-share-button-publicUrlMobile");
    }

    public Button twitterMobile() {
        return create().byId(Button.class, "twitter-share-button-publicUrlMobile");
    }

    public TextInput ampUrlField() {
        return create().byXPath(TextInput.class, "//label[@for='publicUrlAmp']/..//input");
    }

    public Button facebookAmp() {
        return create().byId(Button.class, "facebook-share-button-publicUrlAmp");
    }

    public Button twitterAmp() {
        return create().byId(Button.class, "twitter-share-button-publicUrlAmp");
    }

    //Add URL button in URLs section at video create page
    public Button addUrlButton() {
        return create().byId(Button.class, "article-edit-save-top");
    }

    public SingleValueSelect videoTypeSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[@for='sidebar-urls-video-url-inputs']/parent::div//div[contains(@class, '-container')]");
    }

    public TextInput urlForVideoField() {
        return create().byId(TextInput.class, "video-url");
    }
}
