package sections.sidebarsections.relatedcontentsection;

import data.constants.ContentApiUrl;
import data.customelements.SearchResult;
import data.customelements.SelectedResult;
import lombok.SneakyThrows;
import sections.BaseSidebarSection;
import solutions.bellatrix.web.components.WebComponent;

import java.util.ArrayList;
import java.util.List;

public class RelatedContentSection extends BaseSidebarSection<Map, Asserts> {
    @Override
    protected WebComponent toggleElement() {
        return map().toggle();
    }

    @Override
    protected WebComponent toggleButtonElement() {
        return map().sectionToggleButton();
    }

    @Override
    protected WebComponent elementToWait() {
        return map().suggestionsTabAnchor();
    }

    public void openSuggestionsTab() {
        if (!map().suggestionsTabAnchor().getHtmlClass().contains("active")) {
            map().suggestionsTabAnchor().click();
        }
    }

    public void openSearchTab() {
        if (!map().searchTabAnchor().getHtmlClass().contains("active")) {
            map().searchTabAnchor().click();
        }
    }

    @SneakyThrows
    public void searchForContent(String text) {
        openSearchTab();
        map().searchTextField().setText(text);

        waitForSpinners();
        browser().waitUntil(e -> map().searchLoadingDotsDiv().isEmpty());
        browser().waitUntil(
                e -> !map().searchResults().isEmpty() ||
                        (map().searchResultMessage() != null &&
                                map().searchResultMessage().getText().contains("No results found")));
    }

    public List<SearchResult> getSearchResults() {
        browser().waitUntil(e ->
                !map().searchResults().isEmpty()
                        || map().searchResultMessage().getText().contains("No results found")
                        || map().searchResultMessage().getText().contains("All content from this page has been added to the related content, you can select another page for more results.")
        );
        return map().searchResults();
    }

    public List<SearchResult> getSuggestionsResults() {
        return map().suggestionsResults();
    }

    public List<String> getSuggestionsResultsString() {
        List<String> resultsStringList = new ArrayList<>();

        for (SearchResult entity : map().suggestionsResults()) {
            resultsStringList.add(entity.getText());
        }
        return resultsStringList;
    }

    public SearchResult getSearchResultByTitle(String title) {
        var findElement = map().searchResults().stream().filter(e -> e.getText().contains(title)).findFirst();
        if (findElement.isEmpty()) {
            map().nextButton().click();
            map().searchResults().get(0).toBeVisible(1000, 1000).waitToBe();
            findElement = map().searchResults().stream().filter(e -> e.getText().contains(title)).findFirst();
        }

        return findElement.get();
    }

    public List<SelectedResult> getSelectedRelatedContents() {
        return map().selectedResults();
    }

    public void filterArticlesResults() {
        map().articlesCheckBox().check();
        browser().tryWaitForResponse(ContentApiUrl.ARTICLES_V2_SEARCH_PAGE.url);
        waitForSpinners();
    }

    public void filterVideosResults() {
        map().videosCheckBox().check();
        browser().tryWaitForResponse(ContentApiUrl.VIDEOS_SEARCH.url);
        waitForSpinners();
    }

    public void filterGalleriesResults() {
        map().galleriesCheckBox().check();
        browser().tryWaitForResponse(ContentApiUrl.GALLERIES_SEARCH.url);
        waitForSpinners();
    }
}