package sections.sidebarsections.customdatasection;

import data.constants.ContentPageSection;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    private static final ContentPageSection section = ContentPageSection.CUSTOM_DATA;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", section.getPartialId()));
    }

    public Button editModeSectionDragIcon() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'cursor-draggable')]", section.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", section.getPartialId()));
    }

    public Button toggle() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//i[contains(@class,'fa-toggle')]", section.getPartialId()));
    }

    public TextInput textField() {
        return sectionDiv().createByXPath(TextInput.class, ".//textarea");
    }
}
