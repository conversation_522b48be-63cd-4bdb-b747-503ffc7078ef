package sections.sidebarsections.toolbarsection;

import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {
    public Button previewButton() {
        return create().byXPath(Button.class, "//div[@role='toolbar']//button[@id='on-preview-button']");
    }

    public Button saveButton() {
        return create().byXPath(Button.class, "//div[@role='toolbar']//button[text()='Save']");
    }
}
