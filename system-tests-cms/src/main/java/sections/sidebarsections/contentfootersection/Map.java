package sections.sidebarsections.contentfootersection;

import data.constants.ContentPageSection;
import data.customelements.MultiValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    private static final ContentPageSection section = ContentPageSection.CONTENT_FOOTER;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", section.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", section.getPartialId()));
    }

    public Button toggle() {
        return sectionDiv().createByXPath(Button.class, ".//i[contains(@class,'fa-toggle')]");
    }

    public MultiValueSelect valueSelect() {
        return sectionDiv().createById(MultiValueSelect.class, "sidebar-content-footer-select");
    }
}
