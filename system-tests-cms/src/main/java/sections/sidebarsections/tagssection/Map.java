package sections.sidebarsections.tagssection;

import data.constants.ContentPageSection;
import data.constants.enums.CustomEntitiesFieldsEnum;
import data.constants.enums.CustomEntityEnum;
import data.customelements.*;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Label;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {

    private static final ContentPageSection SECTION = ContentPageSection.TAGS;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", SECTION.getPartialId()));
    }

    public Button editModeSectionDragIcon() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'cursor-draggable')]", SECTION.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", SECTION.getPartialId()));
    }

    public MultiValueSearchSelect tagsSelect() {
        return create().byXPath(MultiValueSearchSelect.class, "//label[text()='Tags']//parent::div");
    }

    public Button domainSelect() {
        return create().byXPath(Button.class, "//div[@id='sidebar-tags-domain-select']");
    }

    public Label domainSelectLabel() {
        return create().byCss(Label.class, "label[for='sidebar-tags-domain-select']");
    }

    public Button domainSelectOption(String option) {
        return create().byXPath(Button.class, "//div[@class='custom-select-options']/div[text()='%s']".formatted(option));
    }

    public Button entityTypeSelect() {
        return create().byXPath(Button.class, "//div[@id='sidebar-tags-entity-type-select']");
    }

    public List<Button> selectOptionsForSelect(CustomEntitiesFieldsEnum field) {
        return create().allByXPath(Button.class,
                "//div[@id='sidebar-tags-%s-select']//following-sibling::div//div".formatted(field.getHtmlValue()));
    }

    public SingleValueSelect sportSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Sport']//parent::div");
    }

    public Button toggle() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//i[contains(@class,'fa-toggle')]", SECTION.getPartialId()));
    }

    public MultiValueSearchSelect footballConnectionsSelect() {
        return create().byXPath(MultiValueSearchSelect.class, "//label[text()='Football connections']//parent::div");
    }

    public MultiValueSearchSelect sportsConnectionsSelect() {
        return create().byXPath(MultiValueSearchSelect.class, "//label[contains(@for,'connection')]//parent::div");
    }

    public MultiValueSearchSelect customEntitiesConnectionsSelect() {
        return create().byCss(MultiValueSearchSelect.class, "#sidebar-tags-custom-entity-select");
    }

    public MultiValueSearchSelect customEntitiesConnectionsSelect(CustomEntityEnum entityType) {
        return create().byXPath(MultiValueSearchSelect.class,
                String.format("//label[text()='%s connections']//parent::div", entityType.getValue()));
    }

    public SingleValueSearchSelect tournamentSelect() {
        return create().byXPath(SingleValueSearchSelect.class, "//div[@id='sidebar-tags-football-connection-tournament-select']/parent::div");
    }

    public SingleValueSelect seasonSelect() {
        return create().byXPath(SingleValueSelect.class, "//div[@id='sidebar-tags-football-connection-tournament-season-select']/parent::div");
    }

    public MultiValueSearchSelect connectionsFieldForCustomEntity(CustomEntityEnum customEntity) {
        return create().byXPath(MultiValueSearchSelect.class,
                "//div[@id='sidebar-tags-custom-entity-%s-connections']".formatted(customEntity.getValue().toLowerCase()));
    }

    //Suggested Entities
    public Label suggestedTagsLabel() {
        return create().byCss(Label.class, "div.suggested-entities-container > div");
    }

    public List<Button> suggestedTagsButtons() {
        return create().allByCss(Button.class, "div.suggested-entities-holder > div > div");
    }

    public Button suggestedEntityButton(String suggestedEntity) {
        Button button = create().byXPath(Button.class, "//div[text()='%s']//ancestor::div[contains(@class, 'suggested-entity')]"
                .formatted(suggestedEntity));
        button.toExist().toBeVisible().toBeClickable().waitToBe();
        return button;
    }

    public Button addAllSuggestedTagsButton() {
        return create().byId(Button.class, "add-all-suggested-entities");
    }

    //Match Results
    public List<MatchResult> upcomingMatchResults() {
        return create().allByXPath(MatchResult.class, "//div[@id='tab-pane-matches-NOT_STARTED']//li[contains(@id, 'line-tags')]");
    }

    public List<MatchResult> pastMatchResults() {
        return create().allByXPath(MatchResult.class, "//div[@id='tab-pane-matches-FINISHED']//li[contains(@id, 'line-tags')]");
    }

    public List<MatchResult> interruptedMatchResults() {
        return create().allByXPath(MatchResult.class, "//div[@id='tab-pane-matches-INTERRUPTED']//li[contains(@id, 'line-tags')]");
    }

    public List<Div> noMatchResultsDiv() {
        return create().allByXPath(Div.class, "//div[text() ='No matches found']");
    }

    public Button upcomingMatchesTabButton() {
        return create().byId(Button.class, "tab-link-NOT_STARTED");
    }

    public Button pastMatchesTabButton() {
        return create().byId(Button.class, "tab-link-FINISHED");
    }

    public Button interruptedMatchesTabButton() {
        return create().byId(Button.class, "tab-link-INTERRUPTED");
    }

    //Match Results Filter
    public Button filterMatchesExpandButton() {
        return create().byXPath(Button.class, "//label[@id='show-match-list-filters']//following-sibling::div");
    }

    public SingleValueSelect filterTournamentSelect() {
        return create().byXPath(SingleValueSelect.class, "//div[@id='sidebar-tags-tournament-filter-select']/parent::div");
    }

    public SingleValueSelect filterTournamentSeasonSelect() {
        return create().byXPath(SingleValueSelect.class, "//div[@id='sidebar-tags-tournament-season-filter-select']/parent::div");
    }

    public Button filterClearButton() {
        return create().byId(Button.class, "clear-tag-matches-filters");
    }

    public DateTimePicker filterDatePickerFrom() {
        return create().byId(DateTimePicker.class, "sidebar-tags-date-from-filter-select");
    }

    public DateTimePicker filterDatePickerTo() {
        return create().byId(DateTimePicker.class, "sidebar-tags-date-to-filter-select");
    }

    //Selected Matches
    public Label selectedMatchesLabel() {
        return create().byXPath(Label.class, "//label[contains(text(),'Selected matches:')]");
    }

    public List<MatchResult> selectedMatchesList() {
        return selectedMatchesLabel().createAllByXPath(MatchResult.class, ".//following-sibling::ul/li");
    }

    //Suggested Entities
    public WebElement tagParentDivElement(Button tag) {
        return tag.getWrappedElement().findElement(By.xpath("./parent::div"));
    }
}