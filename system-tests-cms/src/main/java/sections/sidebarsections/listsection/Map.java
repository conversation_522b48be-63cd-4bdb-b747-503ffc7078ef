package sections.sidebarsections.listsection;

import data.constants.ContentPageSection;
import data.customelements.SelectedResult;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Label;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {

    private static final ContentPageSection section = ContentPageSection.LIST;

    public Div sectionDiv() {
        return create().byXPath(Div.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']/parent::div", section.getPartialId()));
    }

    public Button editModeSectionDragIcon() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//span[contains(@class,'cursor-draggable')]", section.getPartialId()));
    }

    public Button sectionToggleButton() {
        return create().byId(Button.class, String.format("collapsible-element-%1$s-toggle-button", section.getPartialId()));
    }

    public Button toggle() {
        return create().byXPath(Button.class, String.format("//div[@id='collapsible-element-%1$s-toggle-button']//i[contains(@class,'fa-toggle')]", section.getPartialId()));
    }

    public SingleValueSelect listSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Select list']//ancestor::div[@id='sidebar-list-content-wrapper']//div[contains(@class,'container')]");
    }

    public Label selectListLabel() {
        return sectionDiv().createByXPath(Label.class, ".//label");
    }

    public Label listItemsLabel() {
        return sectionDiv().createByXPath(Label.class, ".//div[contains(@class,'animated')]/div[1]");
    }

    public Label noListLabel() {
        return sectionDiv().createByXPath(Label.class, ".//h5");
    }

    public Button showImagesInfoButton() {
        return sectionDiv().createByXPath(Button.class, ".//i[contains(@class,'text-muted')]");
    }

    public Button showImagesToggleButton() {
        return sectionDiv().createByXPath(Button.class, ".//i[contains(@class,'fa-toggle')]");
    }

    public List<SelectedResult> selectedListItems() {
        return sectionDiv().createAllByXPath(SelectedResult.class, ".//ul//li");
    }

    public List<SelectedResult> unselectedListItems() {
        return sectionDiv().createAllByXPath(SelectedResult.class, ".//li");
    }

    public SelectedResult getListItemByTitle(String title) {
        return sectionDiv().createByXPath(SelectedResult.class, ".//li[descendant::*[contains(text(),'%s')]]".formatted(title));
    }

    public Button saveListButton() {
        return sectionDiv().createByXPath(Button.class, ".//button[@id='sidebar-list-save-button']");
    }

    public Button addArticleToListButton() {
        return sectionDiv().createByXPath(Button.class, ".//button[contains(@id,'sidebar-list-content-add-item-button')]");
    }

    public Button arrowNextToAddArticleToListButton() {
        return sectionDiv().createByXPath(Button.class, ".//button[@aria-haspopup]");
    }

    public Button addArticleToListTopButton() {
        return sectionDiv().createByXPath(Button.class, ".//button[@id='sidebar-list-content-add-item-top-button']");
    }

    public Div validationMessageDiv() {
        return sectionDiv().createByXPath(Div.class, ".//div[contains(@class,'alert')]");
    }

    public Button sideBarListContentItemButton() {
        return create().byId(Button.class, "sidebar-list-content-add-item-button");
    }
}