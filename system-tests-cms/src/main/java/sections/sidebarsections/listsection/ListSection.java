package sections.sidebarsections.listsection;

import data.constants.ContentApiUrl;
import org.openqa.selenium.remote.http.HttpMethod;
import sections.BaseSidebarSection;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class ListSection extends BaseSidebarSection<Map, Asserts> {
    @Override
    protected WebComponent toggleElement() {
        return map().toggle();
    }

    @Override
    protected WebComponent toggleButtonElement() {
        return map().sectionToggleButton();
    }

    @Override
    protected WebComponent elementToWait() {
        return map().listSelect();
    }

    public void deleteUnnecessaryListItems() {
        map().saveListButton().toBeClickable().waitToBe();
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.LISTS.url, HttpMethod.GET, 5);
        var listItems = map().selectedListItems();

        if (listItems.size() == 1 && listItems.get(0).removeButton().isDisabled()) {
            return;
        }

        for (int i = 0; i < listItems.size();) {
            var removeButton = listItems.get(i).removeButton();
            if (!removeButton.isDisabled()) {
                removeButton.click();
                listItems = map().selectedListItems();
            } else {
                i++;
            }
        }
    }

    public void addArticleToListTop() {
        map().arrowNextToAddArticleToListButton().click();

        map().addArticleToListTopButton().toBeVisible().toBeClickable().waitToBe();
        map().addArticleToListTopButton().click();
    }
}