package pages.dashboardpage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import pages.CmsWebPage;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class DashboardPage extends CmsWebPage<Map, Asserts> {

    private final SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);

    @Override
    protected String getUrl() {
        return sportalSettings.getCmsInstanceUrl() + "#/dashboard";
    }

    @Override
    public void waitForPageLoad() {
        browser().waitForPartialUrl(CmsPage.DASHBOARD.href);
        waitForSpinners(12); // wait for 30 secs
    }
}