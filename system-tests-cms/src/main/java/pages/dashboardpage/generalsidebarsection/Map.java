package pages.dashboardpage.generalsidebarsection;

import data.constants.CmsPage;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    public Anchor pageAnchor(CmsPage page) {
        return create().byXPath(Anchor.class, String.format("//a[@href='%1$s']", page.href));
    }

    public Anchor imagesListAnchor() {
        return create().byInnerTextContaining(Anchor.class, "Images");
    }

    public Anchor videosListAnchor() {
        return create().byInnerTextContaining(Anchor.class, "Videos");
    }

    public Anchor listsListAnchor() {
        return create().byInnerTextContaining(Anchor.class, "Lists");
    }

    public Anchor liveBlogsAnchor() {
        return create().byInnerTextContaining(Anchor.class, "Live blogs");
    }

    public Div listsList() {
        return create().byXPath(Div.class, "//a[contains(text(), 'Lists')]//parent::li");
    }

    public Div videosList() {
        return create().byXPath(Div.class, "//a[contains(text(), 'Videos')]//parent::li");
    }
}