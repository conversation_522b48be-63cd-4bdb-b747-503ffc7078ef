package pages.articleformpage;

import blockies.text.ParagraphBlock;
import data.constants.AssertMessages;
import data.constants.ButtonPositionEnum;
import data.constants.CmsPage;
import data.constants.StringConstants;
import data.models.articles.ArticleModel;
import data.models.authors.AuthorModel;
import data.models.banners.BannerModel;
import data.models.categories.Category;
import data.models.galleries.GalleryResponseModel;
import data.models.images.ImageModel;
import data.models.lists.ListModel;
import data.models.related.DataListObject;
import data.models.related.DataListTagObject;
import data.models.tags.TagModel;
import data.models.videos.VideoResponseModel;
import data.utils.StringUtils;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.pages.PageAsserts;

import java.util.List;

import static data.constants.AssertMessages.toastMessageNotDisplayedFor;

public class Asserts extends PageAsserts<Map> {

    ArticleFormPage articlesPage = new ArticleFormPage();

    public void validateUpdateSuccessful() {
        map().alertMessage().toBeVisible().waitToBe();
        Assertions.assertTrue(map().alertMessage().isVisible(), toastMessageNotDisplayedFor(StringConstants.ARTICLE_STRING, StringConstants.EDIT_STRING));
//        map().alertMessage().validateUpdateSuccessful();
    }

    public void validateDeleteSuccessful(String articleTitle) {
        map().newArticleButton().toBeVisible().toBeClickable().waitToBe();
        Div div = map().articleRow(articleTitle);
        Assertions.assertNull(div, "Article: %s is not deleted".formatted(articleTitle));
    }

    public void validateRedirectToEditPage(String articleId) {
        Assertions.assertTrue(articlesPage.browser().getUrl().endsWith("/articles/edit/%s".formatted(articleId)), "User not redirected to edit page");
    }

    public void validateErrorMessageDisplayedForTitleField() {
        map().errorMessageForTitleField().toBeVisible().waitToBe();
        map().errorMessageForTitleField().validateIsVisible();
    }

    public void validateErrorMessageDisplayedForMainCategoryField() {
        map().errorMessageForMainCategoryField().toBeVisible().waitToBe();
        map().errorMessageForMainCategoryField().validateIsVisible();
    }

    public void createPageOpened() {
        boolean isUrlEndsWithCreate = articlesPage.browser().getUrl().endsWith(CmsPage.ARTICLE_CREATE.href);
        Assertions.assertTrue(isUrlEndsWithCreate, AssertMessages.userNotRedirectedToCreatePageFor(StringConstants.ARTICLE_STRING));
    }

    public void assertRelatedEntityProperties(String provider, String expectedType, String expectedName, String expectedId, List<DataListObject> articleRelatedResponse) {
        Assertions.assertAll("Related entity response validation",
                () -> Assertions.assertEquals(provider, articleRelatedResponse.get(0).getProvider(),
                        AssertMessages.entityNotExpected("provider")),
                () -> Assertions.assertEquals(expectedType, articleRelatedResponse.get(0).getType(),
                        AssertMessages.entityNotExpected("type")),
                () -> Assertions.assertEquals(expectedType, articleRelatedResponse.get(0).getData().getEntityType(),
                        AssertMessages.entityNotExpected("entity type")),
                () -> Assertions.assertEquals(expectedName, articleRelatedResponse.get(0).getData().getName(),
                        AssertMessages.entityNotExpected("name")),
                () -> Assertions.assertEquals(expectedId, articleRelatedResponse.get(0).getData().getId().toString().replace(".0", ""),
                        AssertMessages.entityNotExpected("ID"))
        );
    }

    public void assertParagraphsTextAfterPasteAt(ButtonPositionEnum buttonPosition, String expectedCopiedText, boolean isArticleSaved) {
        if (buttonPosition == ButtonPositionEnum.BOTTOM) {
            assertParagraphsTextAfterPasteAtBottom(expectedCopiedText, isArticleSaved);
        } else {
            assertParagraphsTextAfterPasteAtTop(expectedCopiedText, isArticleSaved);
        }
    }

    private void assertParagraphsTextAfterPasteAtBottom(String expectedCopiedText, boolean isArticleSaved) {
        String[] expectedTextPerParagraph = StringUtils.splitText(expectedCopiedText, "\n\n");
        List<ParagraphBlock> allParagraphs = map().paragraphList();

        for (int i = 0; i < allParagraphs.size(); i++) {
            int paragraphNumber = i + 1;

            if (isArticleSaved) {
                Assertions.assertEquals(expectedTextPerParagraph[i], allParagraphs.get(i).getText(), AssertMessages.paragraphTextNotCorrect(paragraphNumber));
            } else {
                if (i == 0) {
                    Assertions.assertTrue(allParagraphs.get(i).getText().isEmpty(), "%d Paragraph is not empty.".formatted(paragraphNumber));
                } else {
                    Assertions.assertEquals(expectedTextPerParagraph[i - 1], allParagraphs.get(i).getText(), AssertMessages.paragraphTextNotCorrect(paragraphNumber));
                }
            }
        }
    }

    private void assertParagraphsTextAfterPasteAtTop(String expectedCopiedText, boolean isArticleSaved) {
        String[] expectedTextPerParagraph = StringUtils.splitText(expectedCopiedText, "\n\n");
        List<ParagraphBlock> allParagraphs = map().paragraphList();

        for (int i = 0; i < allParagraphs.size(); i++) {
            int paragraphNumber = i + 1;

            if (isArticleSaved) {
                Assertions.assertEquals(expectedTextPerParagraph[i], allParagraphs.get(i).getText(), AssertMessages.paragraphTextNotCorrect(paragraphNumber));
            } else {
                if (i == allParagraphs.size() - 1) {
                    Assertions.assertTrue(allParagraphs.get(i).getText().isEmpty(), "%d Paragraph is not empty.".formatted(paragraphNumber));
                } else {
                    Assertions.assertEquals(expectedTextPerParagraph[i], allParagraphs.get(i).getText(), AssertMessages.paragraphTextNotCorrect(paragraphNumber));
                }
            }
        }
    }

    public void editPageLoaded(String id) {
        articlesPage.browser().waitForPartialUrl(CmsPage.ARTICLE_EDIT.href.formatted(id));
        boolean isUrlEndsWithCreate = articlesPage.browser().getUrl().endsWith(CmsPage.ARTICLE_EDIT.href.formatted(id));
        Assertions.assertTrue(isUrlEndsWithCreate, AssertMessages.editPageNotLoaded(StringConstants.ARTICLE_STRING));
    }

    public void validateBlockySaveRequest(ArticleModel request, GalleryResponseModel createdGallery) {
        var blockType = "gallery";
        var dataBlock = request.getBody().stream().filter(item -> item.getType().equals(blockType)).findFirst();

        Assertions.assertTrue(dataBlock.isPresent(), "Blocky data not found in Article Created request.");
        Assertions.assertEquals(blockType, dataBlock.get().getType(), AssertMessages.entityNotExpected("Type"));
        Assertions.assertEquals(createdGallery.getId(), dataBlock.get().getData().getId(), AssertMessages.entityNotExpected("Gallery Id"));
        Assertions.assertEquals(createdGallery.getMainMedia().get(0).getResourceId(), dataBlock.get().getData().getMainImageId(), AssertMessages.entityNotExpected("Main Media Id"));
    }

    public void validateBlockySaveRequest(ArticleModel request, VideoResponseModel createdVideo) {
        var blockType = "video";
        var dataBlock = request.getBody().stream().filter(item -> item.getType().equals(blockType)).findFirst();
        Assertions.assertTrue(dataBlock.isPresent(), "Blocky data not found in Article Created request.");
        Assertions.assertEquals(createdVideo.getId(), dataBlock.get().getData().getId(), AssertMessages.entityNotExpected("Video Id"));
        Assertions.assertEquals(blockType, dataBlock.get().getType(), AssertMessages.entityNotExpected("Video Type"));
    }

    public void validateBlockySaveRequest(ArticleModel request, ImageModel uploadedImage) {
        var blockType = "image";
        var dataBlock = request.getBody().stream().filter(item -> item.getType().equals(blockType)).findFirst();

        Assertions.assertTrue(dataBlock.isPresent(), "Blocky data not found in Article Created request.");
        Assertions.assertEquals(uploadedImage.getId(), dataBlock.get().getData().getId(), AssertMessages.entityNotExpected("Id"));
        Assertions.assertEquals(blockType, dataBlock.get().getType(), AssertMessages.entityNotExpected("Type"));
        Assertions.assertEquals(uploadedImage.getAlt(), dataBlock.get().getData().getAlt(), AssertMessages.entityNotExpected("Image Alt"));
        Assertions.assertEquals(uploadedImage.getCaption(), dataBlock.get().getData().getCaption(), AssertMessages.entityNotExpected("Image Caption"));
        Assertions.assertEquals(uploadedImage.getDescription(), dataBlock.get().getData().getDescription(), AssertMessages.entityNotExpected("Image Description"));
    }

    public void validateBlockySaveRequest(ListModel request, ListModel createdEditorialList) {
        Assertions.assertEquals(createdEditorialList.getId(), request.getId(), AssertMessages.entityNotExpected("Id"));
    }

    public void validateBlockySaveRequest(ArticleModel request, BannerModel createdBanner) {
        var blockType = "banner";
        var dataBlock = request.getBody().stream().filter(item -> item.getType().equals(blockType)).findFirst();

        Assertions.assertTrue(dataBlock.isPresent(), "Blocky data not found in Article Created request.");
        Assertions.assertEquals(createdBanner.getId(), dataBlock.get().getData().getId(), AssertMessages.entityNotExpected("Id"));
        Assertions.assertEquals(blockType, dataBlock.get().getType(), AssertMessages.entityNotExpected("Type"));
    }

    public void validateBlockySaveRequest(List<DataListTagObject> request, TagModel createdTag) {
        Assertions.assertFalse(request.isEmpty(), AssertMessages.entityNotExpected("Related Request Data"));
        Assertions.assertEquals(createdTag.getId(), request.get(0).getData(), AssertMessages.entityNotExpected("Tag Id"));
        Assertions.assertEquals(StringConstants.SMP_STRING, request.get(0).getProvider(), AssertMessages.entityNotExpected("Tag Provider"));
        Assertions.assertEquals(StringConstants.TAG_STRING, request.get(0).getType(), AssertMessages.entityNotExpected("Tag Type"));
    }

    public void validateBlockySaveRequest(ArticleModel request, Category createdCategory) {
        Assertions.assertEquals(createdCategory.getId(), request.getCategoryId(), AssertMessages.entityNotExpected("Category Id"));
    }

    public void validateBlockySaveRequest(ArticleModel request, AuthorModel createdAuthor) {
        Assertions.assertFalse(request.getAuthors().isEmpty(), "No Author found in article request." + request.getBody().toString());
        Assertions.assertEquals(createdAuthor.getId(), request.getAuthors().get(request.getAuthors().size() - 1), AssertMessages.entityNotExpected("Author Id"));
    }
}