package pages.rsmarticles;

import data.customelements.AlertMessage;
import data.models.uimodels.gridmodels.ArticlesRsmListGridModel;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.advanced.grid.Grid;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    public Div rsmArticlesNavigationBar() {
        return create().byXPath(Div.class, "//li[contains(@class,'nav-item')]//following-sibling::div[text()='RSM Articles']");
    }

    public Div headingDiv() {
        return create().byClassContaining(Div.class, "card-header");
    }

    public AlertMessage alertMessage() {
        return create().byClassContaining(AlertMessage.class, "Toastify__toast-body");
    }

    public Anchor newArticleButton() {
        return create().byId(Anchor.class, "new-content-article");
    }

    public Button editArticleButton(String articleId) {
        return create().byId(Button.class, "content-edit-btn-%s".formatted(articleId));
    }

    public Button deleteArticleButton(String articleId) {
        return create().byXPath(Button.class, "//div[@id='content-delete-btn-%s']//parent::button".formatted(articleId));
    }

    public Div deleteModal() {
        return create().byCss(Div.class, "div.modal-content");
    }

    public Button deleteButtonModal() {
        return create().byId(Button.class, "modal-delete-button");
    }

    public Div articleRow(String articleName) {
        return create().allByXPath(Div.class, "//span[text()=\"%s\"]//parent::a//..//parent::tr".formatted(articleName)).stream().findFirst().orElse(null);
    }

    public Span newLabel() {
        return create().byXPath(Span.class, "//div[contains(@class,'row-notifications-pill')]//span");
    }

    public Span rsmArticlesCounter() {
        return create().byXPath(Span.class, "//div[contains(@class,'notifications-pill')]//span");
    }

    public Grid articlesRsmListGrid() {
        return create().byXPath(Grid.class, "//table[@class='mb-1 table table-striped']")
                .setModelColumns(ArticlesRsmListGridModel.class);
    }
}