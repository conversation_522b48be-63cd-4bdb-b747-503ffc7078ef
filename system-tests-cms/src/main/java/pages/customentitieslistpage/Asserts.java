package pages.customentitieslistpage;

import data.constants.Language;
import data.constants.SportsSearchApiUrl;
import data.constants.enums.CustomEntityEnum;
import data.constants.enums.ToastMessageEnum;
import data.customelements.CustomEntitiesGridActions;
import data.models.uimodels.gridmodels.CustomEntitiesListGridModel;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.components.advanced.grid.GridCell;
import solutions.bellatrix.web.components.advanced.grid.GridRow;
import solutions.bellatrix.web.infrastructure.ProxyServer;
import solutions.bellatrix.web.pages.PageAsserts;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class Asserts extends PageAsserts<Map> {

    public Asserts assertSuggestRequestExecuted() {
        String entities = Arrays.stream(CustomEntityEnum.values())
                .map(c -> c.name().toLowerCase())
                .collect(Collectors.joining(","));

        String expectedUrl = "%s?&entity_type=%s&offset=0&limit=20&translation_language=%s"
                .formatted(SportsSearchApiUrl.SUGGEST.getUrl(), entities, Language.ENGLISH.getCode());

        ProxyServer.assertRequestMade(expectedUrl);
        return this;
    }

    public Asserts assertPageElements() {
        List<String> expectedGridHeaders = List.of("Name", "Domain", "Entity Type", "Actions");
        map().headingDiv().toExist().toBeVisible().waitToBe();
        map().headingDiv().validateIsVisible();
        map().headingDiv().validateTextIs("Custom Entities");
        map().createNewEntityButton().validateIsVisible();
        map().searchTextField().validateIsVisible();
        map().searchButton().validateIsVisible();
        map().advancedFiltersButton().validateIsVisible();
        map().customEntitiesListGrid().validateIsVisible();
        map().pagination().validateIsVisible();
        List<String> actualGridHeaders = map().customEntitiesListGrid().getHeaderNames();
        Assertions.assertLinesMatch(expectedGridHeaders, actualGridHeaders, "Grid headers are not as expected");
        return this;
    }

    public Asserts assertRowValues(CustomEntitiesListGridModel expectedRowValues) {
        GridRow row = map().customEntitiesListGrid().getRow(0);
        CustomEntitiesGridActions actions = row.getCell("Actions").as(CustomEntitiesGridActions.class);
        map().customEntitiesListGrid().assertTable(CustomEntitiesListGridModel.class, List.of(expectedRowValues));
        assertButtonsInActionsColumn(expectedRowValues.getEntityType(), actions);
        return this;
    }

    public Asserts assertButtonsInActionsColumn(String entityType, CustomEntitiesGridActions actions) {
        if (!entityType.equalsIgnoreCase(CustomEntityEnum.DOMAIN.getValue())) {
            actions.deleteButton().validateIsVisible();
        }
        if (!(entityType.equalsIgnoreCase(CustomEntityEnum.ROLE.getValue()) ||
                entityType.equalsIgnoreCase(CustomEntityEnum.DOMAIN.getValue()))) {
            actions.editButton().validateIsVisible();
        }
        if (entityType.equalsIgnoreCase(CustomEntityEnum.ROLE.getValue())) {
            Assertions.assertNull(actions.editButton(), "Edit button should not be visible for Role entity type");
        }
        if (entityType.equalsIgnoreCase(CustomEntityEnum.DOMAIN.getValue())) {
            Assertions.assertNull(actions.editButton(), "Edit button should not be visible for Domain entity type");
            Assertions.assertNull(actions.deleteButton(), "Delete button should not be visible for Domain entity type");
        }
        return this;
    }

    public Asserts assertToastMessageDisplayedWithMessage(ToastMessageEnum message) {
//        map().alertMessage().validateMessageIs(message);
        return this;
    }

    public Asserts assertEntityDeleted(String name) {
        List<String> valuesInNameColumn = map().customEntitiesListGrid().getColumn("Name").stream()
                .map(GridCell::getText)
                .toList();
        Assertions.assertFalse(valuesInNameColumn.contains(name),
                "Entity with name %s is still present in the grid".formatted(name));
        return this;
    }
}