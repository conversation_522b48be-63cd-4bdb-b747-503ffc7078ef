package pages.customentitieslistpage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.customelements.CustomEntitiesGridActions;
import org.openqa.selenium.Keys;
import pages.CmsWebPage;
import services.KeyboardShortcutsService;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.components.advanced.grid.GridCell;
import solutions.bellatrix.web.components.advanced.grid.GridRow;

import java.util.List;

public class CustomEntitiesListPage extends CmsWebPage<Map, Asserts> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.CUSTOM_ENTITIES_LISTING.getHref();
    }

    public Asserts asserts() {
        return new Asserts();
    }

    public CustomEntitiesListPage searchEntity(String entityName) {
        map().searchTextField().toExist().toBeVisible().waitToBe();
        map().searchTextField().setText(entityName);
        map().searchButton().click();
        browser().waitForAjax();
        return this;
    }

    public CustomEntitiesListPage clearSearchFilter() {
        if (!map().searchTextField().getText().isEmpty()) {
            map().searchTextField().getWrappedElement()
                    .sendKeys(KeyboardShortcutsService.getModifierKey(), "a", Keys.BACK_SPACE);
            map().searchButton().click();
        }
        return this;
    }

    public CustomEntitiesListPage deleteEntity(String name) {

        List<GridRow> rows = map().customEntitiesListGrid().getRows();

        for (GridRow row : rows) {
            List<GridCell> cells = row.getCells();
            if (cells.get(0).getText().equalsIgnoreCase(name)) {
                CustomEntitiesGridActions actions = cells.get(cells.size() - 1).as(CustomEntitiesGridActions.class);
                actions.deleteButton().click();
                map().deleteEntityModal().toExist().toBeVisible().waitToBe();
                map().deleteButtonInModal().click();
                break;
            }
        }
        return this;
    }
}