package pages.videospage;

import org.junit.jupiter.api.Assertions;
import org.testng.Assert;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.pages.PageAsserts;

public class Asserts extends PageAsserts<Map> {

    public void pageLoaded() {
        VideosPage videosPage = new VideosPage();
        videosPage.headingDiv().validateTextContains("Videos");
        map().newVideoButton().validateIsVisible();
        map().newVideoButton().validateHrefIs(videosPage.getUrl() + "/create");
        Assert.assertEquals(videosPage.browser().getUrl(), videosPage.getUrl());
    }

    public void assertVideoDeleted(String videoName) {
        Div div = map().videoRow(videoName);
        Assertions.assertNull(div, "Video is not deleted");
    }
}