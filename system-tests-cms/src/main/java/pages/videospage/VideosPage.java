package pages.videospage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.constants.ContentApiUrl;
import data.constants.video.VideoUrlsType;
import data.models.videos.VideoResponseModel;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.CmsWebPage;
import sections.sidebarsections.urlssection.UrlsSection;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class VideosPage extends CmsWebPage<Map, Asserts> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.VIDEOS.href;
    }

    protected String getCreateUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.VIDEO_CREATE.href;
    }

    public UrlsSection urlsSection() {
        return new UrlsSection();
    }

    @Override
    public void waitForPageLoad() {
        headingDiv().toExist().waitToBe();
        breadcrumbNavigation().toExist().waitToBe();
    }

    public void saveVideo() {
        map().saveVideoButton().click();
        map().alertMessage().toBeVisible().waitToBe();
    }

    public void addVideoFromUrlsSection(String videoUrl, VideoUrlsType videoUrlsType) {
        urlsSection().expand();
        urlsSection().map().addUrlButton().click();
        addVideo(videoUrl, videoUrlsType);
    }

    public void editVideoFromUrlsSection(String videoUrl, VideoUrlsType videoUrlsType) {
        urlsSection().expand();
        addVideo(videoUrl, videoUrlsType);
    }

    public void deleteVideo(String videoId) {
        breadcrumbNavigation().toExist().waitToBe();
        map().editVideoButton(videoId).toExist().toBeClickable().waitToBe();

        javaScript().execute("document.querySelector('[id*=toggle-menu-caret-%s]').click()".formatted(videoId));

        map().deleteVideoButton(videoId).toBeClickable().waitToBe();
        map().deleteVideoButton(videoId).click();

        map().deleteModal().toExist().waitToBe();
        map().deleteButtonModal().click();

        ProxyServer.waitForRequest(browser().getWrappedDriver(), ContentApiUrl.VIDEOS_ID.url.formatted(videoId), HttpMethod.DELETE, 0);
    }

    private void addVideo(String videoUrl, VideoUrlsType videoUrlsType) {
        urlsSection().map().videoTypeSelect().selectOptionByExactText(videoUrlsType.getValue());
        urlsSection().map().urlForVideoField().setText(videoUrl);
    }

    public VideoResponseModel getCreateVideoResponse() {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.VIDEOS.url, HttpMethod.POST, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.VIDEOS.url, HttpMethod.POST.toString(), VideoResponseModel.class);
    }

    public VideoResponseModel getEditVideoResponse(String id) {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.VIDEOS_ID.url.formatted(id), HttpMethod.PATCH, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.VIDEOS_ID.url.formatted(id), HttpMethod.PATCH.toString(), VideoResponseModel.class);
    }
}