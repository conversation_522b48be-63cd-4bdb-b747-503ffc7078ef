package pages.authorspage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.constants.ContentApiUrl;
import data.constants.OperationsEnum;
import data.models.authors.AuthorModel;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.CmsWebPage;
import sections.sidebarsections.mainsection.MainSection;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class AuthorsPage extends CmsWebPage<Map, Asserts> {
    private static final String AUTHOR_ID_URL = ContentApiUrl.AUTHORS.url + "/%s";

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.AUTHORS.href;
    }

    public MainSection mainSection() {
        return new MainSection();
    }

    public void setRequiredField(String name) {
        if (name != null) {
            mainSection().map().nameTextField().setText(name);
        }
    }

    public void setRequiredTitleField(String title) {
        setName(title);
    }

    public void setName(String name) {
        mainSection().map().nameTextField().setText(name);
    }

    public void setBio(String bio) {
        map().biographyTextEditorField().clearText();
        map().biographyTextEditorField().setText(bio);
        Wait.forMilliseconds(200);
    }

    public void saveAuthor() {
        if (browser().getUrl().endsWith("/" + OperationsEnum.CREATE.toString().toLowerCase())) {
            map().saveAuthorButton(OperationsEnum.CREATE).click();
        } else {
            map().saveAuthorButton(OperationsEnum.EDIT).click();
        }
    }

    public AuthorModel getCreateAuthorResponse() {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.AUTHORS.url, HttpMethod.POST, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.AUTHORS.url, HttpMethod.POST.toString(), AuthorModel.class);
    }

    public AuthorModel getUpdateAuthorResponse(String authorId) {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), AUTHOR_ID_URL.formatted(authorId), HttpMethod.PATCH, 0);
        return ProxyServer.getResponseByUrl(AUTHOR_ID_URL.formatted(authorId), HttpMethod.PATCH.toString(), AuthorModel.class);
    }

    public void deleteAuthor(String authorId) {
        map().deleteAuthorButton(authorId).toBeVisible().toBeClickable().waitToBe();
        map().deleteAuthorButton(authorId).click();
        map().deleteModal().toBeVisible().waitToBe();
        map().deleteButtonModal().click();

        ProxyServer.waitForRequest(browser().getWrappedDriver(), AUTHOR_ID_URL.formatted(authorId), HttpMethod.DELETE, 0);
        browser().waitForAjax();
    }

    public void setAllFields(String name, String bio) {
        waitForSpinners();
        setBio(bio);
        setName(name);
    }
}
