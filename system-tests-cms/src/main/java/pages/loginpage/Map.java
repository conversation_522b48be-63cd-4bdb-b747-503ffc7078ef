package pages.loginpage;

import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Heading;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {
    public Heading heading() { return create().byTag(Heading.class, "h1");}

    public TextInput usernameTextField() {return create().byAttributeContaining(TextInput.class, "placeholder", "Username");}

    public TextInput passwordTextField() {return create().byAttributeContaining(TextInput.class, "placeholder", "Password");}

    public Button loginButton() {return create().byClassContaining(Button.class, "btn");}
}
