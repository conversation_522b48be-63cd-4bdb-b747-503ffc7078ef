package pages.categoriespage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.constants.ContentApiUrl;
import data.models.categories.Category;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.CmsWebPage;
import sections.sidebarsections.mainsection.MainSection;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class CategoriesPage extends CmsWebPage<Map, Asserts> {

    private static final String CATEGORY_ID_URL = ContentApiUrl.CATEGORIES.url + "/%s";

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.CATEGORIES.href;
    }

    public MainSection mainSection() {
        return new MainSection();
    }

    public void setRequiredField(String title) {
        mainSection().map().titleTextField().setText(title);
    }

    public void saveCreateCategory() {
        map().saveCreateCategoryButton().click();
    }

    public void saveUpdateCategory() {
        map().saveEditCategoryButton().click();
    }

    public Category getCreateCategoryResponse() {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.CATEGORIES.url, HttpMethod.POST, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.CATEGORIES.url, HttpMethod.POST.toString(), Category.class);
    }

    public Category getUpdateCategoryResponse(String categoryId) {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), CATEGORY_ID_URL.formatted(categoryId), HttpMethod.PATCH, 0);
        return ProxyServer.getResponseByUrl(CATEGORY_ID_URL.formatted(categoryId), HttpMethod.PATCH.toString(), Category.class);
    }

    public void deleteCategory(String categoryId) {
        map().deleteCategoryButton(categoryId).toBeVisible().toBeClickable().waitToBe();
        map().deleteCategoryButton(categoryId).click();
        map().deleteModal().toBeVisible().waitToBe();
        map().deleteButtonModal().click();

        ProxyServer.waitForResponse(browser().getWrappedDriver(), CATEGORY_ID_URL.formatted(categoryId), HttpMethod.DELETE, 0);
    }
}
