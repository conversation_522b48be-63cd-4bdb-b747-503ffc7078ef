package pages.newsforgepage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.models.categories.Category;
import data.models.newsforgeapi.GeneratePostModel;
import pages.CmsWebPage;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class NewsForgePage extends CmsWebPage<Map, Asserts> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.NEWS_FORGE.getHref();
    }

    public void selectUrlLink(GeneratePostModel generatePostModel) {
        if (generatePostModel != null && !generatePostModel.getTargetUrl().isEmpty()) {
            map().newsForgeUrlLinkInput().setText(generatePostModel.getTargetUrl());
        }
        map().extractAndTranslateButton().click();
    }

    public void selectCategory(Category activeCategory) {
        if (activeCategory.getActive() != null && activeCategory.getTitle() != null) {
            map().categoryDropdown().searchSelectByText(activeCategory.getTitle());
        }
    }
}