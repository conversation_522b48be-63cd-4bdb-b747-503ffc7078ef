package pages.newsforgepage;

import data.customelements.NewsForgeCreatedArticleModal;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

public class Map extends PageMap {

    public SingleValueSelect categoryDropdown() {
        return create().byXPath(SingleValueSelect.class, "//div[@id='news-forge-category-select']");
    }

    public TextInput newsForgeUrlLinkInput() {
        return create().byXPath(TextInput.class, "//input[@id='news-forge-url-link']");
    }

    public Button extractAndTranslateButton() {
        return create().byXPath(Button.class, "//div[@title='Extract and translate']");
    }

    public Button clearButton() {
        return create().byXPath(Button.class, "//div[@title='Clear']");
    }

    public Button reGenerateTextButton() {
        return create().byXPath(Button.class, "//div[@data-qa='news-forge-regenerate-text-button']");
    }

    public NewsForgeCreatedArticleModal createdArticleModalHeader() {
        return create().byXPath(NewsForgeCreatedArticleModal.class, "//div[@data-qa='news-forge-created-article-modal']//h5");
    }

    public Button createArticleButton() {
        return create().byXPath(Button.class, "//div[@data-qa='news-forge-create-article-button']");
    }

    public Div newsForgeOperationsContainer() {
        return create().byXPath(Div.class, "//div[@data-qa='news-forge-operations-container']");
    }

    public Div newsForgeHeader() {
        return create().byXPath(Div.class, "//div[contains(@class,'news-forge-header')]//h1");
    }

    public Div headingDiv() {
        return create().byClassContaining(Div.class, "breadcrumb-item active");
    }

    public Div errorMessageForImportUrlLinkField() {
        return create().byXPath(Div.class, "//input[@id='news-forge-url-link']//following-sibling::div");
    }

    public Div errorMessageForCategoryField() {
        return create().byXPath(Div.class, "//div[contains(@class,'error-message') and text()='Mandatory field']");
    }
}