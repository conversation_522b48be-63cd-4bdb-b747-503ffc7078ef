package pages.aiarticlespage;

import data.constants.StringConstants;
import data.constants.enums.AIArticlesSection;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.pages.PageAsserts;

public class Assert extends PageAsserts<Map> {

    public Assert assertSectionIsOpened(AIArticlesSection section) {
        map().tabOption(section).toExist().toBeVisible().waitToBe();
        String classAttribute = map().tabOption(section).getAttribute(StringConstants.CLASS_STRING);
        Assertions.assertTrue(classAttribute.contains("selected-tab"),
                "The %s section is not opened.".formatted(section.getValue()));
        return this;
    }
}