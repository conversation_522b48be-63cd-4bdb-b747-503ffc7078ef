package pages.aiarticlespage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import pages.CmsWebPage;
import pages.aiarticlespage.generatearticlesection.GenerateArticleSection;
import solutions.bellatrix.core.configuration.ConfigurationService;

public class AIArticlesPage extends CmsWebPage<Map, Assert> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.AI_ARTICLES.getHref();
    }

    public GenerateArticleSection generateArticleSection() {
        return new GenerateArticleSection();
    }
}