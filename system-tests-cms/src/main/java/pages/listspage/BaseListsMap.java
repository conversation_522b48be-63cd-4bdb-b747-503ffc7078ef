package pages.listspage;

import data.constants.OperationsEnum;
import data.customelements.AlertMessage;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

import java.util.Random;

public abstract class BaseListsMap extends PageMap {

    public Div headingDiv() { return create().byClassContaining(Div.class, "card-header");}

    public Anchor newListButton() {
        return create().byId(Anchor.class, "new-content-list");
    }

    protected Button saveListButton(OperationsEnum operation) {
        boolean randomBoolean = new Random().nextBoolean();
        String buttonPosition = randomBoolean ? "top" : "bottom";
        String selector = "list-%s-save-%s".formatted(operation.toString().toLowerCase(), buttonPosition);
        return create().byId(Button.class, selector);
    }

    public AlertMessage alertMessage() {
        return create().byClassContaining(AlertMessage.class, "Toastify__toast-body");
    }

    public Div errorMessageForTitleField() {
        return create().byXPath(Div.class, "//div[@data-qa='default-title']//./following-sibling::div");
    }

    public Button deleteListButton(String listId) {
        return create().byId(Button.class, "content-delete-btn-%s".formatted(listId));
    }

    public Div deleteModal() {
        return create().byCss(Div.class, "div.modal-content");
    }

    public Button deleteButtonModal() {
        return create().byId(Button.class, "modal-delete-button");
    }

    public Div listRow(String listName) {
        return create().allByXPath(Div.class, "//span[text()='%s']//parent::a//..//parent::div".formatted(listName)).stream().findFirst().orElse(null);
    }

    public Button editButton(String listId) {
        return create().byId(Button.class, "content-edit-btn-%s".formatted(listId));
    }

    public TextInput slugTextField() {
        return create().byXPath(TextInput.class, "//label[@for='slug']//following-sibling::input");
    }

    public SingleValueSelect statusSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Status']//parent::div");
    }

    public TextInput titleTextField() {
        return create().byXPath(TextInput.class, "//div[@data-qa='default-title']/input");
    }

}