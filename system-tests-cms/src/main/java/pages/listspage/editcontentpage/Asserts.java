package pages.listspage.editcontentpage;

import data.constants.AssertMessages;
import org.junit.jupiter.api.Assertions;
import pages.listspage.BaseListsAsserts;

public class Asserts extends BaseListsAsserts<Map> {

    public void assertCountOfSelectedContent(String expectedContent, int actualContent) {
        Assertions.assertEquals(Integer.parseInt(expectedContent), actualContent, AssertMessages.incorrectNumberOfSelectedContent());
    }

    public void assertContentInSelectedContent(String expectedContent) {
        map().contentType(expectedContent).toExist().toBeVisible().waitToBe();
        map().contentType(expectedContent).validateIsVisible();
        map().contentType(expectedContent).validateTextIs(expectedContent);
    }

    public void assertMakeEditorialButtonIsDisabled() {
        map().disableEditorialButton().toExist().toBeVisible().waitToBe();
        map().disableEditorialButton().validateIsDisabled();
    }

    public void assertMakeEditorialButtonIsEnable() {
        map().makeEditorialButton().toExist().toBeVisible().waitToBe();
        map().makeEditorialButton().validateNotDisabled();
    }
}