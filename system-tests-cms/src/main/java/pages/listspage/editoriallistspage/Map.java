package pages.listspage.editoriallistspage;

import pages.listspage.BaseListsMap;
import solutions.bellatrix.web.components.Button;

public class Map extends BaseListsMap {

    public Button liveBlogRadioButton() {
        return create().byId(Button.class, "search-type-radio-live-blogs");
    }

    public Button removeContentButton() {
        return create().byXPath(Button.class, ".//button[contains(@id, 'content-item-image-remove-btn')]");
    }
}