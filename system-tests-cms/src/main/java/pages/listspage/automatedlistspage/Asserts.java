package pages.listspage.automatedlistspage;

import org.junit.jupiter.api.Assertions;
import pages.listspage.BaseListsAsserts;

public class Asserts extends BaseListsAsserts<Map> {

    public void pageLoaded() {
        AutomatedListsPage automatedListsPage = new AutomatedListsPage();
        automatedListsPage.headingDiv().validateTextContains("Automated Lists");
        map().newListButton().validateIsVisible();
        Assertions.assertEquals(automatedListsPage.browser().getUrl(), automatedListsPage.getUrl(), "Automated List page is not loaded correctly");
    }

    public void assertSaveListButtonIsDisable() {
        map().saveButton().validateIsDisabled();
    }
}