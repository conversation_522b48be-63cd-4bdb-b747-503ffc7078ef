package pages.listspage.automatedlistspage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.models.uimodels.AutomatedListFormModel;
import data.widgets.options.enums.AutomaticContentModeEnum;
import pages.listspage.BaseListsPage;
import solutions.bellatrix.core.configuration.ConfigurationService;

import java.util.Arrays;

public class AutomatedListsPage extends BaseListsPage<Map, Asserts> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.AUTOMATED_LISTS.href;
    }

    public void fillForm(AutomatedListFormModel list) {
        selectTitle(list.getTitle());
        selectSlug(list.getSlug());
        selectMinimumItems(list.getMinimumItems());
        selectAutomatedListMaximumItemsInput(list.getMaximumNumberAutomaticListItems());
        selectMaximumItems(list.getMaximumItems());
        selectTypeAutomaticListItems(list.getTypeOfEditorialItems());
        selectMainCategory(list.getMainCategory());
        selectSport(list.getSport());
        selectFootballConnection(list.getFootballConnection());
        selectAutomaticContentModeSelect(list);
        selectStatus(list.getStatus());
    }

    public AutomatedListsPage selectMinimumItems(String minimumItems) {
        if (minimumItems != null || !minimumItems.isEmpty()) {
            map().automatedListMinimumItemsInput().setText(minimumItems);
        }
        return this;
    }

    public AutomatedListsPage selectMaximumItems(String maximumItems) {
        if (maximumItems != null || !maximumItems.isEmpty()) {
            map().automatedListMaximumItemsInput().setText(maximumItems);
        }
        return this;
    }

    public AutomatedListsPage selectAutomatedListMaximumItemsInput(String automatedListMaximumItemsInput) {
        if (automatedListMaximumItemsInput != null || !automatedListMaximumItemsInput.isEmpty()) {
            map().maximumNumberAutomaticListItems().setText(automatedListMaximumItemsInput);
        }
        return this;
    }

    public AutomatedListsPage selectTypeAutomaticListItems(String typeAutomaticListItems) {
        if (typeAutomaticListItems != null || !typeAutomaticListItems.isEmpty()) {
            map().typeAutomaticListItems().selectOptionByText(typeAutomaticListItems);
        }
        return this;
    }

    public AutomatedListsPage selectAutomaticContentModeSelect(AutomatedListFormModel list) {
        String automaticContentMode = list.getAutomaticContentMode();
        if (automaticContentMode == null) {
            return this;
        }
        map().automaticContentModeSelect().selectOptionByText(automaticContentMode);
        if (automaticContentMode.equals(AutomaticContentModeEnum.MOST_POPULAR.getContent())) {
            selectTimeRange(list.getTimeRange());
            selectFrequencySelect(list.getUpdateFrequency());
        }
        return this;
    }

    public AutomatedListsPage selectSport(String sport) {
        if (sport != null) {
            map().sportSelect().selectOptionByText(sport);
        }
        return this;
    }

    public AutomatedListsPage selectMainCategory(String category) {
        if (category != null) {
            map().mainCategorySelect().searchSelectByText(category);
        }
        return this;
    }

    public AutomatedListsPage selectFootballConnection(String footballConnection) {
        if (footballConnection != null) {
            map().footballConnection().searchSelectByText(footballConnection);
        }
        return this;
    }

    public AutomatedListsPage selectTimeRange(String timeRange) {
        if (timeRange != null) {
            map().timeRangeSelect().selectOptionByText(timeRange);
        }
        return this;
    }

    public AutomatedListsPage selectFrequencySelect(String frequencySelect) {
        if (frequencySelect != null) {
            map().updateFrequencySelect().selectOptionByText(frequencySelect);
        }
        return this;
    }

    public void lockAutomatedList(String... positions) {
        map().lockPositionsDropDown().selectOptionsByText(Arrays.asList(positions));
    }
}