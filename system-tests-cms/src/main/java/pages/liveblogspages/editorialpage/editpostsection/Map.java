package pages.liveblogspages.editorialpage.editpostsection;

import data.constants.StringConstants;
import data.constants.liveblog.LiveBlogNewPostToggleButtonEnum;
import data.customelements.editor.TipTapEditor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {

    public WebComponent getSectionWrapper() {
        return create().byXPath(WebComponent.class, StringConstants.EDIT_BLOCKY_MODAL_LOCATOR);
    }

    public Button saveButton() {
        return getSectionWrapper().createByXPath(Button.class, ".//button[text()='Save']");
    }

    public TipTapEditor postEditor() {
        return getSectionWrapper().createByXPath(TipTapEditor.class, ".//div[@data-qa='collaborative-editor']");
    }

    public CheckBox toggleButton(LiveBlogNewPostToggleButtonEnum toggleButtonEnum) {
        return getSectionWrapper().createByXPath(CheckBox.class, ".//div[text()='%s']//i".formatted(toggleButtonEnum.getValue()));
    }

    public TextInput addMinuteTextField() {
        return getSectionWrapper().createByXPath(TextInput.class, "//div[text()='Add minute']//input");
    }

    public TextInput addInjuryMinuteTextField() {
        List<TextInput> textInputs = getSectionWrapper().createAllByXPath(TextInput.class, "//div[text()='Add injury minute']//input");
        if (textInputs.isEmpty()) {
            return null;
        }
        return textInputs.get(0);
    }
}