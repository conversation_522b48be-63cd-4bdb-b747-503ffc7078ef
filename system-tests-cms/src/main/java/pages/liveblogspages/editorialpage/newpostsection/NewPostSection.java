package pages.liveblogspages.editorialpage.newpostsection;

import blockies.baseblockies.baseblocky.BaseBlocky;
import data.constants.StringConstants;
import data.constants.liveblog.LiveBlogBlockyTypeEnum;
import data.constants.liveblog.LiveBlogNewPostToggleButtonEnum;
import data.customelements.editor.TipTapEditor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.pages.WebSection;

public class NewPostSection extends WebSection<Map, Assert> {

    public <T extends BaseBlocky> T addBlocky(Class<T> blockyType, LiveBlogBlockyTypeEnum blockyTypeEnum) {
        create().byId(Button.class, blockyTypeEnum.getHtmlName()).click();
        return create().byXPath(blockyType, StringConstants.ADD_BLOCKY_MODAL_LOCATOR).toBeClickable();
    }

    public TipTapEditor postEditor() {
        return create().byXPath(TipTapEditor.class, "//div[@data-qa='collaborative-editor']");
    }

    public void publishPost() {
        map().publishButton().toBeVisible().toBeClickable().waitToBe();
        map().publishButton().click();
        browser().waitForAjax();
    }

    public void saveAsDraft() {
        map().publishButton().toBeVisible().toBeClickable().waitToBe();
        map().dropdownToggleButton().click();
        map().saveAsDraftButton().click();
        browser().waitForAjax();
    }

    public void addMinute(String minute) {
        toggleOn(LiveBlogNewPostToggleButtonEnum.ADD_MINUTE);
        map().addMinuteTextField().setText(minute);
    }

    public void addInjuryMinute(String injuryMinute) {
        toggleOn(LiveBlogNewPostToggleButtonEnum.ADD_INJURY_MINUTE);
        map().addInjuryMinuteTextField().setText(injuryMinute);
    }

    protected void toggleOn(LiveBlogNewPostToggleButtonEnum toggleButton) {
        CheckBox toggle = map().toggleButton(toggleButton);
        toggle.toBeVisible().waitToBe();
        toggle.check();
    }
}