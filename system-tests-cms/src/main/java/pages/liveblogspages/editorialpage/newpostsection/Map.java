package pages.liveblogspages.editorialpage.newpostsection;

import data.constants.liveblog.LiveBlogNewPostToggleButtonEnum;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {

    public Button publishButton() {
        return create().byXPath(Button.class, "//button[text()='Publish']");
    }

    public Button dropdownToggleButton() {
        return create().byXPath(Button.class, "//span[text()='Toggle Dropdown']//parent::button");
    }

    public Button saveAsDraftButton() {
        Button saveAsDraftButton = create().byXPath(Button.class, "//button[text()='Save as draft']");
        saveAsDraftButton.toBeVisible().toBeClickable().waitToBe();
        return saveAsDraftButton;
    }

    public List<Div> toggleButtons() {
        return create().allByCss(Div.class, "div.new-post-config-container div.toggle-editor");
    }

    public CheckBox toggleButton(LiveBlogNewPostToggleButtonEnum toggleButtonEnum) {
        return create().byXPath(CheckBox.class, "//div[text()='%s']//i".formatted(toggleButtonEnum.getValue()));
    }

    public TextInput addMinuteTextField() {
        return create().byXPath(TextInput.class, "//div[text()='Add minute']//input");
    }

    public TextInput addInjuryMinuteTextField() {
        return create().byXPath(TextInput.class, "//div[text()='Add injury minute']//input");
    }
}