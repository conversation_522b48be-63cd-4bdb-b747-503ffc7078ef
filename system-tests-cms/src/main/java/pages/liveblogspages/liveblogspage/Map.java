package pages.liveblogspages.liveblogspage;

import data.customelements.AlertMessage;
import data.customelements.LiveBlogTableEntity;
import data.customelements.MultiValueSearchSelect;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.pages.PageMap;

import java.util.List;

public class Map extends PageMap {
    public AlertMessage alertMessage() {
        return create().byClassContaining(AlertMessage.class, "Toastify__toast-body");
    }

    public Div headingDiv() {
        return create().byClassContaining(Div.class, "card-header");
    }

    public Button newLiveBlogButton() {
        return create().byId(Button.class, "new-live-blog");
    }

    public TextInput searchTextField() {
        return create().byId(TextInput.class, "live-blog-search-input");
    }

    public Button searchButton() {
        return create().byId(Button.class, "live-blog-listing-search-button");
    }

    public Button extendFiltersButton() {
        return create().byXPath(Button.class, "//button[@id='live-blog-listing-search-button']//following-sibling::button");
    }

    public Button clearFiltersButton() {
        return create().byId(Button.class, "live-blogs-advanced-filters-clear-button");
    }

    public MultiValueSearchSelect footballConnectionsSelect() {
        return create().byXPath(MultiValueSearchSelect.class, "//label[contains(text(),'Football connections')]//following-sibling::div");
    }

    public SingleValueSelect statusSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Status']//parent::div");
    }

    public SingleValueSelect typeSelect() {
        return create().byXPath(SingleValueSelect.class, "//label[text()='Type']//parent::div");
    }

    public List<LiveBlogTableEntity> tableEntities() {
        return create().allByAttributeContaining(LiveBlogTableEntity.class, "data-qa", "table-row-live_blog");
    }

    //TODO Create a new element "Pagination"
    public Button paginationPreviousButton() {
        return create().byXPath(Button.class, "//li[contains(@class,'previous')]");
    }

    public List<Button> paginationPageButtons() {
        return create().allByXPath(Button.class, "//ul[contains(@class,'pagination')]//li[not(contains(@class,'previous') or contains(@class,'next'))]");
    }

    public Button paginationNextButton() {
        return create().byXPath(Button.class, "//li[contains(@class,'next')]");
    }

    //TODO Create a new element "Modal dialog"
    public Div deleteModalDiv() {
        return create().byXPath(Div.class, "//div[contains(@class,'modal-dialog')]");
    }

    public Div deleteModalHeaderDiv() {
        return deleteModalDiv().createByXPath(Div.class, ".//div[contains(@class,'header')]");
    }

    public Div deleteModalBodyDiv() {
        return deleteModalDiv().createByXPath(Div.class, ".//div[contains(@class,'body')]");
    }

    public Button deleteModalDeleteButton() {
        return deleteModalDiv().createByXPath(Button.class, ".//button[text()='Delete']");
    }

    public Button deleteModalCancelButton() {
        return deleteModalDiv().createByXPath(Button.class, ".//button[text()='Cancel']");
    }
}