package pages.bannerspage;

import data.constants.ButtonPositionEnum;
import data.constants.OperationsEnum;
import data.constants.StringConstants;
import data.customelements.AlertMessage;
import data.customelements.SingleValueSelect;
import solutions.bellatrix.web.components.*;
import solutions.bellatrix.web.pages.PageMap;

import java.util.Random;

public class Map extends PageMap {

    public Div headingDiv() {
        return create().byClassContaining(Div.class, "card-header");
    }

    public Anchor newBannerButton() {
        return create().byId(Anchor.class, "new-content-banner");
    }

    public CheckBox propertyCheckbox(String propertyId) {
        return create().byXPath(CheckBox.class, String.format("//label[text()='%1$s']/../input[@type='checkbox']", propertyId));
    }

    public TextArea bannerCodeTextArea() {
        return create().byId(TextArea.class, "code-input");
    }

    public SingleValueSelect statusDropdown() {
        return create().byCss(SingleValueSelect.class, "[data-qa='banner-status-select']");
    }

    public CheckBox isPreferredCheckbox() {
        return create().byId(CheckBox.class, StringConstants.IS_PREFERRED_STRING);
    }

    public AlertMessage alertMessage() {
        return create().byCss(AlertMessage.class, "div.Toastify__toast-body");
    }

    public Div errorMessageForTitleField() {
        return create().byXPath(Div.class, "//div[@data-qa='default-title']//./following-sibling::div");
    }

    public Div errorMessageForBannerCodeField() {
        return create().byXPath(Div.class, "//textarea[@id='code-input']//following-sibling::div");
    }

    public Button deleteTagButton(String tagId) {
        return create().byId(Button.class, "content-delete-btn-%s".formatted(tagId));
    }

    public Div deleteModal() {
        return create().byCss(Div.class, "div.modal-content");
    }

    public Button deleteButtonModal() {
        return create().byId(Button.class, "modal-delete-button");
    }

    public Div bannerRow(String bannerTitle) {
        return create().allByXPath(Div.class, "//span[text()='%s']//parent::a//..//parent::tr".formatted(bannerTitle)).stream().findFirst().orElse(null);
    }

    public Button editButton(String tagId) {
        return create().byId(Button.class, "content-edit-btn-%s".formatted(tagId));
    }

    protected Button saveBannerButton(OperationsEnum operation, ButtonPositionEnum buttonPosition) {
        String selector = "banner-%s-save-%s".formatted(operation.toString().toLowerCase(), buttonPosition.name().toLowerCase());
        return create().byId(Button.class, selector);
    }

    protected Button saveBannerButton(OperationsEnum operation) {
        boolean randomBoolean = new Random().nextBoolean();
        ButtonPositionEnum buttonPosition = randomBoolean ? ButtonPositionEnum.TOP : ButtonPositionEnum.BOTTOM;
        return saveBannerButton(operation, buttonPosition);
    }
}