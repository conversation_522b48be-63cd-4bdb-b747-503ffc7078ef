package pages.galleriespage;

import data.configuration.SportalSettings;
import data.constants.CmsPage;
import data.constants.ContentApiUrl;
import data.constants.OperationsEnum;
import data.models.galleries.GalleryResponseModel;
import lombok.SneakyThrows;
import org.openqa.selenium.remote.http.HttpMethod;
import pages.CmsWebPage;
import sections.sidebarsections.contentfootersection.ContentFooterSection;
import sections.sidebarsections.customdatasection.CustomDataSection;
import sections.sidebarsections.generalsection.GeneralSection;
import sections.sidebarsections.listsection.ListSection;
import sections.sidebarsections.mainsection.MainSection;
import sections.sidebarsections.mediasection.MediaSection;
import sections.sidebarsections.relatedcontentsection.RelatedContentSection;
import sections.sidebarsections.seosection.SeoSection;
import sections.sidebarsections.tagssection.TagsSection;
import sections.sidebarsections.toolbarsection.ToolBarSection;
import sections.sidebarsections.urlssection.UrlsSection;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class GalleriesPage extends CmsWebPage<Map, Asserts> {

    @Override
    protected String getUrl() {
        return ConfigurationService.get(SportalSettings.class).getCmsInstanceUrl() + CmsPage.GALLERIES.getHref();
    }

    public GeneralSection generalSection() {
        return new GeneralSection();
    }

    public MainSection mainSection() {
        return new MainSection();
    }

    public MediaSection mediaSection() {
        return new MediaSection();
    }

    public ContentFooterSection contentFooterSection() {
        return new ContentFooterSection();
    }

    public TagsSection tagsSection() {
        return new TagsSection();
    }

    public RelatedContentSection relatedContentSection() {
        return new RelatedContentSection();
    }

    public SeoSection seoSection() {
        return new SeoSection();
    }

    public UrlsSection urlsSection() {
        return new UrlsSection();
    }

    public ListSection listSection() {
        return new ListSection();
    }

    public CustomDataSection customDataSection() {
        return new CustomDataSection();
    }

    public ToolBarSection toolBarSection() {
        return new ToolBarSection();
    }

    @SneakyThrows
    public void setRequiredFields(String titleText, String mainCategory) {
        if (titleText != null) {
            mainSection().map().titleTextField().setText(titleText);
        }

        if (mainCategory != null) {
            generalSection().map().mainCategorySelect().searchSelectByText(mainCategory);
        }
    }

    public void clickContentTab() {
        map().contentTab().click();
    }

    public void clickItemsTab() {
        waitForSpinners();
        map().itemsTab().click();
    }

    public void uploadNewImage(String path) {
        map().uploadNewImagesInput().getWrappedElement().sendKeys(path);
        browser().waitUntil(e -> !map().uploadNewImageSpinner().getHtmlClass().contains("fa-spinner"));
//        map().alertMessage().validateMessageIs(ToastMessageEnum.IMAGE_SUCCESSFULLY_UPLOADED);
    }

    public void saveGallery() {
        map().saveGalleryButton().click();
        map().alertMessage().toBeVisible().waitToBe();
    }

    public void saveGalleryFromContentTab() {
        if (browser().getUrl().endsWith("/" + OperationsEnum.CREATE.toString().toLowerCase())) {
            map().saveGalleryButtonAtContentTab(OperationsEnum.CREATE).click();
        } else {
            map().saveGalleryButtonAtContentTab(OperationsEnum.EDIT).click();
        }
        browser().tryWaitForResponse(ContentApiUrl.GALLERIES.getUrl());
    }

    public void saveGalleryFromItemsTab() {
        map().saveGalleryButtonAtItemsTab().click();
    }

    public GalleryResponseModel getCreateGalleryResponse() {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.GALLERIES.getUrl(), HttpMethod.POST, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.GALLERIES.getUrl(), HttpMethod.POST.toString(), GalleryResponseModel.class);
    }

    public GalleryResponseModel getUpdateGalleryResponse(String galleryId) {
        ProxyServer.waitForResponse(browser().getWrappedDriver(), ContentApiUrl.GALLERIES_ID.getUrl().formatted(galleryId), HttpMethod.PATCH, 0);
        return ProxyServer.getResponseByUrl(ContentApiUrl.GALLERIES_ID.getUrl().formatted(galleryId), HttpMethod.PATCH.toString(), GalleryResponseModel.class);
    }

    public void selectAlreadyUploadedImage() {
        map().firstUploadedImage().toExist().toBeClickable().waitToBe();
        waitForSpinners();
        map().firstUploadedImage().click();
        map().editImageButton().toExist().toBeClickable().waitToBe();
    }

    public void deleteGallery(String galleryId) {
        breadcrumbNavigation().toBeVisible().waitToBe();
        map().editGalleryButton(galleryId).toBeVisible().toBeClickable().waitToBe();

        javaScript().execute("document.querySelector('[id*=toggle-menu-caret-%s]').click()".formatted(galleryId));

        map().deleteGalleryButton(galleryId).toBeClickable().waitToBe();
        map().deleteGalleryButton(galleryId).click();

        map().deleteModal().toBeVisible().waitToBe();
        map().deleteButtonModal().click();

        ProxyServer.waitForRequest(browser().getWrappedDriver(), ContentApiUrl.GALLERIES_ID.getUrl().formatted(galleryId), HttpMethod.DELETE, 0);
    }

    @Override
    public void openEditPage(String itemId) {
        String editUrl = String.format(CmsPage.EDIT_PAGE_FORMAT.getHref(), getUrl(), itemId);
        navigate().to(editUrl);
        browser().refresh();
        waitForPageLoad();
        waitForSpinners();
    }
}