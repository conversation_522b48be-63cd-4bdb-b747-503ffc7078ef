package blockies.text;

import data.constants.BlockyTypeEnum;
import data.customelements.editor.ListType;

public class List<PERSON>lock extends BaseTextBlock {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.LIST;
    }

    public String getExpectedContentFor(String[] textAsArray, ListType listType) {
        StringBuilder expectedContent = new StringBuilder();

        expectedContent.append(listType.getOpenTag());
        for (String s : textAsArray) {
            expectedContent.append("<li>%s</li>".formatted(s));
        }
        expectedContent.append(listType.getClosingTag());

        return expectedContent.toString();
    }
}
