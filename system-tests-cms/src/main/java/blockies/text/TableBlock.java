package blockies.text;

import data.constants.BlockyTypeEnum;

public class <PERSON><PERSON><PERSON> extends BaseTextBlock {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.TABLE;
    }

    public String getTableContent(int expectedTableRows, int expectedTableColumns) {
        StringBuilder tableContent = new StringBuilder();

        tableContent.append("<table><tbody>");
        for (int i = 0; i < expectedTableRows; i++) {
            tableContent.append("<tr>");
            tableContent.append("<td colspan=\"1\" rowspan=\"1\"><p></p></td>".repeat(Math.max(0, expectedTableColumns)));
            tableContent.append("</tr>");
        }
        tableContent.append("</tbody></table>");
        return tableContent.toString();
    }
}