package blockies.text;

import blockies.baseblockies.baseblocky.BaseBlocky;
import data.customelements.editor.TipTapEditor;
import solutions.bellatrix.web.components.contracts.ComponentText;

public abstract class BaseTextBlock extends BaseBlocky implements ComponentText {

    @Override
    public String getText() {
        return textEditor().textArea().getText();
    }

    public TipTapEditor textEditor() {
        TipTapEditor tipTapEditor = createByXPath(TipTapEditor.class, ".//div[@data-qa='collaborative-editor-wrapper']");
        tipTapEditor.textArea().getWrappedElement().click();
        return tipTapEditor;
    }

    @Override
    public void waitEditScreenToLoad() {
        textEditor().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        return;
    }
}