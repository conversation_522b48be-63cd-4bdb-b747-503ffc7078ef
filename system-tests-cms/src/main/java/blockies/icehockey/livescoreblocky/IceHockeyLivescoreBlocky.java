package blockies.icehockey.livescoreblocky;

import blockies.baseblockies.baselivescoreblocky.BaseLivescoreBlocky;
import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import data.constants.BlockyTypeEnum;
import data.customelements.CompetitionFilterToggleButton;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.multisportapi.CompetitionModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import data.widgets.options.models.DataDate;
import j2html.attributes.Attribute;
import solutions.bellatrix.web.components.*;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

public class IceHockeyLivescoreBlocky extends BaseLivescoreBlocky<LivescoreBlockyFieldsModel> {

    public Asserts asserts() {
        return new Asserts(this);
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.ICE_HOCKEY_LIVESCORE;
    }

    @Override
    public BaseSportBlocky fillForm(LivescoreBlockyFieldsModel blockyData) {
        dateTimePicker().setDate(blockyData.getDate());
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());
        selectCompetition(blockyData);
        selectDisplayOdds(blockyData.isDisplayOdds());
        selectBookmaker(blockyData.getBookmaker());
        selectDefaultHeaderOption(blockyData.getDefaultHeaderOption());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectCompetition(LivescoreBlockyFieldsModel blockyData) {
        if (blockyData.getCompetitionFilter() != null && !blockyData.getCompetitionFilter().isEmpty()) {
            competitionFilter().click();
            allCompetitionsCheckBox().toggleOff();
            for (CompetitionModel competition : blockyData.getCompetitionFilter()) {
                if (competition != null && competition.getName() != null) {
                    competitionSection(competition.getName(), competition.getGender()).check();
                }
            }
            competitionFilterSaveButton().click();
        } else {
            competitionFilterClearButton().click();
        }
        browserService.waitUntilPageLoadsCompletely();
        saveButton().hover();
    }

    @Override
    public List<Attribute> getBlockyAttributes(LivescoreBlockyFieldsModel blockyData) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        return List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.ICE_HOCKEY_LIVESCORE),
                new DataWidgetSportAttribute(DataWidgetSportEnum.ICE_HOCKEY),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataDateAttribute(DataDate.builder()
                        .date(blockyData.getDate().format(dateTimeFormatter))
                        .dateFormat("DD-MM-YYYY")
                        .build()),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new DataOddsBettingIdAttribute(blockyData.isDisplayOdds() && blockyData.getBookmaker() != null ?
                        blockyData.getBookmaker().getBookmakerId() : null),
                new DataCompetitionsAttribute(blockyData.getCompetitionFilter().stream()
                        .map(CompetitionModel::getId)
                        .collect(Collectors.joining(","))),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataHeaderDefaultOptionAttribute(blockyData.getDefaultHeaderOption() != null ?
                        blockyData.getDefaultHeaderOption().getValue() : null),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime()));
    }

    public RadioButton competitionFilterClearButton() {
        return createByXPath(RadioButton.class, "//div[contains(@id, 'competitions-input')]//div[contains(@class, 'indicatorContainer')]");
    }

    public List<Div> selectedCompetitions() {
        return createAllByXPath(Div.class, "//div[contains(@id,'competitions-input')]//div[contains(@class, 'multiValue')]/div[1]");
    }

    public RadioButton competitionFilter() {
        return createByXPath(RadioButton.class, ".//div[@id='competition-input']");
    }

    public CompetitionFilterToggleButton allCompetitionsCheckBox() {
        return createByXPath(CompetitionFilterToggleButton.class, "//div[@id='all-competitions-toggle-input']//span");
    }

    public Button removeCompetitionButton() {
        return createByXPath(Button.class, "(//div[@id='selected-competitions-container']//div)[7]");
    }

    public Span competitionFilterErrorMessage() {
        return createByXPath(Span.class, "//div[@id='competition-input-error']//following-sibling::span");
    }

    public Button competitionFilterSaveButton() {
        return createByXPath(Button.class, "//button[@id='competition-filter-modal-footer-save-btn']");
    }

    public Span competitionFilterHeader() {
        return createByXPath(Span.class, "//span[@id='competition-filter-modal-header-title']");
    }

    public Div competitionSectionCollapseArrow() {
        return createByXPath(Div.class, "//i[contains(@id,'competitions-section-wrapper-collapseOn-icon')]");
    }

    public Button competitionFilterCloseButton() {
        return createByXPath(Button.class, "//button[@id='competition-filter-modal-footer-close-btn']");
    }

    public CheckBox competitionSection(String competition, String gender) {
        if (gender == null || gender.isEmpty()) {
            return createByXPath(CheckBox.class,
                    "//div[@id='%s-competition-section-input']".formatted(competition));
        } else {
            return createByXPath(CheckBox.class,
                    "//div[@id='%s-%s-competition-section-input']".formatted(competition, gender));
        }
    }
}