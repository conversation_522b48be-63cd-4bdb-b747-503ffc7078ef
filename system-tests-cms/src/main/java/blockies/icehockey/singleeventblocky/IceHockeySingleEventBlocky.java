package blockies.icehockey.singleeventblocky;

import blockies.baseblockies.basesingleeventblocky.BaseEventBlocky;
import data.constants.BlockyTypeEnum;
import data.constants.EventStatusType;
import data.constants.Language;
import data.customelements.MatchesList;
import data.customelements.MultiValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.icehockey.IceHockeySingleEventBlockyFieldsModel;
import data.models.searchapi.Translation;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import lombok.Getter;
import solutions.bellatrix.web.components.Div;

import java.util.ArrayList;
import java.util.List;

@Getter
public class IceHockeySingleEventBlocky extends BaseEventBlocky<IceHockeySingleEventBlockyFieldsModel> {

    public MultiValueSearchSelect entitySearchSelect() {
        return createById(MultiValueSearchSelect.class, "ice_hockey-team-select");
    }

    public MatchesList eventsList() {
        return createByXPath(MatchesList.class, ".//div[@data-qa='ice_hockey-widget-event-select']");
    }

    public SingleValueSelect bookmakerSelect() {
        return createById(SingleValueSelect.class, "ice_hockey-widget-bookmaker-select");
    }

    public SingleValueSelect marketSelect() {
        return createByXPath(SingleValueSelect.class, ".//div[@id='ice_hockey-widget-market-select']/div");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, ".//div[@data-qa='ice-hockey-single-event-view']//div[contains(@class,'row')]");
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.ICE_HOCKEY_SINGLE_EVENT;
    }

    @Override
    public IceHockeySingleEventBlocky fillForm(IceHockeySingleEventBlockyFieldsModel blockyData) {
        Translation enName = blockyData.getTeam().getTranslations().stream()
                .filter(t -> t.getLanguage().equals(Language.ENGLISH.getCode())).findFirst().orElse(null);

        selectTeam(enName != null ? enName.getName() : blockyData.getTeam().getName(), blockyData.getTeam().getId());
        selectEvent(blockyData.getEvent());
        if (!blockyData.getEvent().getStatusType().equals(EventStatusType.FINISHED.toString())) {
            selectDisplayOdds(blockyData.isDisplayOdds());
        }
        selectBookmaker(blockyData.getBookmaker());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(IceHockeySingleEventBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.ICE_HOCKEY_SINGLE_EVENT),
                new DataMatchIdAttribute(blockyData.getEvent().getId()),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.ICE_HOCKEY),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime()),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds())));
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    //Edit screen
    public void selectTeam(String teamName, String teamId) {
        waitEditScreenToLoad();
        entitySearchSelect().searchSelectById(teamName, teamId);
    }

    public void filterMultipleTeams(String firstTeamName, String secondTeamName) {
        waitEditScreenToLoad();
        entitySearchSelect().clearFilter(null);

        waitForSpinners();
        entitySearchSelect().searchSelectByText(firstTeamName);

        waitForSpinners();
        entitySearchSelect().searchSelectByText(secondTeamName);
    }

    public String getSelectedTeams() {
        return entitySearchSelect().getText();
    }
}