package blockies.football.lineupsblocky;

import blockies.baseblockies.basesingleeventblocky.BaseEventBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.customelements.MatchesList;
import data.customelements.MultiValueSearchSelect;
import data.customelements.ToggleButton;
import data.models.articles.NestedMatch;
import data.models.blockymodels.football.FootballLineupsBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.v2.MatchV2Model;
import data.utils.StringUtils;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import org.openqa.selenium.support.Color;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class LineupsBlocky extends BaseEventBlocky<FootballLineupsBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_LINEUPS;
    }

    @Override
    public LineupsBlocky fillForm(FootballLineupsBlockyFieldsModel blockyData) {
        entitySearchSelect().searchSelectByText(blockyData.getTeam().getName());
        selectEvent(blockyData.getEvent());
        setTeamsColor(blockyData);
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());

        if (blockyData.isShowCoach()) {
            showCoachToggleButton().click();
        }

        if (blockyData.isShowFirstTeam()) {
            showFirstTeamToggleButton().click();

            if (blockyData.isToggleFirstTeamOpen()) {
                toggleFirstTeamOpenToggleButton().click();
            }
        }

        if (blockyData.isShowSubstitution()) {
            showSubstitutesToggleButton().click();

            if (blockyData.isToggleSubstituteOpen()) {
                toggleSubstitutesOpenToggleButton().click();
            }
        }
        selectRefreshTime(blockyData.getRefreshTime());

        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballLineupsBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.LINEUPS),
                new DataMatchIdAttribute(blockyData.getEvent().getLegacyId()),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataHomeTeamColorAttribute(getExpectedHomeTeamColor(blockyData)),
                new DataAwayTeamColorAttribute(getExpectedAwayTeamColor(blockyData)),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime() == null ? null : blockyData.getRefreshTime().getAttributeValue()),
                new DataElementsAttribute(getExpectedElementsObject(blockyData)),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay())
        ));
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        teamSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public MultiValueSearchSelect teamSelect() {
        return createById(MultiValueSearchSelect.class, "football-widget-team-select");
    }

    public ToggleButton showCoachToggleButton() {
        return createByXPath(ToggleButton.class, ".//div[@id='widget-lineups-elements' and descendant::span[text()='Show coach']]");
    }

    public ToggleButton showFirstTeamToggleButton() {
        return createByXPath(ToggleButton.class, ".//div[@id='widget-lineups-elements' and descendant::span[text()='Show first team']]");
    }

    public ToggleButton showSubstitutesToggleButton() {
        return createByXPath(ToggleButton.class, ".//div[@id='widget-lineups-elements' and descendant::span[text()='Show substitutes']]");
    }

    public ToggleButton toggleFirstTeamOpenToggleButton() {
        return createByXPath(ToggleButton.class, ".//div[@id='widget-lineups-elements' and descendant::span[text()='Toggle first team open']]");
    }

    public ToggleButton toggleSubstitutesOpenToggleButton() {
        return createByXPath(ToggleButton.class, ".//div[@id='widget-lineups-elements' and descendant::span[text()='Toggle substitutes open']]");
    }

    public List<Button> teamColors() {
        return createAllByXPath(Button.class, ".//div[contains(@class, ' teams-color-picker-box')]/div");
    }

    public Button teamColorPicker(String hexColor) {
        return createByXPath(Button.class, ".//div[@title='%s']".formatted(hexColor));
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-lineups-view']//div[contains(@class,'col')]");
    }

    @Override
    protected MultiValueSearchSelect entitySearchSelect() {
        return teamSelect();
    }

    public MatchesList eventsList() {
        return createByXPath(MatchesList.class, ".//div[@data-qa='matches-container']");
    }

    public void selectTeam(String entityName) {
        teamSelect().searchSelectByText(entityName);
    }

    public void filterMultipleEntities(String firstEntityName, String secondEntityName) {
        selectTeam(firstEntityName);

        selectTeam(secondEntityName);
    }

    public String getSelectedEntities() {
        return teamSelect().getText();
    }

    public List<TeamModel> getTeamsResponse(String teamName) {
        var expectedTeamsSearchApiUrl = FootballApiUrl.TEAMS_BY_NAME.url + teamName;
        var playerListType = new TypeToken<ArrayList<TeamModel>>() {
        }.getType();
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsSearchApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(expectedTeamsSearchApiUrl, HttpMethod.GET.toString(), playerListType);
    }

    public List<NestedMatch> getNotStartedMatchesResponse(String teamName) {
        String expectedUrl = FootballApiUrl.MATCHES_NOT_STARTED_BY_TEAM_ID.url;

        return getMatchesTypeResponse(teamName, expectedUrl);
    }

    public List<NestedMatch> getFinishedMatchesResponse(String teamName) {
        String expectedUrl = FootballApiUrl.MATCHES_FINISHED_BY_TEAM_ID.url;

        return getMatchesTypeResponse(teamName, expectedUrl);
    }

    public List<NestedMatch> getInProgressMatchesResponse(String teamName) {
        String expectedUrl = FootballApiUrl.MATCHES_INPROGRESS_BY_TEAM_ID.url;

        return getMatchesTypeResponse(teamName, expectedUrl);
    }

    public List<NestedMatch> getInterruptedMatchesResponse(String teamName) {
        String expectedUrl = FootballApiUrl.MATCHES_INTERRUPTED_BY_TEAM_ID.url;

        return getMatchesTypeResponse(teamName, expectedUrl);
    }

    public String getExpectedHomeTeamColor(FootballLineupsBlockyFieldsModel blockyData) {
        return StringUtils.replaceSpacesWithSymbol(
                Color.fromString(blockyData.getFirstTeamShirtColor().getColorCode()).asRgba(), "");
    }

    public String getExpectedAwayTeamColor(FootballLineupsBlockyFieldsModel blockyData) {
        return StringUtils.replaceSpacesWithSymbol(
                Color.fromString(blockyData.getSecondTeamShirtColor().getColorCode()).asRgba(), "");
    }

    public void setTeamsColor(FootballLineupsBlockyFieldsModel blockyData) {
        setTeamColor(0, blockyData.getFirstTeamShirtColor().getColorCode());
        setTeamColor(1, blockyData.getSecondTeamShirtColor().getColorCode());
    }

    private List<NestedMatch> getMatchesTypeResponse(String teamName, String expectedUrl) {
        String teamId = getTeamsResponse(teamName).get(0).getId();
        expectedUrl = expectedUrl.formatted(teamId);

        ProxyServer.waitForResponse(getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);
        Type matchListType = new TypeToken<ArrayList<NestedMatch>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), matchListType);
    }

    private MatchV2Model getSelectedMatchResponse() {
        var selectedMatchApiUrl = FootballApiUrlV2.MATCHES.getUrl() + "/";
        ProxyServer.waitForResponse(getWrappedDriver(), selectedMatchApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(selectedMatchApiUrl, HttpMethod.GET.toString(), MatchV2Model.class);
    }

    public String getExpectedElementsObject(FootballLineupsBlockyFieldsModel blockyData) {
        if (!blockyData.isShowCoach() && !blockyData.isShowSubstitution() && !blockyData.isShowFirstTeam()) {

            return null;
        }
        String elements = "[";
        elements += blockyData.isShowCoach() ? "{\"name\":\"coach\"}," : "";
        elements += blockyData.isShowFirstTeam() ? "{\"name\":\"first_team\",\"status\":\"%s\"},".formatted(blockyData.isToggleFirstTeamOpen() ? "opened" : "closed") : "";
        elements += blockyData.isShowFirstTeam() ? "{\"name\":\"substitutes\",\"status\":\"%s\"},".formatted(blockyData.isToggleFirstTeamOpen() ? "opened" : "closed") : "";
        elements += "]";

        return elements.replace(",]", "]");
    }

    private void setTeamColor(int teamIndex, String colorCode) {
        Button teamColorButton = teamColorPicker(colorCode);
        teamColors().get(teamIndex).click();
        teamColorButton.toExist().toBeVisible().toBeClickable().waitToBe();
        teamColorButton.click();
        createByXPath(Div.class, "//div[@class='cover']").getWrappedElement().click();
    }
}