package blockies.football.lineupsblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import data.constants.AssertMessages;
import data.constants.WidgetType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballLineupsBlockyFieldsModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;

import java.util.List;

public class Asserts extends BaseSportBlockyAsserts<FootballLineupsBlockyFieldsModel> {

    private final LineupsBlocky blocky;

    public Asserts(LineupsBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertDefaultSectionIsVisible() {
        blocky.teamSelect().toExist().toBeVisible().waitToBe();
        blocky.teamSelect().validateIsVisible();
        blocky.showCoachToggleButton().validateIsVisible();
        blocky.showFirstTeamToggleButton().validateIsVisible();
        blocky.showSubstitutesToggleButton().validateIsVisible();
        blocky.refreshTimeSelect().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    public Asserts assertValidationMessageDisplayed(String expectedMessage) {
        Assertions.assertTrue(blocky.validationMessagesList().stream().anyMatch(e -> e.getText().contains(expectedMessage)),
                AssertMessages.entityNotVisible(String.format("'%s' validation message", expectedMessage)));
        return this;
    }

    public Asserts assertPreviewControlsAreVisible() {
        blocky.waitPreviewScreenToLoad();
        blocky.assertPreviewControlsVisible();
        return this;
    }

    public Asserts assertEditSectionNotExist() {
        var elementsList = blocky.getWrappedDriver().findElements(By.id(blocky.teamSelect().getFindStrategy().getValue()));
        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Edit Section"));
        return this;
    }

    public Asserts assertEditSectionIsVisibleWithClickToggleButtons(FootballLineupsBlockyFieldsModel blockyData) {
        blocky.teamSelect().toExist().toBeVisible().waitToBe();
        blocky.teamSelect().validateIsVisible();
        blocky.eventsList().validateIsVisible();
        if (blockyData.isShowCoach()) {
            blocky.showCoachToggleButton().validateIsVisible();
            blocky.showCoachToggleButton().isToggleOn();
        }
        if (blockyData.isShowFirstTeam()) {
            blocky.showFirstTeamToggleButton().validateIsVisible();
            blocky.showFirstTeamToggleButton().isToggleOn();
        }

        if (blockyData.isShowSubstitution()) {
            blocky.showSubstitutesToggleButton().validateIsVisible();
            blocky.showSubstitutesToggleButton().isToggleOn();
        }

        if (blockyData.isToggleFirstTeamOpen()) {
            blocky.toggleFirstTeamOpenToggleButton().validateIsVisible();
            blocky.toggleFirstTeamOpenToggleButton().isToggleOn();
        }

        if (blockyData.isToggleSubstituteOpen()) {
            blocky.toggleSubstitutesOpenToggleButton().validateIsVisible();
            blocky.toggleSubstitutesOpenToggleButton().isToggleOn();
        }

        blocky.refreshTimeSelect().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballLineupsBlockyFieldsModel blockyData, BodyItem bodyObject) {
        blockyData.setEmbedCode(blocky.getExpectedEmbedCode(blockyData));
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_LINEUPS_V2.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.LINEUPS);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.teamSelect().toExist().toBeVisible().waitToBe();
        blocky.teamSelect().validateIsVisible();
        blocky.eventsList().validateIsVisible();
        blocky.showCoachToggleButton().validateIsVisible();
        blocky.showFirstTeamToggleButton().validateIsVisible();
        blocky.showSubstitutesToggleButton().validateIsVisible();
        blocky.refreshTimeSelect().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballLineupsBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballLineupsBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballLineupsBlockyFieldsModel blockyData) {
        return (List<String>)this;
    }
}