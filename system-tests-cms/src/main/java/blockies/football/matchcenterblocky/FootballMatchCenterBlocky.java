package blockies.football.matchcenterblocky;

import blockies.baseblockies.basesingleeventblocky.BaseEventBlocky;
import data.constants.BlockyTypeEnum;
import data.constants.api.queryparamenums.StatusType;
import data.customelements.MatchesList;
import data.customelements.MultiValueSearchSelect;
import data.models.blockymodels.football.FootballMatchCenterBlockyFieldsModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;

import java.util.List;

public class FootballMatchCenterBlocky extends BaseEventBlocky<FootballMatchCenterBlockyFieldsModel> {

    public Asserts asserts() {
        return new Asserts(this);
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.MATCH_CENTER;
    }

    @Override
    protected MultiValueSearchSelect entitySearchSelect() {
        return createById(MultiValueSearchSelect.class, "football-widget-team-select");
    }

    public CheckBox displayMainEventCheckbox() {
        return createByXPath(CheckBox.class, "//input[contains(@data-qa,'display-main-events')]");
    }

    public MatchesList eventsList() {
        return createByXPath(MatchesList.class, ".//div[@data-qa='matches-container']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, getFindStrategy().getValue() + "//div[@data-qa='football-match-center-view']//div[contains(@class,'row') and not(descendant::div[contains(@class,'row')])]");
    }

    @Override
    public FootballMatchCenterBlocky fillForm(FootballMatchCenterBlockyFieldsModel blockyData) {
        filterEntity(blockyData.getTeam().getName());
        selectEvent(blockyData.getEvent());
        if (blockyData.getEvent().getStatusType().equals(StatusType.NOT_STARTED.toString())) {
            selectDisplayOdds(blockyData.isDisplayOdds());
            selectBookmaker(blockyData.getBookmaker());
        }
        selectDisplayMainEvent(blockyData.isDisplayMainEvent());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectDisplayMainEvent(boolean displayMainEvent) {
        displayMainEventCheckbox().check(displayMainEvent);
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballMatchCenterBlockyFieldsModel blockyData) {
        return List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.FOOTBALL_MATCH_CENTER),
                new DataMatchIdAttribute(blockyData.getEvent().getLegacyId()),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime()),
                new DataHeaderDisplayAttribute(blockyData.isDisplayOdds()),
                new DataDisplayMainEventMatchCenterAttribute(blockyData.isDisplayMainEvent()));
    }
}