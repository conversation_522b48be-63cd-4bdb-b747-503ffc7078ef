package blockies.football.matchcenterblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOddsAsserts;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.StringConstants;
import data.constants.WidgetType;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.SingleEventMatchesType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballMatchCenterBlockyFieldsModel;
import data.utils.DateUtils;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;

public class Asserts extends BaseSportBlockyWithOddsAsserts<FootballMatchCenterBlockyFieldsModel> {

    private final FootballMatchCenterBlocky blocky;

    public Asserts(FootballMatchCenterBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballMatchCenterBlockyFieldsModel blockyData) {
        var expectedPreviewOptions = new ArrayList<String>();
        var startTime = ZonedDateTime.parse(blockyData.getEvent().getStartTime());
        var localStartTime = startTime.withZoneSameInstant(ZoneId.systemDefault());
        var formatter = DateTimeFormatter.ofPattern("MMM d yyyy, HH:mm");
        var formattedStartTime = localStartTime.format(formatter);

        expectedPreviewOptions.add(DateUtils.formatDate(formattedStartTime));
        expectedPreviewOptions.add(blockyData.getEvent().getName().replace(" - ", "\n:\n"));
        expectedPreviewOptions.add("Display main events: " + (blockyData.isDisplayMainEvent() ? StringConstants.YES_STRING : StringConstants.NO_STRING));
        expectedPreviewOptions.add("Display odds: " + (blockyData.isDisplayOdds() ? StringConstants.YES_STRING : StringConstants.NO_STRING));
        if (blockyData.getRefreshTime() != null) {
            expectedPreviewOptions.add("Refresh time: " + blockyData.getRefreshTime().getValue());
        }
        return expectedPreviewOptions;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.entitySearchSelect().toExist().toBeVisible().waitToBe();
        blocky.entitySearchSelect().validateIsVisible();
        blocky.eventsList().validateIsVisible();
        blocky.eventsList().matchesTabButton(SingleEventMatchesType.UPCOMING).validateIsVisible();
        blocky.eventsList().matchesTabButton(SingleEventMatchesType.PAST).validateIsVisible();
        blocky.eventsList().matchesTabButton(SingleEventMatchesType.INTERRUPTED).validateIsVisible();
        assertDisplayOddsCheckboxNotDisplayed();
        assertBookmakerSelectNotDisplayed();
        assertRefreshTimeFieldVisible();
        assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        blocky.entitySearchSelect().toExist().toBeVisible().waitToBe();
        blocky.entitySearchSelect().validatePlaceholderIs(PlaceholderField.SEARCH.getValue());
        blocky.entitySearchSelect().validateTextIs("[]");
        blocky.eventsList().validateSelectedTabIs(SingleEventMatchesType.UPCOMING);
        Assertions.assertTrue(blocky.eventsList().getUpcomingMatches().isEmpty(), "Match select field must be empty but it is not.");
        blocky.displayOddsCheckbox().validateIsUnchecked();
        assertRefreshTimeDefaultState();
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballMatchCenterBlockyFieldsModel blockyData) {
        blocky.entitySearchSelect().toExist().toBeVisible().waitToBe();
        blocky.entitySearchSelect().validateIsVisible();
        blocky.entitySearchSelect().validateTextIs("[%s]".formatted(blockyData.getTeam().getName()));
        //ToDO hhristov 10/16/2024 - Remove @disable annotation when ticket "SFE-5596" is resolve
        //assertDisplayOddsCheckbox(blockyData.getEvent());
        blocky.eventsList().validateSelectedTabIs(SingleEventMatchesType.UPCOMING);
        //blocky.eventsList().validateSelectedEventIs(blockyData.getEvent());
        //assertDisplayOddsCheckbox(blockyData.getEvent());
        assertRefreshTimeInEditMode(blockyData);
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballMatchCenterBlockyFieldsModel blockyData) {
        blocky.waitPreviewScreenToLoad();
        var expectedViewOptions = getExpectedViewOptions(blockyData);
        var actualViewOptions = blocky.previewRowsList().stream().map(Div::getText).toList();

        Assertions.assertLinesMatch(expectedViewOptions, actualViewOptions, AssertMessages.entityNotExpected("Preview options"));
        blocky.assertPreviewControlsVisible();
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballMatchCenterBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertConfigObject(configObject, DataWidgetIdEnum.FOOTBALL_MATCH_CENTER);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_MATCH_CENTER_V2.getValue()));
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    public Asserts assertEditSectionDefaultState() {
        blocky.entitySearchSelect().toExist().toBeVisible().waitToBe();
        Assertions.assertEquals("[]", blocky.entitySearchSelect().getText(), AssertMessages.entityNotExpected("Team Select"));
        Assertions.assertTrue(blocky.eventsList().getUpcomingMatches().isEmpty(), AssertMessages.entityNotExpected("Team Select"));
        return this;
    }

    public Asserts assertEditSectionNotExist() {
        var elementsList = blocky.getWrappedDriver().findElements(By.id(blocky.entitySearchSelect().getFindStrategy().getValue()));

        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Edit Section"));
        return this;
    }

    public Asserts assertFootballMatchesRequestIsMade() {
        ProxyServer.waitForRequest(blocky.getWrappedDriver(), FootballApiUrlV2.MATCHES.getUrl(), HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(FootballApiUrlV2.MATCHES.getUrl());
        return this;
    }
}