package blockies.football.knockoutschemeblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.PlaceholderField;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballKnockoutBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class KnockoutSchemeBlocky extends BaseSportBlocky<FootballKnockoutBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_KNOCKOUT;
    }

    @Override
    public KnockoutSchemeBlocky fillForm(FootballKnockoutBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectSeason(blockyData);
        selectStage(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    private void selectStage(FootballKnockoutBlockyFieldsModel blockyData) {
        if (blockyData.getStage() != null) {
            stageSelect().selectOptionByText(blockyData.getStage().getName());
        }
    }

    private void selectSeason(FootballKnockoutBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null) {
            seasonSelect().selectOptionByExactText(blockyData.getSeason().getName());
        }
    }

    private void selectTournament(FootballKnockoutBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballKnockoutBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.KNOCKOUT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getId().toString()),
                new DataSeasonAttribute(blockyData.getSeason().getId()),
                new DataStageAttribute(blockyData.getStage().getId()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue())
        ));
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSelect tournamentSelect() {
        return createById(SingleValueSelect.class, "football-widget-tournament-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-season-select");
    }

    public SingleValueSelect stageSelect() {
        return createById(SingleValueSelect.class, "football-widget-stage-select");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='knockout-view-component']//div[@class='col']//div");
    }

    public List<CommonResultModel> getTournamentGetResponses() {
        var expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<ArrayList<CommonResultModel>>() {
        }.getType();

        return Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), responseListType));
    }

    public CommonResultModel getFirstTournamentResponse() {
        var firstTournament = getTournamentGetResponses().get(0);
        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + firstTournament.getId();
        ProxyServer.waitForResponse(getWrappedDriver(), tournamentApiUrl, HttpMethod.GET, 0);

        return Objects.requireNonNull(ProxyServer.getResponseByUrl(tournamentApiUrl, HttpMethod.GET.toString(), CommonResultModel.class));
    }

    public CommonResultModel getSeasonResponse() {
        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + getFirstTournamentResponse().getSeasons().get(0).getId();
        ProxyServer.waitForResponse(getWrappedDriver(), seasonApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(seasonApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
        browserService.waitUntil(e -> !tournamentSelect().getText().equals(PlaceholderField.SELECT.getValue()));
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }
}