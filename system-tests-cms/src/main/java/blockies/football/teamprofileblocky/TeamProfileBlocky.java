package blockies.football.teamprofileblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.TeamDataElementsStatisticsEnum;
import data.constants.enums.football.FootballTeamEnum;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballTeamProfileBlockyFieldsModel;
import data.models.footballapi.common.MatchesV2Response;
import data.models.footballapi.common.Season;
import data.models.footballapi.common.SeasonsResponse;
import data.models.footballapi.teams.TeamModel;
import data.models.footballapi.v2.MatchV2Model;
import data.models.footballapi.v2.TeamV2Model;
import data.models.footballapi.v2.seasons.SeasonV2Model;
import data.utils.StringUtils;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class TeamProfileBlocky extends BaseSportBlockyWithOdds<FootballTeamProfileBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_TEAM_PROFILE;
    }

    @Override
    public void previewResult() {
        super.previewResult();
        browserService.tryWaitForResponse(FootballApiUrlV2.TEAMS.getUrl());
        browserService.tryWaitForResponse(FootballApiUrl.TEAMS_STATISTICS.getUrl());
        browserService.tryWaitForResponse(FootballApiUrlV2.SEASONS.getUrl());
        browserService.tryWaitForResponse(FootballApiUrlV2.MATCHES_LIVESCORE.getUrl());
        browserService.tryWaitUntil(e -> monorepoBlockPreviewContent().isVisible());
    }

    @Override
    public TeamProfileBlocky fillForm(FootballTeamProfileBlockyFieldsModel blockyData) {
        selectTeam(blockyData);
        selectSeason(blockyData);
        selectDisplayMatch(blockyData.isDisplayMatch());
        if (blockyData.isDisplayMatch()) {
            selectMatch(blockyData);
        }
        selectDisplayStatistics(blockyData.isDisplayStatistics());
        if (blockyData.isDisplayStatistics()) {
            selectDisplayStatistics(blockyData);
        }
        selectBookmaker(blockyData.getBookmaker());
        selectDisplayOdds(blockyData.isDisplayOdds());
        selectSocialIcons(blockyData.isDisplaySocialIcons());
        selectDisplayTeamImage(blockyData.isDisplayTeamImage());
        if (blockyData.isDisplayStatistics()) {
            selectStatistics(blockyData);
        }
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectSocialIcons(boolean displaySocialIcons) {
        displaySocialIconsCheckbox().check(displaySocialIcons);
    }

    public void selectDisplayStatistics(boolean displayStatistics) {
        displayStatisticsCheckbox().check(displayStatistics);
    }

    public void selectDisplayStatistics(FootballTeamProfileBlockyFieldsModel blockyData) {
        if (blockyData.getStatistics() != null) {
            statisticsParametersSelect().clearSelection();
            statisticsParametersSelect().searchSelectOptionsByText(blockyData.getStatistics()
                    .stream().map(TeamDataElementsStatisticsEnum::getValue).toList());
        } else {
            statisticsParametersSelect().clearSelection();
        }
    }

    public void selectMatch(FootballTeamProfileBlockyFieldsModel blockyData) {
        if (blockyData.getMatch() != null && blockyData.isDisplayMatch()) {
            matchSelect().searchSelectByText(blockyData.getMatch().getMatchTitle());
        }
    }

    public void selectSeason(FootballTeamProfileBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null && blockyData.getMatch() != null) {
            seasonSelect().searchSelectByText("%s-%s".formatted(blockyData.getSeason().getName(), blockyData.getMatch().getSeason().getName()));
        } else {
            seasonSelect().clearSelection();
        }
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballTeamProfileBlockyFieldsModel blockyData) {
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TEAM)));
        if (blockyData.getTeam() != null) {
            attributes.add(new DataTeamAttribute(blockyData.getTeam().getId()));
        }
        if (blockyData.getSeason() != null && blockyData.getMatch() != null) {
            attributes.add(new DataSeasonAttribute(blockyData.getMatch().getSeason().getId()));
        }
        if (blockyData.getMatch() != null && blockyData.isDisplayMatch()) {
            attributes.add(new DataMatchIdAttribute(blockyData.getMatch().getId().toString()));
        }
        attributes.add(new DataOddsDisplayAttribute(blockyData.isDisplayOdds()));
        if (blockyData.getBookmaker() != null && blockyData.isDisplayOdds()) {
            attributes.add(new DataOddsBettingIdAttribute(blockyData.getBookmaker().getBookmakerId()));
        }
        attributes.add(new DataDisplayTeamImageAttribute(blockyData.isDisplayTeamImage()));
        if (blockyData.getMatch() != null && blockyData.getSeason() != null) {
            attributes.add(new DataElementsAttribute(getExpectedElementsAttribute(blockyData)));
        }
        attributes.add(new DataDisplayMatchAttribute(blockyData.isDisplayMatch()));
        attributes.addAll(List.of(
                new DataStatisticsDisplayAttribute(blockyData.isDisplayStatistics()),
                new DataDisplaySocialIconsAttribute(blockyData.isDisplaySocialIcons()),
                new DataDisplayTeamImageAttribute(blockyData.isDisplayTeamImage()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime())));
        return attributes;
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        teamSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public CheckBox displaySocialIconsCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-social-icons-checkbox");
    }

    public void selectStatistics(FootballTeamProfileBlockyFieldsModel blockyData) {
        displayStatisticsCheckbox().check(blockyData.isDisplayStatistics());
    }

    public void selectDisplayTeamImage(boolean displayTeamImage) {
        displayTeamImageCheckbox().check(displayTeamImage);
    }

    public void selectDisplayMatch(boolean displayMatch) {
        displayMatchCheckbox().check(displayMatch);
    }

    public void selectTeam(FootballTeamProfileBlockyFieldsModel blockyData) {
        if (blockyData.getTeam() != null) {
            teamSelect().searchSelectByText(blockyData.getTeam().getName());
            browserService.waitForAjax();
        } else {
            teamSelect().clearSelection();
        }
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public Div monorepoBlockPreviewContent() {
        return createByXPath(Div.class, "//div[contains(@class,'monorepo-block-preview-content')]");
    }

    public MultiValueSelect statisticsParametersSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-team-profile-elements-select");
    }

    public CheckBox selectDisplayMatchCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-match-checkbox");
    }

    public SingleValueSearchSelect teamSelect() {
        return createById(SingleValueSearchSelect.class, "football-widget-team-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-team-season-select");
    }

    public SingleValueSelect matchSelect() {
        return createById(SingleValueSelect.class, "football-widget-match-select");
    }

    public CheckBox displayStatisticsCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-statistics-checkbox");
    }

    public CheckBox displayTeamImageCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-team-image-checkbox");
    }

    public CheckBox displayMatchCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-match-checkbox");
    }

    public MultiValueSelect infoParametersSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-team-profile-elements-select");
    }

    public MultiValueSelect statisticParametersSelect() {
        return createByXPath(MultiValueSelect.class, "//div[@data-qa='football-team-profile-elements-select']");
    }

    public MultiValueSelect tabsSelect() {
        return createByXPath(MultiValueSelect.class, "//div[@id='columns-select']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-team-block-view']//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }

    public TeamModel getTeamResponse(String teamName) {
        var expectedTeamsSearchApiUrl = FootballApiUrl.TEAMS_BY_NAME.url + StringUtils.urlEncodeString(teamName);
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsSearchApiUrl, HttpMethod.GET, 0);
        var playerListType = new TypeToken<List<TeamModel>>() {
        }.getType();
        List<TeamModel> teamsResponse = ProxyServer.getResponseByUrl(expectedTeamsSearchApiUrl, HttpMethod.GET.toString(), playerListType);

        return Objects.requireNonNull(teamsResponse).get(0);
    }

    public TeamV2Model getTeamV2Response(String teamId) {
        String requestPartialUrl = FootballApiUrlV2.TEAMS.getUrl() +
                "/%s?language_code=%s".formatted(teamId, Language.ENGLISH.getCode());
        ProxyServer.waitForResponse(getWrappedDriver(), requestPartialUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(requestPartialUrl, HttpMethod.GET.toString(), TeamV2Model.class);
    }

    public SeasonV2Model getSeasonV2Response(FootballTeamEnum team) {
        String requestPartialUrl = FootballApiUrlV2.SEASONS.getUrl() +
                "?team_id=%s&language_code=%s".formatted(team.getLegacyId(), Language.ENGLISH.getCode());
        ProxyServer.waitForResponse(getWrappedDriver(), requestPartialUrl, HttpMethod.GET, 0);

        Type responseTypeToken = new TypeToken<Map<String, List<SeasonV2Model>>>() {
        }.getType();
        Map<String, List<SeasonV2Model>> response = Objects.requireNonNull(ProxyServer.getResponseByUrl(requestPartialUrl,
                HttpMethod.GET.toString(), responseTypeToken));

        List<SeasonV2Model> seasons = response.get(StringConstants.SEASONS_STRING);
        return seasons != null && !seasons.isEmpty() ? seasons.get(0) : null;
    }

    public List<Season> getSeasonsResponse(FootballTeamProfileBlockyFieldsModel teamName) {
        String teamId = getTeamResponse(teamName.getTeam().getName()).getId();
        var expectedStatisticsApiUrl = FootballApiUrl.TEAM_SEASONS.url.formatted(teamId);
        ProxyServer.waitForResponse(getWrappedDriver(), expectedStatisticsApiUrl, HttpMethod.GET, 0);
        var seasonResponseType = new TypeToken<SeasonsResponse>() {
        }.getType();

        SeasonsResponse response = Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedStatisticsApiUrl,
                HttpMethod.GET.toString(), seasonResponseType));
        return response.getSeasons().stream()
                .filter(m -> m.getTournament().getName().equals(teamName.getSeason().getName()))
                .toList();
    }

    public List<MatchV2Model> getMatchesResponse(String teamName) {
        String teamId = getTeamResponse(teamName).getId();
        String expectedMatchesApiUrl = FootballApiUrlV2.MATCHES.getUrl() + "?team_ids=%s".formatted(teamId);

        ProxyServer.waitForResponse(getWrappedDriver(), expectedMatchesApiUrl, HttpMethod.GET, 0);
        Type matchesResponseType = new TypeToken<MatchesV2Response>() {
        }.getType();
        MatchesV2Response response = Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedMatchesApiUrl, HttpMethod.GET.toString(), matchesResponseType));

        return response.getMatches();
    }

    public List<String> getTeamMatchesAsString(String team, boolean addStatus) {
        List<String> matches = new ArrayList<>();
        List<MatchV2Model> matchesResponse = getMatchesResponse(team);

        for (MatchV2Model entity : matchesResponse) {
            if (entity.getHomeTeam().getName().equals(team) || entity.getAwayTeam().getName().equals(team)) {
                if (addStatus) {
                    matches.add(entity.getMatchTitle());
                } else {
                    matches.add(String.format("%s - %s", entity.getHomeTeam().getName(), entity.getAwayTeam().getName()));
                }
            }
        }
        return matches;
    }

    public String getExpectedElementsAttribute(FootballTeamProfileBlockyFieldsModel blockyData) {
        return "{\"stats\":[" +
                (blockyData.getStatistics() != null && !blockyData.getStatistics().isEmpty() ?
                        "\"%s\"".formatted(String.join("\",\"", blockyData.getStatistics().stream()
                                .map(TeamDataElementsStatisticsEnum::getDisplayValue)
                                .toList())) : "") +
                "]}";
    }
}