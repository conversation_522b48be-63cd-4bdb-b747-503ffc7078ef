package blockies.football.singleroundblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOddsAsserts;
import data.constants.*;
import data.constants.enums.RoundChangeDropdownEnum;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballSingleRoundBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.v2.RoundV2Model;
import data.models.footballapi.v2.seasons.SeasonV2StageModel;
import data.models.footballapi.v2.seasons.SeasonsV2DetailsStagesModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Asserts extends BaseSportBlockyWithOddsAsserts<FootballSingleRoundBlockyFieldsModel> {

    private final FootballSingleRoundBlocky blocky;

    protected Asserts(FootballSingleRoundBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.tournamentSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateIsVisible();
        blocky.roundChangeDropdownSelect().validateIsVisible();
        blocky.seasonSelect().validateIsVisible();
        blocky.stageSelect().validateIsVisible();
        blocky.roundSelect().validateIsVisible();
        blocky.sortDirectionSelect().validateIsVisible();
        blocky.limitField().validateIsVisible();
        blocky.headerDisplayCheckbox().validateIsVisible();
        blocky.displayOddsCheckbox().validateIsVisible();
        blocky.bookmakerSelect().validateIsVisible();
        assertRefreshTimeFieldVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        CommonResultModel firstTournament = blocky.getTournamentsResponse().get(0);
        Season firstSeason = blocky.getTournamentByIdResponse(firstTournament).getSeasons().get(0);
        SeasonsV2DetailsStagesModel stage = blocky.getSeasonDetailsResponse(firstSeason)
                .getStages()
                .stream()
                .filter(s -> s.getStage().getStatus().equals(StatusEnum.ACTIVE.name()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Active stage not found"));
        RoundV2Model currentRound = getCurrentRound(stage.getRounds());

        blocky.tournamentSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateTextIs(firstTournament.getName());
        blocky.roundChangeDropdownSelect().validateTextIs(RoundChangeDropdownEnum.AVAILABLE.getValue());
        blocky.seasonSelect().validateTextIs(firstSeason.getName());
        blocky.stageSelect().validateTextIs(stage.getStage().getName());
        if (currentRound != null) {
            blocky.roundSelect().validateTextIs(currentRound.getName());
        }
        blocky.sortDirectionSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        blocky.limitField().validatePlaceholderIs(PlaceholderField.THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        blocky.headerDisplayCheckbox().validateIsUnchecked();
        blocky.displayOddsCheckbox().validateIsChecked();
        blocky.bookmakerSelect().validateTextIs(StringConstants.EMPTY_STRING);
        assertRefreshTimeDefaultState();
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballSingleRoundBlockyFieldsModel blockyData) {
        blocky.tournamentSelect().validateTextIs(blockyData.getTournament().getName());
        blocky.roundChangeDropdownSelect().validateTextIs(blockyData.getRoundChangeDropdown().getValue());
        blocky.seasonSelect().validateTextIs(blockyData.getSeason().getName());
        blocky.stageSelect().validateTextIs(blockyData.getStage().getName());
        blocky.roundSelect().validateTextIs(blockyData.getRound().getName());

        if (blockyData.getSortDirection() != null) {
            blocky.sortDirectionSelect().validateTextIs(blockyData.getSortDirection().getValue());
        } else {
            blocky.sortDirectionSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        }

        if (blockyData.getLimit() != null) {
            blocky.limitField().validateTextIs(blockyData.getLimit());
        } else {
            blocky.limitField().validatePlaceholderIs(PlaceholderField.THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        }

        if (blockyData.isDataHeaderDisplay()) {
            blocky.headerDisplayCheckbox().validateIsChecked();
        } else {
            blocky.headerDisplayCheckbox().validateIsUnchecked();
        }

        if (blockyData.isDisplayOdds()) {
            blocky.displayOddsCheckbox().validateIsChecked();
            if (blockyData.getBookmaker() != null) {
                blocky.bookmakerSelect().validateTextIs(blockyData.getBookmaker().getBookmakerName());
            } else {
                blocky.bookmakerSelect().validateTextIs(StringConstants.EMPTY_STRING);
            }
        } else {
            blocky.displayOddsCheckbox().validateIsUnchecked();
        }

        assertRefreshTimeInEditMode(blockyData);

        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballSingleRoundBlockyFieldsModel blockyData) {
        blocky.waitPreviewScreenToLoad();

        List<String> expectedViewOptions = getExpectedViewOptions(blockyData);
        List<String> actualViewOptions = blocky.getActualViewOptions(blocky.previewRowsList());

        Assertions.assertLinesMatch(expectedViewOptions, actualViewOptions, AssertMessages.entityNotExpected("Preview options"));
        blocky.assertPreviewControlsVisible();
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballSingleRoundBlockyFieldsModel blockyData) {
        List<String> expectedPreviewOptions = new ArrayList<>();

        expectedPreviewOptions.add("Tournament: %s".formatted(blockyData.getTournament().getName()));
        expectedPreviewOptions.add("Season: %s".formatted(blockyData.getSeason().getName()));
        expectedPreviewOptions.add("Stage: %s".formatted(blockyData.getStage().getName()));
        expectedPreviewOptions.add("Round:%s".formatted(blockyData.getRound().getName()));

        if (blockyData.getDateFrom() != null) {
            expectedPreviewOptions.add("Date from: %s".formatted(blockyData.getDateFrom().date));
        }

        if (blockyData.getDateTo() != null) {
            expectedPreviewOptions.add("Date to: %s".formatted(blockyData.getDateTo().date));
        }

        if (blockyData.getLimit() != null) {
            expectedPreviewOptions.add("Limit: %s".formatted(blockyData.getLimit()));
        }
        if (blockyData.getSortDirection() != null) {
            expectedPreviewOptions.add("Sort direction: %s".formatted(blockyData.getSortDirection().getValue()));
        }

        expectedPreviewOptions.add("Display odds: %s".formatted(blockyData.isDisplayOdds() ? StringConstants.YES_STRING : StringConstants.NO_STRING));
        if (blockyData.isDisplayOdds() && blockyData.getBookmaker() != null) {
            expectedPreviewOptions.add("Show: %s".formatted(blockyData.getBookmaker().getBookmakerName()));
        }

        expectedPreviewOptions.add("Display widget header: %s".formatted(blockyData.isDataHeaderDisplay() ? StringConstants.YES_STRING : StringConstants.NO_STRING));

        if (blockyData.getRefreshTime() != null) {
            expectedPreviewOptions.add("Refresh time: %s".formatted(blockyData.getRefreshTime().getValue()));
        }

        return expectedPreviewOptions;
    }

    @Override
    public BaseSportBlockyAsserts assertBodyObjectInArticleCreateRequest(FootballSingleRoundBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_SINGLE_ROUND.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.ROUND);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    public Asserts assertTournamentOptions() {
        List<String> expectedTournaments = blocky.getTournamentsResponse().stream().map(CommonResultModel::getName).toList();
        List<String> actualTournaments = blocky.tournamentSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedTournaments, actualTournaments, AssertMessages.entityNotExpected("Tournament options"));
        return this;
    }

    public Asserts assertRoundChangeDropdownOptions() {
        List<String> expectedOptions = Arrays.stream(RoundChangeDropdownEnum.values()).map(RoundChangeDropdownEnum::getValue).toList();
        List<String> actualValues = blocky.roundChangeDropdownSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedOptions, actualValues, AssertMessages.entityNotExpected("Round change dropdown options"));
        return this;
    }

    public Asserts assertSeasonOptions() {
        List<String> expectedOptions = blocky.getTournamentByIdResponse(blocky.getTournamentsResponse().get(0)).getSeasons().stream()
                .map(Season::getName)
                .toList();
        List<String> actualOptions = blocky.seasonSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedOptions, actualOptions, AssertMessages.entityNotExpected("Season options"));
        return this;
    }

    public Asserts assertStageOptions() {
        Season season = blocky.getTournamentByIdResponse(blocky.getTournamentsResponse().get(0)).getSeasons().get(0);
        List<String> expectedOptions = blocky.getSeasonDetailsResponse(season).getStages().stream()
                .map(s -> s.getStage().getName())
                .toList();
        List<String> actualOptions = blocky.stageSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedOptions, actualOptions, AssertMessages.entityNotExpected("Stage options"));
        return this;
    }

    public Asserts assertRoundOptions(CommonResultModel expectedTournament, Season expectedSeason, SeasonV2StageModel expectedStage) {
        Season season = blocky.getTournamentByIdResponse(expectedTournament).getSeasons().stream()
                .filter(s -> s.getId().equals(expectedSeason.getId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Season not found"));

        List<String> expectedRoundOptions = blocky.getSeasonDetailsResponse(season).getStages().stream()
                .filter(s -> s.getStage().getId().equals(expectedStage.getId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Stage not found"))
                .getRounds()
                .stream()
                .map(RoundV2Model::getName)
                .toList();

        List<String> actualOptions = blocky.roundSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedRoundOptions, actualOptions, AssertMessages.entityNotExpected("Stage options"));
        return this;
    }

    @Override
    public Asserts assertSortDirectionOptions() {
        List<String> expectedOptions = SortDirectionEnum.getEnumValues();
        List<String> actualOptions = blocky.sortDirectionSelect().getOptionsValues();
        Assertions.assertLinesMatch(expectedOptions, actualOptions, AssertMessages.entityNotExpected("Sort direction options"));
        return this;
    }

    private RoundV2Model getCurrentRound(List<RoundV2Model> rounds) {
        if (rounds == null || rounds.isEmpty()) {
            return null;
        }

        return rounds.stream()
                .filter(round -> round.getStatus().equals(StatusEnum.ACTIVE.name()))
                .findFirst()
                .orElseGet(() -> rounds.get(rounds.size() - 1));
    }
}