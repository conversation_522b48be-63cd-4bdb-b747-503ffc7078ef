package blockies.football.singleroundblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import com.google.gson.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.RoundChangeDropdownEnum;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballSingleRoundBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.v2.seasons.SeasonV2DetailsModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataSortDirectionEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class FootballSingleRoundBlocky extends BaseSportBlockyWithOdds<FootballSingleRoundBlockyFieldsModel> {

    public SingleValueSelect tournamentSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-tournament-select");
    }

    public SingleValueSelect roundChangeDropdownSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-round-dropdown-select");
    }

    public SingleValueSelect seasonSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-season-select");
    }

    public SingleValueSelect stageSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-stage-select");
    }

    public SingleValueSelect roundSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-round-select");
    }

    public TextInput limitField() {
        return createByIdContaining(TextInput.class, "limit-select");
    }

    public SingleValueSelect sortDirectionSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-sort-direction-select");
    }

    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class,
                "//div[contains(@data-qa, '-view')]//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }

    @Override
    public void previewResult() {
        super.previewResult();
        browserService.tryWaitForResponse("/v2/matches");
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_SINGLE_ROUND;
    }

    @Override
    public FootballSingleRoundBlocky fillForm(FootballSingleRoundBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectRoundChangeDropdown(blockyData);
        selectSeason(blockyData);
        selectStage(blockyData);
        selectRound(blockyData);
        selectSortDirection(blockyData.getSortDirection());
        selectDateFrom(blockyData);
        selectDateTo(blockyData);
        fillLimitField(blockyData);
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());
        selectDisplayOdds(blockyData.isDisplayOdds());
        selectBookmaker(blockyData.getBookmaker());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectDateFrom(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getDateFrom() != null) {
            dateFromDatePicker().setDate(LocalDate.parse(blockyData.getDateFrom().getDate()));
        }
    }

    public void selectDateTo(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getDateTo() != null) {
            dateToDatePicker().setDate(LocalDate.parse(blockyData.getDateTo().getDate()));
        }
    }

    public FootballSingleRoundBlocky selectTournament(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
        return this;
    }

    public FootballSingleRoundBlocky selectRoundChangeDropdown(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getRoundChangeDropdown() != null) {
            roundChangeDropdownSelect().searchSelectByText(blockyData.getRoundChangeDropdown().getValue());
        }
        return this;
    }

    public FootballSingleRoundBlocky selectSeason(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null) {
            seasonSelect().searchSelectByText(blockyData.getSeason().getName());
        }
        return this;
    }

    public FootballSingleRoundBlocky selectStage(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getStage() != null) {
            stageSelect().searchSelectByText(blockyData.getStage().getName());
        }
        return this;
    }

    public FootballSingleRoundBlocky selectRound(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getRound() != null) {
            roundSelect().searchSelectByText(blockyData.getRound().getName());
        }
        return this;
    }

    @Override
    public void selectSortDirection(DataSortDirectionEnum sortDirection) {
        if (sortDirection != null) {
            sortDirectionSelect().searchSelectByText(sortDirection.getValue());
        }
    }

    public FootballSingleRoundBlocky fillLimitField(FootballSingleRoundBlockyFieldsModel blockyData) {
        if (blockyData.getLimit() != null && !blockyData.getLimit().isEmpty()) {
            limitField().setText(blockyData.getLimit());
        }
        return this;
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballSingleRoundBlockyFieldsModel blockyData) {
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.ROUND),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getEntityId()),
                new DataSeasonAttribute(blockyData.getSeason().getId()),
                new DataStageAttribute(blockyData.getStage().getId()),
                new DataRoundAttribute(blockyData.getRound().getId())
        ));
        if (blockyData.getDateFrom() != null) {
            attributes.add(new DataDateFromAttribute(blockyData.getDateFrom()));
        }

        if (blockyData.getDateTo() != null) {
            attributes.add(new DataDateToAttribute(blockyData.getDateTo()));
        }
        attributes.addAll(List.of(
                new DataSortDirectionAttribute(blockyData.getSortDirection() != null ?
                        blockyData.getSortDirection().getStorybookValue() : null),
                new DataLimitAttribute(blockyData.getLimit() != null ? blockyData.getLimit() : null),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new DataOddsBettingIdAttribute(blockyData.getBookmaker() != null && blockyData.isDisplayOdds() ?
                        blockyData.getBookmaker().getBookmakerId() : null),
                new DataDropdownDisplayAttribute(blockyData.getRoundChangeDropdown().equals(RoundChangeDropdownEnum.AVAILABLE)),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime() != null ? blockyData.getRefreshTime() : null)
        ));
        return attributes;
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    protected List<CommonResultModel> getTournamentsResponse() {
        String url = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments?") + "client_order=sportalios";

        var responseListType = new TypeToken<List<CommonResultModel>>() {
        }.getType();

        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), responseListType);
    }

    protected CommonResultModel getTournamentByIdResponse(CommonResultModel tournament) {
        String url = FootballApiUrl.TOURNAMENTS.url + tournament.getId();

        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    protected SeasonV2DetailsModel getSeasonDetailsResponse(Season season) {
        String url = FootballApiUrlV2.SEASONS.getUrl() + "/details?season_id=" + season.getId();

        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), SeasonV2DetailsModel.class);
    }
}