package blockies.football.livescoreblocky;

import blockies.baseblockies.baselivescoreblocky.BaseLivescoreBlocky;
import data.constants.BlockyTypeEnum;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import data.widgets.options.models.DataDate;
import j2html.attributes.Attribute;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class FootballLivescoreBlocky extends BaseLivescoreBlocky<LivescoreBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_LIVESCORE;
    }

    @Override
    public FootballLivescoreBlocky fillForm(LivescoreBlockyFieldsModel blockyData) {
        dateTimePicker().setDate(blockyData.getDate());
        selectDisplayOdds(blockyData.isDisplayOdds());
        selectRefreshTime(blockyData.getRefreshTime());
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(LivescoreBlockyFieldsModel blockyData) {
        String dateFormat = "dd.MM.yyyy";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dateFormat);

        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.LIVESCORE),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataDateAttribute(DataDate.builder().date(blockyData.getDate().format(dateTimeFormatter)).dateFormat(dateFormat.toUpperCase()).build()),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new DataHeaderDisplayAttribute(BooleanEnum.FALSE),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue())
        ));
    }

    public Asserts asserts() {
        return new Asserts(this);
    }
}