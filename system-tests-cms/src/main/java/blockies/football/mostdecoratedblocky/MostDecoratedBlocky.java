package blockies.football.mostdecoratedblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.StringConstants;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.MostDecoratedPlayerColumnsEnum;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballMostDecoratedPlayersBlockyFieldsModel;
import data.models.footballapi.cardlist.CardObjectModel;
import data.models.footballapi.common.CommonResultModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class MostDecoratedBlocky extends BaseSportBlocky<FootballMostDecoratedPlayersBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_MOST_DECORATED_PLAYERS;
    }

    @Override
    public MostDecoratedBlocky fillForm(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectSeason(blockyData);
        selectPlayersToHighlight(blockyData);
        selectColumns(blockyData);
        selectStartFromPosition(blockyData);
        selectShowSelect(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectTournament(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
    }

    public void selectSeason(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null) {
            seasonSelect().searchSelectByText(blockyData.getSeason().getName());
        }
    }

    public void selectPlayersToHighlight(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        if (blockyData.getPlayerToHighLight() != null) {
            playersToHighlightSelect().searchSelectByText(blockyData.getPlayerToHighLight().getPlayer().getName());
        }
    }

    public void selectShowSelect(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        showTextField().setText(String.valueOf(blockyData.getShow()));
    }

    public void selectStartFromPosition(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        startFromPositionTextField().setText(String.valueOf(blockyData.getStartFromPosition()));
    }

    public void selectColumns(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        columnsToShowSelect().clearSelection();
        if (blockyData.getColumns() != null) {
            columnsToShowSelect().selectOptionsByText(blockyData.getColumns().stream().map(MostDecoratedPlayerColumnsEnum::getDisplayValue).toList());
        }
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballMostDecoratedPlayersBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.MOST_DECORATED_PLAYERS),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getId().toString()),
                new DataSeasonAttribute(blockyData.getSeason().getId().toString()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataLimitAttribute(blockyData.getShow()),
                new DataHighlightEntitiesAttribute("[%s]".formatted(blockyData.getPlayerToHighLight().getPlayer().getId().toString())),
                new DataStartPositionAttribute(blockyData.getStartFromPosition()),
                new DataElementsAttribute(blockyData.getColumns().stream().map(MostDecoratedPlayerColumnsEnum::getValue).toList()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue())
        ));
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSelect tournamentSelect() {
        return createById(SingleValueSelect.class, "football-widget-tournament-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-season-select");
    }

    public MultiValueSelect columnsToShowSelect() {
        return createByXPath(MultiValueSelect.class, "//label[text()='Pick columns to show']//following-sibling::div");
    }

    public MultiValueSelect playersToHighlightSelect() {
        return createById(MultiValueSelect.class, "football-widget-most-decorated-players-select");
    }

    public TextInput startFromPositionTextField() {
        return createByXPath(TextInput.class, "//label[text()='Start from position']//following-sibling::input");
    }

    public TextInput showTextField() {
        return createByXPath(TextInput.class, "//label[text()='Show']//following-sibling::input");
    }

    public MultiValueSelect tabsSelect() {
        return createByXPath(MultiValueSelect.class, "//div[@id='columns-select']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-most-decorated-players-block-view']/..//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }

    public List<CommonResultModel> getTournamentsResponse() {
        var expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<ArrayList<CommonResultModel>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), responseListType);
    }

    public CommonResultModel getFirstTournamentResponse() {
        var firstTournamentUrl = FootballApiUrl.TOURNAMENTS.url + getTournamentsResponse().get(0).getId();
        ProxyServer.waitForResponse(getWrappedDriver(), firstTournamentUrl, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(firstTournamentUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    public List<CardObjectModel> selectSeasonWithCards() {
        var counter = 0;
        var lastSeasonUrl = "";
        var firstTournamentSeasons = getFirstTournamentResponse().getSeasons();

        var expectedSeasonId = firstTournamentSeasons.get(counter).getId();
        var expectedSeasonUrl = FootballApiUrl.TOURNAMENTS_SEASON_CARD_LIST.url.replace("SEASON_ID", expectedSeasonId);
        lastSeasonUrl = expectedSeasonUrl;

        while (Objects.equals(playersToHighlightSelect().getOptionsValueByIndex(0), StringConstants.NO_OPTIONS_STRING) && counter < firstTournamentSeasons.size()) {
            expectedSeasonId = firstTournamentSeasons.get(counter).getId();
            expectedSeasonUrl = FootballApiUrl.TOURNAMENTS_SEASON_CARD_LIST.url.replace("SEASON_ID", expectedSeasonId);
            lastSeasonUrl = expectedSeasonUrl;

            seasonSelect().selectOptionByIndex(counter);
            ProxyServer.waitForResponse(getWrappedDriver(), expectedSeasonUrl, HttpMethod.GET, 0);

            counter++;
        }

        if (counter == firstTournamentSeasons.size() - 1) {
            System.err.println("Season with a card list is not found!");
        }

        var responseListType = new TypeToken<ArrayList<CardObjectModel>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(lastSeasonUrl, HttpMethod.GET.toString(), responseListType);
    }
}