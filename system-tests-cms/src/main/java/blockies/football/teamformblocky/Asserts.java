package blockies.football.teamformblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import data.constants.AssertMessages;
import data.constants.StorybookApiUrl;
import data.constants.WidgetType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballFormBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.teams.TeamModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.core.utilities.ClipboardManager;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Asserts extends BaseSportBlockyAsserts<FootballFormBlockyFieldsModel> {

    private final TeamFormBlocky blocky;

    public Asserts(TeamFormBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertDefaultEditSectionIsVisible() {
        blocky.teamSelect().toExist().toBeVisible().waitToBe();
        blocky.teamSelect().validateIsVisible();
        blocky.limitTextField().validateIsVisible();
        blocky.titleTextField().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    public Asserts assertExpectedTournamentsOptions(String team) {
        var tournamentSelectOptions = blocky.tournamentSelect().getOptionsValues();
        var seasonList = new ArrayList<String>();

        for (CommonResultModel entity : ((ArrayList<CommonResultModel>)blocky.getResponsesAfterTeamSelect(team).get("teamTournamentsResponse"))) {
            seasonList.add(entity.getName());
        }

        Assertions.assertLinesMatch(seasonList, tournamentSelectOptions, AssertMessages.entityNotExpected("Tournaments Select options"));
        return this;
    }

    public Asserts assertExpectedSeasonOptions(String team) {
        var seasonSelectOptions = blocky.seasonSelect().getOptionsValues();
        var seasonList = new ArrayList<String>();

        for (Season entity : ((CommonResultModel)blocky.getResponsesAfterTeamSelect(team).get("tournamentResponse")).getSeasons()) {
            seasonList.add(entity.getName());
        }

        Assertions.assertLinesMatch(seasonList, seasonSelectOptions, AssertMessages.entityNotExpected("Season Select options"));
        return this;
    }

    public Asserts assertPreviewControlsAreVisible(java.util.Map<String, Object> responses, String limit, String title) {
        var expectedTags = Arrays.asList(
                "Team: " + ((ArrayList<TeamModel>)responses.get("teamResponses")).get(0).getName(),
                "Tournament: " + ((CommonResultModel)responses.get("tournamentResponse")).getName(),
                "Season: " + ((CommonResultModel)responses.get("tournamentResponse")).getSeasons().get(0).getName(),
                "Limit: " + limit,
                "Title: " + title
        );

        blocky.waitPreviewScreenToLoad();
        var previewRowsValuesList = new ArrayList<String>();
        for (Div element : blocky.previewRowsList()) {
            previewRowsValuesList.add(element.getText());
        }

        Assertions.assertLinesMatch(expectedTags, previewRowsValuesList, AssertMessages.entityNotExpected("Preview tags"));
        blocky.assertPreviewControlsVisible();
        return this;
    }

    public Asserts assertStoryBookGetRequest(String team, String limit) {
        var apiResponses = blocky.getResponsesAfterTeamSelect(team);
        var expectedEvent = (CommonResultModel)apiResponses.get("tournamentResponse");
        var teamId = ((ArrayList<TeamModel>)apiResponses.get("teamResponses")).get(0).getId();

        var expectedRequestUrl = StorybookApiUrl.IFRAME.getUrl() + "id=football-widgets-team-form-widget--team-form-widget&args=";
        expectedRequestUrl += String.format("dataCompetition:%s;", expectedEvent.getId());
        expectedRequestUrl += String.format("dataSeason:%s;", expectedEvent.getSeasons().get(0).getId());
        expectedRequestUrl += String.format("dataTeam:%s;", teamId);
        expectedRequestUrl += String.format("dataLimit:%s;", limit);
        expectedRequestUrl += "dataHeaderDisplay:false;dataDisplayTabs:false;sdkOptions.dataConfigCompetitionList:sportalios;sdkOptions.dataConfigLang:en;sdkOptions.dataConfigProject:sportal365;&viewMode=story";

        ProxyServer.waitForResponse(blocky.getWrappedDriver(), expectedRequestUrl, HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(expectedRequestUrl);
        return this;
    }

    public Asserts assertCopiedEmbedCode(FootballFormBlockyFieldsModel blockyData) {
        var lastCopy = ClipboardManager.getLastEntity();
        var expectedEmbedCode = blocky.getExpectedEmbedCode(blockyData);

        Assertions.assertEquals(expectedEmbedCode, lastCopy, AssertMessages.entityNotExpected("Copied Embed Code", expectedEmbedCode, lastCopy));
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballFormBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Assertions.assertNotNull(bodyObject, AssertMessages.entityNotFound("Blocky body object"));
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_TEAM_FORM_V2.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.TEAM_FORM);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.teamSelect().toExist().toBeVisible().waitToBe();
        blocky.teamSelect().validateIsVisible();
        blocky.limitTextField().validateIsVisible();
        blocky.titleTextField().validateIsVisible();
        blocky.tournamentSelect().validateIsVisible();
        blocky.seasonSelect().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballFormBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballFormBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballFormBlockyFieldsModel blockyData) {
        return (List<String>)this;
    }

    @Override
    public Asserts assertPreviewHtml(FootballFormBlockyFieldsModel blockyData) {
        return this;
    }
}