package blockies.football.teamformblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.customelements.SingleValueSearchSelect;
import data.models.blockymodels.football.FootballFormBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.teams.TeamModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TeamFormBlocky extends BaseSportBlocky<FootballFormBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_TEAM_FORM;
    }

    @Override
    public TeamFormBlocky fillForm(FootballFormBlockyFieldsModel blockyData) {
        teamSelect().selectOptionByText(blockyData.getTeam().getName());
        limitTextField().setText(blockyData.getLimit());
        titleTextField().setText(blockyData.getTitle());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballFormBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_FORM),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataTeamAttribute(blockyData.getTeam().getId()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataCompetitionAttribute(blockyData.getStage().getId()),
                new DataSeasonAttribute(blockyData.getSeason().getId().toString()),
                new DataLimitAttribute(blockyData.getLimit()),
                new DataTitleAttribute(blockyData.getTitle())
        ));
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        teamSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSearchSelect teamSelect() {
        return createByAttributeContaining(SingleValueSearchSelect.class, "data-qa", "football-widget-team-select");
    }

    public SingleValueSearchSelect tournamentSelect() {
        return createByAttributeContaining(SingleValueSearchSelect.class, "data-qa", "football-widget-tournament-select");
    }

    public SingleValueSearchSelect seasonSelect() {
        return createByAttributeContaining(SingleValueSearchSelect.class, "data-qa", "football-widget-season-select");
    }

    public TextInput limitTextField() {
        return createByXPath(TextInput.class, "//label[contains(text(),'Limit')]//following-sibling::input");
    }

    public TextInput titleTextField() {
        return createById(TextInput.class, "form-widget-title-input");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-team-form-block-view']//div[contains(@class,'single-row')]");
    }

    // TODO: ivan.petkov 06.03.2024 - Split method to single methods once widget implemented
    public java.util.Map<String, Object> getResponsesAfterTeamSelect(String teamName) {
        var expectedTeamsApiUrl = FootballApiUrl.TEAMS_BY_NAME.url + teamName;
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsApiUrl, HttpMethod.GET, 0);
        var teamListType = new TypeToken<ArrayList<TeamModel>>() {
        }.getType();
        ArrayList<TeamModel> teamResponses = ProxyServer.getResponseByUrl(expectedTeamsApiUrl, HttpMethod.GET.toString(), teamListType);

        var expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var tournamentListType = new TypeToken<ArrayList<CommonResultModel>>() {
        }.getType();
        ArrayList<CommonResultModel> teamTournamentsResponse = ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), tournamentListType);
        var firstTournament = teamTournamentsResponse.get(0);

        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + firstTournament.getId();
        ProxyServer.waitForResponse(getWrappedDriver(), tournamentApiUrl, HttpMethod.GET, 0);
        var tournamentResponse = ProxyServer.getResponseByUrl(tournamentApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);

        java.util.Map<String, Object> dictionary = new HashMap<>();
        dictionary.put("teamResponses", teamResponses);
        dictionary.put("teamTournamentsResponse", teamTournamentsResponse);
        dictionary.put("tournamentResponse", tournamentResponse);

        return dictionary;
    }
}