package blockies.football.standingsblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import data.constants.AssertMessages;
import data.constants.WidgetType;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballStandingsBlockyFieldsModel;
import data.models.footballapi.common.Stage;
import data.models.footballapi.stage.StageModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static data.constants.BlockyField.START_FROM_POSITION;
import static data.constants.PlaceholderField.SELECT;
import static data.constants.PlaceholderField.THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS;

public class Asserts extends BaseSportBlockyAsserts<FootballStandingsBlockyFieldsModel> {

    private final StandingsBlocky blocky;

    protected Asserts(StandingsBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertRequestsTriggeredAfterAddingBlocky() {
        final String expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        var tournamentGetResponses = blocky.getTournamentsGetResponse();
        var tournamentResponse = blocky.getFirstTournamentResponse();
        var seasonResponse = blocky.getSeasonResponse();
        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + tournamentGetResponses.get(0).getId();
        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + tournamentResponse.getSeasons().get(0).getId();

        ProxyServer.assertRequestMade(expectedTournamentsApiUrl);
        ProxyServer.assertRequestMade(tournamentApiUrl);
        ProxyServer.assertRequestMade(seasonApiUrl);

        Assertions.assertFalse(tournamentGetResponses.isEmpty(), AssertMessages.responseNotContains("Tournaments List"));
        Assertions.assertEquals(tournamentResponse.getName(), tournamentGetResponses.get(0).getName(), AssertMessages.responseNotContains("First Tournament Name"));
        Assertions.assertEquals(tournamentResponse.getSeasons().get(0).getName(), seasonResponse.getName(), AssertMessages.responseNotContains("First Season of the Tournament"));
        return this;
    }

    public Asserts assertExpectedTournamentOptions() {
        List<String> tournamentSelectOptions = blocky.tournamentSelect().getOptionsValues();
        List<String> listTournaments = new ArrayList<>();
        blocky.getTournamentsGetResponse().forEach(tournament -> listTournaments.add(tournament.getName()));
        Assertions.assertLinesMatch(listTournaments, tournamentSelectOptions, AssertMessages.entityNotExpected("Tournament Select options"));
        return this;
    }

    public Asserts assertExpectedSeasonOptions() {
        List<String> seasonSelectOptions = blocky.seasonSelect().getOptionsValues();
        List<String> seasonList = new ArrayList<>();
        blocky.getFirstTournamentResponse().getSeasons().forEach(season -> seasonList.add(season.getName()));
        Assertions.assertLinesMatch(seasonList, seasonSelectOptions, AssertMessages.entityNotExpected("Season Select options"));
        return this;
    }

    public Asserts assertExpectedStageOptions() {
        List<String> stagesSelectOptions = blocky.stageSelect().getOptionsValues();
        List<String> stagesList = new ArrayList<>();
        blocky.getSeasonResponse().getStages().forEach(stage -> stagesList.add(stage.getName()));
        Assertions.assertLinesMatch(stagesList, stagesSelectOptions, AssertMessages.entityNotExpected("Stage Select options"));
        return this;
    }

    public Asserts assertExpectedStageGroupOptions(String stageId) {
        var expectedRoundsApiUrl = String.format(FootballApiUrl.TOURNAMENTS_STAGE.url, stageId);
        ProxyServer.waitForResponse(blocky.getWrappedDriver(), expectedRoundsApiUrl, HttpMethod.GET, 0);
        var stageResponse = Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedRoundsApiUrl, HttpMethod.GET.toString(), StageModel.class));

        if (stageResponse.getGroups() == null) {
            Log.error("Stage contains no groups!");
            return this;
        }

        List<String> stageGroupsSelectOptions = blocky.stageGroupSelect().getOptionsValues();
        List<String> stageGroupsList = new ArrayList<>();
        stageResponse.getGroups().forEach(entity -> stageGroupsList.add(entity.getName()));
        Assertions.assertLinesMatch(stageGroupsList, stageGroupsSelectOptions, AssertMessages.entityNotExpected("Stage Group Select options"));
        return this;
    }

    public Asserts assertHighlightTeams(FootballStandingsBlockyFieldsModel blockyData, long... expectedTeamsPosition) {
        assertFootballApiGetRequest(blockyData);
        assertPreviewHtml(blockyData);
        return this;
    }

    public Asserts assertCountOfTeamsDisplayed(FootballStandingsBlockyFieldsModel blockyData) {
        assertFootballApiGetRequest(blockyData);
        assertPreviewHtml(blockyData);
        return this;
    }

    public Asserts assertTeamRanksDisplayed(FootballStandingsBlockyFieldsModel blockyData) {
        assertFootballApiGetRequest(blockyData);
        assertPreviewHtml(blockyData);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.tournamentSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateIsVisible();
        blocky.seasonSelect().validateIsVisible();
        blocky.stageSelect().validateIsVisible();
        blocky.teamSelect().validateIsVisible();
        blocky.limitField().validateIsVisible();
        blocky.startFromPositionField().validateIsVisible();
        assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        String expectedTournament = blocky.getTournamentsGetResponse().get(0).getName();
        String expectedSeason = blocky.getSeasonResponse().getName();

        blocky.tournamentSelect().validateTextIs(expectedTournament);
        blocky.seasonSelect().validateTextIs(expectedSeason);
        blocky.stageSelect().validateTextIs(SELECT.getValue());
        blocky.teamSelect().validateTextIs(SELECT.getValue());
        blocky.limitField().validatePlaceholderIs(THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        Assertions.assertEquals("1", blocky.startFromPositionField().getValue(), AssertMessages.incorrectDefaultStateOfField(START_FROM_POSITION.getValue()));
        assertRefreshTimeDefaultState();
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballStandingsBlockyFieldsModel blockyData) {
        assertBlockyElements();

        blocky.tournamentSelect().validateTextIs(blockyData.getTournament().getName());
        blocky.seasonSelect().validateTextIs(blockyData.getSeason().getName());
        blocky.stageSelect().validateTextIs(Optional.ofNullable(blockyData.getStage()).map(Stage::getName).orElse(SELECT.getValue()));
        blocky.teamSelect().validateTextIs(Optional.ofNullable(blockyData.getTeamsForHighlight())
                .map(teams -> teams.stream().map(e -> e.getTeam().getName()).collect(Collectors.joining(", ")))
                .orElse(SELECT.getValue()));

        if (blockyData.getLimit() != null) {
            blocky.limitField().validateTextIs(blockyData.getLimit());
        } else {
            blocky.limitField().validatePlaceholderIs(THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        }
        blocky.startFromPositionField().validateTextIs(blockyData.getStartFromPosition());
        assertRefreshTimeInEditMode(blockyData);
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballStandingsBlockyFieldsModel blockyData) {
        List<String> expectedTags = getExpectedViewOptions(blockyData);
        blocky.waitPreviewScreenToLoad();
        List<String> previewRowsValues = new ArrayList<>();

        for (Div element : blocky.previewRowsList()) {
            if (!Objects.equals(element.getText(), "")) {
                previewRowsValues.add(element.getText());
            }
        }
        if (blockyData.getRefreshTime() != null) {
            previewRowsValues.add("Refresh time: " + blockyData.getRefreshTime().getValue());
        }

        Assertions.assertEquals(expectedTags.toString().replace(", ]", "]"), previewRowsValues.toString(), AssertMessages.entityNotExpected("Preview tags") + previewRowsValues);
        assertPreviewControlsVisible();
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballStandingsBlockyFieldsModel blockyData) {
        List<String> expectedTags = new ArrayList<>();

        String tournament = "Tournament:%s - %s".formatted(blockyData.getTournament().getName(), blockyData.getSeason().getName());
        if (blockyData.getStage() != null) {
            tournament += "(%s)".formatted(blockyData.getStage().getName());
        }
        expectedTags.add(tournament);

        if (blockyData.getTeamsForHighlight() != null && !blockyData.getTeamsForHighlight().isEmpty()) {
            expectedTags.add("Highlighted teams: " + blockyData.getTeamsForHighlight().stream().map(e -> e.getTeam().getName()).collect(Collectors.joining(", ")));
        } else {
            expectedTags.add("Highlighted teams: No teams selected");
        }

        expectedTags.add("Start from position:" + blockyData.getStartFromPosition());

        if (!blockyData.getLimit().isEmpty()) {
            expectedTags.add("Limit:" + blockyData.getLimit());
        }

        if (blockyData.getRefreshTime() != null) {
            expectedTags.add("Refresh time: " + blockyData.getRefreshTime().getValue());
        }
        return expectedTags;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballStandingsBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_STANDING_V2.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.STANDINGS);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    public Asserts assertExpectedTeamOptions(FootballStandingsBlockyFieldsModel blockyData) {
        List<String> teamSelectOptions = blocky.teamSelect().getOptionsValues();
        List<String> teams = new ArrayList<>();
        blocky.getTeamsResponse(blockyData.getStage().getId()).forEach(team -> teams.add(team.getName()));
        Assertions.assertLinesMatch(teams, teamSelectOptions, AssertMessages.entityNotExpected("Team Select options"));
        return this;
    }

    public Asserts assertFootballApiGetRequest(FootballStandingsBlockyFieldsModel blockyData) {
        String stageId = blockyData.getStage().getId();

        var firstExpectedRequestUrl = FootballApiUrl.TOURNAMENTS_SEASON.url;
        firstExpectedRequestUrl += String.format("stages/%s", stageId);
        firstExpectedRequestUrl += "?expand=standing.rules&language_code=en";

        var secondExpectedRequestUrl = FootballApiUrl.TOURNAMENTS_SEASON.url;
        secondExpectedRequestUrl += String.format("stages/%s", stageId);
        secondExpectedRequestUrl += "?expand=standing.rules,standing.form.events&language_code=en";

        var thirdExpectedRequestUrl = FootballApiUrlV2.KNOCKOUT_SCHEMES.getUrl();
        thirdExpectedRequestUrl += String.format("%s", stageId);
        thirdExpectedRequestUrl += "?language_code=en";

        ProxyServer.waitForResponse(blocky.getWrappedDriver(), firstExpectedRequestUrl, HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(firstExpectedRequestUrl);
        ProxyServer.assertRequestMade(secondExpectedRequestUrl);
        ProxyServer.assertRequestMade(thirdExpectedRequestUrl);
        return this;
    }
}