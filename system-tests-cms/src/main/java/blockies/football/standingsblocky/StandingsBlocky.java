package blockies.football.standingsblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.customelements.MultiValueSearchSelect;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballStandingsBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.stage.Standing;
import data.models.footballapi.teams.TeamModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StandingsBlocky extends BaseSportBlocky<FootballStandingsBlockyFieldsModel> {

    public SingleValueSelect tournamentSelect() {
        return createById(SingleValueSelect.class, "football-widget-tournament-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-season-select");
    }

    public SingleValueSelect stageSelect() {
        return createById(SingleValueSelect.class, "football-widget-stage-select");
    }

    public MultiValueSelect teamSelect() {
        return createById(MultiValueSelect.class, "football-widget-team-select");
    }

    public TextInput limitField() {
        return createById(TextInput.class, "limit-select");
    }

    public TextInput startFromPositionField() {
        return createByCss(TextInput.class, "input[data-qa='offset-select']");
    }

    public MultiValueSearchSelect stageGroupSelect() {
        return createById(MultiValueSearchSelect.class, "football-widget-group-select");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='standings-view-component']//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_STANDINGS;
    }

    @Override
    public StandingsBlocky fillForm(FootballStandingsBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectSeason(blockyData);
        selectStage(blockyData);
        selectTeam(blockyData);
        fillLimit(blockyData);
        fillStartFromPosition(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballStandingsBlockyFieldsModel blockyData) {
        int expectedOffset = Integer.parseInt(blockyData.getStartFromPosition()) - 1;
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.STANDINGS),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getId().toString()),
                new DataSeasonAttribute(blockyData.getSeason().getId().toString()),
                new DataStageAttribute(blockyData.getStage().getId()),
                new DataHeaderDisplayAttribute(BooleanEnum.FALSE),
                new DataLimitAttribute(blockyData.getLimit()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue()),
                new DataHighlightEntitiesAttribute("[%s]".formatted(blockyData.getTeamsForHighlight().stream().map(e -> e.getTeam().getId()).collect(Collectors.joining(",")))),
                new DataOffsetAttribute(String.valueOf(expectedOffset))
        ));
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public void selectTournament(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
    }

    public void selectSeason(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null) {
            seasonSelect().searchSelectByText(blockyData.getSeason().getName());
        }
    }

    public void selectGroup(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getStage().getStageGroups() > 0) {
            stageGroupSelect().selectOptionByIndex(0);
        }
    }

    public void selectStage(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getStage() != null) {
            stageSelect().searchSelectByText(blockyData.getStage().getName());
        }
    }

    public void selectTeam(FootballStandingsBlockyFieldsModel blockyData) {
        List<Standing> teamsForHighlight = blockyData.getTeamsForHighlight();

        if (teamsForHighlight != null) {
            if (teamsForHighlight.size() == 1) {
                teamSelect().searchSelectByText(teamsForHighlight.get(0).getTeam().getName());
            } else {
                blockyData.getTeamsForHighlight().forEach(t -> teamSelect().searchSelectByText(t.getTeam().getName()));
            }
        }
    }

    public void fillLimit(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getLimit() != null) {
            limitField().setText(blockyData.getLimit());
        }
    }

    private void fillStartFromPosition(FootballStandingsBlockyFieldsModel blockyData) {
        if (blockyData.getStartFromPosition() != null) {
            startFromPositionField().setText(blockyData.getStartFromPosition());
        }
    }

    public List<CommonResultModel> getTournamentsGetResponse() {
        var expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<ArrayList<CommonResultModel>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), responseListType);
    }

    public CommonResultModel getFirstTournamentResponse() {
        var firstTournament = getTournamentsGetResponse().get(0);

        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + firstTournament.getId();
        ProxyServer.waitForResponse(getWrappedDriver(), tournamentApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(tournamentApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }


    public CommonResultModel getSeasonResponse() {
        var firstTournamentResponse = getFirstTournamentResponse();

        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + firstTournamentResponse.getSeasons().get(0).getId();
        ProxyServer.waitForResponse(getWrappedDriver(), seasonApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(seasonApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    public List<TeamModel> getTeamsResponse(String stageId) {
        var expectedTeamsApiUrl = FootballApiUrl.TOURNAMENTS_STAGE_TEAMS.url.formatted(stageId);
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsApiUrl, HttpMethod.GET, 0);
        var teamsListType = new TypeToken<List<TeamModel>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedTeamsApiUrl, HttpMethod.GET.toString(), teamsListType);
    }

    public List<Standing> getTeamsForHighlight(FootballStandingsBlockyFieldsModel blockyData, long... teamStandingPosition) {
        List<Standing> teams = new ArrayList<>();

        for (long position : teamStandingPosition) {
            Standing standing = blockyData.getAllTeams().stream()
                    .filter(t -> t.getRank().equals(position))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("Team with position %s not found".formatted(position)));

            teams.add(standing);
        }

        return teams;
    }
}