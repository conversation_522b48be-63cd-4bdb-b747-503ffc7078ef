package blockies.football.teamh2hblocky;

import blockies.baseblockies.baseblocky.commonblockyelements.StatisticsParameterSelectable;
import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.football.FootballStatisticsEnum;
import data.customelements.MultiValueSearchSelect;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballTeamH2HBlockyFieldsModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.teams.TeamModel;
import data.utils.StringUtils;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextArea;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TeamH2hBlocky extends BaseSportBlocky<FootballTeamH2HBlockyFieldsModel> implements StatisticsParameterSelectable {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_TEAM_H2H;
    }

    @Override
    public TeamH2hBlocky fillForm(FootballTeamH2HBlockyFieldsModel blockyData) {
        selectTeamOne(blockyData);
        selectTeamOneTournamentSeason(blockyData);
        selectTeamTwo(blockyData);
        selectTeamTwoTournamentSeason(blockyData);
        selectStatistics(blockyData);
        selectHeaderDisplayCheckbox(blockyData.getDataHeaderDisplay());
        return this;
    }

    public void selectStatistics(FootballTeamH2HBlockyFieldsModel blockyData) {
        statisticsParametersSelect().clearSelection();
        if (blockyData.getStatistics() != null && !blockyData.getStatistics().isEmpty()) {
            List<String> teamH2HStatistics = FootballStatisticsEnum.getTeamH2HBlockyStatistics();
            statisticsParametersSelect().searchSelectOptionsByText(teamH2HStatistics);
        }
    }

    public void selectTeamTwoTournamentSeason(FootballTeamH2HBlockyFieldsModel blockyData) {
        if (blockyData.getTeamTwoTournamentSeasons() != null) {
            secondSeasonSelect().searchSelectByText("%s-%s".formatted(blockyData.getTeamTwoTournamentSeasons().getTournament().getName(), blockyData.getTeamOneTournamentSeasons().getName()));
        } else {
            secondSeasonSelect().clearSelection();
        }
    }

    public void selectTeamTwo(FootballTeamH2HBlockyFieldsModel blockyData) {
        if (blockyData.getTeamTwo() != null) {
            secondTeamSelect().searchSelectByText(blockyData.getTeamTwo().getName());
            browserService.waitForAjax();
        } else {
            secondTeamSelect().clearSelection();
        }
    }

    public void selectTeamOneTournamentSeason(FootballTeamH2HBlockyFieldsModel blockyData) {
        if (blockyData.getTeamOneTournamentSeasons() != null) {
            firstSeasonSelect().searchSelectByText("%s-%s".formatted(blockyData.getTeamOneTournamentSeasons().getTournament().getName(), blockyData.getTeamOneTournamentSeasons().getName()));
        } else {
            firstSeasonSelect().clearSelection();
        }
    }

    public void selectTeamOne(FootballTeamH2HBlockyFieldsModel blockyData) {
        if (blockyData.getTeamOne() != null) {
            firstTeamSelect().searchSelectByText(blockyData.getTeamOne().getName());
            browserService.waitForAjax();
        } else {
            firstTeamSelect().clearSelection();
        }
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballTeamH2HBlockyFieldsModel blockyData) {
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_H2H),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataHeaderDisplayAttribute(blockyData.getDataHeaderDisplay() != null ? blockyData.getDataHeaderDisplay() : true),
                new DataSportEntityOneAttribute("{\"id\":\"%s\",\"seasonId\":%s}".formatted(blockyData.getTeamOne().getId(), blockyData.getTeamOneTournamentSeasons().getId())),
                new DataSportEntityTwoAttribute("{\"id\":\"%s\",\"seasonId\":%s}".formatted(blockyData.getTeamTwo().getId(), blockyData.getTeamTwoTournamentSeasons().getId()))
        ));

        if (blockyData.getStatistics() != null) {
            attributes.add(new DataElementsAttribute(blockyData.getStatistics().stream()
                    .map(FootballStatisticsEnum::getValue)
                    .toList()));
        }

        return attributes;
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        firstTeamSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSearchSelect firstTeamSelect() {
        return createById(SingleValueSearchSelect.class, "football-widget-team-select-1");
    }

    public SingleValueSelect firstSeasonSelect() {
        return createByAttributeContaining(SingleValueSelect.class, "data-qa", "football-widget-player-season-select-1");
    }

    public SingleValueSearchSelect secondTeamSelect() {
        return createById(SingleValueSearchSelect.class, "football-widget-team-select-2");
    }

    public SingleValueSelect secondSeasonSelect() {
        return createByAttributeContaining(SingleValueSelect.class, "data-qa", "football-widget-player-season-select-2");
    }

    @Override
    public MultiValueSelect statisticsParametersSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-team-h2h-elements-select");
    }

    @Override
    public List<String> getPreSelectedStatistics() {
        return FootballStatisticsEnum.getTeamH2HBlockyStatistics();
    }

    public List<TextArea> validationMessage() {
        return createAllByXPath(TextArea.class, ".//h6[@class='text-danger']");
    }

    protected Div errorMessageForFirstTeamSelectField() {
        return firstTeamSelect().createByXPath(Div.class, ".//following-sibling::h6");
    }

    protected Div errorMessageForSecondTeamSelectField() {
        return secondTeamSelect().createByXPath(Div.class, ".//following-sibling::h6");
    }

    protected MultiValueSearchSelect entitySearchSelect() {
        return createById(MultiValueSearchSelect.class, "football-team-h2h-block-edit");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-team-h2h-view']/div");
    }

    public List<TeamModel> getTeamsGetResponse() {
        String expectedTeamsApiUrl = FootballApiUrl.TEAMS_BY_NAME.getUrl();
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<List<TeamModel>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedTeamsApiUrl, HttpMethod.GET.toString(), responseListType);
    }

    public List<Season> getSeasonsResponse() {
        String id = getTeamsGetResponse().get(0).getId();

        String seasonApiUrl = FootballApiUrl.TEAMS_SEASONS.getUrl().formatted(id);
        ProxyServer.waitForResponse(getWrappedDriver(), seasonApiUrl, HttpMethod.GET, 0);
        var seasonListType = new TypeToken<List<Season>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(seasonApiUrl, HttpMethod.GET.toString(), seasonListType);
    }

    public TeamModel getFirstTeamResponse(String firstTeamName) {
        return getTeamResponse(firstTeamName);
    }

    public TeamModel getSecondTeamResponse(String secondTeamName) {
        return getTeamResponse(secondTeamName);
    }

    public List<Season> getFirstTeamSeasonsResponse(String firstTeamName) {
        return getTeamSeasonsResponse(firstTeamName);
    }

    public List<Season> getSecondTeamSeasonsResponse(String secondTeamName) {
        return getTeamSeasonsResponse(secondTeamName);
    }

    private TeamModel getTeamResponse(String teamName) {
        String teamApiUrl = FootballApiUrl.TEAMS_BY_NAME.getUrl() + StringUtils.replaceSpacesWithSymbol(teamName, "%20");
        ProxyServer.waitForResponse(getWrappedDriver(), teamApiUrl, HttpMethod.GET, 0);
        Type responseListType = new TypeToken<List<TeamModel>>() {
        }.getType();
        List<TeamModel> teamsGetResponse = ProxyServer.getResponseByUrl(teamApiUrl, HttpMethod.GET.toString(), responseListType);
        return Objects.requireNonNull(teamsGetResponse).get(0);
    }

    private List<Season> getTeamSeasonsResponse(String teamName) {
        TeamModel teamResponse = getTeamResponse(teamName);
        String seasonApiUrl = FootballApiUrl.TEAMS_SEASONS.getUrl().formatted(teamResponse.getId());
        ProxyServer.waitForResponse(getWrappedDriver(), seasonApiUrl, HttpMethod.GET, 0);
        var seasonListType = new TypeToken<List<Season>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(seasonApiUrl, HttpMethod.GET.toString(), seasonListType);
    }
}