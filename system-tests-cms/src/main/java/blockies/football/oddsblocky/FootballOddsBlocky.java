package blockies.football.oddsblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import data.constants.BlockyTypeEnum;
import data.customelements.SingleValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballOddsBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.teams.MatchesV2TeamModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import lombok.SneakyThrows;
import solutions.bellatrix.web.components.Div;

import java.util.ArrayList;
import java.util.List;

public class FootballOddsBlocky extends BaseSportBlockyWithOdds<FootballOddsBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_ODDS;
    }

    @Override
    public FootballOddsBlocky fillForm(FootballOddsBlockyFieldsModel blockyData) {
        selectTeam(blockyData.getTeam());
        selectMarketValueType(blockyData);
        selectMatch(blockyData);
        selectBookmaker(Bookmaker.builder().bookmakerName(blockyData.getBookmaker().getName()).build());
        selectDefaultMarket(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballOddsBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.ODDS),
                new DataMatchIdAttribute(blockyData.getEvent().getId()),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataOddsBettingIdAttribute(blockyData.getBookmaker().getId()),
                new DataOddsMarketAttribute(blockyData.getMarketType().getName()),
                new DataOddsMarketValueTypeAttribute(blockyData.getMarketValueType()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay())
        ));
    }

    @Override
    @SneakyThrows
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        teamSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    @SneakyThrows
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public void selectMatch(FootballOddsBlockyFieldsModel blockyData) {
        if (blockyData.getEvent() != null) {
            matchSelect().searchSelectByText("%s - %s".formatted(blockyData.getEvent().getHomeTeam().getName(), blockyData.getEvent().getAwayTeam().getName()));
        }
    }

    public void selectMarketValueType(FootballOddsBlockyFieldsModel blockyData) {
        if (blockyData.getMarketValueType() != null) {
            marketTypeSelect().searchSelectByText(blockyData.getMarketValueType().name());
        }
    }

    public void selectDefaultMarket(FootballOddsBlockyFieldsModel blockyData) {
        if (blockyData.getMarketType() != null) {
            defaultMarketSelect().searchSelectByText(blockyData.getMarketType().getName());
        }
    }

    public SingleValueSearchSelect teamSelect() {
        return createByAttributeContaining(SingleValueSearchSelect.class, "data-qa", "football-widget-team-select");
    }

    public void selectTeam(MatchesV2TeamModel team) {
        teamSelect().searchSelectById(team.getName(), team.getId());
    }

    public SingleValueSelect matchSelect() {
        return createByXPath(SingleValueSelect.class, "//label[contains(text(),'Match')]//following-sibling::div");
    }

    public SingleValueSelect defaultMarketSelect() {
        return createByXPath(SingleValueSelect.class, "//label[contains(text(),'Choose default market')]//following-sibling::div");
    }

    public SingleValueSelect marketTypeSelect() {
        return createByXPath(SingleValueSelect.class, "//label[contains(text(),'Choose market value type')]//following-sibling::div");
    }

    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class,
                "//div[@data-qa='refresh-time-view-row']//parent::div//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }
}