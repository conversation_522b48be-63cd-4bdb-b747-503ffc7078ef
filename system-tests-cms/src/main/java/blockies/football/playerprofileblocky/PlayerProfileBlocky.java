package blockies.football.playerprofileblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.Language;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.PlayerDataElementsStatisticsEnum;
import data.customelements.*;
import data.models.blockymodels.football.FootballPlayerProfileBlockyFieldsModel;
import data.models.footballapi.player.PlayerModel;
import data.models.footballapi.player.PlayerStatisticsModel;
import data.models.footballapi.v2.PlayerV2Model;
import data.utils.StringUtils;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import data.widgets.options.enums.FootballPlayerEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Heading;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PlayerProfileBlocky extends BaseSportBlockyWithOdds<FootballPlayerProfileBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_PLAYER;
    }

    @Override
    public PlayerProfileBlocky fillForm(FootballPlayerProfileBlockyFieldsModel blockyData) {
        if (blockyData.getPlayer() != null && !blockyData.getPlayer().getName().isEmpty()) {
            selectPlayer(blockyData);
            selectSeason(blockyData);
            selectDisplayMatch(blockyData.isDisplayMatch());
            if (blockyData.isDisplayMatch()) {
                selectMatch(blockyData);
            }
            selectSocialIcons(blockyData.isDisplaySocialIcons());
            selectDisplayStatistics(blockyData.isDisplayStatistics());
            selectDisplayOdds(blockyData.isDisplayOdds());
            selectBookmaker(blockyData.getBookmaker());
            if (blockyData.isDisplayStatistics()) {
                selectStatistics(blockyData);
            }
        } else {
            selectPlayer(blockyData);
        }
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectDisplayStatistics(boolean displayStatistics) {
        displayStatisticsCheckbox().check(displayStatistics);
    }

    public void selectDisplayMatch(boolean displayMatch) {
        displayMatchCheckbox().check(displayMatch);
    }

    public void selectStatistics(FootballPlayerProfileBlockyFieldsModel blockyData) {
        if (blockyData.getStatistics() != null) {
            statisticsParametersSelect().clearSelection();
            statisticsParametersSelect().searchSelectOptionsByText(blockyData.getStatistics()
                    .stream()
                    .map(PlayerDataElementsStatisticsEnum::getValue).toList());
        } else {
            statisticsParametersSelect().clearSelection();
        }
    }

    public void selectMatch(FootballPlayerProfileBlockyFieldsModel blockyData) {
        if (blockyData.getMatch() != null) {
            matchSelect().searchSelectByText(blockyData.getMatch().getMatchTitle());
        } else {
            matchSelect().clearSelection();
        }
    }

    public void selectSeason(FootballPlayerProfileBlockyFieldsModel blockyData) {
        browserService.waitForAjax();
        if (blockyData.getSeason() != null) {
            tournamentSeason().searchSelectByText("%s-%s".formatted(blockyData.getStage().getName(),
                    blockyData.getSeason().getName()));
        } else {
            tournamentSeason().clearSelection();
        }
        browserService.tryWaitForResponse(FootballApiUrlV2.MATCHES.getUrl());
    }

    public void selectPlayer(FootballPlayerProfileBlockyFieldsModel blockyData) {
        if (blockyData.getPlayer() != null && !blockyData.getPlayer().getName().isEmpty()) {
            playerSelect().searchSelectByText(blockyData.getPlayer().getName());
        } else {
            playerSelect().clearSelection();
        }
        browserService.tryWaitForResponse(FootballApiUrl.STATISTICS_BY_PLAYERS.getUrl());
    }

    public void selectSocialIcons(boolean displaySocialIcons) {
        displaySocialIconsCheckbox().check(displaySocialIcons);
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballPlayerProfileBlockyFieldsModel blockyData) {
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.PLAYER),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.PLAYER),
                new DataPlayerAttribute(blockyData.getPlayer().getId().toString())
        ));
        if (blockyData.getSeason() != null) {
            attributes.add(new DataSeasonAttribute(blockyData.getSeason().getId()));
        }
        if (blockyData.getMatch() != null && blockyData.isDisplayMatch()) {
            attributes.add(new DataMatchIdAttribute(blockyData.getMatch().getId().toString()));
        }
        attributes.add(new DataOddsDisplayAttribute(blockyData.isDisplayOdds()));
        if (blockyData.getBookmaker() != null && blockyData.isDisplayOdds()) {
            attributes.add(new DataOddsBettingIdAttribute(blockyData.getBookmaker().getBookmakerId()));
        }
        if (blockyData.getMatch() != null && blockyData.getSeason() != null && blockyData.isDisplayStatistics()) {
            attributes.add(new DataElementsAttribute(getExpectedElementsAttribute(blockyData)));
        }
        attributes.add(new DataMatchDisplayAttribute(blockyData.isDisplayMatch()));
        attributes.addAll(List.of(
                new DataStatisticsDisplayAttribute(blockyData.isDisplayStatistics()),
                new DataDisplaySocialIconsAttribute(blockyData.isDisplaySocialIcons()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime())
        ));

        return attributes;
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        playerSelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public CheckBox displaySocialIconsCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-social-icons-checkbox");
    }

    public Heading errorMessageForFootballPlayerField() {
        return createByXPath(Heading.class, ".//h6[@class='text-danger']");
    }

    public SingleValueSearchSelect playerSelect() {
        return createById(SingleValueSearchSelect.class, "football-widget-player-select");
    }

    public SingleValueSelect tournamentSeason() {
        return createById(SingleValueSelect.class, "football-widget-player-season-select");
    }

    public SingleValueSelect matchSelect() {
        return createById(SingleValueSelect.class, "football-widget-match-by-stage-or-season-select");
    }

    public CheckBox displayStatisticsCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-statistics-checkbox");
    }

    public CheckBox displayMatchCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "display-match-checkbox");
    }

    public MultiValueSelect tabsSelect() {
        return createByXPath(MultiValueSelect.class, "//div[@id='columns-select']");
    }

    public MultiValueSelect statisticsParametersSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-player-profile-elements-select");
    }

    public MultiValueSearchSelect entitySearchSelect() {
        return createById(MultiValueSearchSelect.class, "football-widget-team-select");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-player-block-view']//strong/..");
    }

    public PlayerV2Model getPlayerV2Response(String playerId) {
        String requestPartialUrl = FootballApiUrlV2.PLAYERS.getUrl() + "/%s?language_code=%s".formatted(playerId,
                Language.ENGLISH.getCode());
        ProxyServer.waitForResponse(getWrappedDriver(), requestPartialUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(
                requestPartialUrl, HttpMethod.GET.toString(), PlayerV2Model.class);
    }

    public List<PlayerStatisticsModel> getPlayerStatisticsResponse(FootballPlayerEnum footballPlayer) {
        String requestPartialUrl = FootballApiUrl.STATISTICS_BY_PLAYER_ID.getUrl().formatted(footballPlayer.getId());
        ProxyServer.waitForResponse(getWrappedDriver(), requestPartialUrl, HttpMethod.GET, 0);
        Type responseTypeToken = new TypeToken<List<PlayerStatisticsModel>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(requestPartialUrl, HttpMethod.GET.toString(), responseTypeToken);
    }

    public PlayerModel getPlayerResponse(String playerName) {
        String expectedPlayerSearchApiUrl = FootballApiUrl.PLAYERS_BY_NAME.url.formatted(StringUtils.replaceSpacesWithSymbol(playerName, "%20"));
        ProxyServer.waitForResponse(getWrappedDriver(), expectedPlayerSearchApiUrl, HttpMethod.GET, 0);
        Type playerListType = new TypeToken<ArrayList<PlayerModel>>() {
        }.getType();
        ArrayList<PlayerModel> playerResponse = ProxyServer.getResponseByUrl(expectedPlayerSearchApiUrl, HttpMethod.GET.toString(), playerListType);
        return Objects.requireNonNull(playerResponse).get(0);
    }

    public List<PlayerStatisticsModel> getStatisticsResponse(String playerName) {
        String expectedStatisticsApiUrl = FootballApiUrl.STATISTICS_BY_PLAYER_ID.url.formatted(getPlayerResponse(playerName).getId().toString());
        ProxyServer.waitForResponse(getWrappedDriver(), expectedStatisticsApiUrl, HttpMethod.GET, 0);
        var statisticsListType = new TypeToken<ArrayList<PlayerStatisticsModel>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedStatisticsApiUrl, HttpMethod.GET.toString(), statisticsListType);
    }

    public String getExpectedElementsAttribute(FootballPlayerProfileBlockyFieldsModel blockyData) {
        return "{\"stats\":[" +
                (blockyData.getStatistics() != null && !blockyData.getStatistics().isEmpty() ?
                        "\"%s\"".formatted(String.join("\",\"", blockyData.getStatistics().stream()
                                .map(PlayerDataElementsStatisticsEnum::getDisplayValue)
                                .toList())) : "") +
                "]}";
    }
}