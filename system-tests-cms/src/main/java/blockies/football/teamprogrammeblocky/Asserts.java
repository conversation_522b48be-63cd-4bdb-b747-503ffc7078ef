package blockies.football.teamprogrammeblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOddsAsserts;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.StringConstants;
import data.constants.WidgetType;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballTeamProgrammeBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.common.Stage;
import data.models.footballapi.rounds.Round;
import data.models.footballapi.teams.TeamModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

public class Asserts extends BaseSportBlockyWithOddsAsserts<FootballTeamProgrammeBlockyFieldsModel> {

    private final TeamProgrammeBlocky blocky;

    public Asserts(TeamProgrammeBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertExpectedTournamentOptions() {
        var tournamentSelectOptions = blocky.tournamentSelect().getOptionsValues();
        var listTournaments = new ArrayList<String>();

        for (CommonResultModel entity : blocky.getTournamentGetResponses()) {
            listTournaments.add(entity.getName());
        }

        Assertions.assertLinesMatch(listTournaments, tournamentSelectOptions, AssertMessages.entityNotExpected("Tournament Select options"));
        return this;
    }

    public Asserts assertExpectedSeasonOptions() {
        var seasonSelectOptions = blocky.seasonSelect().getOptionsValues();
        var seasonList = new ArrayList<String>();

        for (Season entity : blocky.getTournamentResponse().getSeasons()) {
            seasonList.add(entity.getName());
        }

        Assertions.assertLinesMatch(seasonList, seasonSelectOptions, AssertMessages.entityNotExpected("Season Select options"));
        return this;
    }

    public Asserts assertExpectedStageOptions() {
        var requestStages = blocky.getSeasonResponse().getStages();
        var stagesSelectOptions = blocky.stageSelect().getOptionsValues();
        var stagesList = new ArrayList<String>();

        for (Stage entity : requestStages) {
            stagesList.add(entity.getName());
        }

        Assertions.assertLinesMatch(stagesList, stagesSelectOptions, AssertMessages.entityNotExpected("Stage Select options"));
        return this;
    }

    public Asserts assertExpectedRoundOptions() {
        var roundsSelectOptions = blocky.roundSelect().getOptionsValues();
        var roundsList = new ArrayList<String>();

        for (Round entity : blocky.getRoundsResponse().getRounds()) {
            roundsList.add(entity.getName());
        }

        Assertions.assertLinesMatch(roundsList, roundsSelectOptions, AssertMessages.entityNotExpected("Rounds Select options"));
        return this;
    }

    public Asserts assertExpectedTeamsOptions(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        var teamsSelectOptions = blocky.teamSelect().getOptionsValues();
        var teamsList = new ArrayList<String>();

        for (TeamModel entity : blocky.getTeamsResponse(blockyData)) {
            teamsList.add(entity.getName());
        }

        Assertions.assertLinesMatch(teamsList, teamsSelectOptions, AssertMessages.entityNotExpected("Teams Select options"));
        return this;
    }

    public Asserts assertPreviewControlsAreVisible(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        List<String> expectedTags = getExpectedViewOptions(blockyData);

        blocky.waitPreviewScreenToLoad();
        var previewRowsValuesList = new ArrayList<String>();
        for (Div element : blocky.previewRowsList()) {
            previewRowsValuesList.add(element.getText());
        }

        Assertions.assertLinesMatch(expectedTags, previewRowsValuesList, AssertMessages.entityNotExpected("Preview tags"));
        blocky.assertPreviewControlsVisible();
        return this;
    }

    public Asserts assertFootballApiGetRequest() {
        String expectedRequestUrl = FootballApiUrlV2.MATCHES.getUrl();

        ProxyServer.waitForRequest(blocky.getWrappedDriver(), expectedRequestUrl, HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(expectedRequestUrl);
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballTeamProgrammeBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertConfigObject(configObject, DataWidgetIdEnum.TEAM_PROGRAMME);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_TEAM__PROGRAMME.getValue()));
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.tournamentSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateIsVisible();
        blocky.seasonSelect().validateIsVisible();
        blocky.stageSelect().validateIsVisible();
        blocky.roundSelect().validateIsVisible();
        blocky.teamSelect().validateIsVisible();
        blocky.sortDirectionFixturesSelect().validateIsVisible();
        blocky.sortDirectionResultsSelect().validateIsVisible();
        blocky.limitField().validateIsVisible();
        blocky.dateFromDatePicker().validateIsVisible();
        blocky.dateToDatePicker().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        blocky.stageSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.roundSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.teamSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.sortDirectionFixturesSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        blocky.sortDirectionResultsSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        blocky.limitField().validatePlaceholderIs(PlaceholderField.THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        blocky.dateFromDatePicker().validatePlaceholderIs(PlaceholderField.YYYY_MM_DD.getValue());
        blocky.dateToDatePicker().validatePlaceholderIs(PlaceholderField.YYYY_MM_DD.getValue());
        blocky.displayOddsCheckbox().validateIsChecked();
        blocky.bookmakerSelect().validateTextIs(StringConstants.EMPTY_STRING);
        blocky.tournamentSelect().validateTextIs(blocky.getTournamentResponse().getName());
        blocky.seasonSelect().validateTextIs(blocky.getSeasonResponse().getName());
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        List<String> expectedTags = new ArrayList<>();
        expectedTags.add("Tournament: " + blockyData.getTournament().getName());
        expectedTags.add("Season: " + blockyData.getSeason().getName());
        expectedTags.add("Stage: " + blockyData.getStage().getName());
        if (!blockyData.getRounds().isEmpty()) {
            expectedTags.add("Rounds:" + blockyData.getRounds().get(0).getName());
        }
        expectedTags.add("Team: " + blockyData.getTeam().getName());
        expectedTags.add("Match type: " + blockyData.getMatchType().getValue());
        expectedTags.add("Date from: " + blockyData.getDateFrom().getDate());
        expectedTags.add("Date to: " + blockyData.getDateTo().getDate());
        expectedTags.add("Sort direction of fixtures: " + blockyData.getSortDirection().getValue());
        expectedTags.add("Sort direction of results: " + blockyData.getSortDirection().getValue());
        return expectedTags;
    }
}