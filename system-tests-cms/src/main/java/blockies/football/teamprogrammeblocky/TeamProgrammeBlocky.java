package blockies.football.teamprogrammeblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballTeamProgrammeBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.rounds.RoundsModel;
import data.models.footballapi.teams.TeamModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataOddsBettingIdEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import lombok.SneakyThrows;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class TeamProgrammeBlocky extends BaseSportBlockyWithOdds<FootballTeamProgrammeBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_TEAM_PROGRAMME;
    }

    @Override
    public TeamProgrammeBlocky fillForm(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectSeason(blockyData);
        selectStage(blockyData);
        selectRound(blockyData);
        selectTeam(blockyData.getTeam().getName());
        selectSortDirection(blockyData);
        selectDisplayOdds(blockyData.isDisplayOdds());
        fillLimit(blockyData);
        selectDateFrom(blockyData);
        selectDateTo(blockyData);
        selectMatchType(blockyData);
        selectSortDirection(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        selectBookmaker(blockyData.getBookmaker());
        selectHeaderDisplayCheckbox(blockyData.isDataHeaderDisplay());
        return this;
    }

    public void selectTournament(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
    }

    @Override
    public List<Attribute> getBlockyAttributes(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_PROGRAMME),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getId()),
                new DataSeasonAttribute(blockyData.getSeason().getId().toString()),
                new DataTeamAttribute(blockyData.getTeam().getId()),
                new DataStageAttribute(blockyData.getStage().getId()),
                new DataRoundsAttribute("[%s]".formatted(blockyData.getRounds().get(0).getId())),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE),
                new DataSortDirectionResultsAttribute(blockyData.getSortDirection().getStorybookValue()),
                new DataSortDirectionFixturesAttribute(blockyData.getSortDirection().getStorybookValue()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay())));
        if (blockyData.getDateFrom() != null) {
            attributes.add(new DataDateFromAttribute(blockyData.getDateFrom()));
        }

        if (blockyData.getDateTo() != null) {
            attributes.add(new DataDateToAttribute(blockyData.getDateTo()));
        }
        attributes.add(new DataMatchTypeAttribute(blockyData.getMatchType().getValue().toLowerCase()));
        attributes.add(new DataRefreshTimeAttribute(blockyData.getRefreshTime().getAttributeValue()));
        return attributes;
    }

    @SneakyThrows
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
    }

    @SneakyThrows
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public void selectTeam(String name) {
        if (name != null) {
            teamSelect().searchSelectByText(name);
        }
    }

    public void selectSeason(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getSeason() != null) {
            seasonSelect().searchSelectByText(blockyData.getSeason().getName());
        }
    }

    public void selectMatchType(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getMatchType() != null) {
            matchTypeSelect().searchSelectByText(blockyData.getMatchType().getValue());
        }
    }

    public void selectSortDirection(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getSortDirection() != null) {
            sortDirectionFixturesSelect().searchSelectByText(blockyData.getSortDirection().getValue());
            sortDirectionResultsSelect().searchSelectByText(blockyData.getSortDirection().getValue());
        }
    }

    public void selectRound(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getRounds() != null) {
            roundSelect().clearSelection();
            roundSelect().searchSelectByText(blockyData.getRounds().get(0).getName());
        }
    }

    public void selectStage(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getStage() != null) {
            stageSelect().searchSelectByText(blockyData.getStage().getName());
        }
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSelect tournamentSelect() {
        return createById(SingleValueSelect.class, "football-widget-tournament-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-season-select");
    }

    public SingleValueSelect stageSelect() {
        return createById(SingleValueSelect.class, "football-widget-stage-select");
    }

    public SingleValueSelect roundSelect() {
        return createById(SingleValueSelect.class, "football-widget-round-select");
    }

    public SingleValueSelect teamSelect() {
        return createById(SingleValueSelect.class, "football-widget-team-select");
    }

    public SingleValueSelect matchTypeSelect() {
        return createByXPath(SingleValueSelect.class, "//label[@for='football-widget-match-type-select']//following-sibling::div");
    }

    public CheckBox displayOddsCheckbox() {
        return createByAttributeContaining(CheckBox.class, "data-qa", "football-widget-display-odds-select");
    }

    public SingleValueSelect bookmakerSelect() {
        return createById(SingleValueSelect.class, "football-widget-bookmaker-select");
    }

    public TextInput limitField() {
        return createById(TextInput.class, "limit-select");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, getFindStrategy().getValue() + "//following-sibling::div//div[contains(@class,'single-row')]");
    }

    public List<CommonResultModel> getTournamentGetResponses() {
        var expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<List<CommonResultModel>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), responseListType);
    }

    public CommonResultModel getTournamentResponse() {
        var firstTournament = getTournamentGetResponses().get(0);
        var tournamentApiUrl = FootballApiUrl.TOURNAMENTS.url + firstTournament.getId();
        ProxyServer.waitForResponse(getWrappedDriver(), tournamentApiUrl, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(tournamentApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    public CommonResultModel getSeasonResponse() {
        var seasonApiUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + getTournamentResponse().getSeasons().get(0).getId();
        ProxyServer.waitForResponse(getWrappedDriver(), seasonApiUrl, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(seasonApiUrl, HttpMethod.GET.toString(), CommonResultModel.class);
    }

    public RoundsModel getRoundsResponse() {
        var firstStageId = getSeasonResponse().getStages().get(0).getId();
        var expectedRoundsApiUrl = FootballApiUrlV2.STAGE_ROUNDS.getUrl().formatted(firstStageId);
        ProxyServer.waitForResponse(getWrappedDriver(), expectedRoundsApiUrl, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(expectedRoundsApiUrl, HttpMethod.GET.toString(), RoundsModel.class);
    }

    public List<TeamModel> getTeamsResponse(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        var expectedTeamsApiUrl = FootballApiUrl.TOURNAMENTS_STAGE_TEAMS.url.formatted(blockyData.getStage().getId());
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTeamsApiUrl, HttpMethod.GET, 0);
        var teamsListType = new TypeToken<List<TeamModel>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(expectedTeamsApiUrl, HttpMethod.GET.toString(), teamsListType);
    }

    public void fillLimit(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getLimit() != null) {
            limitField().setText(blockyData.getLimit());
        }
    }

    public void selectDateFrom(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getDateFrom() != null) {
            dateFromDatePicker().setDate(LocalDate.parse(blockyData.getDateFrom().getDate()));
        }
    }

    public void selectDateTo(FootballTeamProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getDateTo() != null) {
            dateToDatePicker().setDate(LocalDate.parse(blockyData.getDateTo().getDate()));
        }
    }
}