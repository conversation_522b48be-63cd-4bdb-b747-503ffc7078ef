package blockies.football.topscorersblocky;

import blockies.baseblockies.baseblocky.SelectStatisticsParametersSelectAssertions;
import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import com.google.common.reflect.TypeToken;
import data.constants.AssertMessages;
import data.constants.WidgetType;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.football.FootballTopScorersBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.common.Season;
import data.models.footballapi.player.TeamPlayerModel;
import data.models.footballapi.topscorer.TopScorerModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.*;

public class Asserts extends BaseSportBlockyAsserts<FootballTopScorersBlockyFieldsModel> implements SelectStatisticsParametersSelectAssertions {

    private static final String TOP_SCORERS_OPTIONS = "TopScorer options";
    private static final String PLAYERS_ENDPOINT = "%s/%s/players";
    private final TopScorersBlocky blocky;

    public Asserts(TopScorersBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertExpectedTournamentOptions() {
        blocky.getBrowserService().waitForAjax();
        List<String> tournamentSelectOptions = blocky.tournamentSelect().getOptionsValues();
        var listTournaments = new ArrayList<String>();

        for (CommonResultModel entity : blocky.getTournamentsResponse()) {
            listTournaments.add(entity.getName());
        }

        Assertions.assertLinesMatch(listTournaments, tournamentSelectOptions, AssertMessages.entityNotExpected("Tournament Select options"));
        return this;
    }

    public Asserts assertExpectedSeasonOptions() {
        var seasonSelectOptions = blocky.seasonSelect().getOptionsValues();
        var seasonList = new ArrayList<String>();

        for (Season entity : blocky.getFirstTournamentResponse().getSeasons()) {
            seasonList.add(entity.getName());
        }

        Assertions.assertLinesMatch(seasonList, seasonSelectOptions, AssertMessages.entityNotExpected("Season Select options"));
        return this;
    }

    public Asserts assertExpectedTopScorersOptions() {
        var topScorerResponse = (ArrayList<TopScorerModel>)blocky.selectFirstSeasonWithTopScorers();
        var playersOptions = blocky.playersToHighlightSelect().getOptionsValues();
        var cardsList = new ArrayList<String>();

        for (TopScorerModel entity : topScorerResponse) {
            cardsList.add(entity.getPlayer().getName());
        }

        Assertions.assertLinesMatch(cardsList, playersOptions, AssertMessages.entityNotExpected(TOP_SCORERS_OPTIONS));
        return this;
    }

    public Asserts assertExpectedTopScorersOptionsAfterTeamSelect(List<TopScorerModel> topScorerResponse) {
        var expectedTeamPlayersApiUrl = PLAYERS_ENDPOINT.formatted(FootballApiUrl.TEAMS.url, topScorerResponse.get(0).getTeam().getId());
        ProxyServer.waitForResponse(blocky.getWrappedDriver(), expectedTeamPlayersApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<ArrayList<TeamPlayerModel>>() {
        }.getType();
        ArrayList<TeamPlayerModel> teamPlayersResponse = Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedTeamPlayersApiUrl, HttpMethod.GET.toString(), responseListType));

        var playersOptions = blocky.playersToHighlightSelect().getOptionsValues();
        var cardsList = new ArrayList<String>();

        for (TeamPlayerModel entity : teamPlayersResponse) {
            cardsList.add(entity.getPlayer().getName());
        }

        Assertions.assertLinesMatch(cardsList, playersOptions, AssertMessages.entityNotExpected(TOP_SCORERS_OPTIONS));
        return this;
    }

    public Asserts assertExpectedSeasonsTeamsOptions() {
        blocky.getBrowserService().waitForAjax();
        List<TopScorerModel> topScorerResponse = blocky.selectFirstSeasonWithTopScorers();
        List<String> teamsOptions = blocky.teamsSelect().getOptionsValues();
        Set<String> cardsList = new HashSet<>();

        for (TopScorerModel entity : topScorerResponse) {
            cardsList.add(entity.getTeam().getName());
        }

        Assertions.assertEquals(cardsList.stream().toList().stream().sorted().toList(), teamsOptions.stream().sorted().toList(), AssertMessages.entityNotExpected(TOP_SCORERS_OPTIONS));
        return this;
    }

    public static String getStatsParametersUrlQueryString(FootballTopScorersBlockyFieldsModel blockyData) {
        StringBuilder expectedRequestUrl = new StringBuilder();
        for (int i = 0; i < blockyData.getStatistics().size(); i++) {
            expectedRequestUrl.append(String.format("dataElements[%d]:%s;", i, blockyData.getStatistics().get(i).getValue()));
        }
        return expectedRequestUrl.toString();
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(FootballTopScorersBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertConfigObject(configObject, DataWidgetIdEnum.TOP_SCORERS);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.FOOTBALL_TOP_SCORERS_V2.getValue()));
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.tournamentSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateIsVisible();
        blocky.seasonSelect().validateIsVisible();
        blocky.filterStatisticsByTeamCheckBox().validateIsVisible();
        blocky.playersToHighlightSelect().validateIsVisible();
        blocky.startFromPositionTextField().validateIsVisible();
        blocky.showTextField().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(FootballTopScorersBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(FootballTopScorersBlockyFieldsModel blockyData) {
        blocky.waitPreviewScreenToLoad();
        List<String> expectedViewOptions = getExpectedViewOptions(blockyData);
        var previewRowsValuesList = new ArrayList<String>();

        for (Div element : blocky.previewRowsList()) {
            previewRowsValuesList.add(element.getText());
        }

        Assertions.assertEquals(expectedViewOptions, previewRowsValuesList, AssertMessages.entityNotExpected("Preview tags"));
        blocky.assertPreviewControlsVisible();
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(FootballTopScorersBlockyFieldsModel blockyData) {
        List<String> expectedPreviewOptions = new ArrayList<>();
        expectedPreviewOptions.add(String.format("Tournament: %sSeason: %s\nTeams: %s\nHighlighted players: %s\n" +
                        "Start from position:1\nShow number of players:4\n" +
                        "Statistics to be shown:Matches played",
                blockyData.getTournament().getName(),
                blockyData.getSeason().getName(),
                blockyData.getPlayerToHighLight().getTeam().getName(),
                blockyData.getPlayerToHighLight().getPlayer().getName()));
        expectedPreviewOptions.add(String.format("Tournament: %sSeason: %s\nTeams: %s",
                blockyData.getTournament().getName(),
                blockyData.getSeason().getName(),
                blockyData.getPlayerToHighLight().getTeam().getName()));
        expectedPreviewOptions.add(String.format("Teams: %s",
                blockyData.getPlayerToHighLight().getTeam().getName()));
        expectedPreviewOptions.add(String.format("Highlighted players: %s",
                blockyData.getPlayerToHighLight().getPlayer().getName()));
        expectedPreviewOptions.add(String.format("Start from position:%s", blockyData.getStartFromPosition()));
        expectedPreviewOptions.add(String.format("Show number of players:%s", blockyData.getShow()));
        expectedPreviewOptions.add("Statistics to be shown:Matches played");

        return expectedPreviewOptions;
    }
}