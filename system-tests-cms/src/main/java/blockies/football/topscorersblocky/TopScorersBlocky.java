package blockies.football.topscorersblocky;

import blockies.baseblockies.baseblocky.commonblockyelements.StatisticsParameterSelectable;
import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.PlaceholderField;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.constants.enums.PlayerDataElementsStatisticsEnum;
import data.constants.enums.football.FootballStatisticsEnum;
import data.customelements.MultiValueSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.football.FootballTopScorersBlockyFieldsModel;
import data.models.footballapi.common.CommonResultModel;
import data.models.footballapi.topscorer.TopScorerModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TopScorersBlocky extends BaseSportBlocky<FootballTopScorersBlockyFieldsModel> implements StatisticsParameterSelectable {

    private static final String TOP_SCORER_ENDPOINT = "/topscorer";

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_TOP_SCORERS;
    }

    @Override
    public TopScorersBlocky fillForm(FootballTopScorersBlockyFieldsModel blockyData) {
        selectTournament(blockyData);
        selectFilterStatistics(blockyData);
        browserService.waitForAjax();
        selectSeason(blockyData);
        selectTeam(blockyData);
        selectPlayerHighlight(blockyData);
        selectStatistics(blockyData);
        selectStartPosition(blockyData);
        selectShow(blockyData);
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    private void selectShow(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getShow() > 0) {
            showTextField().setText(String.valueOf(blockyData.getShow()));
        }
    }

    private void selectStartPosition(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getStartFromPosition() > 0) {
            startFromPositionTextField().setText(String.valueOf(blockyData.getStartFromPosition()));
        }
    }

    private void selectStatistics(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getStatistics() != null) {
            statisticsParametersSelect().clearSelection();
            statisticsParametersSelect().selectOptionsByText(blockyData.getStatistics().stream().map(PlayerDataElementsStatisticsEnum::getValue).toList());
        }
    }

    private void selectPlayerHighlight(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getPlayerToHighLight().getPlayer().getName() != null) {
            playersToHighlightSelect().searchSelectByText(blockyData.getPlayerToHighLight().getPlayer().getName());
        }
    }

    private void selectTeam(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getPlayerToHighLight().getTeam().getName() != null) {
            teamsSelect().searchSelectByText(blockyData.getPlayerToHighLight().getTeam().getName());
        }
    }

    private void selectSeason(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getSeason().getName() != null) {
            seasonSelect().searchSelectByText(blockyData.getSeason().getName());
        }
    }

    private void selectFilterStatistics(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getFilterStatisticsByTeam() != null) {
            filterStatisticsByTeamCheckBox().check(blockyData.getFilterStatisticsByTeam());
        }
    }

    private void selectTournament(FootballTopScorersBlockyFieldsModel blockyData) {
        if (blockyData.getTournament() != null) {
            tournamentSelect().searchSelectByText(blockyData.getTournament().getName());
        }
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballTopScorersBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TOP_SCORERS),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                new DataCompetitionAttribute(blockyData.getTournament().getId().toString()),
                new DataSeasonAttribute(blockyData.getSeason().getId().toString()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataLimitAttribute(String.valueOf(blockyData.getShow())),
                new DataFilterAttribute(BooleanEnum.TRUE),
                new DataHighlightEntitiesAttribute("[%s]".formatted(blockyData.getPlayerToHighLight().getPlayer().getId().toString())),
                new DataStartPositionAttribute(String.valueOf(blockyData.getStartFromPosition())),
                new DataElementsAttribute(blockyData.getStatistics().stream().map(PlayerDataElementsStatisticsEnum::getDisplayValue).toList()),
                new DataTeamIdsAttribute("[\"%s\"]".formatted(blockyData.getPlayerToHighLight().getTeam().getId())),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime()))
        );
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        tournamentSelect().toExist().toBeVisible().waitToBe();
        browserService.waitUntil(e -> !tournamentSelect().getText().equals(PlaceholderField.SELECT.getValue()));
        browserService.waitUntil(e -> !seasonSelect().getText().equals(PlaceholderField.SELECT.getValue()));
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    public SingleValueSelect tournamentSelect() {
        return createById(SingleValueSelect.class, "football-widget-tournament-select");
    }

    public SingleValueSelect seasonSelect() {
        return createById(SingleValueSelect.class, "football-widget-season-select");
    }

    public CheckBox filterStatisticsByTeamCheckBox() {
        return createByXPath(CheckBox.class, "//label[text()='Filter statistics by team']//input");
    }

    public MultiValueSelect teamsSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-widget-top-scorers-team-select");
    }

    public MultiValueSelect playersToHighlightSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-widget-top-scorers-select");
    }

    @Override
    public MultiValueSelect statisticsParametersSelect() {
        return createByAttributeContaining(MultiValueSelect.class, "data-qa", "football-top-scorers-elements-select");
    }

    @Override
    public List<String> getPreSelectedStatistics() {
        return FootballStatisticsEnum.getTopScorersBlockyStatistics();
    }

    public TextInput startFromPositionTextField() {
        return createByXPath(TextInput.class, "//label[text()='Start from position']//following-sibling::input");
    }

    public TextInput showTextField() {
        return createByXPath(TextInput.class, "//label[text()='Show']//following-sibling::input");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, "//div[@data-qa='football-top-scorers-block-view']//div[contains(@class,'col')]");
    }

    public List<CommonResultModel> getTournamentsResponse() {
        String expectedTournamentsApiUrl = FootballApiUrl.TOURNAMENTS.url.replace("tournaments/", "tournaments") + "?client_order=sportalios";
        ProxyServer.waitForResponse(getWrappedDriver(), expectedTournamentsApiUrl, HttpMethod.GET, 0);
        var responseListType = new TypeToken<List<CommonResultModel>>() {
        }.getType();
        return Objects.requireNonNull(ProxyServer.getResponseByUrl(expectedTournamentsApiUrl, HttpMethod.GET.toString(), responseListType));
    }

    public CommonResultModel getFirstTournamentResponse() {
        String firstTournamentUrl = FootballApiUrl.TOURNAMENTS.getUrl() + getTournamentsResponse().get(0).getId();
        ProxyServer.waitForResponse(getWrappedDriver(), firstTournamentUrl, HttpMethod.GET, 0);

        return Objects.requireNonNull(ProxyServer.getResponseByUrl(firstTournamentUrl, HttpMethod.GET.toString(), CommonResultModel.class));
    }

    public List<TopScorerModel> getTopScorersResponse() {
        var topScorerUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + getFirstTournamentResponse().getSeasons().get(0).getId() + TOP_SCORER_ENDPOINT;
        ProxyServer.waitForResponse(getWrappedDriver(), topScorerUrl, HttpMethod.GET, 0);
        var topScorersListType = new TypeToken<List<TopScorerModel>>() {
        }.getType();

        return Objects.requireNonNull(ProxyServer.getResponseByUrl(topScorerUrl, HttpMethod.GET.toString(), topScorersListType));
    }

    public List<TopScorerModel> selectFirstSeasonWithTopScorers() {
        var counter = 0;
        var responseListType = new TypeToken<ArrayList<TopScorerModel>>() {
        }.getType();

        var firstTournamentSeasons = getFirstTournamentResponse().getSeasons();
        var expectedSeasonId = firstTournamentSeasons.get(counter).getId();
        var topScorerUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + expectedSeasonId + TOP_SCORER_ENDPOINT;

        ArrayList<TopScorerModel> topScorerResponse = ProxyServer.getResponseByUrl(topScorerUrl, HttpMethod.GET.toString(), responseListType);

        while (Objects.requireNonNull(topScorerResponse).isEmpty()) {
            seasonSelect().selectOptionByIndex(counter);

            expectedSeasonId = firstTournamentSeasons.get(counter).getId();
            topScorerUrl = FootballApiUrl.TOURNAMENTS_SEASON.url + expectedSeasonId + TOP_SCORER_ENDPOINT;

            ProxyServer.waitForResponse(getWrappedDriver(), topScorerUrl, HttpMethod.GET, 0);
            topScorerResponse = ProxyServer.getResponseByUrl(topScorerUrl, HttpMethod.GET.toString(), responseListType);

            counter++;
        }

        return topScorerResponse;
    }
}