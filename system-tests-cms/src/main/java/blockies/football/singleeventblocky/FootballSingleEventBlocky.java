package blockies.football.singleeventblocky;

import blockies.baseblockies.basesingleeventblocky.BaseEventBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.EventStatusType;
import data.constants.apiurls.football.v1.FootballApiUrl;
import data.customelements.MatchesList;
import data.customelements.MultiValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.articles.NestedMatch;
import data.models.blockymodels.football.FootballSingleEventBlockyFieldsModel;
import data.models.footballapi.teams.TeamModel;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.CheckBox;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

public class FootballSingleEventBlocky extends BaseEventBlocky<FootballSingleEventBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.FOOTBALL_SINGLE_EVENT;
    }

    @Override
    public FootballSingleEventBlocky fillForm(FootballSingleEventBlockyFieldsModel blockyData) {
        filterEntity(blockyData.getTeam().getName());
        selectEvent(blockyData.getEvent());
        if (!blockyData.getEvent().getStatusType().equals(EventStatusType.FINISHED.toString())) {
            selectDisplayOdds(blockyData.isDisplayOdds());
        }
        selectDisplayMainEvent(blockyData.isDisplayMainEvent());
        selectBookmaker(blockyData.getBookmaker());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    @Override
    public ArrayList<Attribute> getBlockyAttributes(FootballSingleEventBlockyFieldsModel blockyData) {
        return new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.FOOTBALL_SINGLE_EVENT),
                new DataMatchIdAttribute(blockyData.getEvent().getLegacyId()),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT),
                new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL),
                new DataOddsDisplayAttribute(blockyData.isDisplayOdds()),
                new FansUnitedExpandedAttribute(blockyData.isFansUnitedExpanded()),
                new FansUnitedEnabledAttribute(blockyData.isFansUnitedEnabled()),
                new DataRefreshTimeAttribute(blockyData.getRefreshTime()),
                new DataHeaderDisplayAttribute(blockyData.isDataHeaderDisplay()),
                new DataDisplayMainEventSingleEventAttribute(blockyData.isDisplayMainEvent())));
    }

    public void selectDisplayMainEvent(boolean displayMainEvent) {
        displayMainEventCheckbox().check(displayMainEvent);
    }

    public CheckBox displayMainEventCheckbox() {
        return createByXPath(CheckBox.class, "//input[contains(@data-qa,'display-main-events')]");
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    protected MultiValueSearchSelect entitySearchSelect() {
        return createById(MultiValueSearchSelect.class, "football-widget-team-select");
    }

    public MatchesList eventsList() {
        return createByXPath(MatchesList.class, ".//div[@data-qa='matches-container']");
    }

    public CheckBox displayOddsCheckbox() {
        var allByXPath = createAllByXPath(CheckBox.class, ".//input[@data-qa='football-widget-display-odds-select']");
        if (allByXPath.isEmpty()) {
            return null;
        }
        return allByXPath.get(0);
    }

    public SingleValueSelect bookmakerSelect() {
        var allByXPath = createAllByXPath(SingleValueSelect.class, ".//div[@id='football-widget-bookmaker-select']/div");
        if (allByXPath.isEmpty()) {
            return null;
        }
        return allByXPath.get(0);
    }

    public SingleValueSelect marketSelect() {
        return createByXPath(SingleValueSelect.class, ".//div[@data-qa='football-widget-market-select']/div");
    }

    public CheckBox fansUnitedEnabledCheckbox() {
        return createByXPath(CheckBox.class, ".//input[@data-qa='football-widget-fans-united-enabled-select']");
    }

    public CheckBox fansUnitedExpandedCheckbox() {
        return createByXPath(CheckBox.class, ".//input[@data-qa='football-widget-fans-united-expanded-select']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, getFindStrategy().getValue() + "//div[@data-qa='football-single-event-view']//div[contains(@class,'row') and not(descendant::div[contains(@class,'row')])]");
    }

    public List<TeamModel> getTeamsResponse(String teamName) {
        var playerListType = new TypeToken<ArrayList<TeamModel>>() {
        }.getType();
        var expectedTeamsSearchApiUrl = FootballApiUrl.TEAMS_BY_NAME.url + teamName;
        ProxyServer.waitForResponse(browserService.getWrappedDriver(), expectedTeamsSearchApiUrl, HttpMethod.GET, 0);

        return ProxyServer.getResponseByUrl(expectedTeamsSearchApiUrl, HttpMethod.GET.toString(), playerListType);
    }

    public List<NestedMatch> getNotStartedMatchesResponse(String teamName) {
        return getMatchesResponse(teamName, FootballApiUrl.MATCHES_NOT_STARTED_BY_TEAM_ID.url);
    }

    public List<NestedMatch> getFinishedMatchesResponse(String teamName) {
        return getMatchesResponse(teamName, FootballApiUrl.MATCHES_FINISHED_BY_TEAM_ID.url);
    }

    public List<NestedMatch> getInProgressMatchesResponse(String teamName) {
        return getMatchesResponse(teamName, FootballApiUrl.MATCHES_INPROGRESS_BY_TEAM_ID.url);
    }

    public List<NestedMatch> getInterruptedMatchesResponse(String teamName) {
        return getMatchesResponse(teamName, FootballApiUrl.MATCHES_INTERRUPTED_BY_TEAM_ID.url);
    }

    private List<NestedMatch> getMatchesResponse(String teamName, String expectedUrl) {
        List<TeamModel> teamsResponse = getTeamsResponse(teamName);
        expectedUrl = expectedUrl.formatted(teamsResponse.get(0).getId());
        ProxyServer.waitForResponse(browserService.getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);
        var matchListType = new TypeToken<ArrayList<NestedMatch>>() {
        }.getType();

        return ProxyServer.getResponseByUrl(expectedUrl, HttpMethod.GET.toString(), matchListType);
    }
}
