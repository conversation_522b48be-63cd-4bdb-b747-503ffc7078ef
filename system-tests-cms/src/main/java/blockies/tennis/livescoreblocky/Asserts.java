package blockies.tennis.livescoreblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import data.constants.AssertMessages;
import data.constants.OddsApiUrl;
import data.constants.TennisApiUrl;
import data.constants.WidgetType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.LivescoreBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.odds.OddsModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

public class Asserts extends BaseSportBlockyAsserts<LivescoreBlockyFieldsModel> {

    private final TennisLivescoreBlocky blocky;

    protected Asserts(TennisLivescoreBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertTennisApiGetRequest(LivescoreBlockyFieldsModel blockyData) {
        String url = TennisApiUrl.MATCHES.getUrl() + String.format("/livescore" +
                        "?date=%s" +
                        "&utc_offset=3" +
                        "&translation_language=en" +
                        "&competition_list=all" +
                        "&limit=200",
                blockyData.getDate());

        ProxyServer.waitForRequest(blocky.getWrappedDriver(), url, HttpMethod.GET, 0);
        ProxyServer.assertRequestMade(url);
        return this;
    }

    public Asserts assertBookmakersList() {
        ProxyServer.waitForResponse(blocky.getWrappedDriver(), OddsApiUrl.FOOTBALL_CONFIGURATION.getUrl(), HttpMethod.GET, 1);
        var oddsResponse = ProxyServer.getResponseByUrl(OddsApiUrl.FOOTBALL_CONFIGURATION.getUrl(), HttpMethod.GET.toString(), OddsModel.class);

        var bookmakersNamesList = new ArrayList<String>();
        for (Bookmaker bookmaker : oddsResponse.getBookmakers()) {
            bookmakersNamesList.add(bookmaker.getBookmakerName());
        }

        Assertions.assertEquals(oddsResponse.getBookmakers().get(0).getBookmakerName(), blocky.bookmakerSelect().getText(), AssertMessages.entityNotExpected("Default bookmaker"));
        Assertions.assertLinesMatch(bookmakersNamesList, blocky.bookmakerSelect().getOptionsValues(), AssertMessages.entityNotExpected("Bookmakers list"));
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(LivescoreBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.TENNIS_LIVESCORE.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.TENNIS_LIVESCORE);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(LivescoreBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(LivescoreBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(LivescoreBlockyFieldsModel blockyData) {
        return (List<String>)this;
    }
}