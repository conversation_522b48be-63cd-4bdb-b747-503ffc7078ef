package blockies.tennis.singleeventblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOddsAsserts;
import data.constants.WidgetType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.tennis.TennisSingleEventBlockyFieldsModel;
import data.widgets.options.enums.DataWidgetIdEnum;

import java.util.List;

public class Asserts extends BaseSportBlockyWithOddsAsserts<TennisSingleEventBlockyFieldsModel> {
    private final TennisSingleEventBlocky blocky;

    public Asserts(TennisSingleEventBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(TennisSingleEventBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertConfigObject(configObject, DataWidgetIdEnum.TENNIS_SINGLE_EVENT);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.TENNIS_SINGLE_EVENT.getValue()));
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(TennisSingleEventBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(TennisSingleEventBlockyFieldsModel blockyData) {
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(TennisSingleEventBlockyFieldsModel blockyData) {
        return (List<String>)this;
    }
}