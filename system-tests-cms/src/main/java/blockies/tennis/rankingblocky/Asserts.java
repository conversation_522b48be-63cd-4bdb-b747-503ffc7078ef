package blockies.tennis.rankingblocky;

import blockies.baseblockies.basesportblocky.BaseSportBlockyAsserts;
import data.constants.AssertMessages;
import data.constants.PlaceholderField;
import data.constants.WidgetType;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.tennis.TennisRankingBlockyFieldsModel;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import solutions.bellatrix.web.components.Div;

import java.util.ArrayList;
import java.util.List;

public class Asserts extends BaseSportBlockyAsserts<TennisRankingBlockyFieldsModel> {

    private final TennisRankingBlocky blocky;

    public Asserts(TennisRankingBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertDefaultStateEditSection() {
        var expectedLimitPlaceholder = "This limit determines the number of items displayed by default and on each new page";
        var expectedOffsetDefaultValue = "1";
        var expectedStandingTypeDefaultValue = PlaceholderField.SELECT.getValue();
        var expectedRefreshTimePlaceholder = "Leave empty if you want to use global settings";

        Assertions.assertEquals(expectedLimitPlaceholder, blocky.limitTextField().getPlaceholder(), AssertMessages.entityNotExpected("Limit field placeholder"));
        Assertions.assertEquals(expectedOffsetDefaultValue, blocky.offsetTextField().getText(), AssertMessages.entityNotExpected("Offset field placeholder"));
        Assertions.assertEquals(expectedStandingTypeDefaultValue, blocky.standingTypeSelect().getText(), AssertMessages.entityNotExpected("Standing Type placeholder"));
        blocky.highlightPlayersCheckbox().validateIsUnchecked();
        Assertions.assertEquals(expectedRefreshTimePlaceholder, blocky.refreshTimeSelect().getText(), AssertMessages.entityNotExpected("Refresh time placeholder"));
        return this;
    }

    public Asserts assertEditSectionNotExist() {
        var elementsList = blocky.getWrappedDriver().findElements(By.xpath(blocky.limitTextField().getFindStrategy().getValue()));

        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Edit Section"));
        return this;
    }


    public Asserts assertValidationMessageDisplayed(String expectedMessage) {
        Assertions.assertTrue(blocky.validationMessagesList().stream().anyMatch(e -> e.getText().contains(expectedMessage)),
                AssertMessages.entityNotVisible(String.format("'%s' validation message", expectedMessage)));
        return this;
    }

    public Asserts assertPreviewControlsAreVisible(TennisRankingBlockyFieldsModel blockyData) {
        List<String> expectedTags = new ArrayList<>();
        blocky.waitPreviewScreenToLoad();

        blocky.assertPreviewControlsVisible();

        var player = blockyData.getHighlightedPlayer() != null ? blockyData.getHighlightedPlayer().getName() : "No players selected";
        expectedTags.add("Standing type: " + blockyData.getStandingType().getName());
        expectedTags.add("Highlighted players: " + player);
        expectedTags.add("Start from position: " + blockyData.getOffset());
        expectedTags.add("Limit: " + blockyData.getLimit());
        expectedTags.add("The widget always works with the current ranking.");

        if (blockyData.getRefreshTime() != null) {
            expectedTags.add("Refresh time: " + blockyData.getRefreshTime().getValue());
        }

        List<String> actualTags = new ArrayList<>();
        for (Div tag : blocky.previewRowsList()) {
            actualTags.add(tag.getText());
        }

        Assertions.assertLinesMatch(expectedTags, actualTags, AssertMessages.entityNotExpected("Preview controls and tags", expectedTags, actualTags));
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(TennisRankingBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertConfigObject(configObject, DataWidgetIdEnum.TENNIS_RANKING);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.TENNIS_RANKING.getValue()));
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    @Override
    public Asserts assertEditSectionIsVisible(TennisRankingBlockyFieldsModel blockyData) {
        blocky.limitTextField().toExist().toBeVisible().waitToBe();
        blocky.limitTextField().validateIsVisible();
        blocky.offsetTextField().validateIsVisible();
        blocky.standingTypeSelect().validateIsVisible();
        blocky.highlightPlayersCheckbox().validateIsVisible();
        blocky.refreshTimeSelect().validateIsVisible();
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.limitTextField().toExist().toBeVisible().waitToBe();
        blocky.limitTextField().validateIsVisible();
        blocky.offsetTextField().validateIsVisible();
        blocky.standingTypeSelect().validateIsVisible();
        blocky.highlightPlayersCheckbox().validateIsVisible();
        blocky.refreshTimeSelect().validateIsVisible();
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(TennisRankingBlockyFieldsModel blockyData) {
        return (List<String>)this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        return this;
    }
}