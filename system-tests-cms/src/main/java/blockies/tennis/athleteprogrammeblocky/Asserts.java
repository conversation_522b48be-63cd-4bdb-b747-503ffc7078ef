package blockies.tennis.athleteprogrammeblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOddsAsserts;
import data.constants.AssertMessages;
import data.constants.BlockyField;
import data.constants.PlaceholderField;
import data.constants.WidgetType;
import data.models.articles.*;
import data.models.blockymodels.tennis.TennisAthleteProgrammeBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.searchapi.suggest.Result;
import data.models.tennisapi.RoundModel;
import data.widgets.options.enums.DataGameTypeEnum;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DataSortDirectionEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.components.Div;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static data.constants.BlockyField.DISPLAY_ODDS;
import static data.constants.BlockyField.TOURNAMENT;
import static data.constants.BlockyField.*;
import static data.constants.StringConstants.*;

public class Asserts extends BaseSportBlockyWithOddsAsserts<TennisAthleteProgrammeBlockyFieldsModel> {

    private final TennisAthleteProgrammeBlocky blocky;

    public Asserts(TennisAthleteProgrammeBlocky blocky) {
        super(blocky);
        this.blocky = blocky;
    }

    public Asserts assertTournamentFieldOptions(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        blocky.tournamentSelect().expandOptionsList();
        List<String> tournamentSelectOptions = blocky.tournamentSelect().getOptionsValues();

        List<String> expectedTournamentOptions = blocky.getTournamentsGetResponse(blockyData).stream()
                .filter(tournament -> tournament.getCompetition() != null)
                .map(tournament -> "%s-%s".formatted(tournament.getName(), tournament.getSeasonYear()))
                .toList();

        Assertions.assertLinesMatch(expectedTournamentOptions, tournamentSelectOptions,
                AssertMessages.entityNotExpected("Tournament select options"));
        return this;
    }

    public Asserts assertErrorMessageDisplayedForPlayerField(String expectedErrorMessageText) {
        blocky.errorMessageForPlayerSelectField().validateIsVisible();
        blocky.errorMessageForPlayerSelectField().validateTextIs(expectedErrorMessageText);
        return this;
    }

    public Asserts assertPlayerFieldOptions(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        List<String> playerSelectOptions = blocky.playerSelect().getOptionsValues();
        List<String> expectedCompetitions = blocky.getSuggestedPlayerGetResponse(blockyData.getPlayer().getName()).stream()
                .map(Result::getName)
                .toList();

        Assertions.assertLinesMatch(expectedCompetitions, playerSelectOptions,
                AssertMessages.entityNotExpected("Player select options"));
        return this;
    }

    public Asserts assertRoundsOptions(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        String actualRounds = blocky.roundSelect().getText();

        String expectedRounds = blockyData.getRounds().stream()
                .map(RoundModel::getName)
                .collect(Collectors.joining("\n"));

        Assertions.assertEquals(expectedRounds, actualRounds, AssertMessages.entityNotExpected("Rounds select options"));
        return this;
    }

    private Asserts assertRoundsFilterOptions(Options optionsObject, TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        List<RoundFilterModel> roundsFilter = Optional.ofNullable(blockyData.getRounds())
                .orElseGet(Collections::emptySet) // Provide an empty list if blockyData.getRounds() is null
                .stream()
                .map(round -> RoundFilterModel.builder()
                        .roundId(round.getId())
                        .tournamentId(blockyData.getTournament().getId())
                        .build())
                .toList();
        Assertions.assertEquals(roundsFilter, optionsObject.getRoundsFilter(), AssertMessages.valueOfPropertyNotCorrect(ROUNDS_FILTER, BODY_DATA_CONFIG_OPTIONS));
        return this;
    }

    public Asserts assertTournamentFieldAutomaticallyFilled(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        Assertions.assertFalse(blocky.tournamentSelect().getText().isEmpty(), AssertMessages.fieldEmpty(TOURNAMENT));
        blocky.tournamentSelect().validateTextIs("%s-%s".formatted(blockyData.getTournament().getName(),
                blockyData.getTournament().getSeasonYear()));
        return this;
    }

    public Asserts assertRoundsFieldAutomaticallyFilled(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        Assertions.assertFalse(blocky.roundSelect().getText().isEmpty(), AssertMessages.fieldEmpty(BlockyField.ROUNDS));
        String expectedRounds = blockyData.getRounds().stream()
                .map(RoundModel::getName)
                .collect(Collectors.joining("\n"));
        blocky.roundSelect().validateTextIs(expectedRounds);
        return this;
    }

    @Override
    public Asserts assertBlockyElements() {
        blocky.playerSelect().toExist().toBeVisible().waitToBe();
        blocky.tournamentSelect().validateIsVisible();
        blocky.roundSelect().validateIsVisible();
        blocky.sortDirectionFixturesSelect().validateIsVisible();
        blocky.sortDirectionResultsSelect().validateIsVisible();
        blocky.limitField().validateIsVisible();
        blocky.dateFromDatePicker().validateIsVisible();
        blocky.dateToDatePicker().validateIsVisible();
        blocky.matchTypeSelect().validateIsVisible();
        blocky.headerDisplayCheckbox().validateIsVisible();
        assertDisplayOddsCheckboxNotDisplayed();
        assertBookmakerSelectNotDisplayed();
        blocky.defaultHeaderOptionSelect().validateIsVisible();
        assertRefreshTimeFieldVisible();
        assertEditControlsVisible();
        return this;
    }

    public Asserts assertBlockyElements(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        Instant startDate = Instant.parse(blockyData.getTournament().getStartDate());

        assertBlockyElements();
        if (startDate.isAfter(Instant.now())) {
            blocky.displayOddsCheckbox().validateIsVisible();
            if (blocky.displayOddsCheckbox().isChecked()) {
                blocky.bookmakerSelect().validateIsVisible();
            }
        } else {
            assertDisplayOddsCheckboxNotDisplayed();
            assertBookmakerSelectNotDisplayed();
        }
        return this;
    }

    @Override
    public Asserts assertFieldsDefaultState() {
        blocky.playerSelect().validateTextIs(PlaceholderField.SEARCH.getValue());
        blocky.tournamentSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.roundSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.sortDirectionFixturesSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        blocky.sortDirectionResultsSelect().validateTextIs(PlaceholderField.SELECT_ORDER_DIRECTION.getValue());
        blocky.limitField().validatePlaceholderIs(PlaceholderField.THIS_LIMIT_DETERMINES_THE_NUMBER_OF_ITEMS.getValue());
        blocky.dateFromDatePicker().validatePlaceholderIs(PlaceholderField.YYYY_MM_DD.getValue());
        blocky.dateToDatePicker().validatePlaceholderIs(PlaceholderField.YYYY_MM_DD.getValue());
        blocky.matchTypeSelect().validateTextIs(PlaceholderField.SELECT.getValue());
        blocky.headerDisplayCheckbox().validateIsUnchecked();
        blocky.defaultHeaderOptionSelect().validateTextIs(PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue());
        assertRefreshTimeDefaultState();
        return this;
    }

    @Override
    public Asserts assertPreviewControlsAreVisible(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        blocky.waitPreviewScreenToLoad();
        List<String> expectedTags = getExpectedViewOptions(blockyData);
        List<String> previewRowsValuesList = new ArrayList<>();

        for (Div element : blocky.previewRowsList()) {
            previewRowsValuesList.add(element.getText());
        }
        if (!blocky.showFieldInPreview().isEmpty()) {
            previewRowsValuesList.set(previewRowsValuesList.size() - 1, "Show:%s".formatted(blocky.showFieldInPreview().get(0).getTitle()));
        }
        if (blockyData.getRefreshTime() != null) {
            previewRowsValuesList.add("Refresh time: " + blockyData.getRefreshTime().getValue());
        }

        Assertions.assertLinesMatch(expectedTags, previewRowsValuesList, AssertMessages.entityNotExpected("Preview tags"));
        super.assertPreviewControlsVisible();
        return this;
    }

    @Override
    protected List<String> getExpectedViewOptions(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        List<String> expectedTags = new ArrayList<>();

        expectedTags.add("%s: %s".formatted(PLAYER.getValue(), blockyData.getPlayer().getName()));

        if (blockyData.getTournament() != null) {
            expectedTags.add("%s: %s".formatted(COMPETITION.getValue(), blockyData.getTournament().getCompetition().getName()));
            expectedTags.add("%s: %s".formatted(TOURNAMENT.getValue(), blockyData.getTournament().getName()));
        }
        if (blockyData.getRounds() != null) {
            expectedTags.add("%s:%s".formatted(ROUNDS.getValue(),
                    blockyData.getRounds().stream().map(RoundModel::getName).collect(Collectors.joining(","))));
        }
        if (blockyData.getMatchType() != null) {
            expectedTags.add("%s: %s".formatted(MATCH_TYPE.getValue(), blockyData.getMatchType()));
        }
        if (blockyData.getDateFrom() != null) {
            expectedTags.add("Date from: %s".formatted(blockyData.getDateFrom().getDate()));
        }
        if (blockyData.getDateTo() != null) {
            expectedTags.add("Date to: %s".formatted(blockyData.getDateTo().getDate()));
        }
        if (blockyData.getSortDirection() != null) {
            expectedTags.add("Sort direction of fixtures: " + blockyData.getSortDirection().getValue());
            expectedTags.add("Sort direction of results: " + blockyData.getSortDirection().getValue());
        }

        expectedTags.add("%s: %s".formatted(DISPLAY_ODDS.getValue(), blockyData.isDisplayOdds() ? YES_STRING : NO_STRING));

        if (blockyData.getBookmaker() != null && !blocky.showFieldInPreview().isEmpty()) {
            expectedTags.add("Show:" + blockyData.getBookmaker().getBookmakerName());
        }
        if (blockyData.getRefreshTime() != null) {
            expectedTags.add("%s: %s".formatted(REFRESH_TIME.getValue(), blockyData.getRefreshTime().getValue()));
        }
        return expectedTags;
    }

    @Override
    public Asserts assertEditSectionIsVisible(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        String expectedTournamentValue = Optional.ofNullable(blockyData.getTournament())
                .map(tournament -> String.format("%s-%s", tournament.getName(), tournament.getSeasonYear()))
                .orElse(PlaceholderField.SELECT.getValue());

        blocky.playerSelect().validateTextIs(blockyData.getPlayer().getName());
        blocky.tournamentSelect().validateTextIs(expectedTournamentValue);
        blocky.roundSelect().validateTextIs(Optional.ofNullable(blockyData.getRounds())
                .map(rounds -> rounds.stream().map(RoundModel::getName).collect(Collectors.joining("\n")))
                .orElse(PlaceholderField.SELECT.getValue()));
        blocky.sortDirectionFixturesSelect().validateTextIs(Optional.ofNullable(blockyData.getSortDirection())
                .map(DataSortDirectionEnum::getValue)
                .orElse(PlaceholderField.SELECT_ORDER_DIRECTION.getValue()));
        blocky.sortDirectionResultsSelect().validateTextIs(Optional.ofNullable(blockyData.getSortDirection())
                .map(DataSortDirectionEnum::getValue)
                .orElse(PlaceholderField.SELECT_ORDER_DIRECTION.getValue()));
        blocky.matchTypeSelect().validateTextIs(Optional.ofNullable(blockyData.getMatchType())
                .map(DataGameTypeEnum::getValue)
                .orElse(PlaceholderField.SELECT.getValue()));

        assertDisplayOddsCheckbox(blockyData.isDisplayOdds());

        Optional.ofNullable(blocky.bookmakerSelect())
                .ifPresent(select -> select.validateTextIs(
                        Optional.ofNullable(blockyData.getBookmaker())
                                .map(Bookmaker::getBookmakerName)
                                .orElse(EMPTY_STRING)
                ));

        blocky.refreshTimeSelect().validateTextIs(Optional.ofNullable(blockyData.getRefreshTime())
                .map(DataRefreshTimeEnum::getValue)
                .orElse(PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue()));
        return this;
    }

    @Override
    public Asserts assertBodyObjectInArticleCreateRequest(TennisAthleteProgrammeBlockyFieldsModel blockyData, BodyItem bodyObject) {
        Data dataObject = bodyObject.getData();
        Config configObject = dataObject.getConfig();
        Options optionsObject = configObject.getOptions();
        var sport = bodyObject.getData().getSport();

        assertBodyObject(bodyObject);
        assertDataObject(dataObject, sport, blockyData, String.valueOf(WidgetType.TENNIS_ATHLETE_PROGRAMME.getValue()));
        assertConfigObject(configObject, DataWidgetIdEnum.TENNIS_ATHLETE_PROGRAMME);
        assertOptionsObject(optionsObject, blockyData, sport);
        return this;
    }

    public Asserts assertMatchTypeOptions() {
        blocky.matchTypeSelect().expandOptionsList();
        List<String> actualMatchTypeOptions = blocky.matchTypeSelect().getOptionsValues();

        List<String> expectedMatchOptions = Arrays.stream(DataGameTypeEnum.values())
                .map(DataGameTypeEnum::getValue)
                .toList();

        Assertions.assertLinesMatch(expectedMatchOptions, actualMatchTypeOptions, AssertMessages.entityNotExpected("%s options".formatted(BlockyField.MATCH_TYPE)));
        return this;
    }
}