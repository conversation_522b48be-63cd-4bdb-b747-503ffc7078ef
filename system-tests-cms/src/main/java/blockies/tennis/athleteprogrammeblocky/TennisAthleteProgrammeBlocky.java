package blockies.tennis.athleteprogrammeblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.SportsSearchApiUrl;
import data.constants.TennisApiUrl;
import data.customelements.SingleValueSearchSelect;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.tennis.TennisAthleteProgrammeBlockyFieldsModel;
import data.models.searchapi.suggest.Result;
import data.models.searchapi.suggest.SuggestModel;
import data.models.tennisapi.PlayerModel;
import data.models.tennisapi.RoundModel;
import data.models.tennisapi.TournamentModel;
import data.utils.StringUtils;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataGameTypeEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.enums.DataWidgetTypeEnum;
import data.widgets.options.models.DataRoundsFilter;
import j2html.attributes.Attribute;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.TextInput;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.lang.reflect.Type;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

public class TennisAthleteProgrammeBlocky extends BaseSportBlockyWithOdds<TennisAthleteProgrammeBlockyFieldsModel> {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.TENNIS_ATHLETE_PROGRAMME;
    }

    public SingleValueSearchSelect playerSelect() {
        return createByIdContaining(SingleValueSearchSelect.class, "player-select");
    }

    public Div errorMessageForPlayerSelectField() {
        return playerSelect().createByXPath(Div.class, ".//following-sibling::h6");
    }

    public SingleValueSelect tournamentSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-tournament-select");
    }

    public SingleValueSelect roundSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-round-select");
    }

    public TextInput limitField() {
        return createById(TextInput.class, "limit-select");
    }

    public SingleValueSelect matchTypeSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-match-type-select");
    }

    public SingleValueSelect defaultHeaderOptionSelect() {
        return createByXPath(SingleValueSelect.class, ".//div[@data-qa='header-default-option-select']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class,
                "//div[@data-qa='tennis-athlete-programme-view']//div[contains(@class, 'row') and not(descendant::div[contains(@class, 'row')])]");
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        playerSelect().toExist().toBeVisible().waitToBe();
        browserService.waitForAjax();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    @Override
    public TennisAthleteProgrammeBlocky fillForm(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        selectPlayer(blockyData);
        selectTournament(blockyData.getTournament());
        selectRounds(blockyData.getRounds());
        selectSortDirection(blockyData.getSortDirection());
        selectLimit(blockyData);
        selectDateFrom(blockyData);
        selectDateTo(blockyData);
        selectMatchType(blockyData.getMatchType());
        selectDisplayOdds(blockyData.isDisplayOdds());
        selectBookmaker(blockyData.getBookmaker());
        selectRefreshTime(blockyData.getRefreshTime());
        return this;
    }

    public void selectPlayer(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        PlayerModel tennisPlayer = blockyData.getPlayer();

        if (tennisPlayer != null) {
            playerSelect().searchSelectByText(tennisPlayer.getName());
        } else {
            playerSelect().clearSelection();
        }
        getTournamentsGetResponse(blockyData);
    }

    public void selectTournament(TournamentModel tournament) {
        if (tournament != null && !tournament.getName().isEmpty()) {
            String tournamentName = "%s-%s".formatted(tournament.getName(), tournament.getSeasonYear());
            tournamentSelect().searchSelectByText(tournamentName);
        } else {
            tournamentSelect().clearSelection();
        }
    }

    public void selectRounds(Set<RoundModel> rounds) {
        if (rounds != null && !rounds.isEmpty()) {
            for (RoundModel round : rounds) {
                roundSelect().searchSelectByText(round.getName());
            }
        } else {
            roundSelect().clearSelection();
        }
    }

    public void selectLimit(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getLimit() != null) {
            limitField().setText(blockyData.getLimit());
        }
    }

    public void selectDateFrom(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getDateFrom() != null) {
            dateFromDatePicker().setDate(LocalDate.parse(blockyData.getDateFrom().getDate()));
        }
    }

    public void selectDateTo(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        if (blockyData.getDateTo() != null) {
            dateToDatePicker().setDate(LocalDate.parse(blockyData.getDateTo().getDate()));
        }
    }

    public void selectMatchType(DataGameTypeEnum matchType) {
        if (matchType != null) {
            matchTypeSelect().searchSelectByText(matchType.getValue());
        }
    }

    public void fillPlayerField(String playerName) {
        playerSelect().filter(playerName, true);
    }

    @Override
    public String getExpectedEmbedCode(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        List<Attribute> attributes = getBlockyAttributes(blockyData);
        return generateEmbedCode(attributes, false);
    }

    @Override
    public List<Attribute> getBlockyAttributes(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        TournamentModel tournament = blockyData.getTournament();
        List<Attribute> attributes = new ArrayList<>(List.of(
                new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_ATHLETE_PROGRAMME),
                new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS),
                new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT),
                (blockyData.getRefreshTime() != null ? new DataRefreshTimeAttribute(blockyData.getRefreshTime()) : new EmptyAttribute()),
                new DataGameTypeAttribute(blockyData.getMatchType()),
                new DataPlayerAttribute(blockyData.getPlayer().getId()),
                new DataCompetitionAttribute(tournament == null || tournament.getCompetition() == null ? null : tournament.getCompetition().getId()),
                new DataTournamentAttribute(tournament == null ? null : tournament.getId()),
                new DataRoundsFilterAttribute(getRounds(blockyData))
        ));

        if (blockyData.getDateFrom() != null) {
            attributes.add(new DataDateFromAttribute(blockyData.getDateFrom()));
        }

        if (blockyData.getDateTo() != null) {
            attributes.add(new DataDateToAttribute(blockyData.getDateTo()));
        }

        if (blockyData.getSortDirection() != null) {
            attributes.add(new DataSortDirectionFixturesAttribute(blockyData.getSortDirection().getStorybookValue().toUpperCase()));
            attributes.add(new DataSortDirectionResultsAttribute(blockyData.getSortDirection().getStorybookValue().toUpperCase()));
        }

        attributes.add(new DataOddsBettingIdAttribute(blockyData.getBookmaker() == null ? null : blockyData.getBookmaker().getBookmakerId()));
        attributes.add(new DataOddsDisplayAttribute(blockyData.isDisplayOdds()));
        return attributes;
    }

    public List<Result> getSuggestedPlayerGetResponse(String playerName) {
        String url = SportsSearchApiUrl.SUGGEST_TENNIS_PLAYER.getUrl().formatted(StringUtils.urlEncodeString(playerName));
        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        return Objects.requireNonNull(ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), SuggestModel.class)).getResults();
    }

    public List<TournamentModel> getTournamentsGetResponse(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        Result firstPlayerInResponse = getSuggestedPlayerGetResponse(blockyData.getPlayer().getName()).get(0);
        String url = TennisApiUrl.TOURNAMENTS.getUrl() + "?player_id=%s&translation_language=en&offset=0&limit=200"
                .formatted(firstPlayerInResponse.getId());

        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        Type tournamentsListType = new TypeToken<List<TournamentModel>>() {
        }.getType();
        return ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), tournamentsListType);
    }

    private List<DataRoundsFilter> getRounds(TennisAthleteProgrammeBlockyFieldsModel blockyData) {
        List<DataRoundsFilter> rounds = null;

        if (blockyData.getRounds() != null && !blockyData.getRounds().isEmpty()) {
            rounds = new ArrayList<>();

            for (RoundModel round : blockyData.getRounds()) {
                rounds.add(DataRoundsFilter.builder()
                        .tournamentId(blockyData.getTournament().getId())
                        .roundId(round.getId())
                        .build());
            }
        }
        return rounds;
    }
}