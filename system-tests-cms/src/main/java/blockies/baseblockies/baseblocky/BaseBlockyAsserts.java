package blockies.baseblockies.baseblocky;

import data.constants.AssertMessages;
import data.constants.BlockyField;
import data.constants.PlaceholderField;
import data.constants.RefreshTime;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;

import java.util.Arrays;
import java.util.List;

import static solutions.bellatrix.web.infrastructure.DriverService.getWrappedDriver;

public abstract class BaseBlockyAsserts {

    private final BaseBlocky blocky;

    protected BaseBlockyAsserts(BaseBlocky blocky) {
        this.blocky = blocky;
    }

    public void assertBlockyNotExist() {
        var elementsList = getWrappedDriver().findElements(By.xpath(blocky.getFindStrategy().getValue()));
        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Widget Block"));
    }

    public void assertEditControlsVisible() {
        blocky.saveButton().validateIsVisible();
        blocky.cancelButton().validateIsVisible();
    }

    public void assertPreviewControlsVisible() {
        blocky.copyEmbedCodeButton().validateIsVisible();
        blocky.previewButton().validateIsVisible();
        blocky.editButton().validateIsVisible();
        blocky.removeButton().validateIsVisible();
    }
}
