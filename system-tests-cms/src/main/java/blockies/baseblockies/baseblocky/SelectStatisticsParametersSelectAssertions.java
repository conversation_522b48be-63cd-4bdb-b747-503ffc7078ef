package blockies.baseblockies.baseblocky;

import blockies.baseblockies.baseblocky.commonblockyelements.StatisticsParameterSelectable;
import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import data.constants.AssertMessages;
import org.junit.jupiter.api.Assertions;

import java.util.Arrays;
import java.util.List;

public interface SelectStatisticsParametersSelectAssertions {

    default void assertAllStatisticsPreSelected(BaseSportBlocky blocky) {
        if (!(blocky instanceof StatisticsParameterSelectable provider)) {
            throw new IllegalArgumentException("Blocky must support select statistics parameters field");
        }
        blocky.getBrowserService().waitForAjax();
        List<String> actualStatistics = Arrays.stream(provider.statisticsParametersSelect().getText().split("\n")).toList();
        List<String> expectedStatistics = provider.getPreSelectedStatistics();

        Assertions.assertLinesMatch(expectedStatistics, actualStatistics,
                AssertMessages.entityNotExpected("Statistics parameters")
        );
    }
}