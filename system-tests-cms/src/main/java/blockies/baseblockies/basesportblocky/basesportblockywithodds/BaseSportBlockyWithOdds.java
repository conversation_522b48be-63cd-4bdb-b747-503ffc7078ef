package blockies.baseblockies.basesportblocky.basesportblockywithodds;

import blockies.baseblockies.basesportblocky.BaseSportBlocky;
import data.configuration.SportalSettings;
import data.constants.OddsApiUrl;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.odds.OddClientCodeEnum;
import data.customelements.DateTimePicker;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.odds.Bookmaker;
import data.models.footballapi.odds.OddsModel;
import data.models.oddsapi.OddsConfigurationModel;
import data.widgets.options.enums.DataHeaderDefaultOptionEnum;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.web.components.*;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseSportBlockyWithOdds<T extends SportBlockyFieldsModel> extends BaseSportBlocky<T> {

    private static final String ODDS_API_URL = ConfigurationService.get(SportalSettings.class).getOddsApiUrl();

    public CheckBox displayOddsCheckbox() {
        List<CheckBox> checkboxes = createAllByXPath(CheckBox.class, ".//input[contains(@data-qa,'widget-display-odds-select')]");
        if (checkboxes == null || checkboxes.isEmpty()) {
            return null;
        }
        return checkboxes.get(0);
    }

    public void selectDefaultHeaderOption(DataHeaderDefaultOptionEnum defaultHeaderOption) {
        if (defaultHeaderOption != null) {
            defaultHeaderOptionsSelect().searchSelectByText(defaultHeaderOption.getValue());
        }
    }

    public SingleValueSelect defaultHeaderOptionsSelect() {
        return createByXPath(SingleValueSelect.class, "//div[@data-qa='header-default-option-select']");
    }

    public SingleValueSelect bookmakerSelect() {
        return createAllByIdContaining(SingleValueSelect.class, "widget-bookmaker-select").stream()
                .findFirst()
                .orElse(null);
    }

    public DateTimePicker dateFromDatePicker() {
        return createByIdEndingWith(DateTimePicker.class, "widget-date-from-select");
    }

    public DateTimePicker dateToDatePicker() {
        return createByIdEndingWith(DateTimePicker.class, "widget-date-to-select");
    }

    public List<Image> showFieldInPreview() {
        return createAllByXPath(Image.class, "//div[text()='Show']//img");
    }

    public OddsModel getOddsResponse() {
        ProxyServer.waitForResponse(getWrappedDriver(), ODDS_API_URL, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(ODDS_API_URL, HttpMethod.GET.toString(), OddsModel.class);
    }

    public void selectBookmaker(Bookmaker bookmaker) {
        if (displayOddsCheckbox() != null) {
            if (displayOddsCheckbox().isChecked() && bookmaker != null) {
                bookmakerSelect().searchSelectByText(bookmaker.getBookmakerName());
            }
        } else {
            if (bookmakerSelect() != null && bookmaker != null) {
                bookmakerSelect().searchSelectByText(bookmaker.getBookmakerName());
            }
        }
    }

    public void selectDisplayOdds(boolean displayOdds) {
        if (displayOddsCheckbox() != null) {
            displayOddsCheckbox().check(displayOdds);
        }
    }

    public OddsConfigurationModel getOddsConfigurationFor(SupportedSports sport, OddClientCodeEnum oddClientCode) {
        String url = OddsApiUrl.CONFIGURATIONS_BY_SPORT.getUrl().formatted(sport.getValue(), oddClientCode.getValue());
        ProxyServer.waitForResponse(getWrappedDriver(), url, HttpMethod.GET, 0);
        return ProxyServer.getResponseByUrl(url, HttpMethod.GET.toString(), OddsConfigurationModel.class);
    }

    @Override
    public List<String> getActualViewOptions(List<Div> divs) {
        List<String> actualViewOptions = new ArrayList<>();

        for (int i = 0; i < divs.size(); i++) {
            String divText = divs.get(i).getText();
            if (divText.contains(StringConstants.SHOW_STRING)) {
                actualViewOptions.add(i, "%s: %s".formatted(StringConstants.SHOW_STRING, showFieldInPreview().get(0).getAlt()));
            } else {
                actualViewOptions.add(divText);
            }
        }
        return actualViewOptions;
    }
}