package blockies.baseblockies.basesportblocky;

import data.configuration.SportalSettings;
import data.constants.*;
import data.models.articles.BodyItem;
import data.models.articles.Config;
import data.models.articles.Data;
import data.models.articles.Options;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DataWidgetIdEnum;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.core.utilities.ClipboardManager;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static data.constants.PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS;
import static data.constants.StringConstants.*;

public abstract class BaseSportBlockyAsserts<T extends SportBlockyFieldsModel> {

    private final BaseSportBlocky blocky;

    protected BaseSportBlockyAsserts(BaseSportBlocky blocky) {
        this.blocky = blocky;
    }

    public abstract BaseSportBlockyAsserts assertBlockyElements();

    public abstract BaseSportBlockyAsserts assertFieldsDefaultState();

    public abstract BaseSportBlockyAsserts assertEditSectionIsVisible(T blockyData);

    public abstract BaseSportBlockyAsserts assertPreviewControlsAreVisible(T blockyData);

    protected abstract List<String> getExpectedViewOptions(T blockyData);

    public BaseSportBlockyAsserts assertPreviewHtml(T blockyData) {
        blocky.getBrowserService().waitForAjax();
        var expectedWidgetCode = blocky.getExpectedBlockyPreviewCode(blockyData);
        var actualHTML = getActualPreviewHTML();

        Assertions.assertEquals(expectedWidgetCode, actualHTML, AssertMessages.entityNotExpected("Blocky Preview HTML"));
        return this;
    }

    public BaseSportBlockyAsserts assertCopiedEmbedCode(T blockyData) {
        var lastCopy = ClipboardManager.getLastEntity();
        var expectedEmbedCode = blocky.getExpectedEmbedCode(blockyData);

        Assertions.assertEquals(expectedEmbedCode, lastCopy, AssertMessages.entityNotExpected("Copied Embed Code", expectedEmbedCode, lastCopy));

        return this;
    }

    public abstract BaseSportBlockyAsserts assertBodyObjectInArticleCreateRequest(T blockyData, BodyItem bodyObject);

    public BaseSportBlockyAsserts assertPreviewControlsVisible() {
        blocky.copyEmbedCodeButton().validateIsVisible();
        blocky.previewButton().validateIsVisible();
        blocky.editButton().validateIsVisible();
        blocky.removeButton().validateIsVisible();

        return this;
    }

    public BaseSportBlockyAsserts assertEditControlsVisible() {
        if (!blocky.getBrowserService().getUrl().contains("live-blogs")) {
            blocky.saveButton().validateIsVisible();
            blocky.cancelButton().validateIsVisible();
        }
        return this;
    }

    public BaseSportBlockyAsserts assertBlockyNotExist() {
        var elementsList = blocky.getWrappedDriver().findElements(By.xpath(blocky.getFindStrategy().getValue()));
        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Widget Block"));

        return this;
    }

    public void assertRefreshTimeFieldVisible() {
        blocky.refreshTimeSelect().validateIsVisible();
    }

    public BaseSportBlockyAsserts assertRefreshTimeOptions() {
        List<String> actualRefreshTimeOptions = blocky.refreshTimeSelect().getOptionsValues();

        List<String> expectedRefreshTimeOptions = Arrays.stream(RefreshTime.values())
                .map(RefreshTime::getValue)
                .toList();

        Assertions.assertLinesMatch(expectedRefreshTimeOptions, actualRefreshTimeOptions, AssertMessages.entityNotExpected("Refresh time options"));
        return this;
    }

    public BaseSportBlockyAsserts assertRefreshTimeDefaultState() {
        Assertions.assertEquals(PlaceholderField.LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue(),
                blocky.refreshTimeSelect().getText(), AssertMessages.defaultStateOfFieldIncorrect(BlockyField.REFRESH_TIME));

        return this;
    }

    public void assertRefreshTimeInEditMode(T blockyData) {
        blocky.refreshTimeSelect().validateTextIs(Optional.ofNullable(blockyData.getRefreshTime())
                .map(DataRefreshTimeEnum::getValue)
                .orElse(LEAVE_EMPTY_IF_YOU_WANT_TO_USE_GLOBAL_SETTINGS.getValue()));
    }

    public BaseSportBlockyAsserts assertSortDirectionOptions() {
        assertSortDirectionResultsOptions();
        assertSortDirectionFixturesOptions();

        return this;
    }

    public BaseSportBlockyAsserts assertSortDirectionFixturesOptions() {
        blocky.sortDirectionFixturesSelect().expandOptionsList();

        List<String> actualSortDirectionOptions = blocky.sortDirectionFixturesSelect().getOptionsValues();
        List<String> expectedSortDirectionOptions = Arrays.stream(SortDirectionEnum.values())
                .map(SortDirectionEnum::getValue)
                .collect(Collectors.toList());

        Assertions.assertLinesMatch(expectedSortDirectionOptions, actualSortDirectionOptions,
                AssertMessages.entityNotExpected("Sort direction select options"));

        return this;
    }

    public BaseSportBlockyAsserts assertSortDirectionResultsOptions() {
        blocky.sortDirectionResultsSelect().expandOptionsList();

        List<String> actualSortDirectionOptions = blocky.sortDirectionResultsSelect().getOptionsValues();
        List<String> expectedSortDirectionOptions = Arrays.stream(SortDirectionEnum.values())
                .map(SortDirectionEnum::getValue)
                .collect(Collectors.toList());

        Assertions.assertLinesMatch(expectedSortDirectionOptions, actualSortDirectionOptions,
                AssertMessages.entityNotExpected("Sort direction select options"));

        return this;
    }

    protected BaseSportBlockyAsserts assertBodyObject(BodyItem bodyObject) {
        Assertions.assertNotNull(bodyObject.getId(), AssertMessages.valueOfPropertyNotCorrect(ID_STRING, BODY));
        Assertions.assertNotNull(bodyObject.getData(), AssertMessages.valueOfPropertyNotCorrect(DATA_STRING, BODY));
        Assertions.assertEquals(BodyType.WIDGET_SMP_V2.getName(), bodyObject.getType(), AssertMessages.valueOfPropertyNotCorrect(TYPE_STRING, BODY));

        return this;
    }

    protected BaseSportBlockyAsserts assertDataObject(Data dataObject, String sport, T blockyData, String expectedWidgetType) {
        Assertions.assertEquals(sport, dataObject.getSport(), AssertMessages.valueOfPropertyNotCorrect(SPORT_STRING, BODY_DATA));
        Assertions.assertNotNull(dataObject.getConfig(), AssertMessages.valueOfPropertyNotCorrect("config", BODY_DATA));
        Assertions.assertEquals(blockyData.getEmbedCode(), dataObject.getContent(), AssertMessages.valueOfPropertyNotCorrect(CONTENT_STRING, BODY_DATA));
        Assertions.assertNotNull(dataObject.getPreview(), AssertMessages.valueOfPropertyNotCorrect("preview", BODY_DATA));
        Assertions.assertNotNull(dataObject.getChangeId(), AssertMessages.valueOfPropertyNotCorrect("changeId", BODY_DATA));
        Assertions.assertEquals(expectedWidgetType, dataObject.getWidgetType(), AssertMessages.valueOfPropertyNotCorrect("widget_type", BODY_DATA));

        return this;
    }

    protected BaseSportBlockyAsserts assertConfigObject(Config configObject, DataWidgetIdEnum widgetIdEnum) {
        Assertions.assertNotNull(configObject.getOptions(), AssertMessages.valueOfPropertyNotCorrect("options", BODY_DATA_CONFIG));
        Assertions.assertEquals(widgetIdEnum.getValue(), configObject.getWidgetId(),
                AssertMessages.valueOfPropertyNotCorrect("widgetId", BODY_DATA_CONFIG));

        return this;
    }

    protected BaseSportBlockyAsserts assertOptionsObject(Options optionsObject, T blockyData, String sport) {
        SportalSettings sportalSettings = ConfigurationService.get(SportalSettings.class);
        String expectedApiURL = getApiUrl(sport, sportalSettings);
        String expectedProject = sportalSettings.getApiFullFeaturesProject();
        String expectedTimeZone = sportalSettings.getApiDefaultTimeZone();

        Assertions.assertEquals(Language.ENGLISH.getCode(), optionsObject.getLang(),
                AssertMessages.valueOfPropertyNotCorrect("lang", BODY_DATA_CONFIG_OPTIONS));
        Assertions.assertEquals("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf", optionsObject.getApiKey(),
                AssertMessages.valueOfPropertyNotCorrect("apiKey", BODY_DATA_CONFIG_OPTIONS));
        Assertions.assertEquals(expectedApiURL.replace(".com/", ".com"), optionsObject.getApiURL(),
                AssertMessages.valueOfPropertyNotCorrect("apiURL", BODY_DATA_CONFIG_OPTIONS));
        Assertions.assertEquals(expectedProject, optionsObject.getProject(),
                AssertMessages.valueOfPropertyNotCorrect("project", BODY_DATA_CONFIG_OPTIONS));
        Assertions.assertEquals(expectedTimeZone, optionsObject.getTimezone(),
                AssertMessages.valueOfPropertyNotCorrect("timezone", BODY_DATA_CONFIG_OPTIONS));

        Optional.ofNullable(blockyData.getRefreshTime())
                .ifPresent(refreshTime -> Assertions.assertEquals(refreshTime.name().toLowerCase(), optionsObject.getRefreshTime(),
                        AssertMessages.valueOfPropertyNotCorrect("refreshTime", BODY_DATA_CONFIG_OPTIONS)));

        return this;
    }

    protected String getActualPreviewHTML() {
        var actualHTML = blocky.getPreviewWrapper().getHtml()
                .replace("\"", "'")
                .replace("&quot;", "\"")
                .replace("prevent-", "data-");
        var endCut = actualHTML.indexOf(" data-id");
        actualHTML = actualHTML.substring(0, endCut) + "></div>";
        return actualHTML;
    }

    private String getApiUrl(String sport, SportalSettings sportalSettings) {
        SupportedSports supportedSports = SupportedSports.valueOf(sport.toUpperCase());

        return switch (supportedSports) {
            case FOOTBALL -> sportalSettings.getFootballApiUrl();
            case TENNIS -> sportalSettings.getTennisApiUrl();
            case BASKETBALL -> sportalSettings.getBasketballApiUrl();
            case ICE_HOCKEY -> sportalSettings.getMultiSportApiUrl();
            default -> null;
        };
    }
}