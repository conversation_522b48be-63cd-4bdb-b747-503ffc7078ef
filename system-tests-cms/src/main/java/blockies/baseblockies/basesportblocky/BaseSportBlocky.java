package blockies.baseblockies.basesportblocky;

import blockies.baseblockies.baseblocky.BaseBlocky;
import data.constants.BlockyTypeEnum;
import data.customelements.SingleValueSelect;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.widgets.attributes.DataRefreshTimeAttribute;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataRefreshTimeEnum;
import data.widgets.options.enums.DataSortDirectionEnum;
import j2html.attributes.Attribute;
import j2html.tags.Tag;
import solutions.bellatrix.web.components.Div;

import java.util.ArrayList;
import java.util.List;

import static j2html.TagCreator.div;

public abstract class BaseSportBlocky<T extends SportBlockyFieldsModel> extends BaseBlocky {

    public abstract BlockyTypeEnum getBlockyType();

    public abstract BaseSportBlocky fillForm(T blockyData);

    public String getExpectedEmbedCode(T blockyData) {
        List<Attribute> attributes = getBlockyAttributes(blockyData);
        return generateEmbedCode(attributes, false);
    }

    public String getExpectedBlockyPreviewCode(T blockyData) {
        List<Attribute> attributes = getBlockyAttributes(blockyData);
        return generateBlockyPreviewDiv(attributes, false);
    }

    public abstract List<Attribute> getBlockyAttributes(T blockyData);

    // Elements Map
    public SingleValueSelect sortDirectionFixturesSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-sort-direction-fixtures-select");
    }

    public SingleValueSelect sortDirectionResultsSelect() {
        return createByIdContaining(SingleValueSelect.class, "widget-sort-direction-results-select");
    }

    public Div getPreviewWrapper() {
        return createByClass(Div.class, "monorepo-block-preview-content");
    }

    public SingleValueSelect refreshTimeSelect() {
        return createByAttributeContaining(SingleValueSelect.class, "data-qa", "refresh-time-select");
    }

    // Element Actions
    public abstract void waitEditScreenToLoad();

    public abstract void waitPreviewScreenToLoad();

    public BaseSportBlocky saveBlock() {
        saveBlockWithoutWait();
        waitForSpinners();
        return this;
    }

    public void copyEmbedCode() {
        waitPreviewScreenToLoad();
        copyEmbedCodeButton().validateIsVisible();
        copyEmbedCodeButton().getWrappedElement().click();
    }

    public void previewResult() {
        waitPreviewScreenToLoad();
        previewButton().validateIsVisible();
        previewButton().getWrappedElement().click();
        waitForSpinners();
    }

    public BaseSportBlocky editResult() {
        waitPreviewScreenToLoad();

        editButton().validateIsVisible();
        editButton().getWrappedElement().click();
        waitEditScreenToLoad();
        return this;
    }

    public BaseSportBlocky selectRefreshTime(DataRefreshTimeEnum refreshTime) {
        if (refreshTime != null) {
            refreshTimeSelect().selectOptionByIndex(refreshTime.ordinal());
        }
        return this;
    }

    public BaseSportBlocky selectHeaderDisplayCheckbox(Boolean headerDisplay) {
        if (headerDisplay != null) {
            headerDisplayCheckbox().check(headerDisplay);
        }
        return this;
    }

    public String generateEmbedCode(List<Attribute> attributes, boolean shouldSkipFalseBoolean) {
        Tag div = div();

        for (Attribute attribute : attributes) {
            String attributeValue = attribute.getValue();

            if (attributeValue == null || (shouldSkipFalseBoolean && attributeValue.equals(BooleanEnum.FALSE.getValue()))) {
                continue;
            }
            div.attr(attribute);
        }
        return div.toString()
                .replace("\"", "'")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
    }

    public String generateBlockyPreviewDiv(List<Attribute> attributes, boolean shouldSkipFalseBoolean) {
        Tag div = div();
        attributes = new ArrayList<>(attributes);

        var refreshTime = attributes.stream().filter(att -> att.getName() != null && att.getName().equals("data-refresh-time")).findFirst().orElse(null);
        if (refreshTime != null && refreshTime.getValue() != null) {
            refreshTime.setValue("never");
        } else {
            attributes.add(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));
        }

        for (Attribute attribute : attributes) {
            String attributeValue = attribute.getValue();

            if (attributeValue == null || (shouldSkipFalseBoolean && attributeValue.equals(BooleanEnum.FALSE.getValue()))) {
                continue;
            }
            div.attr(attribute);
        }

        return div.toString()
                .replace("\"", "'")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
    }

    public void selectSortDirection(DataSortDirectionEnum sortDirection) {
        if (sortDirection != null) {
            sortDirectionFixturesSelect().searchSelectByText(sortDirection.getValue());
            sortDirectionResultsSelect().searchSelectByText(sortDirection.getValue());
        }
    }

    public List<String> getActualViewOptions(List<Div> divs) {
        return divs.stream().map(Div::getText).toList();
    }
}