package blockies.baseblockies.basesingleeventblocky;

import blockies.baseblockies.basesportblocky.basesportblockywithodds.BaseSportBlockyWithOdds;
import data.constants.AssertMessages;
import data.constants.EventStatusType;
import data.customelements.MatchResult;
import data.customelements.MatchesList;
import data.customelements.MultiValueSearchSelect;
import data.models.blockymodels.SportBlockyFieldsModel;
import data.models.footballapi.v2.MatchV2Model;
import data.models.searchapi.ResultModel;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import solutions.bellatrix.web.components.Div;

import java.util.List;

public abstract class BaseEventBlocky<T extends SportBlockyFieldsModel> extends BaseSportBlockyWithOdds<T> {

    protected abstract MultiValueSearchSelect entitySearchSelect();

    protected abstract MatchesList eventsList();

    protected abstract List<Div> previewRowsList();

    public void filterEntity(String entityName) {
        waitEditScreenToLoad();
        entitySearchSelect().clearFilter(null);
        entitySearchSelect().searchSelectByText(entityName);
        waitForSpinners();
    }

    public void filterMultipleEntities(String firstEntityName, String secondEntityName) {
        filterEntity(firstEntityName);

        entitySearchSelect().selectOptionByText(secondEntityName);
    }

    public String getSelectedEntities() {
        return entitySearchSelect().getText();
    }

    public void selectEvent(ResultModel event) {
        browserService.waitForAjax();
        if (event != null) {
            if (event.getStatusType().equals(EventStatusType.FINISHED.toString())) {
                eventsList().selectPastMatch(eventsList(), event);
            } else if (event.getStatusType().equals(EventStatusType.INTERRUPTED.toString())) {
                eventsList().getInterruptedMatches();
            } else {
                eventsList().selectUpcomingMatch(eventsList(), event);
            }
            browserService.waitForAjax();
        }
    }

    public void selectEvent(MatchV2Model event) {
        if (event != null) {
            List<MatchResult> events;
            if (event.getStatus().getType().equals(EventStatusType.FINISHED.toString())) {
                events = eventsList().getPastMatches();
            } else if (event.getStatus().getType().equals(EventStatusType.INTERRUPTED.toString())) {
                events = eventsList().getInterruptedMatches();
            } else {
                events = eventsList().getUpcomingMatches();
            }

            events.stream().filter(
                            x ->
                                    x.getText().contains(event.getHomeTeam().getName()) &&
                                            x.getText().contains(event.getAwayTeam().getName()))
                    .findFirst().orElseThrow(() -> new RuntimeException("No event was found."))
                    .select();

            browserService.waitForAjax();
        }
    }

    @SneakyThrows
    public void waitEditScreenToLoad() {
        this.waitForSpinners();
        this.scrollToBottom();
        entitySearchSelect().toExist().toBeVisible().waitToBe();
    }

    //Preview screen
    @SneakyThrows
    public void waitPreviewScreenToLoad() {
        this.getBrowserService().waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public void validateEditSectionIsVisible() {
        entitySearchSelect().toExist().toBeVisible().waitToBe();

        entitySearchSelect().validateIsVisible();
        eventsList().validateIsVisible();
    }

    public void validateEditSectionNotExist() {
        var elementsList = this.getWrappedDriver().findElements(By.id(entitySearchSelect().getFindStrategy().getValue()));

        Assertions.assertTrue(elementsList.isEmpty(), AssertMessages.entityShouldNotExist("Edit Section"));
    }

    public void validatePreviewControlsAreVisible() {
        waitPreviewScreenToLoad();

        assertPreviewControlsVisible();
    }
}