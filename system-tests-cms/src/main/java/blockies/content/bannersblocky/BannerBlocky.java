package blockies.content.bannersblocky;

import blockies.baseblockies.baseblocky.BaseBlocky;
import data.constants.BlockyTypeEnum;
import data.customelements.SingleValueSearchSelect;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Heading;

import java.util.List;

public class BannerBlocky extends BaseBlocky {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.BANNER;
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    @Override
    public void waitEditScreenToLoad() {
        try {
            waitForSpinners();
            browserService.scrollToBottom();
            chooseBannerSelect().toExist().toBeVisible().waitToBe();
        } catch (Exception ex) {
            Log.error("Failed to wait for edit screen");
        }
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }


    public SingleValueSearchSelect chooseBannerSelect() {
        return createByXPath(SingleValueSearchSelect.class, ".//label[text()='Change banner']/..//div[contains(@class,'mb-3')]");
    }

    public List<Heading> validationMessagesList() {
        return createAllByXPath(Heading.class, ".//h6[@class='text-danger']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, ".//*[contains(@class, 'mb-2')]");
    }

    public void filterBanners(String title) {
        chooseBannerSelect().selectOptionByText(title);
    }
}