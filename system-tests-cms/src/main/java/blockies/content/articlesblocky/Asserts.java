package blockies.content.articlesblocky;

import data.constants.ContentApiUrl;
import data.models.articles.ArticleResponseModel;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.web.infrastructure.ProxyServer;

public class Asserts {
    private final ArticleBlocky blocky;

    public Asserts(ArticleBlocky blocky) {
        this.blocky = blocky;
    }

    public Asserts validateEditSectionIsVisible() {
        blocky.chooseArticleSelect().toExist().toBeVisible().waitToBe();
        blocky.chooseArticleSelect().validateIsVisible();
        blocky.assertEditControlsVisible();
        return this;
    }

    public Asserts validateChooseArticleTitleIs(String expectedArticleTitle) {
        blocky.chooseArticleSelect().validateTextIs(expectedArticleTitle);
        return this;
    }

    public Asserts validateArticlesByIdCallExecuted(String articleId) {
        ProxyServer.waitForResponse(blocky.getWrappedDriver(), ContentApiUrl.ARTICLES_ID.url.formatted(articleId), HttpMethod.GET, 0);
        return this;
    }

    public Asserts validatePreviewBlockControlsFor(ArticleResponseModel article) {
        blocky.waitPreviewScreenToLoad();
        blocky.articleImage().validateIsVisible();
        blocky.articleTitle().validateIsVisible();
        blocky.articleTitle().validateTextIs(article.getTitle());
        if (article.getSubtitle() != null) {
            blocky.articleSummary().validateIsVisible();
            blocky.articleSummary().validateTextIs(article.getSubtitle());
        }
        blocky.articlePublishedDate().validateIsVisible();
        blocky.assertPreviewControlsVisible();
        return this;
    }
}