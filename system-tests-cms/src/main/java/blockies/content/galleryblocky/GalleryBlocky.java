package blockies.content.galleryblocky;

import blockies.baseblockies.baseblocky.BaseBlocky;
import data.constants.BlockyTypeEnum;
import data.customelements.SingleValueSearchSelect;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Heading;

import java.util.List;

public class GalleryBlocky extends BaseBlocky {

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.GALLERY;
    }

    public Asserts asserts() {
        return new Asserts(this);
    }

    @Override
    public void waitEditScreenToLoad() {
        waitForSpinners();
        browserService.scrollToBottom();
        chooseGallerySelect().toExist().toBeVisible().waitToBe();
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public SingleValueSearchSelect chooseGallerySelect() {
        return createByXPath(SingleValueSearchSelect.class, ".//label[text()='Choose gallery']/..//div[contains(@class,'mb-2')]");
    }

    public List<Heading> validationMessagesList() {
        return createAllByXPath(Heading.class, ".//h6[@class='text-danger']");
    }

    // Preview block
    public List<Div> previewRowsList() {
        return createAllByXPath(Div.class, ".//div[contains(@class, 'gallery-title')]/*");
    }

    public void selectGallery(String title) {
        chooseGallerySelect().searchSelectByText(title);
    }
}