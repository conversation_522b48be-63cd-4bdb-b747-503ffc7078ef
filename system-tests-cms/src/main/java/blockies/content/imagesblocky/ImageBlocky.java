package blockies.content.imagesblocky;

import blockies.baseblockies.baseblocky.BaseBlocky;
import com.google.common.reflect.TypeToken;
import data.constants.BlockyTypeEnum;
import data.constants.ContentApiUrl;
import data.customelements.ImageProperties;
import data.customelements.ImagePropertiesToggler;
import data.customelements.SingleValueSelect;
import data.models.images.ImageModel;
import data.utils.StringUtils;
import facades.ContentApiFacade;
import org.openqa.selenium.remote.http.HttpMethod;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.web.components.*;
import solutions.bellatrix.web.infrastructure.ProxyServer;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ImageBlocky extends BaseBlocky {

    protected Anchor selectTab() {
        return createById(Anchor.class, "image-block-select-tab");
    }

    protected Anchor uploadTab() {
        return createById(Anchor.class, "image-block-upload-tab");
    }

    public SingleValueSelect chooseImageSelect() {
        return createByXPath(SingleValueSelect.class, ".//input[@id='block-image-search-input']/..");
    }

    protected Button searchButton() {
        return createByXPath(Button.class, ".//div[@class='input-group-append']/button[@bssize='sm']");
    }

    protected Button showAdvancedFiltersLink() {
        return createByXPath(Button.class, ".//button[text()='Show advanced filters']");
    }

    protected Div imageGrid() {
        return createByXPath(Div.class, ".//div[@class='image-blocky-masonry-grid']");
    }

    protected Image imageFromGrid(String imageSrc) {
        return imageGrid().createByXPath(Image.class, ".//img[@src='%s']".formatted(imageSrc));
    }

    protected Image firstImage() {
        return imageGrid().createByXPath(Image.class, ".//img");
    }

    protected Div pagination() {
        return createByCss(Div.class, "ul.smp-pagination");
    }

    protected Anchor nextPage() {
        return createByXPath(Anchor.class, ".//a[text()='...']");
    }

    protected ImagePropertiesToggler imagePropertiesToggler() {
        return createByXPath(ImagePropertiesToggler.class, ".//div[@id='togglerImageProperties']");
    }

    public ImageProperties imageProperties() {
        return createByXPath(ImageProperties.class, ".//div[@toggler='#togglerImageProperties']");
    }

    public Div errorMessageForImageDescription() {
        return createByXPath(Div.class, ".//label[contains(@for, 'image-block-description-error-message')]");
    }

    public TextInput linkField() {
        return createByXPath(TextInput.class, ".//label[contains(text(), 'Link')]//following-sibling::input");
    }

    public TextArea textField() {
        return createByXPath(TextArea.class, ".//label[contains(text(), 'Text')]//following-sibling::textarea");
    }

    // Preview block
    private Div previewBlock() {
        return createByXPath(Div.class, ".//div[contains(@class, 'gallery-title')]");
    }

    public List<Div> previewRowsList() {
        return previewBlock().createAllByXPath(Div.class, "./*");
    }

    public Div previewImageWidth() {
        return previewBlock().createByXPath(Div.class, ".//div[@class='mt-3']");
    }

    public Div previewImageAlignment() {
        return previewBlock().createByXPath(Div.class, ".//div[@class='mt-1']");
    }

    public Button previewLink() {
        return previewBlock().createByXPath(Button.class, ".//a[contains(@target, 'target=')]");
    }

    public Div previewOpensIn() {
        return previewBlock().createByXPath(Div.class, ".//p[contains(@class, 'text-muted')]");
    }

    @Override
    public BlockyTypeEnum getBlockyType() {
        return BlockyTypeEnum.IMAGE;
    }

    public ImageBlockyAsserts asserts() {
        return new ImageBlockyAsserts(this);
    }

    @Override
    public void waitEditScreenToLoad() {
        try {
            waitForSpinners();
            browserService.scrollToBottom();
            chooseImageSelect().toExist().toBeVisible().waitToBe();
        } catch (Exception ex) {
            Log.error("Failed to wait for edit screen");
        }
    }

    @Override
    public void waitPreviewScreenToLoad() {
        browserService.waitUntil(e -> !previewRowsList().isEmpty());
        previewRowsList().get(0).hover();
    }

    public void clickOnLinkButton() {
        previewLink().toExist().toBeVisible().waitToBe();
        previewLink().click();
        waitForSpinners();
    }

    public List<Image> imagesPreviewImages() {
        return createAllByXPath(Image.class, ".//div[@class='image-blocky-masonry-grid']//img");
    }

    public void filterImages(String title) {
        chooseImageSelect().filter(title, false);
    }

    public ImageBlocky selectFirstImageFromGrid() {
        firstImage().click();

        return this;
    }

    public ImageBlocky setLink(String link) {
        linkField().setText(link);
        return this;
    }

    public ImageBlocky setLinkText(String link) {
        textField().setText(link);
        return this;
    }

    public ImageProperties toggleOnImageProperties() {
        imagePropertiesToggler().toExist().toBeVisible().waitToBe();
        imagePropertiesToggler().toggleOn();

        return imageProperties();
    }

    public ImageBlocky chooseImage(String imageDescription) {
        filterImages(imageDescription);
        searchButton().click();
        waitForSearchImageResponse(imageDescription);
        selectFirstImageFromGrid();

        return this;
    }

    private void waitForSearchImageResponse(String imageDescription) {
        String expectedUrl = "%s/search?query=%s&page=1".formatted(
                ContentApiUrl.IMAGES_V2.url,
                StringUtils.replaceSpacesWithSymbol(imageDescription, "%20")
        );

        ProxyServer.waitForResponse(getWrappedDriver(), expectedUrl, HttpMethod.GET, 0);
    }

    public ImageBlocky selectImageFromGrid(boolean hasWatermark) {
        ImageModel image = new ContentApiFacade().getImage(hasWatermark);

        while (true) {
            boolean isImageVisible = imageFromGrid(image.getUrls().getUploaded().getEmbed()).isVisible();
            if (isImageVisible) {
                imageFromGrid(image.getUrls().getUploaded().getEmbed()).click();

                return this;
            }
            nextPage().click();
            waitForSpinners();
        }
    }

    protected ImageModel getImageByIdResponse(String id) {
        ProxyServer.waitForResponse(getWrappedDriver(), ContentApiUrl.IMAGES_ID.url.formatted(id), HttpMethod.PATCH, 0);

        return ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES_ID.url.formatted(id), HttpMethod.PATCH.toString(), ImageModel.class);
    }

    public ImageModel getFirstImageFromResponse() {
        ProxyServer.waitForResponse(getWrappedDriver(), ContentApiUrl.IMAGES_PAGE.url.formatted(1), HttpMethod.GET, 0);
        var responseListType = new TypeToken<ArrayList<ImageModel>>() {
        }.getType();
        ArrayList<ImageModel> imagesResponse = ProxyServer.getResponseByUrl(ContentApiUrl.IMAGES_PAGE.url.formatted(1),
                HttpMethod.GET.toString(), responseListType);

        return Objects.requireNonNull(imagesResponse).get(0);
    }
}