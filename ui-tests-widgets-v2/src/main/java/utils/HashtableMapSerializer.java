package utils;

import com.google.gson.*;

import java.lang.reflect.Type;
import java.util.Hashtable;

public class HashtableMapSerializer implements JsonSerializer<Hashtable<String, String>> {
    private final Gson gson;

    public HashtableMapSerializer() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.disableHtmlEscaping();
        gsonBuilder.setLenient();
        gson = gsonBuilder.setPrettyPrinting().create();
    }
    @Override
    public JsonElement serialize(Hashtable<String, String> stringStringHashMap, Type type, JsonSerializationContext jsonSerializationContext) {
        JsonObject json = new JsonObject();

        for (var key :
                stringStringHashMap.keySet()) {
             json.addProperty("'" + key + "'", "'" + stringStringHashMap.get(key)+ "'");
        }

        return json;
    }
}
