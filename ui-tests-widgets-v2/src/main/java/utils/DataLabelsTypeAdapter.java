package utils;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import data.widgets.options.models.DataLabels;

import java.io.IOException;

public class DataLabelsTypeAdapter extends TypeAdapter<DataLabels> {
    @Override
    public void write(JsonWriter out, DataLabels value) throws IOException {
        out.beginObject();
        for (var field :
                value.getClass().getFields()) {
            try {
                out.name(field.getName());
                out.value(field.get(value).toString());
            } catch (IllegalAccessException e) {
            }
        }
        out.endObject();
    }

    @Override
    public DataLabels read(JsonReader in){
        return null;
    }
}