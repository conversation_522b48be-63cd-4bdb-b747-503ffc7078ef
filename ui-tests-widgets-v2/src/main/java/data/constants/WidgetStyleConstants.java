package data.constants;

import plugins.visualregression.ViewportSize;

/**
 * Constants for common widget styling values used in responsive testing.
 * These constants follow the project pattern of using ViewportSize enum values.
 */
public class WidgetStyleConstants {
    
    /**
     * Max-width values based on ViewportSize enum
     */
    public static final String MAX_WIDTH_MOBILE_S = ViewportSize.MOBILE_S.getWidth() + "px";
    public static final String MAX_WIDTH_MOBILE_M = ViewportSize.MOBILE_M.getWidth() + "px";
    public static final String MAX_WIDTH_MOBILE_L = ViewportSize.MOBILE_L.getWidth() + "px";
    public static final String MAX_WIDTH_TABLET = ViewportSize.TABLET.getWidth() + "px";
    public static final String MAX_WIDTH_DESKTOP_M = ViewportSize.DESKTOP_M.getWidth() + "px";
    
    /**
     * Common CSS styles for testing widget behavior
     */
    public static final String BORDERED_CONTAINER = "border: 2px solid #ccc; padding: 10px;";
    public static final String CENTERED_CONTAINER = "margin: 0 auto; display: block;";
    public static final String SHADOW_CONTAINER = "box-shadow: 0 4px 8px rgba(0,0,0,0.1); padding: 15px;";
    public static final String ROUNDED_CONTAINER = "border-radius: 8px; overflow: hidden;";
    
    /**
     * Percentage-based widths for responsive testing
     */
    public static final String HALF_WIDTH = "50%";
    public static final String QUARTER_WIDTH = "25%";
    public static final String THREE_QUARTER_WIDTH = "75%";
    
    /**
     * Private constructor to prevent instantiation
     */
    private WidgetStyleConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
