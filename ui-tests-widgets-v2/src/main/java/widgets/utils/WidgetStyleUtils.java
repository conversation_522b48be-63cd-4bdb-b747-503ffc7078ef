package widgets.utils;

import widgets.pages.WidgetsBasePage;

/**
 * Utility class for applying common CSS styles to widgets in tests.
 * This provides convenient methods for testing responsive behavior and styling.
 */
public class WidgetStyleUtils {

    /**
     * Common max-width values for testing responsive behavior
     */
    public static class MaxWidth {
        public static final String MOBILE_S = "320px";
        public static final String MOBILE_M = "375px";
        public static final String MOBILE_L = "425px";
        public static final String TABLET = "768px";
        public static final String LAPTOP = "1024px";
        public static final String CONTAINER_300 = "300px";
        public static final String CONTAINER_400 = "400px";
        public static final String CONTAINER_500 = "500px";
        public static final String HALF_WIDTH = "50%";
        public static final String QUARTER_WIDTH = "25%";
    }

    /**
     * Common CSS styles for testing widget behavior
     */
    public static class CommonStyles {
        public static final String BORDERED_CONTAINER = "border: 2px solid #ccc; padding: 10px;";
        public static final String CENTERED_CONTAINER = "margin: 0 auto; display: block;";
        public static final String SHADOW_CONTAINER = "box-shadow: 0 4px 8px rgba(0,0,0,0.1); padding: 15px;";
        public static final String ROUNDED_CONTAINER = "border-radius: 8px; overflow: hidden;";
    }

    /**
     * Applies a max-width constraint to the widget for responsive testing
     * @param widgetPage the widget page instance
     * @param maxWidth the maximum width (use MaxWidth constants or custom value)
     */
    public static void applyMaxWidth(WidgetsBasePage<?, ?> widgetPage, String maxWidth) {
        widgetPage.setWidgetMaxWidth(maxWidth);
    }

    /**
     * Applies a max-width with additional styling for enhanced visual testing
     * @param widgetPage the widget page instance
     * @param maxWidth the maximum width
     * @param additionalStyles additional CSS styles to apply
     */
    public static void applyMaxWidthWithStyles(WidgetsBasePage<?, ?> widgetPage, String maxWidth, String additionalStyles) {
        String combinedStyles = String.format("max-width: %s; %s", maxWidth, additionalStyles);
        widgetPage.setCustomWidgetStyles(combinedStyles);
    }

    /**
     * Applies container-style testing with border and padding
     * @param widgetPage the widget page instance
     * @param maxWidth the maximum width
     */
    public static void applyContainerTesting(WidgetsBasePage<?, ?> widgetPage, String maxWidth) {
        applyMaxWidthWithStyles(widgetPage, maxWidth, CommonStyles.BORDERED_CONTAINER);
    }

    /**
     * Applies mobile-like container constraints for desktop testing
     * @param widgetPage the widget page instance
     */
    public static void applyMobileConstraints(WidgetsBasePage<?, ?> widgetPage) {
        applyMaxWidth(widgetPage, MaxWidth.MOBILE_L);
    }

    /**
     * Applies tablet-like container constraints for desktop testing
     * @param widgetPage the widget page instance
     */
    public static void applyTabletConstraints(WidgetsBasePage<?, ?> widgetPage) {
        applyMaxWidth(widgetPage, MaxWidth.TABLET);
    }

    /**
     * Clears any applied widget styles
     * @param widgetPage the widget page instance
     */
    public static void clearStyles(WidgetsBasePage<?, ?> widgetPage) {
        widgetPage.clearCustomWidgetStyles();
    }

    /**
     * Applies a complete responsive testing setup with visual enhancements
     * @param widgetPage the widget page instance
     * @param maxWidth the maximum width
     */
    public static void applyResponsiveTestingSetup(WidgetsBasePage<?, ?> widgetPage, String maxWidth) {
        String responsiveStyles = String.format(
            "max-width: %s; %s %s %s", 
            maxWidth, 
            CommonStyles.BORDERED_CONTAINER,
            CommonStyles.CENTERED_CONTAINER,
            CommonStyles.SHADOW_CONTAINER
        );
        widgetPage.setCustomWidgetStyles(responsiveStyles);
    }
}
