package widgets.molecules.tabpills;

import core.WidgetMap;
import solutions.bellatrix.web.components.Button;

public class IceHockeyStandingsTabPillSection extends TabPillsSection {

    public IceHockeyStandingsTabPillSection(WidgetMap widgetMap) {
        super(widgetMap);
    }

    public Button nhlPillButton() {
        return getPillButtonByIndex(1);
    }

    public Button nhlPlayoffsPillButton() {
        return getPillButtonByIndex(0);
    }
}