package widgets.molecules.livescore;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;

import java.util.List;

public class LivescoreSection {

    protected WidgetMap widgetMap;

    public LivescoreSection(WidgetMap widgetMap) {
        this.widgetMap = widgetMap;
    }

    /**
     * First CSS selector is for refactored widgets (football, basketball and ice hockey)
     * Second CSS selector is for widgets yet to be refactored (tennis)
     * @return Anchor element representing the tournament link
     */
    public Anchor tournamentAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "a.tournament, " +
                        "div > div:nth-child(2) > div > a:nth-child(1)");
    }

    /**
     * @return Anchor element representing the tournament standings link
     */
    public Anchor tournamentStandingsAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "a.standings");
    }

    /**
     * First CSS selector is for refactored widgets (football, basketball and ice hockey)
     * Second CSS selector is for tennis
     * @return Div element containing the tournament name
     */
    public Div tournamentName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class,
                "[class*='tournament-container'] a div div p:first-child, " +
                        "div[class*='variant-column'] > span[class*='c-TextContainer-CommonWidget']:nth-child(1)");
    }

    /**
     * @return List of Div elements containing tournament names
     */
    public List<Div> tournamentNamesList() {
        return widgetMap.shadowHost().shadowRootCreateAllByCss(Div.class,
                "[class*='tournament-container'] a div div p:first-child");
    }

    /**
     * First CSS selector is for refactored widgets (football, basketball and ice hockey)
     * Second CSS selector is for tennis
     * @return Div element containing the country name
     */
    public Div countryName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class,
                "[class*='tournament-container'] a div div p:nth-child(2), " +
                        "div[class*='variant-column'] > span[class*='c-TextContainer-CommonWidget']:nth-child(2)");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for football widgets
     * Third selector is for tennis widgets
     * @return Anchor element representing the first match link
     */
    public Anchor firstMatchAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div.match-info > a, " +
                        "div[class*='match-container'] > a, " +
                        "div[class*='livescoreMatch'] > a");
    }

    /**
     * First CSS selector is for football and second selector is for tennis
     * @return Anchor element representing the first match
     */
    public Anchor firstMatch() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div[class*='match-container'], " +
                        "div[class*='livescoreMatch']");
    }

    /**
     * @return Div element representing the match row container
     */
    public Div matchRowContainer() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div.match-row-container");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for football
     * Third CSS selector is for tennis
     * @return List of Div elements representing match row containers
     */
    public List<Div> matchRowContainers() {
        return widgetMap.shadowHost().shadowRootCreateAllByCss(Div.class,
                "div.match-row-container, " +
                        "div[class*='match-container'], " + "[class*='EventRowContainer']");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for football
     * Third selector is for tennis
     * @return Div element containing the first match date
     */
    public Span firstMatchDate() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class,
                "div[class*='match-status-container'] p[class*='match-date'], " +
                        "p.match-date, " +
                        "div[class*='variant-column'] > span[class*='variant-primaryGray']:nth-of-type(1)");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for football
     * Third selector is for tennis
     * @return Div element containing the first match status
     */
    public Span firstMatchStatus() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class,
                "div[class*='match-status-container'] p[class*='match-status'], " +
                        "span.match-status, " +
                        "div[class*='variant-column'] > span[class*='variant-primaryGray']:nth-of-type(2)");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for widgets yet to be refactored (football and tennis)
     * @return Anchor element representing the first team link
     */
    public Anchor firstTeamAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div.first-team > a, " +
                        "div[class*='match-container'] > a");
    }

    /**
     * First CSS selector is for refactored widgets (basketball and ice hockey)
     * Second CSS selector is for widgets yet to be refactored (football and tennis)
     * @return Anchor element representing the second team link
     */
    public Anchor secondTeamAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div.second-team > a, " +
                        "[class*='MatchRow'] > div:nth-child(3) > a");
    }

    /**
     * The first CSS selector is for refactored widgets (basketball and ice hockey)
     * The second CSS selector is for football
     * @return Anchor element containing the first team name
     */
    public Anchor firstTeamName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "div.first-team > a span, " +
                "div[class*='home-team'] > p");
    }

    /**
     * The first CSS selector is for refactored widgets (basketball and ice hockey)
     * The second CSS selector is for football
     * @return Anchor element containing the second team name
     */
    public Anchor secondTeamName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "div.second-team > a span, " +
                "div[class*='away-team'] > p");
    }

    /**
     * @return Anchor element representing the first player link
     */
    public Anchor firstPlayerAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div[class*='livescoreMatch'] div[class*='-variant-base']:nth-child(2) a");
    }

    /**
     * @return Span element containing the first player name
     */
    public Span firstPlayerName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class,
                "div[class*='livescoreMatch'] div[class*='-variant-base']:nth-child(2) a span");
    }

    /**
     * @return Anchor element representing the second player link
     */
    public Anchor secondPlayerAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class,
                "div[class*='livescoreMatch'] div[class*='-variant-base']:nth-child(3) a");
    }

    /**
     * @return Span element containing the second player name
     */
    public Span secondPlayerName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class,
                "div[class*='livescoreMatch'] div[class*='-variant-base']:nth-child(3) a span");
    }

    /**
     * @return Span element containing the center message
     */
    public Span centerMessage() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class, "[class*='sportEntityContainer'] span");
    }

    public Span homeTeamScore() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class, "span[class*='home-team-score']");
    }

    public Span awayTeamScore() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Span.class, "span[class*='away-team-score']");
    }
}