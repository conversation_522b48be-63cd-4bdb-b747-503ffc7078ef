package widgets.molecules.knockoutgame;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.WebComponent;
import solutions.bellatrix.web.infrastructure.TouchableWebDriver;

import java.util.List;

public class KnockoutGamesSection {

    protected WidgetMap widgetMap;

    public KnockoutGamesSection(WidgetMap widgetMap) {
        this.widgetMap = widgetMap;
    }

    public List<Anchor> getGameContainers() {
        return widgetMap.shadowHost().shadowRootCreateAllByCss(Anchor.class, "div[class*='c-KnockoutGameContainer']");
    }

    public List<Anchor> getMobileGameContainers() {
        return widgetMap.shadowHost().shadowRootCreateAllByCss(Anchor.class, "div[class*='c-KnockoutMobileGameContainer-CommonWidget']");
    }

    public Anchor firstMatchHomeTeamNameAnchor() {
        return getGameContainers().get(0).createByCss(Anchor.class, "div[class*='variant-column'] > div:nth-child(1) a");
    }

    public Span firstMatchHomeTeamName() {
        return firstMatchHomeTeamNameAnchor().createByCss(Span.class, "span");
    }

    public Anchor firstMatchAwayTeamNameAnchor() {
        return getGameContainers().get(0).createByCss(Anchor.class, "div[class*='variant-column'] > div:nth-child(2) a");
    }

    public Span firstMatchAwayTeamName() {
        return firstMatchAwayTeamNameAnchor().createByCss(Span.class, "span");
    }

    public Anchor firstMatch() {
        return getGameContainers().get(0);
    }

    public Anchor firstMatchAnchor() {
        return getGameContainers().get(0).createByCss(Anchor.class, "a");
    }

    public Anchor lastMatchAnchor() {
        return getGameContainers().get(getGameContainers().size() - 1).createByCss(Anchor.class, "div[class*=variant-livescoreMatch] > div:nth-child(2) a");
    }

    public Div knockoutStageSlider() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, ".slick-initialized");
    }

    public Div penaltiesBadge() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div[class*='c-KnockoutGameContainer'] a div:nth-child(3)");
    }

    public Div aggregateBadge() {
        return penaltiesBadge();
    }

    public Div penaltiesBadgeMobile() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div[class*='slick-active'] div[class*='c-KnockoutMobileGameContainer'] a div:nth-child(3) > span");
    }

    public Div aggregateBadgeMobile() {
        return penaltiesBadgeMobile();
    }

    public WebComponent nextStageButton() {
        return sliderButtons().size() == 1 ? null : sliderButtons().get(1);
    }

    public WebComponent previousStageButton() {
        return sliderButtons().get(0);
    }

    public void swipeToNextStage() {
        var firstMatch = getMobileGameContainers().stream().findFirst().orElseThrow();
        ((TouchableWebDriver)(widgetMap.create().getWrappedDriver())).triggerSwipeEvent(firstMatch.getWrappedElement());
    }

    public void tapToNextStage() {
        var nextStageButton = nextStageButton();
        if (nextStageButton == null) {
            throw new RuntimeException("There is no next stage. Final stage displayed.");
        }
        ((TouchableWebDriver)(widgetMap.create().getWrappedDriver())).triggerTapEvent(nextStageButton.getWrappedElement());
    }

    public void tapToPreviousStage() {
        var previousStageButton = previousStageButton();
        ((TouchableWebDriver)(widgetMap.create().getWrappedDriver())).triggerTapEvent(previousStageButton.getWrappedElement());
        previousStageButton.toBeVisible().waitToBe();
    }

    public void holdOverPenaltiesBadge() {
        var nextStageButton = penaltiesBadgeMobile();
        ((TouchableWebDriver)(widgetMap.create().getWrappedDriver())).triggerHoldEvent(nextStageButton.getWrappedElement());
    }

    public void holdOverAggregateBadge() {
        holdOverPenaltiesBadge();
    }

    private List<WebComponent> sliderButtons() {
        return widgetMap.shadowHost().shadowRootCreateAllByCss(WebComponent.class, "div[class*='slick-active'] svg");
    }
}