package widgets.molecules.seasonselect;

import core.WidgetMap;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;

import java.util.List;

public class SeasonSelectSection {

    protected WidgetMap widgetMap;

    public SeasonSelectSection(WidgetMap widgetMap) {
        this.widgetMap = widgetMap;
    }

    public Div wrapper() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div[class*='dropdown-container']");
    }

    /**
     * The first CSS selector is for football widgets
     * The second selector is for tennis widgets
     */
    public Button selectButton() {
        return wrapper().createByCss(Button.class, "div[class*='variant-base'], " + "[class*='text-selectButton']");
    }

    /**
     * The first CSS selector is for football widgets
     * The second selector is for tennis widgets
     */
    public Button selectedOption() {
        return wrapper().createByCss(Button.class, ".c-TextContainer-CommonWidget, " + "span[class*='dropdown-value']");
    }

    /**
     * The first CSS selector is for football widgets
     * The second selector is for tennis widgets
     */
    public List<Button> options() {
        return wrapper().createAllByCss(Button.class, "div[class*='size-4'] .c-SMPContainer-CommonWidget, " + "div[class*='dropdown-menu-option']");
    }

    public void selectCurrentSeason() {
        selectButton().click();
        waitForAnimation();
        options().get(0).click();
    }

    public void selectPreviousSeason() {
        selectButton().click();
        waitForAnimation();
        options().get(1).click();
    }

    private void waitForAnimation() {
        Wait.forMilliseconds(1000);
    }
}