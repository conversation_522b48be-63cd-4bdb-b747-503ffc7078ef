package widgets.molecules.singleevent;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;

public class SingleEventSection {

    protected WidgetMap widgetMap;

    public SingleEventSection(WidgetMap widgetMap) {
        this.widgetMap = widgetMap;
    }

    public Anchor homeTeam() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "a[class*='home-team']");
    }

    public Anchor awayTeam() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "a[class*='away-team']");
    }

    public Div score() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div[class*='score-container'] > a > div");
    }

    public Anchor scoreAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='score-container'] > a");
    }

    public Div status() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Div.class, "div.status");
    }

    public Anchor competitionInfoAnchor() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='competition-container']");
    }

    public Anchor competitionName() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "a p[class*='competition-name']");
    }

    public Anchor competitionDate() {
        return widgetMap.shadowHost().shadowRootCreateByCss(Anchor.class, "a p[class*='competition-date']:nth-of-type(2)");
    }
}