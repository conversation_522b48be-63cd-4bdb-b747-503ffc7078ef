package widgets.molecules.standingstable;

import core.WidgetMap;
import solutions.bellatrix.web.components.Span;

public class MostDecoratedTableSection extends TableSection {

    public MostDecoratedTableSection(WidgetMap widgetMap) {
        super(widgetMap);
    }

    public Span getPlayerHeader() {
        return getStickyColumnHeaders().get(1);
    }

    public Span getFirstYellowCardHeader() {
        return getTableHeaders().get(2);
    }

    public Span getRedCardsHeader() {
        return getTableHeaders().get(3);
    }

    public Span getTotalCardsHeader() {
        return getTableHeaders().get(4);
    }

    public Span getYellowCardsHeader() {
        return getTableHeaders().get(5);
    }

    public Span getFirstYellowCardFullHeader() {
        return getTableHeadersFull().get(0);
    }

    public Span getRedCardsFullHeader() {
        return getTableHeadersFull().get(1);
    }

    public Span getTotalCardsFullHeader() {
        return getTableHeadersFull().get(2);
    }

    public Span getYellowCardsFullHeader() {
        return getTableHeadersFull().get(3);
    }
}
