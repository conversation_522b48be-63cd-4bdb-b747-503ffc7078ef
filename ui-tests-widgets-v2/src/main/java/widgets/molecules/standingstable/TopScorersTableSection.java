package widgets.molecules.standingstable;

import core.WidgetMap;
import solutions.bellatrix.web.components.Span;

public class TopScorersTableSection extends TableSection {

    public TopScorersTableSection(WidgetMap widgetMap) {
        super(widgetMap);
    }

    public Span getPlayerHeader() {
        return getStickyColumnHeaders().get(1);
    }

    public Span getMissedPenaltiesCardHeader() {
        return getTableHeaders().get(2);
    }

    public Span getAssistsHeader() {
        return getTableHeaders().get(3);
    }

    public Span getGoalsHeader() {
        return getTableHeaders().get(4);
    }

    public Span getMinutesHeader() {
        return getTableHeaders().get(5);
    }

    public Span getPenaltiesHeader() {
        return getTableHeaders().get(6);
    }

    public Span getMatchesPlayedHeader() {
        return getTableHeaders().get(7);
    }

    public Span getRedCardsHeader() {
        return getTableHeaders().get(8);
    }

    public Span getScoredFirstHeader() {
        return getTableHeaders().get(9);
    }

    public Span getYellowCardsHeader() {
        return getTableHeaders().get(10);
    }

    public Span getMissedPenaltiesCardFullHeader() {
        return getTableHeadersFull().get(0);
    }

    public Span getAssistsFullHeader() {
        return getTableHeadersFull().get(1);
    }

    public Span getGoalsFullHeader() {
        return getTableHeadersFull().get(2);
    }

    public Span getMinutesFullHeader() {
        return getTableHeadersFull().get(3);
    }

    public Span getPenaltiesFullHeader() {
        return getTableHeadersFull().get(4);
    }

    public Span getMatchesPlayedFullHeader() {
        return getTableHeadersFull().get(5);
    }

    public Span getRedCardsFullHeader() {
        return getTableHeadersFull().get(6);
    }

    public Span getScoredFirstFullHeader() {
        return getTableHeadersFull().get(7);
    }

    public Span getYellowCardsFullHeader() {
        return getTableHeadersFull().get(8);
    }

}
