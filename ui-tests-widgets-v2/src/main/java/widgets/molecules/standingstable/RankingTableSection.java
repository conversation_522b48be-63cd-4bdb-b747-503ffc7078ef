package widgets.molecules.standingstable;

import core.WidgetMap;
import solutions.bellatrix.web.components.Span;

public class RankingTableSection extends TableSection {

    public RankingTableSection(WidgetMap widgetMap) {
        super(widgetMap);
    }

    public Span getNameHeader() {
        return getTableHeaders().get(1);
    }

    public Span getNationalityHeader() {
        return getTableHeaders().get(2);
    }

    public Span getNationalityHeaderMobile() {
        return getTableHeaders().get(3);
    }

    public Span getPointsHeader() {
        return getTableHeaders().get(4);
    }

    public Span getPointsHeaderMobile() {
        return getTableHeaders().get(5);
    }
}
