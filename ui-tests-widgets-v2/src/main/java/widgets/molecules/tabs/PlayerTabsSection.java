package widgets.molecules.tabs;

import core.WidgetMap;
import solutions.bellatrix.web.components.Button;

public class PlayerTabsSection extends TabsSection {

    public PlayerTabsSection(WidgetMap widgetMap) {
        super(widgetMap);
    }

    public Button getInfoTab() {
        return getTabButtons().get(0);
    }

    public Button getStatsTab() {
        return getTabButtons().get(1);
    }

    public Button getMatchesTab() {
        return getTabButtons().get(2);
    }

    public Button getPredictionsTab() {
        return getTabButtons().get(3);
    }
}