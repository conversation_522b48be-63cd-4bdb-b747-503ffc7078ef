package widgets.pages;

import data.configuration.SportalSettings;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.enums.MatchDataElementsStatisticsEnum;
import data.constants.enums.TeamDataElementsStatisticsEnum;
import data.widgets.options.enums.DataPopularListEnum;
import data.widgets.options.enums.DataRoundEnum;
import data.widgets.options.enums.DataTournamentEnum;
import data.widgets.options.enums.DataWidgetSportEnum;
import data.widgets.options.models.*;
import org.openqa.selenium.support.Colors;
import plugins.authentication.Project;
import solutions.bellatrix.core.configuration.ConfigurationService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class WidgetSettingsFactory {

    public static SdkOptions getFootballSdkOptions() {
        return getFootballSdkOptions(DataPopularListEnum.SPORTALIOS);
    }

    public static SdkOptions getFootballSdkOptions(DataPopularListEnum dataCompetition) {
        return SdkOptions.builder()
                .dataConfigApiUrl(ConfigurationService.get(SportalSettings.class).getFootballDataConfigApiUrl())
                .dataConfigMultisportApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigStandingsApiUrl(ConfigurationService.get(SportalSettings.class).getFootballDataConfigStandingsApiUrl())
                .dataConfigPlayoffApiUrl(ConfigurationService.get(SportalSettings.class).getFootballDataConfigPlayoffApiUrl())
                .dataConfigApiKey("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf")
                .dataConfigProject(Project.FULLSETUP.getDomain())
                .dataConfigOddClient(StringConstants.SPORTAL_STRING)
                .dataConfigTimezone(StringConstants.EUROPE_SOFIA_STRING)
                .dataConfigCompetitionList(dataCompetition.getValue())
                .dataConfigLang(Language.ENGLISH.getCode())
                .build();
    }

    public static SdkOptions getBasketballSdkOptions() {
        return SdkOptions.builder()
                .dataConfigApiUrl(ConfigurationService.get(SportalSettings.class).getBasketballDataConfigApiUrl())
                .dataConfigMultisportApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigStandingsApiUrl(ConfigurationService.get(SportalSettings.class).getBasketballDataConfigStandingsApiUrl())
                .dataConfigPlayoffApiUrl(ConfigurationService.get(SportalSettings.class).getBasketballDataConfigPlayoffApiUrl())
                .dataConfigApiKey("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf")
                .dataConfigProject("sportal.bg")
                .dataConfigOddClient(StringConstants.SPORTAL_365_STRING)
                .dataConfigTimezone(StringConstants.EUROPE_SOFIA_STRING)
                .dataConfigCompetitionList(DataPopularListEnum.ALL.getValue())
                .dataConfigLang(Language.ENGLISH.getCode())
                .build();
    }

    public static SdkOptions getTennisSdkOptions() {
        return SdkOptions.builder()
                .dataConfigApiUrl(ConfigurationService.get(SportalSettings.class).getTennisApiUrl())
                .dataConfigMultisportApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigStandingsApiUrl(ConfigurationService.get(SportalSettings.class).getTennisDataConfigStandingsApiUrl())
                .dataConfigPlayoffApiUrl(ConfigurationService.get(SportalSettings.class).getTennisDataConfigPlayoffApiUrl())
                .dataConfigApiKey("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf")
                .dataConfigProject(StringConstants.SPORTAL_365_STRING)
                .dataConfigOddClient(StringConstants.SPORTAL_365_STRING)
                .dataConfigTimezone(StringConstants.EUROPE_SOFIA_STRING)
                .dataConfigCompetitionList(DataPopularListEnum.ALL.getValue())
                .dataConfigLang(Language.ENGLISH.getCode())
                .build();
    }

    public static SdkOptions getIceHockeySdkOptions() {
        return SdkOptions.builder()
                .dataConfigApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigMultisportApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigStandingsApiUrl(ConfigurationService.get(SportalSettings.class).getIceHockeyDataConfigStandingsApiUrl())
                .dataConfigPlayoffApiUrl(ConfigurationService.get(SportalSettings.class).getIceHockeyDataConfigPlayoffApiUrl())
                .dataConfigApiKey("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf")
                .dataConfigProject("sportal.bg")
                .dataConfigOddClient(StringConstants.SPORTAL_365_STRING)
                .dataConfigTimezone(StringConstants.EUROPE_SOFIA_STRING)
                .dataConfigCompetitionList(DataPopularListEnum.MULTI_QA.getValue())
                .dataConfigLang(Language.ENGLISH.getCode())
                .build();
    }

    public static SdkOptions getMultisportSdkOptions() {
        return SdkOptions.builder()
                .dataConfigApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigMultisportApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigApiUrl())
                .dataConfigStandingsApiUrl(ConfigurationService.get(SportalSettings.class).getMultisportDataConfigStandingsApiUrl())
                .dataConfigPlayoffApiUrl(ConfigurationService.get(SportalSettings.class).getIceHockeyDataConfigPlayoffApiUrl())
                .dataConfigApiKey("sportal365:zxTHFqTOP73fm9TKkukzvXhClH05tsUf")
                .dataConfigProject("sportal.bg")
                .dataConfigOddClient(StringConstants.SPORTAL_365_STRING)
                .dataConfigTimezone(StringConstants.EUROPE_SOFIA_STRING)
                .dataConfigCompetitionList(DataPopularListEnum.MULTI_QA.getValue())
                .dataConfigLang(Language.ENGLISH.getCode())
                .build();
    }

    public static DataLabels getTeamProgrammeDataLabels() {
        return DataLabels.builder()
                .labelCurrentSeason("Текущ сезон")
                .labelFixture("Програма")
                .labelResults("Резултати")
                .build();
    }

    public static DataLabels getBasketballTournamentProgrammeDataLabels() {
        return DataLabels.builder()
                .labelFixture("Програма")
                .labelResults("Резултати")
                .round("Рундове")
                .build();
    }

    public static DataLabels getBasketballTournamentProgrammeDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelFixture("Fixtures")
                .labelResults("Results")
                .round("")
                .build();
    }

    public static DataLabels getTennisAthleteProgrammeDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelFixture("FIXTURES")
                .labelResults("RESULTS")
                .round("")
                .build();
    }

    public static DataLabels getTennisTournamentProgrammeDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelFixture("FIXTURES")
                .labelResults("RESULTS")
                .round("")
                .build();
    }

    public static List<DataRoundsFilter> getBasketballTournamentProgrammeDataRoundsFilter() {
        ArrayList<DataRoundsFilter> filter = new ArrayList<>();
        return filter;
    }

    public static List<DataRoundsFilter> getTennisTournamentProgrammeDataRoundsFilter(DataTournamentEnum tournamentId, DataRoundEnum... roundIds) {
        List<DataRoundsFilter> rounds = new ArrayList<>();

        for (DataRoundEnum roundId : roundIds) {
            rounds.add(
                    DataRoundsFilter.builder()
                            .roundId(roundId.getId())
                            .tournamentId(tournamentId.getId())
                            .build()
            );
        }

        return rounds;
    }

    public static DataLabels getRankingDataLabels() {
        return DataLabels.builder()
                .labelNameShort("Име")
                .labelName("ИМЕ")
                .pointsShort("Точки")
                .points("ТОЧКИ")
                .labelNationalityShort("Националност")
                .labelNationality("НАЦИОНАЛНОСТ")
                .showMore("Покажете още")
                .noData("Няма налични данни за класирането")
                .build();
    }

    public static DataLabels getRankingDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelName("Name")
                .pointsShort("Pts")
                .points("Points")
                .labelNationalityShort("Nat")
                .labelNationality("Nationality")
                .showMore("Show more")
                .noData("No ranking data available")
                .build();
    }

    public static DataLabels getStandingsDataLabels() {
        return DataLabels.builder()
                .team("Team")
                .matchesWonShort("MW")
                .pointsShort("P")
                .matchesPlayedShort("MP")
                .matchLossesShort("L")
                .matchDrawsShort("D")
                .goalDifferenceShort("GD")
                .teamFormShort("F")
                .matchesPlayed("MATCHES PLAYED")
                .goalDifference("GOAL DIFFERENCE")
                .points("POINTS")
                .matchesWon("MATCHES WON")
                .matchDraw("MATCH DRAWS")
                .matchLosses("MATCH LOSSES")
                .build();
    }

    public static DataLabels getStandingsDataLabelsDefaultValues() {
        return DataLabels.builder()
                .team("TEAM")
                .matchesWonShort("W")
                .pointsShort("P")
                .matchesPlayedShort("M")
                .matchLossesShort("L")
                .matchDrawsShort("D")
                .goalDifferenceShort("GD")
                .teamFormShort("TEAM FORM")
                .matchesPlayed("MATCHES PLAYED")
                .goalDifference("GOAL DIFFERENCE")
                .points("POINTS")
                .matchesWon("MATCHES WON")
                .matchDraw("MATCH DRAWS")
                .matchLosses("MATCH LOSSES")
                .build();
    }

    public static DataLabels getBasketballStandingsDataLabels() {
        return DataLabels.builder()
                .team("Team")
                .matchesWonShort("MW")
                .matchesWon("MATCHES WON")
                .pointsShort("P")
                .points("POINTS")
                .matchesPlayedShort("MP")
                .matchesPlayed("MATCHES PLAYED")
                .matchLossesShort("L")
                .matchLosses("MATCH LOSSES")
                .pointsDifferenceShort("PD")
                .pointsDifference("POINTS DIFFERENCE")
                .percentagesShort("PCT")
                .percentages("PERCENTAGES")
                .teamForm("FORM")
                .teamFormShort("FORM")
                .prevStageBtn("PREVIOUS STAGE")
                .nextStageBtn("NEXT STAGE")
                .noGames("Липсват данни за избраният плейоф")
                .build();
    }

    public static DataLabels getBasketballStandingsDataLabelsDefaultValues() {
        return DataLabels.builder()
                .team("Team")
                .matchesWonShort("W")
                .matchesWon("Wins")
                .matchesPlayedShort("MP")
                .matchesPlayed("Matches Played")
                .matchLossesShort("L")
                .matchLosses("Losses")
                .pointsDifferenceShort("PD")
                .pointsDifference("Points Difference")
                .percentagesShort("PCT")
                .percentages("Percentage")
                .teamForm("FORM")
                .teamFormShort("FORM")
                .prevStageBtn("Previous stage")
                .nextStageBtn("Next stage")
                .noGames("No games for the selected playoff")
                .build();
    }

    public static DataLabels getIceHockeyStandingsDataLabels() {
        return DataLabels.builder()
                .team("Team")
                .matchesPlayedShort("MP")
                .matchesPlayed("MATCHES PLAYED")
                .matchesWonShort("MW")
                .matchesWon("MATCHES WON")
                .matchesWonOvertimeShort("WO")
                .matchesWonOvertime("MATCHES WON OVERTIME")
                .matchLossesShort("L")
                .matchLosses("MATCH LOSSES")
                .matchesLossesOvertimeShort("LO")
                .matchesLossesOvertime("MATCHES LOST OVERTIME")
                .winAfterPenaltyShort("WAP")
                .winAfterPenalty("WINS AFTER PENALTY")
                .lossAfterPenaltyShort("LAP")
                .lossAfterPenalty("LOSSES AFTER PENALTY")
                .goalsDifferenceShort("GD")
                .goalsDifference("GOALS DIFFERENCE")
                .pointsShort("P")
                .points("POINTS")
                .teamFormShort("FORM")
                .teamForm("TEAM FORM")
                .pointsDifferenceShort("PD")
                .pointsDifference("POINTS DIFFERENCE")
                .percentagesShort("PCT")
                .percentages("PERCENTAGES")
                .noData("No standings data available")
                .teamForm("FORM")
                .teamFormShort("FORM")
                .prevStageBtn("PREVIOUS STAGE")
                .nextStageBtn("NEXT STAGE")
                .noGames("Липсват данни за избраният плейоф")
                .build();
    }

    public static DataLabels getIceHockeyStandingsDataLabelsDefaultValues() {
        return DataLabels.builder()
                .team("Team")
                .matchesWonShort("W")
                .matchesWon("Wins")
                .pointsShort("PTS")
                .points("Points")
                .matchesPlayedShort("MP")
                .matchesPlayed("Matches Played")
                .matchLossesShort("L")
                .matchLosses("Losses")
                .pointsDifferenceShort("PD")
                .pointsDifference("Points Difference")
                .percentagesShort("PCT")
                .percentages("Percentages")
                .teamFormShort("FORM")
                .teamForm("Team Form")
                .winAfterPenaltyShort("WAP")
                .winAfterPenalty("Wins After Penalty")
                .lossAfterPenaltyShort("LAP")
                .lossAfterPenalty("Losses After Penalty")
                .goalsDifferenceShort("GD")
                .goalsDifference("Goals Difference")
                .matchesLossesOvertimeShort("LO")
                .matchesLossesOvertime("Losses Overtime")
                .matchesWonOvertimeShort("WO")
                .matchesWonOvertime("Wins Overtime")
                .teamForm("FORM")
                .teamFormShort("FORM")
                .prevStageBtn("Previous stage")
                .nextStageBtn("Next stage")
                .noData("No standings data available")
                .noGames("No games for the selected playoff")
                .build();
    }

    public static DataLabels getFootballLineupsDataLabelsDefaultValues() {
        return DataLabels.builder()
                .firstTeam("First team")
                .substitutes("Substitutes")
                .noData("No data to display")
                .build();
    }

    public static ArrayList<String> getFootballLineupsDataLabels() {
        var info = new ArrayList<String>();
        info.add("no_data");
        return info;
    }

    public static DataLabels getLivescoreDataLabels() {
        return DataLabels.builder()
                .all("Всички")
                .popular("Популярни")
                .upcoming("Предстоящи")
                .live("На Живо")
                .finished("Приключили")
                .odds("С коефициенти")
                .noGames("Няма игри, отговарящи на тези критерии")
                .build();
    }

    public static DataLabels getLivescoreDataLabelsDefaultValues() {
        return DataLabels.builder()
                .all("ALL")
                .popular("POPULAR")
                .upcoming("UPCOMING")
                .live("LIVE")
                .finished("FINISHED")
                .odds("ODDS")
                .noGames("No games to show")
                .build();
    }

    public static DataLabels getTennisLivescoreDataLabels() {
        return getLivescoreDataLabels().builder()
                .noMatches("Няма игри, отговарящи на тези критерии")
                .build();
    }

    public static DataLabels getPlayerDataLabels() {
        return DataLabels.builder()
                .playerBio("Player bio")
                .goals("Goals")
                .minutes("Minutes")
                .position("Position")
                .shirtNumber("Shirt number")
                .dateOfBirth("Age")
                .height("Height")
                .weight("Weight")
                .playedShort("Played")
                .shots("Shots")
                .assistsShort("Assists")
                .minutes("Minutes")
                .foulsCommitted("Fouls committed")
                .shots("Shots")
                .shotsOnTarget("Shots on target")
                .redCardsShort("Red cards")
                .yellowCardsShort("Yellow Cards")
                // Match outcomes
                .winShort("W")
                .winOvertimeShort("WO")
                .winAfterPenaltyShort("WAP")
                .drawShort("D")
                .lossShort("L")
                .lossOvertimeShort("LO")
                .lossAfterPenaltyShort("LAP")
                .build();
    }

    public static DataLabels getFootballOddsDefaultDataLabels() {
        return DataLabels.builder()
                ._1x2("1X2")
                .firstHalf("First half")
                .secondHalf("Second half")
                .overUnder("Over/Under")
                .doubleChance("Double chance")
                .bothToScore("Both to score")
                .drawNoBet("Draw no bet")
                .firstTeamToScore("First team to score")
                .correctScore("Correct score")
                .firstHalfGoals("First half goals")
                .firstPlayerToScore("First player to score")
                .playerToScoreDuringGame("Player to score during game")
                .playerToReceiveCard("Player to receive card")
                .firstHalfAndFinalResult("First half and final result")
                .over("Over")
                .under("Under")
                .yes(StringConstants.YES_STRING)
                .no(StringConstants.NO_STRING)
                .build();
    }

    public static DataLabels getFootballOddsDataLabels() {
        return DataLabels.builder()
                ._1x2("1X2")
                .firstHalf("Първо полувреме")
                .secondHalf("Второ полувреме")
                .overUnder("Над/Под")
                .doubleChance("Двоен шанс")
                .bothToScore("Двата отбора да отбележат")
                .drawNoBet("Без равен")
                .firstTeamToScore("Първи отбор който ще отбележи")
                .correctScore("Точен резултат")
                .firstHalfGoals("Голове първо полувреме")
                .firstPlayerToScore("Първи играч който ще отбележи")
                .playerToScoreDuringGame("Играч да отбележи през мача")
                .playerToReceiveCard("Играч да получи картон")
                .firstHalfAndFinalResult("Първо полувреме и краен резултат")
                .over("Над")
                .under("Под")
                .yes("Да")
                .no("Не")
                .build();
    }

    public static DataLabels getPlayerDataLabelsDefaultValues() {
        return DataLabels.builder()
                .playerBio("Player bio")
                .goals("Goals")
                .minutes("Minutes")
                .position("Position")
                .shirtNumber("Shirt number")
                .dateOfBirth("Age")
                .height("Height")
                .weight("Weight")
                .playedShort("Played")
                .assistsShort("Assists")
                .foulsCommitted("Fouls committed")
                .shots("Shots")
                .shotsOnTarget("Shots on target")
                .redCardsShort("Red cards")
                .yellowCardsShort("Yellow cards")
                .build();
    }

    public static DataLabels getDecoratedPlayerDataLabels() {
        return DataLabels.builder()
                .labelName("Играч")
                .firstYellowCardsShort("FYC")
                .firstYellowCards("Първи жълти")
                .redCardsShort("RC")
                .redCards("Червени")
                .totalCardsShort("TC")
                .totalCards("Всички")
                .yellowCardsShort("YC")
                .yellowCards("Жълти")
                .build();
    }

    public static DataLabels getDecoratedPlayerDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelNameShort("Name")
                .firstYellowCardsShort("FYC")
                .firstYellowCards("First yellow cards")
                .redCardsShort("RC")
                .redCards("Red cards")
                .totalCardsShort("TC")
                .totalCards("Total cards")
                .yellowCardsShort("YC")
                .yellowCards("Yellow cards")
                .build();
    }

    public static DataLabels getTopScorersDataLabels() {
        return DataLabels.builder()
                .assistsShort("ASS")
                .assists("Асистенции")
                .goalsShort("G")
                .goals("Голове")
                .redCardsShort("RC")
                .redCards("Червени картони")
                .penaltiesShort("P")
                .penalties("Фалове")
                .labelName("Играч")
                .yellowCardsShort("YC")
                .yellowCards("Жълти картони")
                .playedShort("PL")
                .played("Играл")
                .minutesShort("M")
                .minutes("Минути")
                .scoredFirstShort("SF")
                .scoredFirst("Отбелязал първи")
                .missedPenaltiesShort("MP")
                .missedPenalties("Пропунати дузпи")
                .build();
    }

    public static DataLabels getTopScorersDataLabelsDefaultValues() {
        return DataLabels.builder()
                .assistsShort("ASS")
                .assists("Assists")
                .goalsShort("G")
                .goals("Goals")
                .redCardsShort("RC")
                .redCards("Red cards")
                .penaltiesShort("P")
                .penalties("Penalties")
                .labelNameShort("Name")
                .yellowCardsShort("YC")
                .yellowCards("Yellow cards")
                .playedShort("PL")
                .played("Played")
                .minutesShort("M")
                .minutes("Minutes")
                .scoredFirstShort("SF")
                .scoredFirst("Scored first")
                .missedPenaltiesShort("MP")
                .missedPenalties("Missed penalties")
                .build();
    }

    public static DataLabels getPlayerH2HDataTabsInfoLabels() {
        return DataLabels.builder()
                .vs("VS.")
                .played("Играл")
                .minutes("Минути")
                .goals("Голове")
                .assists("Асистенции")
                .yellowCards("Жълти картони")
                .redCards("Червени картони")
                .foulsCommitted("Фалове")
                .shotsOnTarget("Изстрели в целта")
                .shots("Изстрели")
                .build();
    }

    public static DataLabels getPlayerH2HDataTabsInfoLabelsDefaultValues() {
        return DataLabels.builder()
                .vs("VS.")
                .assistsShort("Assists")
                .cleanSheetsShort("Cleansheets")
                .foulsCommitted("Fouls committed")
                .goalsShort("Goals")
                .goalsSubstitute("Goals substitute")
                .minutesShort("Minutes")
                .minutesSubstituteShort("Minutes substitute")
                .offsides("Offsides")
                .ownGoals("Own goals")
                .penaltiesCommitted("Penalties committed")
                .penaltiesMissed("Penalties missed")
                .penaltiesReceived("Penalties received")
                .penaltiesSaved("Penalties saved")
                .penaltyGoals("Penalty goals")
                .playedShort("Played")
                .redCardsShort("Red cards")
                .shots("Shots")
                .shotsOnTarget("Shots on target")
                .startedShort("Started")
                .substituteIn("Substitute in")
                .substituteOut("Substitute out")
                .yellowCardsShort("Yellow cards")
                .build();
    }

    public static DataLabels getTeamH2HDataTabsInfoLabels() {
        return DataLabels.builder()
                .defeats(TeamDataElementsStatisticsEnum.DEFEATS.getValue())
                .draw(TeamDataElementsStatisticsEnum.DRAW.getValue())
                .goalsConceded(TeamDataElementsStatisticsEnum.GOALS_CONCEDED.getValue())
                .goalsScored(TeamDataElementsStatisticsEnum.GOALS_SCORED.getValue())
                .playedShort(TeamDataElementsStatisticsEnum.PLAYED.getValue())
                .win(TeamDataElementsStatisticsEnum.WIN.getValue())
                .pointsShort(TeamDataElementsStatisticsEnum.POINTS.getValue())
                .rank(TeamDataElementsStatisticsEnum.RANK.getValue())
                .build();
    }

    public static DataLabels getTeamH2HMatchDataTabsInfoLabels() {
        return DataLabels.builder()
                .corners(MatchDataElementsStatisticsEnum.CORNERS.getDisplayValue())
                .counterAttacks(MatchDataElementsStatisticsEnum.COUNTER_ATTACKS.getDisplayValue())
                .crosses(MatchDataElementsStatisticsEnum.CROSSES.getDisplayValue())
                .foulsCommitted(MatchDataElementsStatisticsEnum.FOULS_COMMITTED.getDisplayValue())
                .goalKicks(MatchDataElementsStatisticsEnum.GOAL_KICKS.getDisplayValue())
                .goals(MatchDataElementsStatisticsEnum.GOALS.getDisplayValue())
                .offside(MatchDataElementsStatisticsEnum.OFFSIDE.getDisplayValue())
                .possession(MatchDataElementsStatisticsEnum.POSSESSION.getDisplayValue())
                .redCards(MatchDataElementsStatisticsEnum.RED_CARDS.getDisplayValue())
                .shotsBlocked(MatchDataElementsStatisticsEnum.SHOTS_BLOCKED.getDisplayValue())
                .shotsOff(MatchDataElementsStatisticsEnum.SHOTS_OFF.getDisplayValue())
                .shotsOn(MatchDataElementsStatisticsEnum.SHOTS_ON.getDisplayValue())
                .substitutions(MatchDataElementsStatisticsEnum.SUBSTITUTIONS.getDisplayValue())
                .throwIn(MatchDataElementsStatisticsEnum.THROW_IN.getDisplayValue())
                .treatments(MatchDataElementsStatisticsEnum.TREATMENTS.getDisplayValue())
                .yellowCards(MatchDataElementsStatisticsEnum.YELLOW_CARDS.getDisplayValue())
                .build();
    }

    public static DataLabels getTeamH2HDataTabsInfoLabelsDefaultValues() {
        return DataLabels.builder()
                .odds("Odds")
                .defeats("Defeats")
                .draw("Draw")
                .goalsConceded("Goals conceded")
                .goalsScored("Goals scored")
                .playedShort("Played")
                .win("Win")
                .pointsShort("Points")
                .rank("Rank")
                .build();
    }

    public static DataLabels getMatchesH2HDataLabels() {
        return DataLabels.builder()
                .h2h("Д/С")
                .w("П")
                .l("З")
                .d("Р")
                .build();
    }

    public static DataLabels getMatchesH2HDataLabelsDefaultValues() {
        return DataLabels.builder()
                .h2h("H2H")
                .w("W")
                .l("L")
                .d("D")
                .build();
    }

    public static DataLabels getTeamDataLabels() {
        return DataLabels.builder()
                .est("Est")
                .playedShort("Played")
                .win("Wins")
                .draw("Draws")
                .defeats("Defeats")
                .goalsConceded("Goals Conceded")
                .goalsScored("Goals Scored")
                .pointsShort("Points")
                .rank("Rank")
                .defeats("Defeats")
                .build();
    }

    public static DataElements getUpdatedTeamDataLabels() {
        List<String> stats = new ArrayList<>();
        stats.add("rank");
        stats.add("points");
        stats.add("win");
        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataLabels getMatchCenterLabels() {
        return DataLabels.builder()
                ._1x2("1X2")
                .firstHalf("Първо полувреме")
                .secondHalf("Второ полувреме")
                .overUnder("Под/Над")
                .doubleChance("Двоен шанс")
                .bothToScore("Двата отбора да вкарат")
                .drawNoBet("Равенство без залог")
                .firstTeamToScore("Първия отбор да вкара")
                .correctScore("Точен резултат")
                .firstHalfGoals("Голове в първото полувреме")
                .firstPlayerToScore("Първи играч да вкара")
                .playerToScoreDuringGame("Играч да вкара по време на мача")
                .playerToReceiveCard("Играч да получи картон")
                .firstHalfAndFinalResult("Първо полувреме и краен резултат")
                .over("Над")
                .under("Под")
                .yes("Да")
                .no("не")
                .fuWinQuestion("Кой ще спечели мача?")
                .fuDraw("Равенство")
                .fuPlayFree("fu")
                .round("Рунд")
                .inRegularTime("В редовното време")
                .afterPenalties("След дузпи")
                .aggregate("Агр.")
                .firstTeam("Стартови състави")
                .substitutes("Резерви")
                .substitutedPlayers("Сменени играчи")
                .formation("формация")
                .noData("Няма данни")
                .defeats("Загуби")
                .draw("Равенства")
                .goalsConceded("Получени голове")
                .goalsScored("Вкарани голове")
                .playedShort("Играни")
                .win("Победи")
                .pointsShort("Точки")
                .rank("Ранг")
                .team("Отбор")
                .matchesWonShort("П")
                .matchesPlayedShort("М")
                .matchLossesShort("З")
                .matchDrawsShort("Р")
                .goalDifferenceShort("ГР")
                .teamFormShort("Ф")
                .teamForm("Форма на отбора")
                .matchesPlayed("ИГРАНИ МАЧОВЕ")
                .goalDifference("ГОЛОВА РАЗЛИКА")
                .points("ТОЧКИ")
                .matchesWon("СПЕЧЕЛЕНИ МАЧОВЕ")
                .matchDraw("РАВЕНСТВА")
                .matchLosses("ЗАГУБИ")
                .footerWin("Победа")
                .footerLoss("Загуба")
                .footerDraw("Равенство")
                .onFor("влиза за")
                .assistBy("асистенция от")
                .halfTime("1во полувреме")
                .finishedAfterExtraTime("завършил след продължения")
                .finishedAfterPenalties("завършил след дузпи")
                .firstHalfTimeFinish("1во полувреме прикл.")
                .secondHalfTimeFinish("2ро полувреме прикл.")
                .gameStarts("начало на мача")
                .noEventsYet("още няма събития")
                .minutesShort("мин")
                .matchInformation("Информация за мача")
                .matchInfoTime("Дата и час")
                .matchInfoVenue("Стадион")
                .matchInfoReferee("Рефер")
                .matchInfoSpectators("Посещаемост")
                .matchInfoCompetition("Турнир")
                .matchInformationTab("Информация")
                .matchTimelineTab("Детайли")
                .matchStatisticsTab("Статистики")
                .matchStandingsTab("Класиране")
                .matchPlayoffTab("Плейофи")
                .matchH2hTab("Един срещу друг")
                .matchOddsTab("Коефициенти")
                .build();
    }

    public static DataLabels getMatchCenterDefaultLabels() {
        return DataLabels.builder()
                ._1x2("1X2")
                .firstHalf("1st half-time")
                .secondHalf("2nd half-time")
                .overUnder("Под/Над")
                .doubleChance("Двоен шанс")
                .bothToScore("Двата отбора да вкарат")
                .drawNoBet("Равенство без залог")
                .firstTeamToScore("Първия отбор да вкара")
                .correctScore("Точен резултат")
                .firstHalfGoals("Голове в първото полувреме")
                .firstPlayerToScore("Първи играч да вкара")
                .playerToScoreDuringGame("Играч да вкара по време на мача")
                .playerToReceiveCard("Играч да получи картон")
                .firstHalfAndFinalResult("Първо полувреме и краен резултат")
                .over("Над")
                .under("Под")
                .yes("Да")
                .no("не")
                .fuWinQuestion("Кой ще спечели мача?")
                .fuDraw("Равенство")
                .fuPlayFree("fu")
                .round("Рунд")
                .inRegularTime("В редовното време")
                .afterPenalties("След дузпи")
                .aggregate("Агр.")
                .firstTeam("Starting lineups")
                .substitutes("Substitutes")
                .substitutedPlayers("Substituted Players")
                .formation("Formations")
                .noData("Няма данни")
                .defeats("Загуби")
                .draw("Равенства")
                .goalsConceded("Получени голове")
                .goalsScored("Вкарани голове")
                .played("Играни")
                .win("Победи")
                .points("Точки")
                .rank("Ранг")
                .team("Отбор")
                .matchesWon("П")
                .matchesPlayed("М")
                .matchLosses("З")
                .matchDrawsShort("Р")
                .goalDifference("ГР")
                .teamForm("Ф")
                .teamForm("Форма на отбора")
                .matchesPlayed("ИГРАНИ МАЧОВЕ")
                .goalDifference("ГОЛОВА РАЗЛИКА")
                .points("ТОЧКИ")
                .matchesWon("СПЕЧЕЛЕНИ МАЧОВЕ")
                .matchDraw("РАВЕНСТВА")
                .matchLosses("ЗАГУБИ")
                .footerWin("Победа")
                .footerLoss("Загуба")
                .footerDraw("Равенство")
                .onFor("влиза за")
                .assistBy("асистенция от")
                .halfTime("1st half-time")
                .finishedAfterExtraTime("завършил след продължения")
                .finishedAfterPenalties("завършил след дузпи")
                .firstHalfTimeFinish("1st half-time")
                .secondHalfTimeFinish("2nd half-time")
                .gameStarts("начало на мача")
                .noEventsYet("още няма събития")
                .minutes("мин")
                .matchInformation("Информация за мача")
                .matchInfoTime("Date and time")
                .matchInfoVenue("Venue")
                .matchInfoReferee("Referee")
                .matchInfoSpectators("Attendance")
                .matchInfoCompetition("Competition")
                .matchInformationTab("Information")
                .matchTimelineTab("Timeline")
                .matchStatisticsTab("Statistics")
                .matchStandingsTab("Standings")
                .matchPlayoffTab("Playoff")
                .matchH2hTab("H2H")
                .build();
    }

    public static DataLabels getTeamDataLabelsDefaultValues() {
        return DataLabels.builder()
                .est("Est")
                .win("Win")
                .draw("Draw")
                .playedShort("Played")
                .goalsConceded("Goal conceded")
                .goalsScored("Goals scored")
                .rank("Rank")
                .defeats("Defeats")
                .build();
    }

    public static DataLabels getTeamProgrammeDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelCurrentSeason("Current Season")
                .labelFixture("Fixtures")
                .labelResults("Results")
                .build();
    }

    public static DataLabels getTournamentProgrammeDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelCurrentSeason("Current Season")
                .labelFixture("Fixtures")
                .labelResults("Results")
                .build();
    }

    public static DataLabels getTeamSquadDataLabels() {
        return DataLabels.builder()
                .labelName("Name")
                .ageShort("Y")
                .age("Years")
                .matchesPlayedShort("Matches played")
                .matchesPlayed("Matches played")
                .goalsShort("Goals scored")
                .goals("Goals scored")
                .assistsShort("Assists")
                .assists("Assists provided")
                .yellowCardsShort("Yellow cards")
                .yellowCards("Yellow cards received")
                .redCardsShort("Red cards")
                .redCards("Red cards received")
                .concededShort("Goals conceded")
                .conceded("Goals conceded all")
                .cleanSheetsShort("CS")
                .cleanSheets("Clean sheets")
                .minutesSubstituteShort("Min as sub")
                .minutesSubstitute("Minutes as substitute")
                .minutesShort("Min")
                .minutes("Minutes")
                .startedShort("Started")
                .started("Started time")
                .build();
    }

    public static DataLabels getTeamSquadDataLabelsDefaultValues() {
        return DataLabels.builder()
                .labelNameShort("NAME")
                .ageShort("AGE")
                .age("AGE")
                .matchesPlayedShort("P")
                .matchesPlayed("MATCHES PLAYED")
                .goalsShort("G")
                .goals("GOALS")
                .assistsShort("A")
                .assists("ASSISTS")
                .yellowCardsShort("YC")
                .yellowCards("YELLOW CARDS")
                .redCardsShort("RC")
                .redCards("RED CARDS")
                .build();
    }

    public static DataLabels getMultisportDataLabels() {
        return DataLabels.builder()
                .topEvents("Top Events")
                .finalLabel("Final")
                .winner("Winner")
                .football("Football")
                .basketball("Basketball")
                .tennis("Tennis")
                .cycling("Cycling")
                .handball("Handball")
                .iceHockey("Ice Hockey")
                .motorsports("Motorsports")
                .rugbyLeague("Rugby")
                .cricket("Cricket")
                .rollerHockey("Roller Hockey")
                .ageRestriction("WHAT DOES GAMBLING COST YOU? STOP ON TIME.")
                .build();
    }

    public static ArrayList<String> getPlayerH2HDataElements() {
        var info = new ArrayList<String>();
        info.add("played");
        info.add("minutes");
        info.add("assists");
        info.add("yellow_cards");
        info.add("red_cards");
        return info;
    }

    public static ArrayList<String> getTeamH2HDataElements() {
        var info = new ArrayList<String>();
        info.add(TeamDataElementsStatisticsEnum.DEFEATS.getLabel());
        info.add(TeamDataElementsStatisticsEnum.DRAW.getLabel());
        info.add(TeamDataElementsStatisticsEnum.GOALS_CONCEDED.getLabel());
        info.add(TeamDataElementsStatisticsEnum.GOALS_SCORED.getLabel());
        info.add(TeamDataElementsStatisticsEnum.PLAYED.getLabel());
        info.add(TeamDataElementsStatisticsEnum.WIN.getLabel());
        info.add(TeamDataElementsStatisticsEnum.POINTS.getLabel());
        info.add(TeamDataElementsStatisticsEnum.RANK.getLabel());
        return info;
    }

    public static ArrayList<String> getTeamH2HMatchDataElements() {
        var info = new ArrayList<String>();
        info.add(MatchDataElementsStatisticsEnum.CORNERS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.COUNTER_ATTACKS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.CROSSES.getLabel());
        info.add(MatchDataElementsStatisticsEnum.FOULS_COMMITTED.getLabel());
        info.add(MatchDataElementsStatisticsEnum.GOAL_KICKS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.GOALS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.OFFSIDE.getLabel());
        info.add(MatchDataElementsStatisticsEnum.POSSESSION.getLabel());
        info.add(MatchDataElementsStatisticsEnum.RED_CARDS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.YELLOW_CARDS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.SHOTS_BLOCKED.getLabel());
        info.add(MatchDataElementsStatisticsEnum.SHOTS_OFF.getLabel());
        info.add(MatchDataElementsStatisticsEnum.SHOTS_ON.getLabel());
        info.add(MatchDataElementsStatisticsEnum.SUBSTITUTIONS.getLabel());
        info.add(MatchDataElementsStatisticsEnum.THROW_IN.getLabel());
        info.add(MatchDataElementsStatisticsEnum.TREATMENTS.getLabel());
        return info;
    }

    public static DataLabels getFootballStakesDataElements(String amount) {
        String formattedDescription = String.format("With a €%s bet ", amount).trim();
        return DataLabels.builder()
                .stakesAmountDescription(formattedDescription)
                .stakesBet("Bet now")
                .stakesBetOnTeam("Bet on your team to win!")
                .stakesCurrency("€")
                .stakesPayout("Payout")
                .stakesStake("Stake")
                .vs("VS")
                .build();
    }

    public static ArrayList<String> getMostDecoratedPlayersDataElements() {
        var info = new ArrayList<String>();
        info.add("first_yellow_cards");
        info.add("red_cards");
        info.add("total_cards");
        info.add("yellow_cards");
        return info;
    }

    public static ArrayList<String> getTopScorersDataElements() {
        var info = new ArrayList<String>();
        info.add("missed_penalties");
        info.add("assists");
        info.add("goals");
        info.add("minutes");
        info.add("penalties");
        info.add("played");
        info.add("red_cards");
        info.add("scored_first");
        info.add("yellow_cards");
        return info;
    }

    public static ArrayList<String> getTopScorersMinimumDataElements() {
        var info = new ArrayList<String>();
        info.add("missed_penalties");
        return info;
    }

    public static List<String> getDataHighlightEntities() {
        return Arrays.asList(
                "1341",
                "633",
                "16304",
                "15624",
                "177971"
        );
    }

    public static ArrayList<String> getTennisRankingDataHighlightEntities() {
        var info = new ArrayList<String>();
        info.add("bde1c724-a2d7-4560-a74e-339463a1f5db");
        return info;
    }

    public static ArrayList<String> getTopScorersDataHighlightEntities() {
        var info = new ArrayList<String>();
        info.add("931");
        return info;
    }

    public static ArrayList<String> getDataTeamIds(String... ids) {
        var info = new ArrayList<String>(Arrays.asList(ids));
        return info;
    }

    public static DataElements getPlayerDataElements() {
        ArrayList<String> stats = new ArrayList<>();
        stats.add("played");
        stats.add("goals");
        stats.add("shots");
        stats.add("assists");
        stats.add("minutes");
        stats.add("yellow_cards");

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataElements getPlayerDataElementsMinimalSetup() {
        ArrayList<String> stats = new ArrayList<>();
        stats.add("played");

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataElements getUpdatedRedCardsPlayerDataElements() {
        ArrayList<String> stats = new ArrayList<>();
        stats.add("played");
        stats.add("goals");
        stats.add("shots");
        stats.add("assists");
        stats.add("minutes");
        stats.add("red_cards");

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataElements getUpdatedShootOnTargetPlayerDataElements() {
        ArrayList<String> stats = new ArrayList<>();
        stats.add("played");
        stats.add("goals");
        stats.add("shots");
        stats.add("assists");
        stats.add("minutes");
        stats.add("shots_on_target");

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataElements getTeamDataElements() {
        DataElements teamDefaultDataElements = getTeamDefaultDataElements();
        List<String> stats = teamDefaultDataElements.getStats();
        stats.add(TeamDataElementsStatisticsEnum.POINTS.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.RANK.getLabel());

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataElements getTeamDefaultDataElements() {
        List<String> stats = new ArrayList<>();
        stats.add(TeamDataElementsStatisticsEnum.PLAYED.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.WIN.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.DRAW.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.DEFEATS.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.GOALS_SCORED.getLabel());
        stats.add(TeamDataElementsStatisticsEnum.GOALS_CONCEDED.getLabel());

        return DataElements.builder()
                .stats(stats)
                .build();
    }

    public static DataEntityLinks getDataEntityLinks(DataWidgetSportEnum sport) {
        return DataEntityLinks.builder()
                .team(
                        Team.builder()
                                .url(String.format("https://dev.sportal.bg/%s/team-{teamId}", sport.getValue()))
                                .build())
                .competition(
                        Competition.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}", sport.getValue()))
                                .build())
                .standings(
                        Standings.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}#standings", sport.getValue()))
                                .build())
                .player(
                        Player.builder()
                                .url(String.format("https://dev.sportal.bg/%s/player-{playerId}", sport.getValue()))
                                .build())
                .coach(
                        Coach.builder()
                                .url(String.format("https://dev.sportal.bg/%s/coach-{coachId}", sport.getValue()))
                                .build())
                .match(
                        Match.builder()
                                .url(String.format("https://dev.sportal.bg/%s/match-{teamId}-{teamId}#{matchId}", sport.getValue()))
                                .sort(
                                        Sort.builder()
                                                .criteria("alphabetically")
                                                .direction("asc")
                                                .build())
                                .build())
                .configuration(
                        Configuration.builder()
                                .newWindow(true)
                                .build())
                .build();
    }

    public static DataEntityLinks getTennisEntityLinks(DataWidgetSportEnum sport) {
        return DataEntityLinks.builder()
                .team(
                        Team.builder()
                                .url(String.format("https://dev.sportal.bg/%s/team-{teamId}", sport.getValue()))
                                .build())
                .competition(
                        Competition.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}", sport.getValue()))
                                .build())
                .standings(
                        Standings.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}#standings", sport.getValue()))
                                .build())
                .player(
                        Player.builder()
                                .url(String.format("https://dev.sportal.bg/%s/player-{playerId}", sport.getValue()))
                                .build())
                .match(
                        Match.builder()
                                .url(String.format("https://dev.sportal.bg/%s/match-{teamId}-{teamId}#{matchId}", sport.getValue()))
                                .sort(
                                        Sort.builder()
                                                .criteria("alphabetically")
                                                .direction("asc")
                                                .build())
                                .build())
                .configuration(
                        Configuration.builder()
                                .newWindow(true)
                                .build())
                .build();
    }

    public static DataEntityLinks getMultisportEntityLinks(DataWidgetSportEnum sport) {
        return DataEntityLinks.builder()
                .team(
                        Team.builder()
                                .url(String.format("https://dev.sportal.bg/%s/team-{teamId}", sport.getValue()))
                                .build())
                .competition(
                        Competition.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}", sport.getValue()))
                                .build())
                .standings(
                        Standings.builder()
                                .url(String.format("https://dev.sportal.bg/%s/league-{competitionId}#standings", sport.getValue()))
                                .build())
                .player(
                        Player.builder()
                                .url(String.format("https://dev.sportal.bg/%s/player-{playerId}", sport.getValue()))
                                .build())
                .match(
                        Match.builder()
                                .url(String.format("https://dev.sportal.bg/%s/match-{teamId}-{teamId}#{matchId}", sport.getValue()))
                                .sort(
                                        Sort.builder()
                                                .criteria("alphabetically")
                                                .direction("asc")
                                                .build())
                                .build())
                .configuration(
                        Configuration.builder()
                                .newWindow(true)
                                .build())
                .build();
    }

    public static Themes getDefaultThemeOptions() {
        return Themes.builder()
                .dark(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add colors
                                .build())
                        .build())
                .light(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add colors
                                .build())
                        .build())
                .client(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add colors
                                .primaryBackgroundColor(Colors.DARKRED.getColorValue().asHex())
                                .surfaceBackgroundColor(Colors.DARKRED.getColorValue().asHex())
                                .sportEntityContainerPrimaryBgColor(Colors.DARKRED.getColorValue().asHex())
                                .rowBackgroundColor(Colors.DARKRED.getColorValue().asHex())
                                .build())
                        .build())
                .build();
    }
}