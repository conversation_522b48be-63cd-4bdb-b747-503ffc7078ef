package widgets.pages.icehockey.livescorewidgetpage;

import org.junit.jupiter.api.Assertions;
import solutions.bellatrix.web.pages.PageAsserts;

public class Asserts extends PageAsserts<Map> {

    public Asserts assertDisplayedTournamentsCount(int expectedTournamentsCount, int actualTournamentsCount) {
        Assertions.assertEquals(
                expectedTournamentsCount,
                actualTournamentsCount,
                "Ice Hockey livescore widget displays incorrect number of tournaments. Expected %s but found %s"
                        .formatted(expectedTournamentsCount, actualTournamentsCount)
        );
        return this;
    }
}