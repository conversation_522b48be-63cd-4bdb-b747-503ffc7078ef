package widgets.pages.icehockey.livescorewidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataDate;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class IceHockeyLivescoreWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/ice-hockey-livescore-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getIceHockeyWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/multi-sport/events";
    }

    public IceHockeyLivescoreWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.ICE_HOCKEY));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.ICE_HOCKEY_LIVESCORE));
        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataPopularListAttribute(DataPopularListEnum.SPORTAL_POPULAR));
        attributesList.add(new DataDateAttribute(
                DataDate.builder()
                        .date("22-01-2025")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
        attributesList.add(new DataRefreshTimeAttribute(DataRefreshTimeEnum.FAST));
        attributesList.add(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.ALL));
        attributesList.add(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOptionsDisplayAttribute(BooleanEnum.TRUE));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(tennisWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .iceHockeyWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getIceHockeySdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.FAST.getValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.ALL.getValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataTheme(DataThemeEnum.DARK.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getLivescoreDataLabels())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.ICE_HOCKEY))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}