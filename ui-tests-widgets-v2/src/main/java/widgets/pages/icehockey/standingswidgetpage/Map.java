package widgets.pages.icehockey.standingswidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Image;
import widgets.molecules.knockoutgame.basketball.KnockoutGamesSection;
import widgets.molecules.seasonselect.basketball.SeasonSelectSection;
import widgets.molecules.standingstable.IceHockeyStandingsTableSection;
import widgets.molecules.tabpills.IceHockeyStandingsTabPillSection;

import java.util.List;

@Getter
public class Map extends WidgetMap {

    private final IceHockeyStandingsTabPillSection dataHeaderPills = new IceHockeyStandingsTabPillSection(this);
    private final IceHockeyStandingsTableSection tableSection = new IceHockeyStandingsTableSection(this);
    private final KnockoutGamesSection knockoutSection = new KnockoutGamesSection(this);
    private final SeasonSelectSection seasonSelect = new SeasonSelectSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='ice-hockey-standings']");
    }

    public Anchor tournament() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='header-information-container']");
    }

    public Image competitionLogo() {
        return widgetWrapper().shadowRootCreateByCss(Image.class, "img[class*='header-information-image']");
    }

    public List<Image> teamLogos() {
        return widgetWrapper().shadowRootCreateAllByCss(Image.class, "img[class*='cell-TEAM-image']");
    }

    public List<Anchor> teamAnchors() {
        return widgetWrapper().shadowRootCreateAllByCss(Anchor.class,
                "tbody[class*='table-Overall-body'] > a");
    }

    public List<Anchor> teamFormAnchors() {
        return widgetWrapper().shadowRootCreateAllByCss(Anchor.class,
                ".table-Overall-body-row-0 div[class*='cell-FORM-info-container'] a");
    }
}