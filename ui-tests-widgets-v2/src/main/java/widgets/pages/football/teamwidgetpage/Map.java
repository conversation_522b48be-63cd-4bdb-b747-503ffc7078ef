package widgets.pages.football.teamwidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Image;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.tabs.TeamTabsSection;

import java.util.List;

@Getter
public class Map extends WidgetMap {

    private final TeamTabsSection tabsSection = new TeamTabsSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='team']");
    }

    @Override
    public Div widgetDiv() {
        return widgetWrapper().getShadowRoot().createByCss(Div.class, "div[style]").toBeVisible();
    }

    public Div teamName() {
        return widgetWrapper().getShadowRoot().createByCss(Div.class, ".team-name");
    }

    public Anchor teamNameAnchor() {
        return teamDetailsContainer().createByCss(Anchor.class, "[class*='team-details-container'] a");
    }

    public Anchor teamLogoAnchor() {
        return widgetWrapper().getShadowRoot().createByCss(Anchor.class, "[class*='team-info-wrapper'] a");
    }

    // Info tab
    public Span getInfoLabelByIndex(int index) {
        return getInfoLabels().get(index);
    }

    public List<Span> getInfoLabels() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "[class*='team-profile-container'] span");
    }

    public List<Anchor> teamSocialIcons() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "[class*='social-icons-container'] > div > a");
    }

    public List<Image> teamSquadImage() {
        return widgetWrapper().getShadowRoot().createAllByCss(Image.class, "[class*='team-squad-image-wrapper'] > div > img");
    }

    public Image teamImageLogo() {
        return widgetWrapper().getShadowRoot().createByCss(Image.class, "[class*='team-image']");
    }

    public List<Image> teamFormDesktopLabel() {
        return shadowHost().shadowRootCreateAllByCss(Image.class, "div[class*='desktop'] [class*='form-container'] > a");
    }

    public List<Image> teamFormMobileLabel() {
        return shadowHost().shadowRootCreateAllByCss(Image.class, "div[class*='mobile'] [class*='form-container'] > a");
    }

    public Div currentSeasonLabel() {
        return widgetWrapper().getShadowRoot().createByCss(Div.class, "[class*='stats-rows-header-dropdown'] > div");
    }

    public Span getInfoValueByIndex(int index) {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "div.c-SMPContainer-CommonWidget.c-SMPContainer-CommonWidget-gdopcj-size-4 > div> span:nth-child(1)").get(index);
    }

    public Span foundedInfoLabel() {
        return widgetWrapper().getShadowRoot().createByCss(Span.class, ".team-foundation");
    }

    public Span venueInfoLabel() {
        return widgetWrapper().getShadowRoot().createByCss(Span.class, ".team-stadium");
    }

    public Span countryInfoLabel() {
        return widgetWrapper().getShadowRoot().createByCss(Span.class, ".team-country");
    }

    // Stats tab
    public Span getStatsLabelByIndex(int index) {
        return statsLabels().get(index);
    }

    public List<Div> statistics() {
        return widgetWrapper().getShadowRoot().createAllByCss(Div.class, "div[class='stats-row']");
    }

    public List<Span> statsLabels() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "[class*='stats-rows-list'] > div[class*='tb:block'] > span");
    }

    public Span getStatsValueByIndex(int index) {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "[class*='variant-column'] > div:nth-child(1) > span").get(index);
    }

    public Span winsStatsLabel() {
        return getStatsLabelByIndex(1);
    }

    public Span drawsStatsLabel() {
        return getStatsLabelByIndex(2);
    }

    public Span playedStatsLabel() {
        return getStatsLabelByIndex(3);
    }

    public Span pointsStatsLabel() {
        return getStatsLabelByIndex(4);
    }

    public Span goalsConcededStatsLabel() {
        return getStatsLabelByIndex(5);
    }

    public Span goalsScoredStatsLabel() {
        return getStatsLabelByIndex(6);
    }

    public Span rankStatsLabel() {
        return getStatsLabelByIndex(7);
    }

    public Span defeatsStatsLabel() {
        return getStatsLabelByIndex(8);
    }

    private Div teamDetailsContainer() {
        return widgetWrapper().getShadowRoot().createByCss(Div.class, ".team-details-container");
    }

    //Match elements
    public Div matchTournament() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='match-tournament-container']");
    }

    public Div matchContainer() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='match-participants']");
    }

    public Anchor homeTeamName() {
        return matchContainer().createByCss(Anchor.class, "p[class*='home-team-name']");
    }

    public Anchor awayTeamName() {
        return matchContainer().createByCss(Anchor.class, "p[class*='away-team-name']");
    }

    public Div oddsContainer() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='odds-container']:nth-child(1)");
    }
}