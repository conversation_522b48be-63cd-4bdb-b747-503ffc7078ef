package widgets.pages.football.teamwidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.ThemeColors;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;
import widgets.pages.football.singleeventwidgetpage.FootballSingleEventWidgetPage;

public class FootballTeamWidgetPage extends WidgetsBasePage<Map, Asserts> {

    public final FootballSingleEventWidgetPage matchPage = new FootballSingleEventWidgetPage();

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-team-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/statistics/teams";
    }

    public FootballTeamWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));

        attributesList.add(new DataTeamAttribute(DataTeamEnum.BAYERN_MUNICH));
        attributesList.add(new DataSeasonAttribute(DataSeasonBundesLeagueEnum.TWENTYTWO_TWENTYTHREE));
        attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.SC_LYON_VS_ANNECY_FC));

        attributesList.add(new DataDefaultTabAttribute(DataDefaultTabEnum.INFO));

        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        attributesList.add(new DataShortStatusTypeAttribute(DataShortStatusTypeEnum.SHORT_NAME));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getTeamDataLabels())
                                        .dataElements(
                                                WidgetSettingsFactory.getTeamDataElements())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .build();
    }

    public ThemeColors getClientColorsTeam(String clientColor) {
        return ThemeColors.builder()
                .hiContrast(clientColor)
                .teamPrimaryBackgroundColor(clientColor)
                .statsContainerBackgroundColor(clientColor)
                .teamSecondaryTextColor(clientColor)
                .matchRowBgColor(clientColor)
                .tournamentHeaderBgColor(clientColor)
                .emptyTeamImage(clientColor)
                .build();
    }
}