package widgets.pages.football.mostdecoratedplayerswidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.standingstable.MostDecoratedTableSection;

public class Map extends WidgetMap {
    public MostDecoratedTableSection tableSection = new MostDecoratedTableSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='player']");
    }

    public Anchor playerOneNameAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "table tbody > a");
    }

    public Anchor playerOneName() {
        return playerOneNameAnchor().createByCss(Anchor.class, "td:nth-child(1) div[class*='variant-base'] > span");
    }

    public Span playerOneTeamName() {
        return playerOneNameAnchor().createByCss(Span.class, "td:nth-child(1) div[class*='variant-base'] [class*='variant-base'] > span");
    }

    public Anchor playerOneTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "table > tbody > tr:nth-child(1) > td.c-StickyColD-CommonWidget.c-StickyColD-CommonWidget-ibxirJZ-css > div > div > div > span > a");
    }
}