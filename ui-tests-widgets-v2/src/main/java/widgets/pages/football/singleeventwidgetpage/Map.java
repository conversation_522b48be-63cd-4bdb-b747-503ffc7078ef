package widgets.pages.football.singleeventwidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;

public class Map extends WidgetMap {

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='football-single-event']");
    }

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*=variant-footballSingleEventContainer]");
    }

    public Anchor homeTeamAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='variant-vertical']").stream().findFirst().orElseThrow();
    }

    public Anchor homeTeamName() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='variant-vertical'] > span").stream().findFirst().orElseThrow();
    }

    public Anchor awayTeamAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='variant-vertical']").get(1);
    }

    public Anchor homeTeam() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "p[class*='home-team-name']");
    }

    public Anchor awayTeamName() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "p[class*='away-team-name']");
    }

    public Div score() {
        return shadowHost().shadowRootCreateByCss(Div.class, "[class*='variant-score']");
    }

    public Anchor scoreAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "[class*='variant-score'] a");
    }

    public Div mainEvents() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='variant-footballSingleEventContainer'] > div:nth-child(2)");
    }

    public Span mainEventFirstParticipantName() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "span[style*='mainEventParticipantNameColor']").stream().findFirst().orElseThrow();
    }

    public Anchor matchAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "[class*='match-container'] a");
    }

    public Anchor playerTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "[class*='player-clubs-container'] a:nth-child(2)");
    }

    public Anchor competitionInfoAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='div[class*='tournament']");
    }

    public Anchor competitionName() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='tournament'] span");
    }

    public Anchor roundInfo() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='singleEventRound'] span");
    }
}