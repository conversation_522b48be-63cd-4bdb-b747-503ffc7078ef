package widgets.pages.football.singleeventwidgetpage;


import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class FootballSingleEventWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-singleevent-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/v2/matches";
    }

    public FootballSingleEventWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.FOOTBALL_SINGLE_EVENT));

        attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.ASTON_VILLA_VS_BRENTFORD));

        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsMarketAttribute(DataOddsMarketEnum.ONE_X_TWO));
        attributesList.add(new DataOddsPreEventOnlyAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsMarketValueTypeAttribute(DataOddsMarketValueTypeEnum.DECIMAL));
        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.SHORT_NAME));
        attributesList.add(new DataShortStatusTypeAttribute(DataShortStatusTypeEnum.SHORT_NAME));

        attributesList.add(new FansUnitedEnabledAttribute(BooleanEnum.TRUE));
        attributesList.add(new FansUnitedExpandedAttribute(BooleanEnum.FALSE));
        attributesList.add(new FansUnitedEnvironmentAttribute(FansUnitedEnvironmentEnum.DEV));
        attributesList.add(new FansUnitedHideBeforeGameStartAttribute(MinutesEnum.FIFTEEN));
        attributesList.add(new FansUnitedIdSchemaAttribute(FansUnitedIdSchemaEnum.SPORTAL_365));
        attributesList.add(new FansUnitedTokenAttribute(""));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}
