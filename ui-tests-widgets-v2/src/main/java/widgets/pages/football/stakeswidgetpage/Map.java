package widgets.pages.football.stakeswidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Button;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;

import java.util.List;

public class Map extends WidgetMap {

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='stakes']");
    }

    public Div oddsContainer() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='variant-base'] a[class*='StakesDisplayFixed']");
    }

    public Div sportEntityContainer() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='variant-base'] a[class*='StakesDisplayFixed']").get(0);
    }

    public Anchor homeTeamAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='variant-vertical']").stream().findFirst().orElseThrow();
    }

    public Anchor awayTeamAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='variant-vertical']").get(1);
    }

    public Anchor scoreAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, ".c-SportEntity-CommonWidget").get(1);
    }

    public Anchor bookmakerAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='variant-base'] a[class*='BookmakerLogo']");
    }

    public Anchor competitionAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, ".c-SMPFlex-CommonWidget[class*='variant-tournament'] a");
    }

    public Span stakesBetOnTeamLabel() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "div[class*='variant-base']").get(3);
    }

    public Anchor stakesBetNowLabel() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "a[class*='StakesDisplayCustom']");
    }

    public Span stakesAmountDescriptionTeamLabel() {
        return getStakeAmountLabel(2);
    }

    public Span positionCurrencySymbolLabel() {
        return getStakeAmountLabel(0);
    }

    public Span bettingAmountDisclaimerAndDomainName() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "span[class*='variant-contrast']").get(7);
    }

    public Div stakeCurrencyLabel() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='StakesDisplayCustom'] span[class*='variant-contrast']").get(5);
    }

    public Span stakeLabel() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "div[class*='StakesDisplayCustom'] span[class*='variant-contrast']").get(4);
    }

    public Button payoutLabel() {
        return shadowHost().shadowRootCreateAllByCss(Button.class, "button[class*='StakesDisplayCustom'] span").get(6);
    }

    public Anchor versusAnchor() {
        return shadowHost().shadowRootCreateAllByCss(Anchor.class, "a[class*='SportEntity-CommonWidget']").get(1);
    }

    public Span getStakeAmountLabel(int index) {
        return stakeAmountLabels().get(index);
    }

    public List<Span> stakeAmountLabels() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "span[class*='variant-contrast'] strong");
    }
}