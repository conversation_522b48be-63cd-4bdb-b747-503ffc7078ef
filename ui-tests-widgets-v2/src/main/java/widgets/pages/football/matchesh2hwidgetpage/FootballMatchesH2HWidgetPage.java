package widgets.pages.football.matchesh2hwidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class FootballMatchesH2HWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-matches-h2h-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/v2/matches";
    }

    public FootballMatchesH2HWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.MATCHES_H2H));

        attributesList.add(new DataSportEntityOneAttribute(DataTeamEnum.ASTON_VILLA.getId()));
        attributesList.add(new DataSportEntityTwoAttribute(DataTeamEnum.MANCHESTER_UNITED.getId()));
        attributesList.add(new DataLimitAttribute(DataLimitEnum.TEN));

        attributesList.add(new DataOptionsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.H2H));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getTeamH2HDataTabsInfoLabels())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .build();
    }
}