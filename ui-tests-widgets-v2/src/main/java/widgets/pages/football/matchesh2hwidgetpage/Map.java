package widgets.pages.football.matchesh2hwidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.livescore.LivescoreSection;
import widgets.molecules.tabpills.MatchesH2HTabPillsSection;

import java.util.List;

@Getter
public class Map extends WidgetMap {

    private final LivescoreSection livescoreSection = new LivescoreSection(this);
    private final MatchesH2HTabPillsSection tabPillsSection = new MatchesH2HTabPillsSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='matches-h2h']");
    }

    public List<Anchor> matchAnchors() {
        return shadowHost().shadowRootCreateAllByCss(
                Anchor.class,
                "a.c-LinkWrapper-CommonWidget"
        );
    }

    public Anchor firstMatchAnchor() {
        List<Anchor> anchors = matchAnchors();
        return anchors.isEmpty() ? null : anchors.get(0);
    }

    public Anchor winningTeam() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                ".home-team-information:has(.winner-team-badge) .home-team-name, .away-team-information:has(.winner-team-badge) .away-team-name"
        );
    }

    public Anchor losingTeam() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                ".home-team-information:not(:has(.winner-team-badge)) .home-team-name, .away-team-information:not(:has(.winner-team-badge)) .away-team-name"
        );
    }

    public Span getMatchResult(int matchIndex) {
        return shadowHost().shadowRootCreateByCss(
                Span.class,
                String.format("div.c-Wrapper-MatchRow:nth-child(%d) span.c-TextContainer-CommonWidget[class*='variant-teamWinnerForm']",
                        matchIndex)
        );
    }

    public List<Span> matchOutcomeLabel() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "span[class*='rounded-full']");
    }
}