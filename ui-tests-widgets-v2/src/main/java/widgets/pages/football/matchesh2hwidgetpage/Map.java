package widgets.pages.football.matchesh2hwidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.tabpills.MatchesH2HTabPillsSection;

import java.util.List;

public class Map extends WidgetMap {
    public MatchesH2HTabPillsSection tabPillsSection = new MatchesH2HTabPillsSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='matches-h2h']");
    }

    public Anchor getCompetitionLink() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,"div.c-SMPFlex-CommonWidget-MQpAv-variant-tournament:nth-of-type(1) a:nth-child(1)"
        );
    }

    public Anchor getCompetitionStandingsLink() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,"div.c-SMPFlex-CommonWidget-MQpAv-variant-tournament:nth-of-type(1) a:nth-child(2)"
        );
    }

    public List<Anchor> matchAnchors() {
        return shadowHost().shadowRootCreateAllByCss(
                Anchor.class,
                "a.c-LinkWrapper-CommonWidget"
        );
    }

    public Anchor firstMatchAnchor() {
        List<Anchor> anchors = matchAnchors();
        return anchors.isEmpty() ? null : anchors.get(0);
    }

    public Span firstMatchHomeTeamName() {
        return shadowHost().shadowRootCreateByCss(
                Span.class,
                "div.c-Wrapper-MatchRow:nth-child(1) div.c-MatchRowContainer-FootballWidget div.c-SMPFlex-CommonWidget-JrrAq-align-start > div:nth-child(1) span.c-TextContainer-CommonWidget[class*='with-ellipsis']"
        );
    }

    public Span firstMatchAwayTeamName() {
        return shadowHost().shadowRootCreateByCss(
                Span.class,
                "div.c-Wrapper-MatchRow:nth-child(1) div.c-MatchRowContainer-FootballWidget div.c-SMPFlex-CommonWidget-JrrAq-align-start > div:nth-child(2) span.c-TextContainer-CommonWidget[class*='with-ellipsis']"
        );
    }

    public Anchor firstMatchHomeTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                "div.c-Wrapper-MatchRow:nth-child(1) div.c-MatchRowContainer-FootballWidget div.c-SMPFlex-CommonWidget-JrrAq-align-start > div:nth-child(1) a.c-SportEntity-CommonWidget"
        );
    }

    public Anchor firstMatchAwayTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                "div.c-Wrapper-MatchRow:nth-child(1) div.c-MatchRowContainer-FootballWidget div.c-SMPFlex-CommonWidget-JrrAq-align-start > div:nth-child(2) a.c-SportEntity-CommonWidget"
        );
    }

    public Anchor winningTeam() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                "span[class*='c-TextContainer-CommonWidget'][class*='weight-700'][class*='with-winner-badge']"
        );
    }

    public Anchor losingTeam() {
        return shadowHost().shadowRootCreateByCss(
                Anchor.class,
                "span[class*='c-TextContainer-CommonWidget'][class*='weight-400']:not([class*='with-winner-badge'])"
        );
    }

    public Span getMatchResult(int matchIndex) {
        return shadowHost().shadowRootCreateByCss(
                Span.class,
                String.format("div.c-Wrapper-MatchRow:nth-child(%d) span.c-TextContainer-CommonWidget[class*='variant-teamWinnerForm']",
                        matchIndex)
        );
    }
}