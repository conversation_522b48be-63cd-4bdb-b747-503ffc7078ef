package widgets.pages.football.matchcenterwidgetpage;

import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;

import java.util.List;

public class MatchInfoMap extends Map {
    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='match-center']");
    }

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class='match-information-section']");
    }

    public Div getMatchGeneralInfoByIndex(int index) {
        List<Div> allMatchInfoElements = shadowHost().shadowRootCreateAllByCss(Div.class, "div[class='match-general-info'] > div > div");
        if (allMatchInfoElements.size() > index) {
            return allMatchInfoElements.get(index);
        } else {
            throw new RuntimeException("Match info with index %s not found. Total count: %s".formatted(index, allMatchInfoElements.size()));
        }
    }

    public Div getDateAndTimeLabel() {
        return getMatchGeneralInfoLabelByIndex(0);
    }

    public Span getDateAndTimeValue() {
        return getMatchGeneralInfoValueByIndex(1);
    }

    public Div getStadiumInfo() {
        return getMatchGeneralInfoLabelByIndex(2);
    }

    public Div getRefereeInfo() {
        return getMatchGeneralInfoLabelByIndex(4);
    }

    public Div getAudienceInfo() {
        return getMatchGeneralInfoLabelByIndex(6);
    }

    public Div getTournamentInfo() {
        return getMatchGeneralInfoLabelByIndex(8);
    }

    protected Div getMatchGeneralInfoLabelByIndex(int index) {
        return getMatchGeneralInfoByIndex(index);
    }

    protected Span getMatchGeneralInfoValueByIndex(int index) {
        return getMatchGeneralInfoByIndex(index).createByCss(Span.class, "div>div>div>span");
    }
}