package widgets.pages.football.matchcenterwidgetpage;

import solutions.bellatrix.web.components.Div;

public class TimelineMap extends Map {
    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='match-center']");
    }

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class='c-TimelineWidgetWrapper-TimelineWidget']");
    }

    public Div getGeneralEventInfoByIndex(int index) {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class='c-GeneralTabInfo-TimelineWidget']").get(index);
    }

    public Div getFirstHalfEndLabel() {
        return getGeneralEventInfoByIndex(0);
    }

    public Div getSecondHalfEndLabel() {
        return getGeneralEventInfoByIndex(1);
    }
}