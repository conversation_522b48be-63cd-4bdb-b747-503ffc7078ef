package widgets.pages.football.matchcenterwidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.tabs.MatchCenterTabsSection;

import java.util.List;

public class Map extends WidgetMap {

    public MatchCenterTabsSection getTabsSection() {
        return new MatchCenterTabsSection(this);
    }

    public widgets.pages.football.singleeventwidgetpage.Map getSingleEventMap() {
        return new widgets.pages.football.singleeventwidgetpage.Map();
    }

    public widgets.pages.football.lineupswidgetpage.Map getLineupsMap() {
        return new widgets.pages.football.lineupswidgetpage.Map();
    }

    public widgets.pages.football.teamh2hmatchwidgetpage.Map getTeamH2HMatchMap() {
        return new widgets.pages.football.teamh2hmatchwidgetpage.Map();
    }

    public TimelineMap getTimelineMap() {
        return new TimelineMap();
    }

    public MatchInfoMap getMatchInfoMap() {
        return new MatchInfoMap();
    }

    public widgets.pages.football.standingswidgetpage.Map getStandingsMap() {
        return new widgets.pages.football.standingswidgetpage.Map();
    }

    public widgets.pages.football.oddswidgetpage.Map getOddsMap() {
        return new widgets.pages.football.oddswidgetpage.Map();
    }

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='match-center']");
    }

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*=-css]");
    }

    public List<Span> teamNames() {
        return shadowHost().shadowRootCreateAllByCss(Span.class,
                "span.c-TextContainer-CommonWidget.with-ellipsis");
    }

    public Span firstTeamName() {
        List<Span> allSpans = teamNames();
        return allSpans.isEmpty() ? null : allSpans.get(0);
    }

    public Span secondTeamName() {
        List<Span> allSpans = teamNames();
        return (allSpans.size() >= 2) ? allSpans.get(1) : null;
    }

    public Anchor matchAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "a[style*='text-decoration: none;'][href*='match']");
    }
}