package widgets.pages.football.matchcenterwidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.ThemeColors;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class FootballMatchCenterWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-match-center-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/v2/matches";
    }

    public FootballMatchCenterWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.MATCH_CENTER));

        attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.ASTON_VILLA_VS_WOLVERHAMPTON));

        attributesList.add(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        attributesList.add(new DataShortStatusTypeAttribute(DataShortStatusTypeEnum.SHORT_NAME));

        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataOddsMarketAttribute(DataOddsMarketEnum.ONE_X_TWO));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions(DataPopularListEnum.ALL))
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(WidgetSettingsFactory.getMatchCenterLabels())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }

    public ThemeColors getClientColorsMatchCenter(String clientColor) {
        return ThemeColors.builder()
                .oddsContainerPrimaryBgColor(clientColor)
                .oddsSelectionBackgroundColor(clientColor)
                .oddItemHoverColor(clientColor)
                .oddsSelectionBorderColor(clientColor)
                .hiContrast(clientColor)
                .sportEntityContainerPrimaryBgColor(clientColor)
                .roundColor(clientColor)
                .statusBadgeBgColor(clientColor)
                .statusBadgeTextColor(clientColor)
                .footballFieldTone(clientColor)
                .collapsibleBgColor(clientColor)
                .footballFieldLineColor(clientColor)
                .lineupsHeaderContainerBgColor(clientColor)
                .lineupsRowEvenBgColor(clientColor)
                .lineupsRowOddBgColor(clientColor)
                .lineupsSecondaryTextColor(clientColor)
                .mobileTabsSelectedBgColor(clientColor)
                .lineupsPrimaryBackgroundColor(clientColor)
                .surfaceBackgroundColor(clientColor)
                .borderColor(clientColor)
                .rowBackgroundColor(clientColor)
                .progressOneBgColor(clientColor)
                .progressTwoBgColor(clientColor)
                .subHeaderBgColor(clientColor)
                .primaryBackgroundColor(clientColor)
                .relegationColor(clientColor)
                .relegationPlayoff(clientColor)
                .promotion(clientColor)
                .promotionPlayoff(clientColor)
                .championshipPlayoff(clientColor)
                .tierTwo(clientColor)
                .tierTwoPlayoff(clientColor)
                .topPlayoff(clientColor)
                .top(clientColor)
                .knockoutBgColor(clientColor)
                .knockoutHeaderBgColor(clientColor)
                .knockoutHeaderBorderColor(clientColor)
                .knockoutGameBgColor(clientColor)
                .knockoutGameBorderColor(clientColor)
                .teamLostColor(clientColor)
                .timelineBackgroundColor(clientColor)
                .secondaryHeaderTextColor(clientColor)
                .timelineTabBackgroundColor(clientColor)
                .tabTextColor(clientColor)
                .matchCenterPrimaryBackgroundColor(clientColor)
                .tabBackgroundColor(clientColor)
                .tabActiveTextColor(clientColor)
                .tabActiveBackgroundColor(clientColor)
                .tabMenuBorderColor(clientColor)
                .build();
    }
}
