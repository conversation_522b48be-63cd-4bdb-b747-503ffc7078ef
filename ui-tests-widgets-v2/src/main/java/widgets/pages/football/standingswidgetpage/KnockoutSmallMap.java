package widgets.pages.football.standingswidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Div;
import widgets.molecules.knockoutgame.KnockoutGamesSection;

@Getter
public class KnockoutSmallMap extends WidgetMap {

    private final KnockoutGamesSection knockoutGamesSection = new KnockoutGamesSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='football-standings']");
    }
}