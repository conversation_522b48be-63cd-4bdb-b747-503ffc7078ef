package widgets.pages.football.teamh2hmatchwidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;

import java.util.List;

public class Map extends WidgetMap {

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='team-h2h']");
    }

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class] div").toBeVisible();
    }

    public Anchor teamOneNameAnchor() {
        return teamOneAnchor().createByCss(Anchor.class, "span");
    }

    public Anchor teamOneImageAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class='c-Header-TeamH2hWidget'] > div:nth-of-type(1) a.profile-link");
    }

    public Anchor teamTwoNameAnchor() {
        return teamTwoAnchor().createByCss(Anchor.class, "span");
    }

    public Anchor teamTwoImageAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class='c-Header-TeamH2hWidget'] > div:nth-of-type(3) a.profile-link");
    }

    public Anchor teamOneTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div > a.c-SportEntity-CommonWidget.c-SportEntity-CommonWidget-igLYcjg-css");
    }

    public Anchor teamTwoTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div > a.c-SportEntity-CommonWidget.c-SportEntity-CommonWidget-idmrNvd-css");
    }

    public Anchor teamOneCompetitionAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='SubHeader'] > a:nth-child(1)");
    }

    public Anchor teamTwoCompetitionAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='SubHeader'] > a:nth-child(2)");
    }

    public Anchor vsLabel() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div.c-gqwkJN.c-gqwkJN-jJYtOo-align-center.c-gqwkJN-bICGYT-justify-center.c-gqwkJN-jHdiiA-direction-column.c-gqwkJN-eaMVsW-variant-base span");
    }

    // Stats
    public Span getStatsLabelByIndex(int index) {
        return getStatsLabels().get(index);
    }

    public Span getStatsLabelByText(String text) {
        return getStatsLabels().stream().filter(x -> x.getText().equals(text)).findFirst().orElse(null);
    }

    public List<Span> getStatsLabels() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "div[class*='StatsWrapper-H2hWidget'] span[class*='variant-contrast']");
    }

    public Span getDefeatsLabel() {
        return getStatsLabelByIndex(0);
    }

    public Span getCornersLabel() {
        return getStatsLabelByIndex(1);
    }

    public Span getGoalsConcededLabel() {
        return getStatsLabelByIndex(2);
    }

    public Span getGoalsScoredLabel() {
        return getStatsLabelByIndex(3);
    }

    public Span getPlayedLabel() {
        return getStatsLabelByIndex(4);
    }

    public Span getWinsLabel() {
        return getStatsLabelByIndex(5);
    }

    public Anchor teamOneAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='TeamH2hWidget'] a:nth-child(1)");
    }

    public Anchor teamOne() {
        return teamOneAnchor().createByCss(Anchor.class, "div");
    }

    public Anchor teamTwoAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='TeamH2hWidget'] a:nth-child(3)");
    }
}