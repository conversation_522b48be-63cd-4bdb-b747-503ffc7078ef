package widgets.pages.football.teamprogrammewidgetpage;


import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.ThemeColors;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class FootballTeamProgrammeWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-teamprogramme-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigMultisportApiUrl() + "/multi-sport/events/search";
    }

    public FootballTeamProgrammeWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_PROGRAMME));

        attributesList.add(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));
        attributesList.add(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_ONE_TWENTY_TWO));
        attributesList.add(new DataMatchTypeAttribute(DataMatchTypeEnum.HOME));

        attributesList.add(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.RESULTS));

        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        attributesList.add(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.FIXTURES.getValue())
                                        .dataPopularList(DataPopularListEnum.SPORTAL_POPULAR.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getTeamProgrammeDataLabels())
                                        .dataOddsDisplay(BooleanEnum.TRUE.getValue())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}
