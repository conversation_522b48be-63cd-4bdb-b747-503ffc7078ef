package widgets.pages.football.teamprogrammewidgetpage;


import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.Theme;
import data.widgets.options.models.ThemeColors;
import data.widgets.options.models.Themes;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class FootballTeamProgrammeWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/football-teamprogramme-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getFootballWidgetOptions().sdkOptions.getDataConfigMultisportApiUrl() + "/multi-sport/events/search";
    }

    public FootballTeamProgrammeWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_PROGRAMME));

        attributesList.add(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));
        attributesList.add(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_ONE_TWENTY_TWO));
        attributesList.add(new DataMatchTypeAttribute(DataMatchTypeEnum.HOME));

        attributesList.add(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.RESULTS));

        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        attributesList.add(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(footballWidgetsVersion)
                .tennisWidgetsVersion(footballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .footballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getFootballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.FIXTURES.getValue())
                                        .dataPopularList(DataPopularListEnum.SPORTAL_POPULAR.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getTeamProgrammeDataLabels())
                                        .dataOddsDisplay(BooleanEnum.TRUE.getValue())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.FOOTBALL))
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }

    /**
     * Builds complete theme options for FootballTeamProgramme widget with the specified color value.
     * Follows the same builder pattern structure as getDefaultThemeOptions().
     *
     * @param colorValue the hex color value to apply to all relevant color properties
     * @return Themes object with all three themes (dark, light, client) configured
     */
    public Themes getTeamProgrammeThemeOptions(String colorValue) {
        return Themes.builder()
                .dark(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add all colors for dark theme
                                .pillBackgroundColor(colorValue)
                                .dropdownBorderColor(colorValue)
                                .pillActiveBackgroundColor(colorValue)
                                .selectButtonSecondaryColor(colorValue)
                                .selectOptionBorderColor(colorValue)
                                .hiContrast(colorValue)
                                .dropdownActiveBgColor(colorValue)
                                .dropdownBtnColor(colorValue)
                                .pillBorderColor(colorValue)
                                .pillActiveTextColor(colorValue)
                                .pillTextColor(colorValue)
                                .disabledPillTextColor(colorValue)
                                .rowBackgroundColor(colorValue)
                                .headerBoxShadowColor(colorValue)
                                .oddsContainerPrimaryBgColor(colorValue)
                                .oddsSelectionBackgroundColor(colorValue)
                                .oddItemHoverColor(colorValue)
                                .oddsSelectionBorderColor(colorValue)
                                .sportEntityContainerPrimaryBgColor(colorValue)
                                .standingsLinkColor(colorValue)
                                .primaryBackgroundColor(colorValue)
                                .eventStatusColor(colorValue)
                                .build())
                        .build())
                .light(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add all colors for light theme
                                .pillBackgroundColor(colorValue)
                                .dropdownBorderColor(colorValue)
                                .pillActiveBackgroundColor(colorValue)
                                .selectButtonSecondaryColor(colorValue)
                                .selectOptionBorderColor(colorValue)
                                .hiContrast(colorValue)
                                .dropdownActiveBgColor(colorValue)
                                .dropdownBtnColor(colorValue)
                                .pillBorderColor(colorValue)
                                .pillActiveTextColor(colorValue)
                                .pillTextColor(colorValue)
                                .disabledPillTextColor(colorValue)
                                .rowBackgroundColor(colorValue)
                                .headerBoxShadowColor(colorValue)
                                .oddsContainerPrimaryBgColor(colorValue)
                                .oddsSelectionBackgroundColor(colorValue)
                                .oddItemHoverColor(colorValue)
                                .oddsSelectionBorderColor(colorValue)
                                .sportEntityContainerPrimaryBgColor(colorValue)
                                .standingsLinkColor(colorValue)
                                .primaryBackgroundColor(colorValue)
                                .eventStatusColor(colorValue)
                                .build())
                        .build())
                .client(Theme.builder()
                        .colors(ThemeColors.builder()
                                // Add all colors for client theme
                                .pillBackgroundColor(colorValue)
                                .dropdownBorderColor(colorValue)
                                .pillActiveBackgroundColor(colorValue)
                                .selectButtonSecondaryColor(colorValue)
                                .selectOptionBorderColor(colorValue)
                                .hiContrast(colorValue)
                                .dropdownActiveBgColor(colorValue)
                                .dropdownBtnColor(colorValue)
                                .pillBorderColor(colorValue)
                                .pillActiveTextColor(colorValue)
                                .pillTextColor(colorValue)
                                .disabledPillTextColor(colorValue)
                                .rowBackgroundColor(colorValue)
                                .headerBoxShadowColor(colorValue)
                                .oddsContainerPrimaryBgColor(colorValue)
                                .oddsSelectionBackgroundColor(colorValue)
                                .oddItemHoverColor(colorValue)
                                .oddsSelectionBorderColor(colorValue)
                                .sportEntityContainerPrimaryBgColor(colorValue)
                                .standingsLinkColor(colorValue)
                                .primaryBackgroundColor(colorValue)
                                .eventStatusColor(colorValue)
                                .build())
                        .build())
                .build();
    }

    /**
     * Sets all dark theme colors to the specified color value.
     * Uses the comprehensive theme builder that follows getDefaultThemeOptions() structure.
     *
     * @param colorValue the hex color value to apply to all dark theme colors
     */
    public void setAllDarkThemeColors(String colorValue) {
        Themes themes = getTeamProgrammeThemeOptions(colorValue);
        getWidgetOptions().getFootballWidgetOptions().themes.dark.colors = themes.dark.colors;
    }

    /**
     * Sets all light theme colors to the specified color value.
     * Uses the comprehensive theme builder that follows getDefaultThemeOptions() structure.
     *
     * @param colorValue the hex color value to apply to all light theme colors
     */
    public void setAllLightThemeColors(String colorValue) {
        Themes themes = getTeamProgrammeThemeOptions(colorValue);
        getWidgetOptions().getFootballWidgetOptions().themes.light.colors = themes.light.colors;
    }

    /**
     * Sets all client theme colors to the specified color value.
     * Uses the comprehensive theme builder that follows getDefaultThemeOptions() structure.
     *
     * @param colorValue the hex color value to apply to all client theme colors
     */
    public void setAllClientThemeColors(String colorValue) {
        Themes themes = getTeamProgrammeThemeOptions(colorValue);
        getWidgetOptions().getFootballWidgetOptions().themes.client.colors = themes.client.colors;
    }

    /**
     * Sets all theme colors (dark, light, client) to the specified color value.
     * Uses the comprehensive theme builder that follows getDefaultThemeOptions() structure.
     *
     * @param colorValue the hex color value to apply to all theme colors
     */
    public void setAllThemeColors(String colorValue) {
        getWidgetOptions().getFootballWidgetOptions().themes = getTeamProgrammeThemeOptions(colorValue);
    }
}
