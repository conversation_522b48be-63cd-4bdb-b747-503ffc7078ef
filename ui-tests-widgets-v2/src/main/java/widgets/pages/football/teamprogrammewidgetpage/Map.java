package widgets.pages.football.teamprogrammewidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Div;
import widgets.molecules.programme.ProgrammeSection;
import widgets.molecules.seasonselect.SeasonSelectSection;
import widgets.molecules.tabpills.TeamProgrammeTabPillsSection;

@Getter
public class Map extends WidgetMap {

    private final TeamProgrammeTabPillsSection tabPillsSection = new TeamProgrammeTabPillsSection(this);
    private final SeasonSelectSection seasonSelect = new SeasonSelectSection(this);
    private final ProgrammeSection programmeSection = new ProgrammeSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='team-programme']");
    }
}