package widgets.pages.football.knockoutwidgetpage;

import core.WidgetMap;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import widgets.molecules.knockoutgame.KnockoutGamesSection;

import java.util.List;

public class Map extends WidgetMap {
    public KnockoutGamesSection knockoutGamesSection = new KnockoutGamesSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='knockout']");
    }

    public List<Div> getStageWrappers() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div.c-SMPFlex-CommonWidget.c-SMPFlex-CommonWidget-eaMVsW-variant-base.c-SMPFlex-CommonWidget-ikEoBWy-css > div > div");
    }

    public Div finalStageHeader() {
        return getStageWrappers().get(3).createByCss(Div.class, "div > div > span");
    }

    public Anchor tournamentAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div.c-gqwkJN.c-gqwkJN-eGaVeY-grow-1.c-gqwkJN-bZJlhX-variant-column > a");
    }
}