package widgets.pages.football.playerwidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.tabs.PlayerTabsSection;

import java.util.List;

@Getter
public class Map extends WidgetMap {

    private final PlayerTabsSection tabsSection = new PlayerTabsSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='player']");
    }

    public Div playerBackground() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[style*='playerPrimaryBackgroundColor']");
    }

    public Anchor playerNameAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "a:has(p[class*='player-name'])");
    }

    public Anchor playerName() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "p[class*='player-name']");
    }

    public Anchor playerImageAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "a:has([class*='player-image'])");
    }

    public Anchor playerClubTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "a[class*='player-team']");
    }

    public Anchor countryTeamAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "span[class*='country-team-name']");
    }

    public Anchor homeTeamName() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "p[class*='home-team-name']");
    }

    // Info tab
    public Div getInfoLabelByIndex(int index) {
        return getInfoLabels().get(index);
    }

    public List<Div> getInfoLabels() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='metric-container'] p");
    }

    public Span getSeasonDropdownValueByIndex(int index) {
        List<Span> x = shadowHost().shadowRootCreateAllByCss(Span.class, "div[class*='stats-rows-header-dropdown'] span[class*='dropdown-value']");
        return x.get(index);
    }

    public Span getInfoValueByIndex(int index) {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "div.c-SMPContainer-CommonWidget.c-SMPContainer-CommonWidget-gdopcj-size-4 > div> span:nth-child(1)").get(index);
    }

    public Span playerBioInfoLabel() {
        return shadowHost().shadowRootCreateByCss(Span.class, "span[class*='player-bio-header-title']");
    }

    public Div positionInfoLabel() {
        return getInfoLabelByIndex(0);
    }

    public Div dateOfBirthInfoLabel() {
        return getInfoLabelByIndex(6);
    }

    public Div shirtNumberInfoLabel() {
        return getInfoLabelByIndex(2);
    }

    public Div heightWeightInfoLabel() {
        return getInfoLabelByIndex(4);
    }

    public Div weightInfoLabel() {
        return getInfoLabelByIndex(5);
    }

    // Stats tab
    public Div getStatsLabelByIndex(int index) {
        return getStatsLabels().get(index);
    }

    public List<Div> getStatsLabels() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='stats-rows-list'] > div[class*='tb:block'] > span");
    }

    public Span getStatsValueByIndex(int index) {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "[class*='variant-column'] > div:nth-child(1) > span").get(index);
    }

    public Div matchesPlayedStatsLabel() {
        return getStatsLabelByIndex(0);
    }

    public Div goalsStatsLabel() {
        return getStatsLabelByIndex(1);
    }

    public Div assistsStatsLabel() {
        return getStatsLabelByIndex(3);
    }

    public Div minutesStatsLabel() {
        return getStatsLabelByIndex(4);
    }

    public Div yellowCardsStatsLabel() {
        return getStatsLabelByIndex(5);
    }

    public Div foulsStatsLabel() {
        return getStatsLabelByIndex(3);
    }

    public Div shotsStatsLabel() {
        return getStatsLabelByIndex(2);
    }

    public Div shotsOnTargetStatsLabel() {
        return getStatsLabelByIndex(5);
    }

    public Div redCardsStatsLabel() {
        return getStatsLabelByIndex(5);
    }

    public Div getFormLabelByIndex(int index) {
        return getFormLabels().get(index);
    }

    public List<Div> getFormLabels() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='form-container'] > a > div > div:nth-child(1)");
    }

    public Div formLabelFirstMatch() {
        return getFormLabelByIndex(0);
    }

    public Div formLabelSecondMatch() {
        return getFormLabelByIndex(1);
    }

    public Div formLabelThirdMatch() {
        return getFormLabelByIndex(2);
    }

    public Div formLabelFourthMatch() {
        return getFormLabelByIndex(3);
    }

    public Div formLabelFifthMatch() {
        return getFormLabelByIndex(4);
    }

    public Div matchContainer() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='match-container']");
    }

    public Div statsContainer() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='stats-rows-container']");
    }
}