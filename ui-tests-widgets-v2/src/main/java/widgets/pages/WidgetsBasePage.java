package widgets.pages;

import data.configuration.SportalSettings;
import data.constants.StringConstants;
import data.widgets.options.models.SdkOptions;
import data.widgets.options.models.Themes;
import data.widgets.options.models.WidgetAttributes;
import j2html.attributes.Attribute;
import j2html.tags.ContainerTag;
import j2html.tags.Tag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.NotImplementedException;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.*;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.core.utilities.Log;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.pages.PageAsserts;
import solutions.bellatrix.web.pages.PageMap;
import solutions.bellatrix.web.pages.WebPage;
import utils.FileOperations;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static j2html.TagCreator.*;

public abstract class WidgetsBasePage<Map extends PageMap, Asserts extends PageAsserts<Map>> extends WebPage<Map, Asserts> {

    protected Integer defaultIntervalInSeconds = 3;

    protected abstract String getFilePath();

    @Override
    protected String getUrl() {
        return getFileBaseUrl() + getFilePath();
    }

    public String getDataRequestUrl() {
        throw new NotImplementedException("No data URL added as override for this widget.");
    }

    @Override
    public void open() {
        super.open();
        waitForSpinners();
        browser().tryWaitForResponse(getDataRequestUrl());
    }

    protected String getFileSavePath() {
        return getBaseFileSavePath() + getFilePath();
    }

    protected String getBaseFileSavePath() {
        ClassLoader classLoader = getClass().getClassLoader();
        String pagePath = getFilePath();
        return Objects.requireNonNull(classLoader.getResource(pagePath)).toString().replace(pagePath, "");
    }

    protected String getFileBaseUrl() {
        String resourcePath = URLDecoder.decode(getBaseFileSavePath(), StandardCharsets.UTF_8);
        int workspaceIndex = resourcePath.indexOf("workspace");
        if (workspaceIndex > 0) {
            return resourcePath.replace("file:/C:/Jenkins_new/workspace/", "http://localhost:80/").replace("\\", "/");
        } else {
            return resourcePath;
        }
    }

    @Override
    public void waitForPageLoad() {
        waitForSpinners();
    }

    @Getter
    @Setter
    public String tennisWidgetsVersion = System.getProperty(StringConstants.TENNIS_WIDGETS_VERSION_STRING);

    @Getter
    @Setter
    public String footballWidgetsVersion = System.getProperty(StringConstants.FOOTBALL_WIDGETS_VERSION_STRING);

    @Getter
    @Setter
    public String basketballWidgetsVersion = System.getProperty(StringConstants.BASKETBALL_WIDGETS_VERSION_STRING);

    @Getter
    @Setter
    public String iceHockeyWidgetsVersion = System.getProperty(StringConstants.ICE_HOCKEY_WIDGETS_VERSION_STRING);

    @Getter
    @Setter
    public String multisportWidgetsVersion = System.getProperty(StringConstants.MULTI_SPORT_WIDGETS_VERSION_STRING);

    public final List<Attribute> attributesList = new ArrayList<>();

    @Getter
    @Setter
    protected String customWidgetStyles = "";

    public void waitForSpinners() {
        waitForSpinners(1);
    }

    public void waitForSpinners(int appearTimeoutSeconds) {
        long startTime = System.currentTimeMillis();
        AtomicReference<Boolean> spinnerFound = new AtomicReference<>(false);

        Wait.retry(() -> {
                    var anySpinner = getAnySpinnerInShadowRoot();
                    if (anySpinner.isPresent()) {
                        System.out.println("spinner found");
                        spinnerFound.set(true);
                        Wait.retry(() -> {
                                    System.out.println("waiting spinner to disappear.");
                                    if (getAnySpinnerInShadowRoot().isPresent()) {
                                        System.out.println("spinner still present.");
                                        throw new TimeoutException("Spinner found.");
                                    }
                                },
                                50,
                                1,
                                TimeoutException.class, NotFoundException.class, StaleElementReferenceException.class, NumberFormatException.class, InterruptedException.class);
                    } else {
                        System.out.println("no spinner found yet");
                        throw new NotFoundException("No spinner found yet.");
                    }
                },
                appearTimeoutSeconds,
                1,
                TimeoutException.class, NotFoundException.class, StaleElementReferenceException.class, NumberFormatException.class, InterruptedException.class);

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        if (spinnerFound.get() && executionTime > 1000) {
            System.out.println("Time spent waiting for Spinner to disappear: " + executionTime / 1000 + " seconds");
        }
    }

    private Optional<WebElement> getAnySpinnerInShadowRoot() {
        return create().getWrappedDriver().findElements(By.xpath("//body/div"))
                .stream()
                .findFirst()
                .flatMap(wrapper -> wrapper.getShadowRoot().findElements(By.cssSelector("[class*='color-spinner']")).stream().findFirst());
    }

    public abstract WidgetSettings getWidgetOptions();

    public void generatePage(WidgetSettings options) {
        String source = buildPageSource(options);

        try {
            String filePath = FileOperations.saveFile(source, getFileSavePath());
            Log.info("Saving file at location: %s", filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void generatePage(List<WidgetSettings> optionsList) {
        String source = buildPageSource(optionsList);

        try {
            String filePath = FileOperations.saveFile(source, getUrl());
            Log.info("Saving file at location: %s", filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public Tag generateWidgetDiv() {
        return generateWidgetDiv(attributesList);
    }

    public Tag generateWidgetDiv(List<Attribute> attributesList) {
        Tag div = div();
        for (var item : attributesList) {
            div.attr(item);
        }
        return div;
    }

    public void updateAttributeValue(Attribute attribute) {
        removeAttribute(attribute);
        attributesList.add(attribute);
    }

    public void removeAttribute(Attribute attribute) {
        attributesList.removeIf((a) -> a.getName().equals(attribute.getName()));
    }

    /**
     * Sets a max-width constraint on all widget containers
     * @param maxWidth the maximum width (e.g., "300px", "50%", "20rem")
     */
    public void setWidgetMaxWidth(String maxWidth) {
        customWidgetStyles = String.format("""
                [data-widget-id] {
                    max-width: %s;
                }
                """, maxWidth);
    }

    /**
     * Sets custom CSS styles for widget containers
     * @param cssStyles the CSS styles to apply to [data-widget-id] selector
     */
    public void setCustomWidgetStyles(String cssStyles) {
        customWidgetStyles = String.format("""
                [data-widget-id] {
                    %s
                }
                """, cssStyles);
    }

    /**
     * Clears any custom widget styles
     */
    public void clearCustomWidgetStyles() {
        customWidgetStyles = "";
    }

    protected String buildPageSource(WidgetSettings options) {
        return html().with(
                head().with(
                        meta()
                                .withCharset("UTF-8"),
                        meta()
                                .withName("viewport")
                                .withContent("width=device-width, initial-scale=1"),
                        title("SMP Widgets")),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/football." + System.getProperty(StringConstants.FOOTBALL_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/basketball." + System.getProperty(StringConstants.BASKETBALL_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/tennis." + System.getProperty(StringConstants.TENNIS_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/ice-hockey." + System.getProperty(StringConstants.ICE_HOCKEY_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/multisport." + System.getProperty(StringConstants.MULTI_SPORT_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                style("""
                        a {
                                color: red
                            }
                        %s
                        """.formatted(customWidgetStyles)),
                body(
                        generateWidgetDiv(),
                        script(iff(options.getFootballWidgetOptions() != null,
                                generateSmpFootballWidgetScriptTag(
                                        options.getFootballWidgetOptions() != null ? options.getFootballWidgetOptions().sdkOptions : null,
                                        options.getFootballWidgetOptions() != null ? options.getFootballWidgetOptions().widgetAttributes : null,
                                        options.getFootballWidgetOptions() != null && options.getFootballWidgetOptions().themes != null ? options.getFootballWidgetOptions().themes : null))
                        ).withType("application/javascript"),
                        script(iff(options.getBasketballWidgetOptions() != null,
                                generateSmpBasketballWidgetScriptTag(
                                        options.getBasketballWidgetOptions() != null ? options.getBasketballWidgetOptions().sdkOptions : null,
                                        options.getBasketballWidgetOptions() != null ? options.getBasketballWidgetOptions().widgetAttributes : null,
                                        options.getBasketballWidgetOptions() != null && options.getBasketballWidgetOptions().themes != null ? options.getBasketballWidgetOptions().themes : null))
                        ).withType("application/javascript"),
                        script(iff(options.getTennisWidgetOptions() != null,
                                generateSmpTennisWidgetScriptTag(
                                        options.getTennisWidgetOptions() != null ? options.getTennisWidgetOptions().sdkOptions : null,
                                        options.getTennisWidgetOptions() != null ? options.getTennisWidgetOptions().widgetAttributes : null,
                                        options.getTennisWidgetOptions() != null && options.getTennisWidgetOptions().themes != null ? options.getTennisWidgetOptions().themes : null))
                        ).withType("application/javascript"),
                        script(iff(options.getIceHockeyWidgetOptions() != null,
                                generateSmpIceHockeyWidgetScriptTag(
                                        options.getIceHockeyWidgetOptions() != null ? options.getIceHockeyWidgetOptions().sdkOptions : null,
                                        options.getIceHockeyWidgetOptions() != null ? options.getIceHockeyWidgetOptions().widgetAttributes : null,
                                        options.getIceHockeyWidgetOptions() != null && options.getIceHockeyWidgetOptions().themes != null ? options.getIceHockeyWidgetOptions().themes : null))
                        ).withType("application/javascript"),
                        script(iff(options.getMultisportWidgetOptions() != null,
                                generateSmpMultisportWidgetScriptTag(
                                        options.getMultisportWidgetOptions() != null ? options.getMultisportWidgetOptions().sdkOptions : null,
                                        options.getMultisportWidgetOptions() != null ? options.getMultisportWidgetOptions().widgetAttributes : null,
                                        options.getMultisportWidgetOptions() != null && options.getMultisportWidgetOptions().themes != null ? options.getMultisportWidgetOptions().themes : null))
                        ).withType("application/javascript"),
                        script(iff(options.getIntervalOfOverlappingOddsInSeconds() != null,
                                generateBettingOverlapScript(options.getIntervalOfOverlappingOddsInSeconds()))
                        ).withType("application/javascript")
                )).render();
    }

    protected String buildPageSource(List<WidgetSettings> options) {
        ContainerTag headTag = head().with(
                meta().withCharset("UTF-8"),
                title("SMP Widgets"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/football." + System.getProperty(StringConstants.FOOTBALL_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/basketball." + System.getProperty(StringConstants.BASKETBALL_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/tennis." + System.getProperty(StringConstants.TENNIS_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/ice-hockey." + System.getProperty(StringConstants.ICE_HOCKEY_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                script().withType("module").withSrc(ConfigurationService.get(SportalSettings.class)
                        .getWidgetsCdnUrl() + "/multisport." + System.getProperty(StringConstants.MULTI_SPORT_WIDGETS_VERSION_STRING) + ".js").attr("crossorigin"),
                style("""
                        div {
                           max-height: 400px;
                           overflow: auto;
                           border: 1px solid black;
                        }
                        %s
                        """.formatted(customWidgetStyles)));
        ContainerTag bodyTag = body();

        List<String> scriptSnippets = new ArrayList<>();
        for (WidgetSettings option : options) {
            bodyTag.with(generateWidgetDiv(option.getAttributesList()));
        }

        Optional<WidgetSettings> footballSettings = options.stream().filter(e -> e.getFootballWidgetOptions() != null).findFirst();
        Optional<WidgetSettings> basketballSettings = options.stream().filter(e -> e.getBasketballWidgetOptions() != null).findFirst();
        Optional<WidgetSettings> tennisSettings = options.stream().filter(e -> e.getTennisWidgetOptions() != null).findFirst();
        Optional<WidgetSettings> iceHockeySettings = options.stream().filter(e -> e.getIceHockeyWidgetOptions() != null).findFirst();
        Optional<WidgetSettings> multisportSettings = options.stream().filter(e -> e.getMultisportWidgetOptions() != null).findFirst();
        Optional<WidgetSettings> intervalOfOverlappingOdds = options.stream().filter(e -> e.getIntervalOfOverlappingOddsInSeconds() != null).findFirst();

        footballSettings.ifPresent(widgetSettings -> scriptSnippets.add(generateSmpFootballWidgetScript(widgetSettings.getFootballWidgetOptions().sdkOptions)));
        basketballSettings.ifPresent(widgetSettings -> scriptSnippets.add(generateSmpBasketballWidgetScript(widgetSettings.getBasketballWidgetOptions().sdkOptions)));
        tennisSettings.ifPresent(widgetSettings -> scriptSnippets.add(generateSmpTennisWidgetScript(widgetSettings.getTennisWidgetOptions().sdkOptions)));
        iceHockeySettings.ifPresent(widgetSettings -> scriptSnippets.add(generateSmpIceHockeyWidgetScript(widgetSettings.getIceHockeyWidgetOptions().sdkOptions)));
        multisportSettings.ifPresent(widgetSettings -> scriptSnippets.add(generateSmpMultisportWidgetScript(widgetSettings.getMultisportWidgetOptions().sdkOptions)));
        intervalOfOverlappingOdds.ifPresent(widgetSettings -> scriptSnippets.add(generateBettingOverlapScript(widgetSettings.getIntervalOfOverlappingOddsInSeconds())));

        bodyTag.with(
                script().withType("application/javascript").with(
                        rawHtml("window.onload = () => { " + String.join(" ", scriptSnippets) + " };")
                )
        );

        return html().with(headTag, bodyTag).render();
    }

    protected String generateSmpFootballWidgetScript(SdkOptions sdkOptions) {
        if (sdkOptions == null) {
            return StringConstants.EMPTY_STRING;
        }
        return """
                window.smpFootballWidgets.LoadSmpWidget({
                    sdkOptions: %s,
                });
                """.formatted(sdkOptions.toScript());
    }

    protected String generateSmpBasketballWidgetScript(SdkOptions sdkOptions) {
        if (sdkOptions == null) {
            return StringConstants.EMPTY_STRING;
        }
        return """
                window.smpBasketballWidgets.LoadSmpWidget({
                    sdkOptions: %s,
                });
                """.formatted(sdkOptions.toScript());
    }

    protected String generateSmpTennisWidgetScript(SdkOptions sdkOptions) {
        if (sdkOptions == null) {
            return StringConstants.EMPTY_STRING;
        }
        return """
                window.smpTennisWidgets.LoadSmpWidget({
                    sdkOptions: %s,
                });
                """.formatted(sdkOptions.toScript());
    }

    protected String generateSmpIceHockeyWidgetScript(SdkOptions sdkOptions) {
        if (sdkOptions == null) {
            return StringConstants.EMPTY_STRING;
        }
        return """
                window.smpIceHockeyWidgets.LoadSmpWidget({
                    sdkOptions: %s,
                });
                """.formatted(sdkOptions.toScript());
    }

    protected String generateSmpMultisportWidgetScript(SdkOptions sdkOptions) {
        if (sdkOptions == null) {
            return StringConstants.EMPTY_STRING;
        }
        return """
                window.smpMultisportWidgets.LoadSmpWidget({
                    sdkOptions: %s,
                });
                """.formatted(sdkOptions.toScript());
    }

    protected String generateSmpFootballWidgetScriptTag(SdkOptions sdkOptions, WidgetAttributes widgetAttributes, Themes themes) {
        if (sdkOptions == null && widgetAttributes == null) {
            return "";
        }
        return """
                window.onload = () => {
                        smpFootballWidgets.LoadSmpWidget({
                            sdkOptions:"""
                + sdkOptions.toScript()
                + """
                ,
                widgetAttributes:
                """
                + widgetAttributes.toScript()
                + """
                ,
                themes:
                """
                + themes.toScript()
                + """
                                    ,
                                onLoaded: (data) => {
                                    console.log('Widget is loaded', data);
                                },
                                getAuthToken: () => {
                                    return window.rcHelper ?? '';
                                },
                                signOut: () => {
                                    // Custom logic here.
                                },
                            });
                            console.log('Bookie: sportal_efbet with id: 43')
                    };
                """;
    }

    protected String generateSmpBasketballWidgetScriptTag(SdkOptions sdkOptions, WidgetAttributes widgetAttributes, Themes themes) {
        if (sdkOptions == null && widgetAttributes == null) {
            return "";
        }
        return """
                window.onload = () => {
                        smpBasketballWidgets.LoadSmpWidget({
                            sdkOptions:"""
                + sdkOptions.toScript()
                + """
                ,
                widgetAttributes:
                """
                + widgetAttributes.toScript()
                + """
                ,
                themes:
                """
                + themes.toScript()
                + """
                                ,
                                onLoaded: (data) => {
                                    console.log('Widget is loaded', data);
                                },
                                getAuthToken: () => {
                                    return window.rcHelper ?? '';
                                },
                                signOut: () => {
                                    // Custom logic here.
                                },
                            });
                            console.log('Bookie: sportal_efbet with id: 43')
                    };
                """;
    }

    protected String generateSmpTennisWidgetScriptTag(SdkOptions sdkOptions, WidgetAttributes widgetAttributes, Themes themes) {
        if (sdkOptions == null && widgetAttributes == null) {
            return "";
        }
        return """
                window.onload = () => {
                        smpTennisWidgets.LoadSmpWidget({
                            sdkOptions:"""
                + sdkOptions.toScript()
                + """
                ,
                widgetAttributes:
                """
                + widgetAttributes.toScript()
                + """
                ,
                themes:
                """
                + themes.toScript()
                + """
                                    ,
                                onLoaded: (data) => {
                                    console.log('Widget is loaded', data);
                                },
                                getAuthToken: () => {
                                    return window.rcHelper ?? '';
                                },
                                signOut: () => {
                                    // Custom logic here.
                                },
                            });
                    };
                """;
    }

    protected String generateSmpIceHockeyWidgetScriptTag(SdkOptions sdkOptions, WidgetAttributes widgetAttributes, Themes themes) {
        if (sdkOptions == null && widgetAttributes == null) {
            return "";
        }
        return """
                window.onload = () => {
                        smpIceHockeyWidgets.LoadSmpWidget({
                            sdkOptions:"""
                + sdkOptions.toScript()
                + """
                ,
                widgetAttributes:
                """
                + widgetAttributes.toScript()
                + """
                ,
                themes:
                """
                + themes.toScript()
                + """
                                    ,
                                onLoaded: (data) => {
                                    console.log('Widget is loaded', data);
                                },
                                getAuthToken: () => {
                                    return window.rcHelper ?? '';
                                },
                                signOut: () => {
                                    // Custom logic here.
                                },
                            });
                    };
                """;
    }

    protected String generateSmpMultisportWidgetScriptTag(SdkOptions sdkOptions, WidgetAttributes widgetAttributes, Themes themes) {
        if (sdkOptions == null && widgetAttributes == null) {
            return "";
        }
        return """
                window.onload = () => {
                        smpMultisportWidgets.LoadSmpWidget({
                            sdkOptions:"""
                + sdkOptions.toScript()
                + """
                ,
                widgetAttributes:
                """
                + widgetAttributes.toScript()
                + """
                ,
                themes:
                """
                + themes.toScript()
                + """
                                    ,
                                onLoaded: (data) => {
                                    console.log('Widget is loaded', data);
                                },
                                getAuthToken: () => {
                                    return window.rcHelper ?? '';
                                },
                                signOut: () => {
                                    // Custom logic here.
                                },
                            });
                    };
                """;
    }

    protected String generateBettingOverlapScript(Integer intervalOfOverlappingOddsInSeconds) {
        if (intervalOfOverlappingOddsInSeconds == null) {
            return "";
        }
        return """
                const intervalOfOverlappingOdds = %d;
                window.smpCustomBettingOverlapValue = false;
                
                setInterval(() => {
                    window.smpCustomBettingOverlapValue = !window.smpCustomBettingOverlapValue;
                    window.dispatchEvent(new Event('smpCustomBettingOverlapValueChange'));
                },
                intervalOfOverlappingOdds);
                """.formatted(intervalOfOverlappingOddsInSeconds * 1000);
    }

    public void assertRequestsCount(int expectedCount, String partialUrl) {
        List<String> requestsMade;
        try {
            requestsMade = app().browser().getRequestEntries(partialUrl);
        } catch (ScriptTimeoutException ex) {
            requestsMade = app().browser().getRequestEntries(partialUrl);
        }

        Assertions.assertEquals(expectedCount, requestsMade.size(), "Data Requests count do not match the expected value");
    }

    public void assertDataRequestsCount(int expectedCount, String dataRequestUrl) {
        List<String> requestsMade;
        try {
            requestsMade = app().browser().getRequestEntries(dataRequestUrl);
        } catch (ScriptTimeoutException ex) {
            requestsMade = app().browser().getRequestEntries(dataRequestUrl);
        }

        Assertions.assertEquals(expectedCount, requestsMade.size(), "Data Requests count to url: '%s' do not match the expected value".formatted(dataRequestUrl));
    }
}