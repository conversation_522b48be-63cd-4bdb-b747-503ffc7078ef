package widgets.pages.enums;

import data.widgets.options.enums.StorybookStringConstants;
import lombok.Getter;

@Getter
public enum StorybookPageUrl {

    BASKETBALL_LIVESCORE_WIDGET("/basketball-widgets-livescore-widget--livescore-widget"),
    BASKETBALL_SINGLE_EVENT_WIDGET("/basketball-widgets-single-event-widget--single-event-widget"),
    BASKETBALL_STANDINGS_WIDGET("/basketball-widgets-standings-widget--standings-widget"),
    BASKETBALL_TEAM_PROGRAMME_WIDGET("/basketball-widgets-team-programme-widget--team-programme-widget"),
    BASKETBALL_TOURNAMENT_PROGRAMME_WIDGET("/basketball-widgets-tournament-programme-widget--tournament-programme-widget"),
    BASKETBALL_ODDS_WIDGET("/basketball-widgets-odds-widget--odds-widget"),

    FOOTBALL_KNOCKOUT_WIDGET("/football-widgets-knockout-widget--knockout-widget"),
    FOOTBALL_KNOCKOUT_WIDGET_SMALL_FINAL("/football-widgets-knockout-widget--knockout-widget-small-final"),
    FOOTBALL_LINEUPS_WIDGET("/football-widgets-lineups-widget--lineups-widget"),
    FOOTBALL_LIVESCORE_WIDGET("/football-widgets-livescore-widget--livescore-widget"),
    FOOTBALL_MATCHES_H2H_WIDGET("/football-widgets-matches-h2h-widget--matches-h-2-h-widget"),
    FOOTBALL_MOST_DECORATE_PLAYERS_WIDGET("/football-widgets-most-decorated-players-widget--most-decorated-players-widget"),
    FOOTBALL_ODDS_WIDGET("/football-widgets-odds-widget--odds-widget"),
    FOOTBALL_PLAYER_WIDGET("/football-widgets-player-widget--player-widget"),
    FOOTBALL_PLAYER_H2H_WIDGET("/football-widgets-player-h2h-widget--player-h-2-h-widget"),
    FOOTBALL_PROGRAMME_WIDGET("/football-widgets-programme-widget--programme-widget"),
    FOOTBALL_SINGLE_EVENT_WIDGET("/football-widgets-single-event-widget--single-event-widget"),
    FOOTBALL_STAKES_WIDGET("/football-widgets-stakes-widget--stakes-widget"),
    FOOTBALL_MATCH_CENTER_WIDGET("/football-widgets-match-center-widget--match-center-widget"),
    FOOTBALL_STANDINGS_WIDGET_LEAGUE_STANDINGS("/football-widgets-standings-widget--league-standings"),
    FOOTBALL_STANDINGS_WIDGET_GROUP_STANDINGS("/football-widgets-standings-widget--group-standings"),
    FOOTBALL_STANDINGS_WIDGET_KNOCKOUT_STANDINGS("/football-widgets-standings-widget--knockout-standings"),
    FOOTBALL_TEAM_WIDGET("/football-widgets-team-widget--team-widget"),
    FOOTBALL_TEAM_H2H_WIDGET("/football-widgets-team-h2h-widget--team-h-2-h-widget"),
    FOOTBALL_TEAM_H2H_WIDGET_WITH_MATCH("/football-widgets-team-h2h-match-widget--team-match-h-2-h-widget"),
    FOOTBALL_TEAM_SQUAD_WIDGET("/football-widgets-team-squad-widget--team-squad-widget"),
    FOOTBALL_TOP_SCORERS_WIDGET("/football-widgets-top-scorers-widget--top-scorers-widget"),
    FOOTBALL_TOURNAMENT_PROGRAMME_WIDGET("/football-widgets-tournament-programme-widget--tournament-programme-widget"),

    TENNIS_ODDS_WIDGET("/tennis-widgets-odds-widget--odds-widget"),
    TENNIS_ATHLETE_PROGRAMME_WIDGET("/tennis-widgets-athlete-programme-widget--tennis-athlete-programme-widget"),
    TENNIS_LIVESCORE_WIDGET("/tennis-widgets-livescore-widget--livescore-widget"),
    TENNIS_PLAYOFF_WIDGET("/tennis-widgets-playoff-widget--playoff-widget"),
    TENNIS_RANKING_WIDGET("/tennis-widgets-ranking-widget--ranking-widget"),
    TENNIS_SINGLE_EVENT_WIDGET("/tennis-widgets-single-event-widget--single-event-widget"),
    TENNIS_TOURNAMENT_PROGRAMME_WIDGET("/tennis-widgets-tournament-programme-widget--tennis-tournament-programme-widget"),

    MULTISPORT_EVENTS_WIDGET("/multisport-widgets-multisport-events-widget--multisport-events-widget"),

    ICE_HOCKEY_LIVESCORE_WIDGET("/ice-hockey-widgets-livescore-widget--livescore-widget"),
    ICE_HOCKEY_SINGLE_EVENT_WIDGET("/ice-hockey-widgets-single-event-widget--single-event-widget"),
    ICE_HOCKEY_STANDINGS_WIDGET("/ice-hockey-widgets-standings-widget--standings-widget"),

    KNOCKOUT_DESKTOP_ORGANISM("/organisms-knockout--knockout-desktop"),
    KNOCKOUT_MOBILE_ORGANISM("/organisms-knockout--knockout-mobile"),
    PLAYOFF_DESKTOP_ORGANISM("/organisms-playoff-knockout--knockout-desktop"),
    PLAYOFF_MOBILE_ORGANISM("/organisms-playoff-knockout--knockout-mobile"),
    RANKING_ORGANISM("/organisms-ranking--ranking"),
    FOOTBALL_STANDINGS_ORGANISM("/organisms-standings--football-standings"),
    BASKETBALL_STANDINGS_ORGANISM("/organisms-standings--basketball-standings"),

    COMPARISON_ROW_MOLECULE("/molecules-comparison-row--comparison-row"),
    DROPDOWN_MOLECULE("/molecules-dropdown--dropdown"),
    EVENT_INFO_MOLECULE("/molecules-event-info--event-info"),
    FANS_UNITED_FULL_TIME_OUTCOME_MOLECULE("/molecules-fu-full-time-outcome--fans-united-full-time-outcome"),
    FORM_ICON_MOLECULE("/molecules-form-icon--form-icon"),
    GAME_SCORE_MOLECULE("/molecules-game-score--game-score"),
    GAME_TEAM_MOLECULE("/molecules-game-team--game-team"),
    GROUP_SECTION_TITLE_MOLECULE("/molecules-group-section-title--group-section-title"),
    H2H_HEADER_DELIMITER_MOLECULE("/molecules-h2h-header-delimiter--h-2-h-header-delimiter"),
    MATCH_PARTICIPANT_MOLECULE("/molecules-match-participant--match-participant"),
    MATCH_SCORE_MOLECULE("/molecules-match-score--match-score"),
    PILL_TABS_MOLECULE("/molecules-pill-tabs--pill-tabs"),
    PROFILE_HEAD_MOLECULES("/molecules-profile-head--profile-head"),
    PROFILE_SEASON_STATISTICS_MOLECULE("/molecules-profile-season-statistics--profile-season-statistics"),
    PROFILE_SEASON_STATISTICS_WITH_MATCH_MOLECULE("/molecules-profile-season-statistics--profile-season-statistics-with-match"),
    SHOW_MORE_MOLECULE("/molecules-show-more--show-more"),
    SUB_HEADER_MOLECULE("/molecules-subheader--sub-header"),
    TEXT_BLOCK_MOLECULE("/molecules-text-block--text-block"),
    TOOLTIP_MOLECULE("/molecules-tooltip--tooltip"),
    FOOTBALL_TOURNAMENT_MOLECULE("/molecules-tournament--football-tournament"),
    BASKETBALL_TOURNAMENT_MOLECULE("/molecules-tournament--basketball-tournament");

    private final String path;

    StorybookPageUrl(String path) {
        this.path = path;
    }

    public String getPath() {
        return this.path;
    }

    public String getStoryUrl() {
        return StorybookStringConstants.STORY_PATH + this.getPath();
    }

    public String getDocsUrl() {
        return StorybookStringConstants.DOCS_PATH + this.getPath();
    }
}
