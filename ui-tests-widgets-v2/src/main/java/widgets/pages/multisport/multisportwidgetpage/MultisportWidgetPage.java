package widgets.pages.multisport.multisportwidgetpage;

import com.google.gson.JsonObject;
import data.constants.SupportedSports;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataDate;
import data.widgets.options.models.ThemeColors;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class MultisportWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/multisport-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getMultisportWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/multi-sport/competition-events";
    }

    public MultisportWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.MULTISPORT));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.MULTISPORT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.MULTISPORT_EVENTS));

        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.FALSE));
        attributesList.add(new DataOddsClickableAttribute(BooleanEnum.TRUE));

        attributesList.add(new DataAgeRestrictionDisplayAttribute(DataAgeRestrictionDisplayEnum.VISIBLE));
        attributesList.add(new DataOptionsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataBettingLogoDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataShowCompetitionNames(BooleanEnum.TRUE));
        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));
        attributesList.add(new DataCompetitionsAttribute());
        attributesList.add(new DataSportAttribute(SupportedSports.FOOTBALL));
        attributesList.add(new DataLimitAttribute(DataLimitEnum.THREE));
        attributesList.add(new DataEntityLinksAttribute(new JsonObject()));

        attributesList.add(new DataDateAttribute(
                DataDate.builder()
                        .date("05-09-2024")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(tennisWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .multisportWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getMultisportSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.FAST.getValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.ALL.getValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataTheme(DataThemeEnum.DARK.getValue())
                                        .dataLabels(WidgetSettingsFactory.getLivescoreDataLabels())
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }

    public ThemeColors getClientColorsMultisport(String clientColor) {
        return ThemeColors.builder()
                .borderMultiSportColor(clientColor)
                .multiSportCommonColor(clientColor)
                .teamLostColor(clientColor)
                .gamePartColor(clientColor)
                .multiSportWinnerColor(clientColor)
                .multiSportTextColor(clientColor)
                .multisportHighlightColor(clientColor)
                .multiSportBgColor(clientColor)
                .multiSportHoverColor(clientColor)
                .dropdownBgColor(clientColor)
                .liveIndicatorColor(clientColor)
                .postponedMatchColor(clientColor)
                .surfaceBackgroundColor(clientColor)
                .progressContainerBgColor(clientColor)
                .ageRestrictTextColor(clientColor)
                .multiSportDropdownButtonBgColor(clientColor)
                .dropdownActiveDateBgColor(clientColor)
                .build();
    }

    public void openDropDownMenu() {
        map().dropDownButton().click();
    }
}