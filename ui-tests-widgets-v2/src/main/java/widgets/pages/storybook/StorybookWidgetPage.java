package widgets.pages.storybook;


import lombok.Getter;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.components.Frame;
import widgets.pages.StorybookBasePage;
import widgets.pages.enums.StorybookPageUrl;

@Getter
public class StorybookWidgetPage extends StorybookBasePage<Map, Asserts> {
    private String storyPath;
    private String docsPath;


    public StorybookWidgetPage(StorybookPageUrl pagePath) {
        storyPath = pagePath.getStoryUrl();
        docsPath = pagePath.getDocsUrl();
    }

    @Override
    protected String getUrl() {
        return getBaseUrl() + storyPath;
    }

    protected String getDocsUrl() {
        return getBaseUrl() + docsPath;
    }

    public void closeControls() {
        if (map().closeControlsButton() != null) {
            map().closeControlsButton().scrollToVisible();
            map().closeControlsButton().click();
        }
    }

    protected Frame getDocsStorybookPreviewFrame() {
        return create().byId(Frame.class, "storybook-preview-iframe");
    }

    public void openDocs() {
        navigate().to(getDocsUrl());
        waitForSpinners();
        browser().switchToFrame(getDocsStorybookPreviewFrame());
        map().docsTable().scrollToTop();
        Wait.forMilliseconds(500);
        browser().switchToDefault();
    }
}
