package widgets.pages;

import data.widgets.options.models.WidgetOptions;
import j2html.attributes.Attribute;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class WidgetSettings {

    private String footballWidgetsVersion;
    private String basketballWidgetsVersion;
    private String tennisWidgetsVersion;
    private String iceHockeyWidgetsVersion;
    private String multisportWidgetsVersion;
    private Integer intervalOfOverlappingOddsInSeconds;
    private WidgetOptions footballWidgetOptions;
    private WidgetOptions basketballWidgetOptions;
    private WidgetOptions tennisWidgetOptions;
    private WidgetOptions iceHockeyWidgetOptions;
    private WidgetOptions multisportWidgetOptions;
    private List<Attribute> attributesList;
}