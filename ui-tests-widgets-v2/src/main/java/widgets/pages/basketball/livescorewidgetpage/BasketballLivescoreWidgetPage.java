package widgets.pages.basketball.livescorewidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataDate;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class BasketballLivescoreWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/basketball-livescore-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getBasketballWidgetOptions().sdkOptions.getDataConfigMultisportApiUrl() + "/basketball/events";
    }

    public BasketballLivescoreWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_LIVESCORE));

        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataOddsPreEventOnlyAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsMarketValueTypeAttribute(DataOddsMarketValueTypeEnum.FRACTIONAL));
        attributesList.add(new DataPopularListAttribute(DataPopularListEnum.SPORTAL_POPULAR));

        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));
        attributesList.add(new DataDateAttribute(
                DataDate.builder()
                        .date("17-02-2023")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(basketballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(basketballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .basketballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getBasketballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.ALL.getValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataPopularList(DataPopularListEnum.SPORTAL_POPULAR.getValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getLivescoreDataLabels())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.BASKETBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }

    public void openLiveTab() {
        map().getTabPillsSection().livePillButton().click();
        waitForSpinners();
    }
}
