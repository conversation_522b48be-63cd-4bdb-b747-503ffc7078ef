package widgets.pages.basketball.singleeventwidgetpage;


import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataDate;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class BasketballSingleEventWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/basketball-single-event-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getBasketballWidgetOptions().sdkOptions.getDataConfigMultisportApiUrl() + "/multi-sport/events";
    }

    public BasketballSingleEventWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_SINGLE_EVENT));

        attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.BASKETBALL_BOSCO_OLIMPIKO));

        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataOddsMarketAttribute(DataOddsMarketEnum.TWELVE));
        attributesList.add(new DataOddsPreEventOnlyAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsMarketValueTypeAttribute(DataOddsMarketValueTypeEnum.FRACTIONAL));
        attributesList.add(new DataDateAttribute(
                DataDate.builder()
                        .date("17-02-2023")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(basketballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(basketballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .basketballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getBasketballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.ALL.getValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataPopularList(DataPopularListEnum.DEFAULT.getValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.BASKETBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}
