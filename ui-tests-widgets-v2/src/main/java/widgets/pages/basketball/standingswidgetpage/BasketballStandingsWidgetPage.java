package widgets.pages.basketball.standingswidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class BasketballStandingsWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/basketball-standings-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getBasketballWidgetOptions().sdkOptions.getDataConfigStandingsApiUrl() + "/standings/basketball";
    }

    public BasketballStandingsWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_STANDINGS));

        attributesList.add(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        attributesList.add(new DataSeasonAttribute(DataSeasonNBAEnum.TWENTY_THREE_TWENTY_FOUR));
        attributesList.add(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));

        //data-stage String
        attributesList.add(new DataCompetitionAttribute(DataCompetitionEnum.NBA));
        attributesList.add(new DataStageAttribute(DataStageEnum.NBA_STANDINGS_OVERALL));
    }

    public void openNbaPlayoffs() {
        super.open();
        map().getDataHeaderPills().nbaPlayoffsPillButton().click();
        waitForSpinners();
        browser().tryWaitForResponse("/playoffs/basketball");
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(basketballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(basketballWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .basketballWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getBasketballSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getBasketballStandingsDataLabels())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.BASKETBALL))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .build();
    }
}
