package widgets.pages.basketball.teamprogrammewidgetpage;

import data.widgets.options.enums.DataMatchTypeEnum;
import solutions.bellatrix.web.pages.PageAsserts;

public class Asserts extends PageAsserts<Map> {

    private final BasketballTeamProgrammeWidgetPage widgetsPage = new BasketballTeamProgrammeWidgetPage();

    public void validateTeamPositions(String expectedTeam, DataMatchTypeEnum matchType, int... positions) {
        for (int position : positions) {
            if (DataMatchTypeEnum.HOME.equals(matchType)) {
                widgetsPage.map().getProgrammeSection().firstTeamNames().get(position).validateTextIs(expectedTeam);
            } else {
                widgetsPage.map().getProgrammeSection().secondTeamNames().get(position).validateTextIs(expectedTeam);
            }
        }
    }
}
