package widgets.pages.tennis.tournamentprogrammewidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class TennisTournamentProgrammeWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/tennis-tournament-programme-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getTennisWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/matches";
    }

    public TennisTournamentProgrammeWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_TOURNAMENT_PROGRAMME));

        attributesList.add(new DataCompetitionAttribute(DataCompetitionEnum.SHANGHAI_ROLEX_MASTERS));
        attributesList.add(new DataSeasonAttribute(DataSeasonTennisEnum.TWENTY_TWENTY_THREE));
        attributesList.add(new DataTournamentAttribute(DataTournamentEnum.SHANGHAI_ROLEX_MASTERS));

        attributesList.add(new DataRoundsFilterAttribute(WidgetSettingsFactory.getTennisTournamentProgrammeDataRoundsFilter(
                DataTournamentEnum.SHANGHAI_ROLEX_MASTERS, DataRoundEnum.ROUND_1_16, DataRoundEnum.ROUND_1_32
        )));

        attributesList.add(new DataSortDirectionFixturesAttribute(DataSortDirectionEnum.DESCENDING.getStorybookValue().toUpperCase()));
        attributesList.add(new DataSortDirectionResultsAttribute(DataSortDirectionEnum.DESCENDING.getStorybookValue().toUpperCase()));

        attributesList.add(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.RESULTS));

        attributesList.add(new DataLimitAttribute(DataLimitEnum.TWENTY));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(footballWidgetsVersion)
                .basketballWidgetsVersion(basketballWidgetsVersion)
                .tennisWidgetsVersion(tennisWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .tennisWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getTennisSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataLabels(
                                                WidgetSettingsFactory.getTennisTournamentProgrammeDataLabelsDefaultValues())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getDataEntityLinks(DataWidgetSportEnum.TENNIS))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}
