package widgets.pages.tennis.athleteprogrammewidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Anchor;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import widgets.molecules.seasonselect.SeasonSelectSection;
import widgets.molecules.tabpills.ProgrammeTabPillsSection;

import java.util.List;

@Getter
public class Map extends WidgetMap {

    private final ProgrammeTabPillsSection tabPillsSection = new ProgrammeTabPillsSection(this);
    private final SeasonSelectSection seasonSelect = new SeasonSelectSection(this);

    @Override
    public Div widgetDiv() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div > div").toBeVisible();
    }

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='basketball-tournament-programme']");
    }

    public Anchor firstMatch() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='variant-livescoreMatch']");
    }

    public Anchor firstMatchAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='variant-livescoreMatch'] > a");
    }

    public Anchor standingAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='variant-tournament'] a:nth-child(2)");
    }

    public Anchor tournamentAnchor() {
        return shadowHost().shadowRootCreateByCss(Anchor.class, "div[class*='variant-tournament'] a:nth-child(1)");
    }

    public List<Div> matchRowContainers() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='TennisEventRowContainer']");
    }

    public List<Div> displayedRounds() {
        return shadowHost().shadowRootCreateAllByCss(Div.class, "div[class*='-direction-row'][class*='align-center']");
    }

    public Anchor firstPlayerNameAnchor() {
        return widgetDiv().createAllByCss(Anchor.class, "a[class*=c-SportEntity-CommonWidget]").get(0);
    }

    public Anchor secondPlayerNameAnchor() {
        return widgetDiv().createAllByCss(Anchor.class, "a[class*=c-SportEntity-CommonWidget]").get(1);
    }

    public Div firstTwoPlayersTeam() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='variant-livescoreMatch'] div > div[class*='variant-column'] div:nth-child(2)");
    }

    public Div secondTwoPlayersTeam() {
        return shadowHost().shadowRootCreateByCss(Div.class, "div[class*='variant-livescoreMatch'] div > div[class*='variant-column'] div:nth-child(3)");
    }

    public List<Span> eventDate() {
        return shadowHost().shadowRootCreateAllByCss(Span.class, "span.c-TextContainer-CommonWidget.c-TextContainer-CommonWidget-cmygur-variant-primaryGray.c-TextContainer-CommonWidget-iefDHuJ-css");
    }

    public Span noGamesMessage() {
        return shadowHost().shadowRootCreateByCss(Span.class, "span.c-TextContainer-CommonWidget.c-TextContainer-CommonWidget-hakyQ-display-block.c-TextContainer-CommonWidget-gHgbiV-size-4.c-TextContainer-CommonWidget-czEtpN-variant-contrast");
    }
}