package widgets.pages.tennis.singleeventwidgetpage;

import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.WidgetAttributes;
import data.widgets.options.models.WidgetOptions;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.WidgetsBasePage;

public class TennisSingleEventWidgetPage extends WidgetsBasePage<Map, Asserts> {

    @Override
    protected String getFilePath() {
        return "widgetsPages/tennis-single-event-widget.html";
    }

    @Override
    public String getDataRequestUrl() {
        return getWidgetOptions().getTennisWidgetOptions().sdkOptions.getDataConfigApiUrl() + "/matches";
    }

    public TennisSingleEventWidgetPage() {
        attributesList.add(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        attributesList.add(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        attributesList.add(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_SINGLE_EVENT));

        attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.TENNIS_GRIGOR_DIMITROV_VS_EMIL_RUUSUVUORI));

        attributesList.add(new DataOddsDisplayAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.FORTY_THREE));
        attributesList.add(new DataOddsPreEventOnlyAttribute(BooleanEnum.TRUE));
        attributesList.add(new DataOddsMarketAttribute(DataOddsMarketEnum.TWELVE));
        attributesList.add(new DataOddsMarketValueTypeAttribute(DataOddsMarketValueTypeEnum.FRACTIONAL));
    }

    @Override
    public WidgetSettings getWidgetOptions() {
        return WidgetSettings.builder()
                .footballWidgetsVersion(tennisWidgetsVersion)
                .basketballWidgetsVersion(tennisWidgetsVersion)
                .tennisWidgetsVersion(tennisWidgetsVersion)
                .iceHockeyWidgetsVersion(iceHockeyWidgetsVersion)
                .multisportWidgetsVersion(multisportWidgetsVersion)
                .tennisWidgetOptions(WidgetOptions.builder().sdkOptions(
                                WidgetSettingsFactory.getTennisSdkOptions())
                        .widgetAttributes(
                                WidgetAttributes.builder()
                                        .dataRefreshTime(DataRefreshTimeEnum.MEDIUM.getAttributeValue())
                                        .dataHeaderDefaultOption(DataHeaderDefaultOptionEnum.ALL.getValue())
                                        .dataHeaderDisplay(BooleanEnum.TRUE.getValue())
                                        .dataPopularList(DataPopularListEnum.SPORTAL_POPULAR.getValue())
                                        .dataTheme(DataThemeEnum.LIGHT.getValue())
                                        .dataEntityLinks(
                                                WidgetSettingsFactory.getTennisEntityLinks(DataWidgetSportEnum.TENNIS))
                                        .build())
                        .themes(
                                WidgetSettingsFactory.getDefaultThemeOptions())
                        .build())
                .attributesList(attributesList)
                .intervalOfOverlappingOddsInSeconds(defaultIntervalInSeconds) // Applied here since there is odd for the widget
                .build();
    }
}
