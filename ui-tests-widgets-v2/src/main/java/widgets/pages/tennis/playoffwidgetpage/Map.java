package widgets.pages.tennis.playoffwidgetpage;

import core.WidgetMap;
import lombok.Getter;
import solutions.bellatrix.web.components.Div;
import widgets.molecules.knockoutgame.KnockoutGamesSection;
import widgets.molecules.tabpills.TabPillsSection;

@Getter
public class Map extends WidgetMap {

    private final TabPillsSection tabPillsSection = new TabPillsSection(this);
    private final KnockoutGamesSection knockoutGamesSection = new KnockoutGamesSection(this);

    @Override
    public Div widgetWrapper() {
        return create().byXPath(Div.class, "//div[@data-widget-id='team-programme']");
    }
}