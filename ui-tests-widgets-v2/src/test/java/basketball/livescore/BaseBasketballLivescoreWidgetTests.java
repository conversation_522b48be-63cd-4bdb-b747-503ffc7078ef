package basketball.livescore;

import core.WidgetsBaseWebTest;
import data.constants.Language;
import data.constants.MultiSportSportsEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.odds.OddClientCodeEnum;
import data.constants.enums.odds.OddFormatEnum;
import data.constants.enums.odds.OddTypeEnum;
import data.models.multisportapi.CompetitionListByDateModel;
import data.models.multisportapi.EventListByDateModel;
import data.models.multisportapi.EventListModel;
import data.utils.StringUtils;
import repositories.multisport.MultiSportEventsHttpRepository;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.livescorewidgetpage.BasketballLivescoreWidgetPage;

import java.util.Map;

public class BaseBasketballLivescoreWidgetTests extends WidgetsBaseWebTest {

    protected BasketballLivescoreWidgetPage widgetsPage;
    protected WidgetSettings widgetsOptions;
    protected CompetitionListByDateModel competition;
    protected EventListModel event;
    protected String[] teams;

    @Override
    public void beforeAll() {
        super.beforeAll();
        MultiSportEventsHttpRepository multiSportEventsHttpRepo = new MultiSportEventsHttpRepository(getCurrentTestProject());

        EventListByDateModel result = multiSportEventsHttpRepo.getCompetitionListWithEvents(Map.of(
                StringConstants.DATE_STRING, "2023-02-17",
                StringConstants.UTC_OFFSET_STRING, "+2",
                StringConstants.SPORT_STRING, SupportedSports.BASKETBALL.getValue(),
                StringConstants.COMPETITION_LIST_STRING, MultiSportSportsEnum.ALL.getValue(),
                StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode(),
                StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue(),
                StringConstants.ODD_FORMAT_STRING, OddFormatEnum.FRACTIONAL.name(),
                StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT.name()
        )).getResult();

        event = result.getCompetitions().get(0).getEvents().get(0);

        teams = StringUtils.splitText(result.getCompetitions().get(0).getEvents().get(0).getName(), "\\s+-\\s+");
        competition = result.getCompetitions().get(0).getCompetition();

    }

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getBasketballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }
}