package basketball.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@Authenticate(project = Project.FULLSETUP)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class BasketballLiveScoreWidgetDataEntityLinksTests extends BaseBasketballLivescoreWidgetTests {

    // @Issue("PLT-67")
    @Test
    public void standingsAnchorUpdated_when_setDataEntityLinksStandingsUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-league-{competitionId}#standings";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.standings.setUrl(expectedUrlFormat);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentStandingsAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{competitionId}", competition.getId()));
    }

    // @Issue("PLT-67")
    @Test
    public void competitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-league-{competitionId}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{competitionId}", competition.getId()));
    }

    // @Issue("PLT-67")
    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/basketball/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        event.getParticipants().get(0) + "-" +
                                event.getParticipants().get(1) + "#" +
                                event.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks = null;

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().getLivescoreSection().tournamentAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}