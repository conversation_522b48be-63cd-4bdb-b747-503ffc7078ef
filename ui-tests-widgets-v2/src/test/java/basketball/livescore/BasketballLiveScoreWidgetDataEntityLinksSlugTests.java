package basketball.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@Authenticate(project = Project.FULLSETUP)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class BasketballLiveScoreWidgetDataEntityLinksSlugTests extends BaseBasketballLivescoreWidgetTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
    }

    // @Issue("PLT-67")
    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setDataEntityLinksCompetitionSlugUrl() {
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().getLivescoreSection().tournamentAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", competition.getSlug()));
    }

    // @Issue("PLT-67")
    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setCompetitionSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", competition.getSlug()));
    }

    // @Issue("PLT-67")
    @Test
    public void widgetCompetitionStandingsUrlUpdatedWithSlug_when_setDataEntityLinksCompetitionStandingsSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-standings-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .standings(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentStandingsAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", competition.getSlug()));
    }

    // @Issue("PLT-67")
    @Test
    public void widgetCompetitionStandingsUrlUpdatedWithSlug_when_setCompetitionStandingsWithoutEntityLinksSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-standings-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.standings = null;
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .standings(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentStandingsAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", competition.getSlug()));
    }

    // @Issue("PLT-67")
    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setDataEntityLinksMatchScoreSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-match-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}",
                event.getSlug()));
    }

    // @Issue("PLT-67")
    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setMatchScoreSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-match-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.match = null;
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}",
                event.getSlug()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-team-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-team-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}