package basketball.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import data.widgets.attributes.DataLabelsAttribute;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettingsFactory;

@Authenticate(project = Project.FULLSETUP)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_LABELS)
public class BasketballLiveScoreWidgetDataLabelsTests extends BaseBasketballLivescoreWidgetTests {

    @Test
    public void widgetAllTabLabelsUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateTextIs(expectedDataLabels.all.trim());
    }

    @Test
    public void widgetPopularTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().popularPillButton().validateTextIs(expectedDataLabels.popular.trim());
    }

    @Test
    public void widgetUpcomingTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().upcomingPillButton().validateTextIs(expectedDataLabels.upcoming.trim());
    }

    @Test
    public void widgetLiveTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().livePillButton().validateTextIs(expectedDataLabels.live.trim());
    }

    @Test
    public void widgetFinishedTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().finishedPillButton().validateTextIs(expectedDataLabels.finished.trim());
    }

    @Test
    public void widgetWithOddsTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().withOddsPillButton().validateTextIs(expectedDataLabels.odds.trim());
    }

    @Test
    public void widgetNoGamesLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openLiveTab();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(expectedDataLabels.noGames);
    }

    @Test
    @Story(WidgetsStories.DATA_LABELS)
    public void widgetPillsLabelsDisplayed_when_setDataLabelsWithDefaultValues() {
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabelsDefaultValues();

        // Apply configuration
        widgetsPage.removeAttribute(new DataLabelsAttribute(
                DataLabels.builder()
                        .build()));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateTextIs(expectedDataLabels.all.trim());
        widgetsPage.map().getTabPillsSection().popularPillButton().validateTextIs(expectedDataLabels.popular.trim());
        widgetsPage.map().getTabPillsSection().upcomingPillButton().validateTextIs(expectedDataLabels.upcoming.trim());
        widgetsPage.map().getTabPillsSection().livePillButton().validateTextIs(expectedDataLabels.live.trim());
        widgetsPage.map().getTabPillsSection().finishedPillButton().validateTextIs(expectedDataLabels.finished.trim());
        widgetsPage.map().getTabPillsSection().withOddsPillButton().validateTextIs(expectedDataLabels.odds.trim());
    }


    @Test
    @Story(WidgetsStories.DATA_LABELS)
    public void widgetNoGamesLabelDisplayed_when_setDataLabelsWithDefaultValues() {
        DataLabels expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabelsDefaultValues();

        // Apply configuration
        widgetsPage.removeAttribute(new DataLabelsAttribute(
                DataLabels.builder()
                        .build()));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;

        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openLiveTab();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(expectedDataLabels.noGames);
    }
}