package basketball.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataWidgetIdAttribute;
import data.widgets.attributes.DataWidgetSportAttribute;
import data.widgets.attributes.DataWidgetTypeAttribute;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.singleeventwidgetpage.BasketballSingleEventWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.SINGLEEVENT)
@Story(WidgetsStories.SINGLEEVENT)
public class BasketballSingleEventWidgetTests extends WidgetsBaseWebTest {

    private BasketballSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        String expectedScore = "90\n:\n85";
        String expectedStatus = "FIN";
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_SINGLE_EVENT));
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.BASKETBALL_BOSCO_OLIMPIKO));

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam().validateTextIs(BasketballTeamEnum.CHICAGO_BULLS.getName());
        widgetsPage.map().singleEventSection.awayTeam().validateTextIs(BasketballTeamEnum.WASHINGTON_WIZARDS.getName());
        widgetsPage.map().singleEventSection.score().validateTextIs(expectedScore);
        widgetsPage.map().singleEventSection.status().validateTextIs(expectedStatus);
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData_dataMatchId() {
        // Apply configuration
        String expectedScore = "90\n:\n85";
        String expectedStatus = "FIN";
        String expectedHomeTeam = BasketballTeamEnum.CHICAGO_BULLS.getName();
        String expectedAwayTeam = BasketballTeamEnum.WASHINGTON_WIZARDS.getName();
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.CHICAGO_BULLS_WASHINGTON_WIZARDS));

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam().validateTextIs(expectedHomeTeam);
        widgetsPage.map().singleEventSection.awayTeam().validateTextIs(expectedAwayTeam);
        widgetsPage.map().singleEventSection.score().validateTextIs(expectedScore);
        widgetsPage.map().singleEventSection.status().validateTextIs(expectedStatus);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
//    @Issue("WIDGETSDK-410")
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_SINGLE_EVENT));

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
//    @Issue("WIDGETSDK-410")
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.BASKETBALL_SINGLE_EVENT));

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.BASKETBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs(StringConstants.EMPTY_STRING);
        app().browser().assertNoConsoleErrorsLogged();
    }
}