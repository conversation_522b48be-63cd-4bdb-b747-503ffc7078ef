package basketball.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.enums.basketball.BasketballMatchEnum;
import data.constants.enums.basketball.BasketballTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.singleeventwidgetpage.BasketballSingleEventWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class BasketballSingleEventWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private BasketballSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-team-{teamId}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam()
                .validateHrefIs(expectedUrlFormat.replace("{teamId}", BasketballTeamEnum.CHICAGO_BULLS.getId()));
        widgetsPage.map().singleEventSection.awayTeam().
                validateHrefIs(expectedUrlFormat.replace("{teamId}", BasketballTeamEnum.WASHINGTON_WIZARDS.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/basketball/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.scoreAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        BasketballTeamEnum.CHICAGO_BULLS.getId() + "-" +
                                BasketballTeamEnum.WASHINGTON_WIZARDS.getId() + "#" +
                                BasketballMatchEnum.CHICAGO_BULLS_WASHINGTON_WIZARDS.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks = null;

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().singleEventSection.awayTeam().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().singleEventSection.scoreAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.homeTeam().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}