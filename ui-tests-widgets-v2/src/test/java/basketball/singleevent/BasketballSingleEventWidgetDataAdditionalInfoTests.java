package basketball.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataCompetitionEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.singleeventwidgetpage.BasketballSingleEventWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.SINGLEEVENT)
@Story(WidgetsStories.SINGLEEVENT)
public class BasketballSingleEventWidgetDataAdditionalInfoTests extends WidgetsBaseWebTest {

    private BasketballSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void additionalInfoDisplayed_when_setDataAdditionalInfoTrue() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataAdditionalInfo(BooleanEnum.TRUE.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
          widgetsPage.map().singleEventSection.competitionInfoAnchor().isVisible();
          widgetsPage.map().singleEventSection.competitionName().validateTextIs(DataCompetitionEnum.NBA_SUMMER_LEAGUE.getName());
          widgetsPage.map().singleEventSection.competitionDate().validateTextIs("16 July 2023");
    }

    @Test
    public void additionalInfoHidden_when_setDataAdditionalInfoFalse() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataAdditionalInfo(BooleanEnum.FALSE.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.competitionInfoAnchor().validateNotVisible();
    }

    @Test
    public void additionalInfoHidden_when_noDataAdditionalInfoDefault() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().singleEventSection.competitionInfoAnchor().validateNotVisible();
    }
}