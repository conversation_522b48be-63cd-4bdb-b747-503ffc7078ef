package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.widgets.attributes.DataLimitAttribute;
import data.widgets.attributes.DataMatchTypeAttribute;
import data.widgets.options.enums.DataLimitEnum;
import data.widgets.options.enums.DataMatchTypeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
//@Issue("PLT-673")
public class BasketballTeamProgrammeWidgetDataLimitTests extends WidgetsBaseWebTest {

    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
//    @Issue("PLT-168")
    public void basketballTeamProgrammeWidgetDisplaysCorrectNumberOfItems_when_setDataLimit() {
        widgetsPage.removeAttribute(new DataMatchTypeAttribute(DataMatchTypeEnum.HOME));
        widgetsPage.updateAttributeValue(new DataLimitAttribute(DataLimitEnum.FIVE));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String numberOfMatches = String.valueOf(widgetsPage.map().getProgrammeSection().eventDate().size());
        Assertions.assertEquals(DataLimitEnum.FIVE.getValue(), numberOfMatches,
                AssertMessages.expectedEventDate(DataLimitEnum.FIVE.getValue(), numberOfMatches));
    }

    @Test
    public void basketballTournamentProgrammeWidgetDisplaysCorrectNumberOfItems_when_setDataLimitEqualsToZero() {
        widgetsPage.updateAttributeValue(new DataLimitAttribute(DataLimitEnum.ZERO));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String numberOfMatches = String.valueOf(widgetsPage.map().getProgrammeSection().eventDate().size());
        String errorMessage = widgetsPage.map().getProgrammeSection().noGamesMessage().getText();
        Assertions.assertEquals(DataLimitEnum.ZERO.getValue(), numberOfMatches, AssertMessages.expectedEventDate(DataLimitEnum.ZERO.getValue(), numberOfMatches));
        Assertions.assertEquals(StringConstants.NO_GAMES_TO_SHOW, errorMessage, AssertMessages.noGamesDisplayed(numberOfMatches, errorMessage));
    }
}