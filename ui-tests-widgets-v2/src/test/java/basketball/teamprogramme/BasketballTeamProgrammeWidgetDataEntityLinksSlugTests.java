package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataHeaderDefaultOptionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
//@Issue("PLT-673")
public class BasketballTeamProgrammeWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/basketball/auto-match-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataHeaderDefaultOption(DataHeaderDefaultOptionEnum.RESULTS.getValue());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().firstMatchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.BASQUET_GIRONA_VS_CAJA_LABORAL_BASKONIA.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setDataEntityLinksCompetitionSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.LIGA_ENDESA.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setCompetitionSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.LIGA_ENDESA.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/basketball/auto-competition-{slug}";
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}