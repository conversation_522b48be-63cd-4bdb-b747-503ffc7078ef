package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataMatchTypeAttribute;
import data.widgets.options.enums.DataHeaderDefaultOptionEnum;
import data.widgets.options.enums.DataMatchTypeEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_LABELS)
public class BasketballTeamProgrammeWidgetDataMatchTypeTests extends WidgetsBaseWebTest {

    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private static final String expectedTeam = DataTeamEnum.BASQUET_GIRONA.getFullName();

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setDataMatchTypeHome() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchTypeAttribute(DataMatchTypeEnum.HOME));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataHeaderDefaultOption(DataHeaderDefaultOptionEnum.RESULTS.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts().validateTeamPositions(expectedTeam, DataMatchTypeEnum.HOME, 0, 1, 2, 3, 4);
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setDataMatchTypeAway() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchTypeAttribute(DataMatchTypeEnum.AWAY));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataHeaderDefaultOption(DataHeaderDefaultOptionEnum.RESULTS.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts().validateTeamPositions(expectedTeam, DataMatchTypeEnum.AWAY, 0, 1, 2, 3, 4);
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setDataMatchTypeDefault() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataMatchTypeAttribute(DataMatchTypeEnum.HOME));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.setDataHeaderDefaultOption(DataHeaderDefaultOptionEnum.RESULTS.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts().validateTeamPositions(expectedTeam, DataMatchTypeEnum.HOME, 0, 2, 3, 5, 8);
        widgetsPage.asserts().validateTeamPositions(expectedTeam, DataMatchTypeEnum.AWAY, 1, 4, 6, 7, 9);
    }
}