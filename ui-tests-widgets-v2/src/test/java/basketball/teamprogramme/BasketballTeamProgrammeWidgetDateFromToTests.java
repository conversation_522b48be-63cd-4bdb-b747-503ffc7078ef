package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.AssertMessages;
import data.utils.DateUtils;
import data.widgets.attributes.*;
import data.widgets.options.models.DataDate;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

import java.time.LocalDate;
import java.util.Locale;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
public class BasketballTeamProgrammeWidgetDateFromToTests extends WidgetsBaseWebTest {
    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private static final LocalDate EXPECTED_DATE_FROM = LocalDate.of(2023, 1, 3);
    private static final LocalDate EXPECTED_DATE_TO = LocalDate.of(2023, 5, 24);
    private static final String DATE_FORMAT = "YYYY-MM-DD";

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    //TODO: remove the data limit attribute once issue PLT-432 is fixed
    @Test
    @Issue("PLT-432")
//    @Issue("PLT-170")
    public void basketballTeamTournamentDataDateTimeFrom_when_setDateFrom() {
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_FROM);

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        widgetsPage.updateAttributeValue(new DataLimitAttribute("50"));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String lastEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(widgetsPage.map().getProgrammeSection().eventDate().size() - 1).getText();
        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
    }

    @Test
//    @Issue("PLT-225")
    public void basketballTeamProgrammeDataDateTimeTo_when_setDateTo() {
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_TO);

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        String firstEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(0).getText();
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
    }

    //TODO: remove the data limit attribute once issue PLT-432 is fixed
    @Test
//    @Issue("PLT-170")
//    @Issue("PLT-225")
    @Issue("PLT-432")
    public void basketballTeamProgrammeDataDateTimeFromTo_when_setDateFromTo() {
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_FROM, Locale.ENGLISH);
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_TO, Locale.ENGLISH);

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        widgetsPage.updateAttributeValue(new DataLimitAttribute("50"));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        String firstEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(0).getText();
        String lastEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(widgetsPage.map().getProgrammeSection().eventDate().size() - 1).getText();

        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
    }
}