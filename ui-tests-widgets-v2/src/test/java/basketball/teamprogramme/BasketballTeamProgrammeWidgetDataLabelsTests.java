package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import com.github.javafaker.Faker;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.options.enums.DataSeasonLigaEndesaEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_LABELS)
public class BasketballTeamProgrammeWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void currentSeasonLabelChanged_when_setDataLabelsText() {
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonLigaEndesaEnum.EMPTY));

        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelCurrentSeason = "CSeason- " + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelCurrentSeason);
    }

    @Test
    public void fixturesLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelFixture = "Fixture-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().programmePillButton().validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelFixture);
    }

    @Test
    public void resultsLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelResults = "Result-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelResults.trim());
    }

    @Test
    public void widgetLabelChanged_when_setDataLabelsTextDefaultValues_season() {
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonLigaEndesaEnum.EMPTY));
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs(expectedDataLabels.labelCurrentSeason);
    }

    @Test
    public void widgetLabelChanged_when_setDataLabelsTextDefaultValues_fixtures() {
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().programmePillButton().validateTextIs(expectedDataLabels.labelFixture);
    }

    @Test
    public void widgetLabelChanged_when_setDataLabelsTextDefaultValues_results() {
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateTextIs(expectedDataLabels.labelResults);
    }
}