package basketball.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Color;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.teamprogrammewidgetpage.BasketballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_THEME)
public class BasketballTeamProgrammeWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private BasketballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Color expectedColor = SportalColors.PRIMARY_GREY.getColorValue();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getBasketballWidgetOptions().themes.dark.colors.setPrimaryBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.dark.colors.setRowBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.dark.colors.setPillBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.dark.colors.setPillActiveBackgroundColor(expectedColor.asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().firstMatchAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Color expectedColor = SportalColors.PRIMARY_GREY.getColorValue();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getBasketballWidgetOptions().themes.light.colors.setPrimaryBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.light.colors.setRowBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.light.colors.setPillBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.light.colors.setPillActiveBackgroundColor(expectedColor.asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().firstMatchAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Color expectedColor = SportalColors.PRIMARY_GREY.getColorValue();
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getBasketballWidgetOptions().themes.client.colors.setPrimaryBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.client.colors.setRowBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.client.colors.setPillBackgroundColor(expectedColor.asHex());
        widgetsOptions.getBasketballWidgetOptions().themes.client.colors.setPillActiveBackgroundColor(expectedColor.asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().firstMatchAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.asRgba());
    }
}