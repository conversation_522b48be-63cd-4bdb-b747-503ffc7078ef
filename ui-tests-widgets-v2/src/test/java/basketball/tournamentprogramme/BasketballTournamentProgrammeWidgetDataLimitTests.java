package basketball.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.tournamentprogrammewidgetpage.BasketballTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
public class BasketballTournamentProgrammeWidgetDataLimitTests extends WidgetsBaseWebTest {

    private BasketballTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void basketballTournamentProgrammeWidgetDisplaysCorrectNumberOfItems_when_DataLimitIsSet() {
        widgetsPage.updateAttributeValue(new DataLimitAttribute(DataLimitEnum.THREE));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String numberOfMatches = String.valueOf(widgetsPage.map().eventDate().size());
        Assertions.assertEquals(DataLimitEnum.THREE.getValue(), numberOfMatches, AssertMessages.expectedEventDate(DataLimitEnum.THREE.getValue(), numberOfMatches));
    }

    @Test
    public void basketballTournamentProgrammeWidgetDisplaysCorrectNumberOfItems_when_DataLimitIsSetToZero() {
        widgetsPage.updateAttributeValue(new DataLimitAttribute(DataLimitEnum.ZERO));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String numberOfMatches = String.valueOf(widgetsPage.map().eventDate().size());
        String errorMessage = widgetsPage.map().getProgrammeSection().noGamesMessage().getText();
        Assertions.assertEquals(DataLimitEnum.ZERO.getValue(), numberOfMatches, AssertMessages.expectedEventDate(DataLimitEnum.ZERO.getValue(), numberOfMatches));
        Assertions.assertEquals(errorMessage, StringConstants.NO_GAMES_TO_SHOW, AssertMessages.noGamesDisplayed(numberOfMatches, errorMessage));
    }
}