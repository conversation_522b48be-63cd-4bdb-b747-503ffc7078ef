package basketball.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.tournamentprogrammewidgetpage.BasketballTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_THEME)
public class BasketballTournamentProgrammeWidgetDataThemeTests extends WidgetsBaseWebTest {

    private BasketballTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.BLACK.getColorValue().asRgba());
        widgetsPage.map().firstTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, Colors.WHITE.getColorValue().asRgba());
        widgetsPage.map().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.GREY_800.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.ORANGE_500.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITESMOKE.getColorValue().asRgba());
        widgetsPage.map().firstTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
        widgetsPage.map().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITE.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_250.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.ORANGE_500.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.DARKRED.getColorValue().asRgba());
        widgetsPage.map().firstTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
        widgetsPage.map().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, Colors.DARKRED.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_250.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.ORANGE_500.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataTheme = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITESMOKE.getColorValue().asRgba());
        widgetsPage.map().firstTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
        widgetsPage.map().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITE.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_250.getColorValue().asRgba());
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.ORANGE_500.getColorValue().asRgba());
    }
}