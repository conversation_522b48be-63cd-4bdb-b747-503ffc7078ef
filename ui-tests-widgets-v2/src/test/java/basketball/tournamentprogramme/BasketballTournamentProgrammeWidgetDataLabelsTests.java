package basketball.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import com.github.javafaker.Faker;
import core.WidgetsBaseWebTest;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.basketball.tournamentprogrammewidgetpage.BasketballTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_LABELS)
public class BasketballTournamentProgrammeWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private BasketballTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
//    @Issue("PLT-175")
    public void roundLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.round = Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().roundSections().get(0).validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.round + " Final");
    }

    @Test
    public void fixturesLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelFixture = "Fixture-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelFixture);
    }

    @Test
    public void resultsLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelResults = "Result-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateTextIs(
                widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels.labelResults.trim());
    }

    @Test
//    @Issue("PLT-175")
    public void roundLabelChanged_when_setDataLabelsDefaultValues() {
        var expectedDataLabels = WidgetSettingsFactory.getBasketballTournamentProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().roundSections().get(0).validateTextIs(expectedDataLabels.round + "Final");
    }

    @Test
    public void fixturesLabelChanged_when_setDataLabelsTextDefaultValues() {
        var expectedDataLabels = WidgetSettingsFactory.getBasketballTournamentProgrammeDataLabelsDefaultValues();

        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDataHeaderPills().fixturesPillButton().validateTextIs(expectedDataLabels.labelFixture);
    }

    @Test
    public void resultsLabelChanged_when_setDataLabelsTextDefaultValues() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getBasketballTournamentProgrammeDataLabelsDefaultValues();

        widgetsOptions.getBasketballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDataHeaderPills().resultsPillButton().validateTextIs(expectedDataLabels.labelResults);
    }
}