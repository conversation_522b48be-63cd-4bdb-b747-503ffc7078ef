package basketball.standings;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataStageAttribute;
import data.widgets.options.enums.DataStageEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.standingswidgetpage.BasketballStandingsWidgetPage;

@ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.BASKETBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(SMPCategories.VISUAL)
public class BasketballStandingsWidgetMobileVisualTests extends WidgetsBaseWebTest {
    private BasketballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new BasketballStandingsWidgetPage();
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.KNOCKOUT_STAGE));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void widgetDisplayed_when_setDefaultData_mobileSwipe() {
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.openNbaPlayoffs();
        widgetsPage.map().getKnockoutSection().swipeToNextStage();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.IPHONE_SE_MOBILE + " Swipe");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void widgetDisplayed_when_setDefaultData_mobileTap() {
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.openNbaPlayoffs();
        widgetsPage.map().getKnockoutSection().tapToPreviousStage();
        widgetsPage.map().getKnockoutSection().tapToPreviousStage();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.IPHONE_SE_MOBILE + " Tap");
    }
}