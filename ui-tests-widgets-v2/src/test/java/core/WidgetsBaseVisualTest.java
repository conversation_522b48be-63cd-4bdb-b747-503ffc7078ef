package core;

import data.widgets.attributes.DataImageDisplayAttribute;
import data.widgets.options.enums.BooleanEnum;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetsBasePage;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

import static data.constants.StringConstants.DISPLAYED_STRING;
import static data.constants.StringConstants.NOT_DISPLAYED_STRING;

public class WidgetsBaseVisualTest extends WidgetsBaseWebTest {

    protected WidgetsBasePage widgetsPage;
    protected WidgetSettings widgetsOptions;

    protected void visualCheckDataImageDisplay(BooleanEnum dataImageDisplay) {
        String info = Boolean.parseBoolean(dataImageDisplay.getValue()) ? DISPLAYED_STRING : NOT_DISPLAYED_STRING;
        widgetsPage.updateAttributeValue(new DataImageDisplayAttribute(dataImageDisplay));
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, "data-image-display=%s | Player image  %s".formatted(dataImageDisplay.getValue(), info));
    }

    protected void visualCheckDataImageDisplay(FootballTeamSquadWidgetPage.ViewEnum view, BooleanEnum dataImageDisplay) {
        String info = Boolean.parseBoolean(dataImageDisplay.getValue()) ? DISPLAYED_STRING : NOT_DISPLAYED_STRING;

        widgetsPage.updateAttributeValue(new DataImageDisplayAttribute(dataImageDisplay));
        openWidgetPage(widgetsPage, widgetsOptions);

        if (view == FootballTeamSquadWidgetPage.ViewEnum.CARD) {
            ((FootballTeamSquadWidgetPage)widgetsPage).map().getDataHeaderPills().cardViewPillButton().click();
            info = DISPLAYED_STRING;
        }

        assertSameAsBaseline(widgetsPage, "%s | data-image-display=%s | Player image  %s".formatted(view.getValue(), dataImageDisplay.getValue(), info));
    }
}