package tennis.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.livescorewidgetpage.TennisLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class TennisLiveScoreWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private TennisLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void playerAnchorHrefEmpty_when_setDataEntityLinksPlayerUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-player-{playerId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.player.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().getLivescoreSection().secondPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    public void competitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-league-{competitionId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.BNP_PARIBAS_OPEN_2023.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/tennis/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataPlayerEnum.BERNABE_ZAPATA_MIRALLES.getId() + "-" + DataPlayerEnum.UGO_HUMBERT.getId() + "#" + DataMatchIdEnum.TENNIS_BERNABE_ZAPATA_MIRALLES_VS_UGO_HUMBERT.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().getLivescoreSection().tournamentAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatch().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatch().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}