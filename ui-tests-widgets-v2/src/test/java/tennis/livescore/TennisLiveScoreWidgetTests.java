package tennis.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataDateAttribute;
import data.widgets.attributes.DataWidgetIdAttribute;
import data.widgets.attributes.DataWidgetSportAttribute;
import data.widgets.attributes.DataWidgetTypeAttribute;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataDate;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.livescorewidgetpage.TennisLivescoreWidgetPage;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.CORE_ATTRIBUTES)
public class TennisLiveScoreWidgetTests extends WidgetsBaseWebTest {

    private TennisLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_LIVESCORE));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetWrapper().validateIsVisible();
    }

    @Test
    public void widgetDataDisplayed_when_setSportData_dataDate() {
        String expectedDate = "08-03-2023";
        String expectedDateFormat = "DD-MM-YYYY";

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataDateAttribute(
                DataDate.builder()
                        .date(expectedDate)
                        .dateFormat(expectedDateFormat)
                        .build()));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDatePickerSection().datePicker().validateTextIs(expectedDate);
        widgetsPage.map().getLivescoreSection().firstPlayerName().validateTextIs(DataPlayerEnum.BERNABE_ZAPATA_MIRALLES.getNameEn());
        widgetsPage.map().getLivescoreSection().tournamentAnchor().validateTextIs("BNP Paribas Open-2023\n" + "USA");
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDisplayed_when_setDataDateWithDefaultValues() {
        String expectedDate = ZonedDateTime.now().format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));
        // Apply configuration
        widgetsPage.removeAttribute(new DataDateAttribute(
                DataDate.builder()
                        .build()));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getDatePickerSection().datePicker().validateTextIs(expectedDate);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_LIVESCORE));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_LIVESCORE));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs("");
        app().browser().assertNoConsoleErrorsLogged();
    }
}