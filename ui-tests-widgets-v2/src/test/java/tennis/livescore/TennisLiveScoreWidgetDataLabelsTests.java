package tennis.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataLabelsAttribute;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.tennis.livescorewidgetpage.TennisLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_LABELS)
public class TennisLiveScoreWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private TennisLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetAllTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateTextIs(
                expectedDataLabels.all.trim());
    }

    @Test
    public void widgetPopularTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().popularPillButton().validateTextIs(
                expectedDataLabels.popular.trim());
    }

    @Test
    public void widgetUpcomingTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().upcomingPillButton().validateTextIs(
                expectedDataLabels.upcoming.trim());
    }

    @Test
    public void widgetLiveTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().livePillButton().validateTextIs(
                expectedDataLabels.live.trim());
    }

    @Test
    public void widgetFinishedTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().finishedPillButton().validateTextIs(
                expectedDataLabels.finished.trim());
    }

    @Test
    public void widgetWithOddsTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().withOddsPillButton().validateTextIs(
                expectedDataLabels.odds.trim());
    }

    @Test
    public void widgetNoGamesLabelUpdated_when_setDataLabels() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTennisLivescoreDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().livePillButton().click();

        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(
                expectedDataLabels.noMatches);
    }

    @Test
    @Story(WidgetsStories.DATA_LABELS)
    public void widgetPillsLabelsDisplayed_when_setDataLabelsWithDefaultValues() {
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabelsDefaultValues();

        // Apply configuration
        widgetsPage.removeAttribute(new DataLabelsAttribute(
                DataLabels.builder()
                        .build()));
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateTextIs(expectedDataLabels.all.toUpperCase().trim());
        widgetsPage.map().getTabPillsSection().popularPillButton().validateTextIs(expectedDataLabels.popular.toUpperCase().trim());
        widgetsPage.map().getTabPillsSection().upcomingPillButton().validateTextIs(expectedDataLabels.upcoming.toUpperCase().trim());
        widgetsPage.map().getTabPillsSection().livePillButton().validateTextIs(expectedDataLabels.live.toUpperCase().trim());
        widgetsPage.map().getTabPillsSection().finishedPillButton().validateTextIs(expectedDataLabels.finished.toUpperCase().trim());
        widgetsPage.map().getTabPillsSection().withOddsPillButton().validateTextIs(expectedDataLabels.odds.toUpperCase().trim());
    }

    @Test
    @Story(WidgetsStories.DATA_LABELS)
    public void widgetNoGamesLabelDisplayed_when_setDataLabelsWithDefaultValues() {
        var expectedDataLabels = WidgetSettingsFactory.getLivescoreDataLabelsDefaultValues();

        // Apply configuration
        widgetsPage.removeAttribute(new DataLabelsAttribute(
                DataLabels.builder()
                        .build()));
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        widgetsPage.map().getTabPillsSection().livePillButton().click();

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(expectedDataLabels.noGames);
    }
}