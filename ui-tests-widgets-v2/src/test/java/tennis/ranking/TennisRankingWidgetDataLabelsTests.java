package tennis.ranking;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataStandingTypeSubcategoryAttribute;
import data.widgets.options.enums.DataStandingTypeSubcategoryEnum;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.tennis.rankingwidgetpage.TennisRankingWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.RANKING)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.DATA_LABELS)
public class TennisRankingWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private TennisRankingWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisRankingWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_name() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.labelName);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_nationality() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNationalityHeader().validateTextIs(expectedDataLabels.labelNationality);
        widgetsPage.map().getTableSection().getNationalityHeaderMobile().validateHtmlIs(expectedDataLabels.labelNationalityShort);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_points() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPointsHeader().validateTextIs(expectedDataLabels.points);
        widgetsPage.map().getTableSection().getPointsHeaderMobile().validateHtmlIs(expectedDataLabels.pointsShort);
    }

    @Test
    public void widgetLinkTextUpdated_when_setDataLabels_showMore() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().showMoreButton().validateTextIs(expectedDataLabels.showMore);
    }

    @Test
//    @Issue("PLT-492")
//    @Issue("PLT-512")
    public void widgetTableUpdated_when_setDataLabels_noMatches() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabels();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.updateAttributeValue(new DataStandingTypeSubcategoryAttribute(DataStandingTypeSubcategoryEnum.NOT_VALID));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().centerMessage().validateTextIs(expectedDataLabels.noData);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_nameDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabelsDefaultValues();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.labelName);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_nationalityDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabelsDefaultValues();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNationalityHeader().validateTextIs(expectedDataLabels.labelNationality);
        widgetsPage.map().getTableSection().getNationalityHeaderMobile().validateHtmlIs(expectedDataLabels.labelNationalityShort);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_pointsDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabelsDefaultValues();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPointsHeader().validateTextIs(expectedDataLabels.points);
        widgetsPage.map().getTableSection().getPointsHeaderMobile().validateHtmlIs(expectedDataLabels.pointsShort);
    }

    @Test
    public void widgetLinkTextUpdated_when_setDataLabels_showMoreDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabelsDefaultValues();

        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().showMoreButton().validateTextIs(expectedDataLabels.showMore);
    }

    @Test
//    @Issue("PLT-492")
//    @Issue("PLT-512")
    public void widgetTableUpdated_when_setDataLabels_noMatchesDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getRankingDataLabelsDefaultValues();

        widgetsPage.updateAttributeValue(new DataStandingTypeSubcategoryAttribute(DataStandingTypeSubcategoryEnum.NOT_VALID));
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().centerMessage().validateTextIs(expectedDataLabels.noData);
    }
}