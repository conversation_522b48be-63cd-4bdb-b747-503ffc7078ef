package tennis.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.AssertMessages;
import data.utils.DateUtils;
import data.widgets.attributes.*;
import data.widgets.options.models.DataDate;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.tournamentprogrammewidgetpage.TennisTournamentProgrammeWidgetPage;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
public class TennisTournamentProgrammeWidgetDateFromToTests extends WidgetsBaseWebTest {

    private TennisTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private static final LocalDate EXPECTED_DATE_FROM = LocalDate.of(2023, 10, 8);
    private static final LocalDate EXPECTED_DATE_TO = LocalDate.of(2023, 10, 9);
    private static final String DATE_FORMAT = "YYYY-MM-DD";

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void tennisTournamentDataDateTimeTo_when_setDateTo() {
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_TO);

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        var lastEventDate = widgetsPage.map().eventDate().get(widgetsPage.map().eventDate().size() - 1).getText();
        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
    }

    @Test
    public void tennisTournamentProgrammeDataDateTimeFrom_when_setDateFrom() {
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_FROM);

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        var firstEventDate = widgetsPage.map().eventDate().get(0).getText();
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
    }

    @Test
    public void tennisTournamentProgrammeDataDateTimeFromTo_when_setDateFromTo() {
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_TO);
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_FROM);

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        var firstEventDate = widgetsPage.map().eventDate().get(0).getText();
        var lastEventDate = widgetsPage.map().eventDate().get(widgetsPage.map().eventDate().size() - 1).getText();
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
    }
}