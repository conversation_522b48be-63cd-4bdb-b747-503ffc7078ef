package tennis.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataGameTypeEnum;
import data.widgets.options.enums.DataGenderEnum;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.tournamentprogrammewidgetpage.TennisTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
public class TennisTournamentProgrammeWidgetDataGenderTests extends WidgetsBaseWebTest {

    private TennisTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new TennisTournamentProgrammeWidgetPage();
        widgetsPage.updateAttributeValue(new DataCompetitionAttribute(DataCompetitionEnum.AUSTRALIA_OPEN));
        widgetsPage.updateAttributeValue(new DataTournamentAttribute(StringConstants.EMPTY_STRING));
        widgetsPage.updateAttributeValue(new DataRoundsFilterAttribute(null));
        widgetsPage.updateAttributeValue(new DataGameTypeAttribute(DataGameTypeEnum.SINGLE));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void maleMatchesDisplayed_when_setDataGenderMale() {
        widgetsPage.updateAttributeValue(new DataGenderAttribute(DataGenderEnum.MALE));

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().firstPlayerAnchor().validateTextIs(DataPlayerEnum.STEFANOS_TSITSIPAS.getNameEn());
        widgetsPage.map().secondPlayerAnchor().validateTextIs(DataPlayerEnum.NOVAK_DJOKOVIC.getNameEn());
    }

    @Test
    public void femaleMatchesDisplayed_when_setDataGenderFemale() {
        widgetsPage.updateAttributeValue(new DataGenderAttribute(DataGenderEnum.FEMALE));

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().firstPlayerAnchor().validateTextIs(DataPlayerEnum.ELENA_RYBAKINA.getNameEn());
        widgetsPage.map().secondPlayerAnchor().validateTextIs(DataPlayerEnum.ARYNA_SABALENKA.getNameEn());
    }

    @Test
    public void mixedMatchesDisplayed_when_setDataGenderMixed() {
        widgetsPage.updateAttributeValue(new DataGameTypeAttribute(DataGameTypeEnum.DOUBLE));
        widgetsPage.updateAttributeValue(new DataGenderAttribute(DataGenderEnum.MIXED));

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().firstTwoPlayersTeam().validateTextIs(DataPlayerEnum.SANIA_MIRZA_AND_ROHAN_BOPANNA.getNameEn());
        widgetsPage.map().secondTwoPlayersTeam().validateTextIs(DataPlayerEnum.LUISA_STEFANI_AND_RAFAEL_MATOS.getNameEn());
    }
}