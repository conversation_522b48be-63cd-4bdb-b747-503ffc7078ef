package tennis.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.tournamentprogrammewidgetpage.TennisTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class TennisTournamentProgrammeWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private TennisTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void playerAnchorHrefEmpty_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-player-{playerId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.player.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().secondPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    public void tournamentAnchorUpdated_when_setDataEntityLinksTournamentUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-tournament-{competitionId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.SHANGHAI_ROLEX_MASTERS.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataPlayerEnum.STEFANOS_TSITSIPAS.getId() + "-" +
                                DataPlayerEnum.UGO_HUMBERT.getId() + "#" +
                                DataMatchIdEnum.STEFANOS_TSITSIPAS_VS_UGO_HUMBERT.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().secondPlayerAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().firstMatchAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().tournamentAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournament().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournament().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}