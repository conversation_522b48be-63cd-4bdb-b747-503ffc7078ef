package tennis.athleteprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.tennis.athleteprogrammewidgetpage.TennisAthleteProgrammeWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.ATHLETE_PROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
public class TennisAthleteProgrammeWidgetTests extends WidgetsBaseWebTest {

    private TennisAthleteProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisAthleteProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_ATHLETE_PROGRAMME));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerNameAnchor().validateIsVisible();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetRoundsDisplayed_when_setTournamentRounds() {
        DataTournamentEnum expectedTournament = DataTournamentEnum.SHANGHAI_ROLEX_MASTERS;
        DataRoundEnum[] expectedRounds = {DataRoundEnum.ROUND_1_16, DataRoundEnum.ROUND_1_32};

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRoundsFilterAttribute(
                WidgetSettingsFactory.getTennisTournamentProgrammeDataRoundsFilter(expectedTournament, expectedRounds))
        );

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.asserts().assertDisplayedRounds(expectedRounds, widgetsPage.getDisplayedRoundsText());
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData_dataTeam_dataMatchType() {
        String expectedHomeTeamHeader = DataPlayerEnum.GRIGOR_DIMITROV.getNameEn();
        String expectedAwayTeamHeader = DataPlayerEnum.ANDREY_RUBLEV.getNameEn();

        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.setDataHeaderDefaultOption(DataHeaderDefaultOptionEnum.RESULTS.getValue());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerNameAnchor().validateTextIs(expectedHomeTeamHeader);
        widgetsPage.map().secondPlayerNameAnchor().validateTextIs(expectedAwayTeamHeader);
    }

    @Test
    public void widgetDataDisplayed_when_setSportData_dataPlayer_dataMatchType_dataRoundsFilter() {
        String expectedTeamHeader = DataPlayerEnum.GRIGOR_DIMITROV.getNameEn();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataPlayerAttribute(DataPlayerEnum.GRIGOR_DIMITROV));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerNameAnchor().validateTextIs(expectedTeamHeader);
        widgetsPage.map().firstMatch().validateIsVisible();
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_ATHLETE_PROGRAMME));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TENNIS_ATHLETE_PROGRAMME));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.TENNIS));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs("");
        app().browser().assertNoConsoleErrorsLogged();
    }
}