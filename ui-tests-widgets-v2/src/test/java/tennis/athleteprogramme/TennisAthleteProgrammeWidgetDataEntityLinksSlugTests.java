package tennis.athleteprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.athleteprogrammewidgetpage.TennisAthleteProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.ATHLETE_PROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class TennisAthleteProgrammeWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private TennisAthleteProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisAthleteProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void playerAnchorHrefEmpty_when_setDataEntityLinksPlayerSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-player-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .player(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstPlayerNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().secondPlayerNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-match-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().firstMatchAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.TENNIS_GRIGOR_DIMITROV_VS_ANDREY_RUBLEV.getSlugEn()));
    }

    @Test
    public void standingsAnchorUpdated_when_setDataEntityLinksStandingsSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/tennis/auto-standings-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .standings(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().standingAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.SHANGHAI_ROLEX_MASTERS.getSlugEn()));
    }

    @Test
    public void competitionUrlUpdated_when_setDataEntityLinksCompetitionSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-competition-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().tournamentAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.SHANGHAI_ROLEX_MASTERS.getSlugEn()));
    }

    @Test
    public void competitionUrlUpdated_when_setCompetitionSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-competition-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.SHANGHAI_ROLEX_MASTERS.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-competition-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournamentAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-competition-{slug}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournamentAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}