package tennis.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.tennis.singleeventwidgetpage.TennisSingleEventWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.TENNIS)
@Tag(WidgetsTags.SINGLEEVENT)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class TennisSingleEventWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private TennisSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new TennisSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/tennis/auto-team-{playerId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.player.setUrl(expectedUrlFormat);
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().validateHrefIs(expectedUrlFormat.replace("{playerId}", DataPlayerEnum.GRIGOR_DIMITROV.getId()));
        widgetsPage.map().awayTeamName().validateHrefIs(expectedUrlFormat.replace("{playerId}", DataPlayerEnum.EMIL_RUUSUVUORI.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/tennis/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().scoreAnchors().get(0).validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataPlayerEnum.EMIL_RUUSUVUORI.getId() + "-" + DataPlayerEnum.GRIGOR_DIMITROV.getId() + "#" + DataMatchIdEnum.TENNIS_GRIGOR_DIMITROV_VS_EMIL_RUUSUVUORI.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks = null;
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().awayTeamName().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().scoreAnchors().get(0).validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getTennisWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}