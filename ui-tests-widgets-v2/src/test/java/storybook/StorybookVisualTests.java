package storybook;

import categories.SMPCategories;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.enums.StorybookPageUrl;
import widgets.pages.storybook.StorybookWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(SMPCategories.STORYBOOK)
@Tag(SMPCategories.VISUAL)
public class StorybookVisualTests extends WidgetsBaseWebTest {
    private StorybookWidgetPage widgetsPage;

    @Override
    public void beforeAll() {
        widgetsPage = new StorybookWidgetPage(StorybookPageUrl.BASKETBALL_LIVESCORE_WIDGET);
        widgetsPage.open();
        widgetsPage.closeControls();
    }

    @ParameterizedTest
    @EnumSource(StorybookPageUrl.class)
    @VisualRegression(projectName = VisualRegressionProject.STORYBOOK, viewportSize = ViewportSize.DESKTOP)
    public void widgetDisplayed_when_openStorybookUrl(StorybookPageUrl page) {
        widgetsPage = new StorybookWidgetPage(page);

        // Navigate to page
        widgetsPage.open();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, page.toString());
    }
}