package icehockey.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.singleeventwidgetpage.IceHockeySingleEventWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.SINGLEEVENT)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.DATA_THEME)
public class IceHockeySingleEventWidgetDataThemeTests extends WidgetsBaseWebTest {

    private IceHockeySingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeySingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
        widgetsPage.map().singleEventSection.homeTeam().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, Colors.WHITE.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().singleEventSection.homeTeam().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.DARKRED.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataTheme = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().singleEventSection.homeTeam().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
    }
}