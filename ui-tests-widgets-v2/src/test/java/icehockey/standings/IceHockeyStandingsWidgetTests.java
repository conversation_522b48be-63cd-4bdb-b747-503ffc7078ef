package icehockey.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.standingswidgetpage.IceHockeyStandingsWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.STANDINGS)
@Story(WidgetsStories.STANDINGS)
public class IceHockeyStandingsWidgetTests extends WidgetsBaseWebTest {

    private IceHockeyStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs("2023/2024");
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData_dataSeason_dataCompetition() {
        // Apply configuration
        String expectedSeason = "2023/2024";
        String expectedFirstTeam = DataTeamEnum.ICE_HOCKEY_NEW_YORK_RANGERS.getFullName();
        String expectedAGGBadgeText = "AGG ✓";
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonNHLEnum.TWENTY_THREE_TWENTY_FOUR));
        widgetsPage.updateAttributeValue(new DataCompetitionAttribute(DataCompetitionEnum.ICE_HOCKEY_NHL));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs(expectedSeason);
        widgetsPage.map().getKnockoutSection().firstGroupHomeTeamName().validateTextIs(expectedFirstTeam);
        widgetsPage.map().getKnockoutSection().aggregateStatusBadgeForTeam(0,true).validateTextIs(expectedAGGBadgeText);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.ICE_HOCKEY_STANDINGS));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.ICE_HOCKEY));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.ICE_HOCKEY_STANDINGS));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.ICE_HOCKEY));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs(StringConstants.EMPTY_STRING);
        app().browser().assertNoConsoleErrorsLogged();
    }

    @Test
    public void competitionCountryLogoHasBorderRadius_when_openIceHockeyStandingsWidget() {
        openWidgetPage(widgetsPage, widgetsOptions);

        String competitionLogoClass = widgetsPage.map().competitionLogo().getAttribute(StringConstants.CLASS_STRING);
        Assertions.assertTrue(competitionLogoClass.contains("rounded"),
                "The competition country logo does not have a border radius.");
    }
}