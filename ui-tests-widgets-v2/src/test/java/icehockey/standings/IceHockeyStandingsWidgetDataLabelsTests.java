package icehockey.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.icehockey.standingswidgetpage.IceHockeyStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.STANDINGS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.DATA_LABELS)
public class IceHockeyStandingsWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private IceHockeyStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_team() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.team);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesPlayedHeader().validateTextIs(expectedDataLabels.matchesPlayedShort);
        widgetsPage.map().getTableSection().getMatchesPlayedFullHeader().validateTextIs(expectedDataLabels.matchesPlayed);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesWon() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesWonHeader().validateTextIs(expectedDataLabels.matchesWonShort);
        widgetsPage.map().getTableSection().getMatchesWonFullHeader().validateTextIs(expectedDataLabels.matchesWon);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesWonOvertime() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesWonOvertimeHeader().validateTextIs(expectedDataLabels.matchesWonOvertimeShort);
        widgetsPage.map().getTableSection().getMatchesWonOvertimeFullHeader().validateTextIs(expectedDataLabels.matchesWonOvertime);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesLost() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesLostHeader().validateTextIs(expectedDataLabels.matchLossesShort);
        widgetsPage.map().getTableSection().getMatchesLostFullHeader().validateTextIs(expectedDataLabels.matchLosses);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesLostOvertime() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesLostOvertimeHeader().validateTextIs(expectedDataLabels.matchesLossesOvertimeShort);
        widgetsPage.map().getTableSection().getMatchesLostOvertimeFullHeader().validateTextIs(expectedDataLabels.matchesLossesOvertime);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesWinsAfterPenalty() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getWinsAfterPenalty().validateTextIs(expectedDataLabels.winAfterPenaltyShort);
        widgetsPage.map().getTableSection().getWinsAfterPenaltyFullHeader().validateTextIs(expectedDataLabels.winAfterPenalty);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesLossesAfterPenalty() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getLossesAfterPenalty().validateTextIs(expectedDataLabels.lossAfterPenaltyShort);
        widgetsPage.map().getTableSection().getLossesAfterPenaltyFullHeader().validateTextIs(expectedDataLabels.lossAfterPenalty);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_goalsDifference() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getGoalsDifferenceHeader().validateTextIs(expectedDataLabels.getGoalsDifferenceShort());
        widgetsPage.map().getTableSection().getGoalsDifferenceFullHeader().validateTextIs(expectedDataLabels.getGoalsDifference());
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_points() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPointsHeader().validateTextIs(expectedDataLabels.getPointsShort());
        widgetsPage.map().getTableSection().getPointsFullHeader().validateTextIs(expectedDataLabels.getPoints());
    }

    @Test
    @Issue("PLT-272")
    public void widgetTableHeadersUpdated_when_setDataLabels_form() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getFormHeader().validateTextIs(expectedDataLabels.teamFormShort);
        widgetsPage.map().getTableSection().getFormFullHeader().validateTextIs(expectedDataLabels.teamForm);
    }

    @Test
    public void widgetStageButtonLabelsUpdated_when_setDataLabels_stageButtons() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabels();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getKnockoutSection().stageButtons().get(0).validateTextIs(expectedDataLabels.getPrevStageBtn());
        widgetsPage.map().getKnockoutSection().stageButtons().get(1).validateTextIs(expectedDataLabels.getNextStageBtn());
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_team() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.team);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesPlayedHeader().validateTextIs(expectedDataLabels.matchesPlayedShort);
        widgetsPage.map().getTableSection().getMatchesPlayedFullHeader().validateTextIs(expectedDataLabels.matchesPlayed);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesWon() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesWonHeader().validateTextIs(expectedDataLabels.matchesWonShort);
        widgetsPage.map().getTableSection().getMatchesWonFullHeader().validateTextIs(expectedDataLabels.matchesWon);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesWonOvertime() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesWonOvertimeHeader().validateTextIs(expectedDataLabels.matchesWonOvertimeShort);
        widgetsPage.map().getTableSection().getMatchesWonOvertimeFullHeader().validateTextIs(expectedDataLabels.matchesWonOvertime);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesLost() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesLostHeader().validateTextIs(expectedDataLabels.matchLossesShort);
        widgetsPage.map().getTableSection().getMatchesLostFullHeader().validateTextIs(expectedDataLabels.matchLosses);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesLostOvertime() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesLostOvertimeHeader().validateTextIs(expectedDataLabels.matchesLossesOvertimeShort);
        widgetsPage.map().getTableSection().getMatchesLostOvertimeFullHeader().validateTextIs(expectedDataLabels.matchesLossesOvertime);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesWinsAfterPenalty() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getWinsAfterPenalty().validateTextIs(expectedDataLabels.winAfterPenaltyShort);
        widgetsPage.map().getTableSection().getWinsAfterPenaltyFullHeader().validateTextIs(expectedDataLabels.winAfterPenalty);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesLossesAfterPenalty() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getLossesAfterPenalty().validateTextIs(expectedDataLabels.lossAfterPenaltyShort);
        widgetsPage.map().getTableSection().getLossesAfterPenaltyFullHeader().validateTextIs(expectedDataLabels.lossAfterPenalty);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_goalsDifference() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getGoalsDifferenceHeader().validateTextIs(expectedDataLabels.getGoalsDifferenceShort());
        widgetsPage.map().getTableSection().getGoalsDifferenceFullHeader().validateTextIs(expectedDataLabels.getGoalsDifference());
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_points() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPointsHeader().validateTextIs(expectedDataLabels.getPointsShort());
        widgetsPage.map().getTableSection().getPointsFullHeader().validateTextIs(expectedDataLabels.getPoints());
    }

    @Test
//    @Issue("PLT-272")
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_form() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getIceHockeyStandingsDataLabelsDefaultValues();
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.openStandings();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getFormHeader().validateTextIs(expectedDataLabels.getTeamFormShort());
        widgetsPage.map().getTableSection().getFormFullHeader().validateTextIs(expectedDataLabels.getTeamForm());
    }
}