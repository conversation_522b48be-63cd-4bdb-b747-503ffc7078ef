package icehockey.standings;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.constants.VisualRegressionProject;
import data.constants.enums.enetpulseproxy.IceHockeyCompetition;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.standingswidgetpage.IceHockeyStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.STANDINGS)
@Tag(SMPCategories.VISUAL)
public class IceHockeyStandingsWidgetVisualTests extends WidgetsBaseWebTest {

    private static final String NHL_PLAYOFFS = "NHL Playoffs";
    private IceHockeyStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_openNhlStanding_desktop() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(IceHockeyCompetition.NHL.getName(), ViewportSize.DESKTOP_M.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openNhlStanding_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(IceHockeyCompetition.NHL.getName(), ViewportSize.MOBILE_S.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openNhlStanding_mobileL() {
        widgetsPage.openStandings();

        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(IceHockeyCompetition.NHL.getName(), ViewportSize.MOBILE_L.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openNhlStanding_tablet() {
        widgetsPage.openStandings();

        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(IceHockeyCompetition.NHL.getName(), ViewportSize.TABLET.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_openNHLPlayoffs_desktop() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(NHL_PLAYOFFS, ViewportSize.DESKTOP_M.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openNHLPlayoffs_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(NHL_PLAYOFFS, ViewportSize.MOBILE_S.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openNHLPlayoffs_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(NHL_PLAYOFFS, ViewportSize.MOBILE_L.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openNHLPlayoffs_tablet() {
        assertSameAsBaseline(widgetsPage, "%s_%s".formatted(NHL_PLAYOFFS, ViewportSize.TABLET.toString()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthMobileS_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthTablet_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthPercentage_desktop() {
        widgetsPage.setWidgetMaxWidth("40%");

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-40percent");
    }
}