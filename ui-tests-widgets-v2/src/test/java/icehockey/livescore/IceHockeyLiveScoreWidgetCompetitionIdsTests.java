package icehockey.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.Language;
import data.constants.MultiSportSportsEnum;
import data.constants.StringConstants;
import data.constants.enums.enetpulseproxy.IceHockeyCompetition;
import data.models.multisportapi.CompetitionListModel;
import data.models.multisportapi.CompetitionModel;
import data.models.multisportapi.CompetitionsModel;
import data.widgets.attributes.DataCompetitionIdsAttribute;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.multisport.MultiSportCompetitionListHttpRepository;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.icehockey.livescorewidgetpage.IceHockeyLivescoreWidgetPage;

import java.util.List;
import java.util.Map;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
public class IceHockeyLiveScoreWidgetCompetitionIdsTests extends WidgetsBaseWebTest {

    private static final DataLabels EXPECTED_DATA_LABELS = WidgetSettingsFactory.getLivescoreDataLabels();
    private IceHockeyLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private CompetitionListModel competitionList;
    private List<CompetitionModel> competitions;

    @Override
    public void beforeAll() {
        super.beforeAll();
        competitionList = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.MULTI_SPORT.getValue())
                .getById(StringConstants.MULTI_QA, Map.of(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode()))
                .getResult();
    }

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void displaySingleCompetition_when_competitionIdProvided_and_requestUrlContainsId() {
        // Apply configuration
        competitions = competitionList.getCompetitions().stream()
                .map(CompetitionsModel::getCompetition)
                .filter(competition ->
                        competition.getName().equals(IceHockeyCompetition.NHL.getName()))
                .toList();
        widgetsPage.updateAttributeValue(new DataCompetitionIdsAttribute(competitions.get(0).getId()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts().assertDisplayedTournamentsCount(competitions.size(), widgetsPage.map().getLivescoreSection().tournamentNamesList().size());
        widgetsPage.map().getLivescoreSection().tournamentNamesList().get(0).validateTextIs(competitions.get(0).getName());
    }

    @Test
    public void displayMultipleCompetitions_when_multipleCompetitionIdsProvided_and_requestUrlContainsAllIds() {
        // Apply configuration
        competitions = competitionList.getCompetitions().stream()
                .map(CompetitionsModel::getCompetition)
                .filter(competition ->
                        competition.getName().equals(IceHockeyCompetition.NHL.getName()) ||
                                competition.getName().equals(IceHockeyCompetition.CHL.getName()) ||
                                competition.getName().equals(IceHockeyCompetition.LIGA_1.getName()))
                .toList();
        widgetsPage.updateAttributeValue(new DataCompetitionIdsAttribute(competitions.get(0).getId(), competitions.get(1).getId(), competitions.get(2).getId()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentNamesList().get(0).validateTextIs(competitions.get(0).getName());
        widgetsPage.map().getLivescoreSection().tournamentNamesList().get(1).validateTextIs(competitions.get(1).getName());
        widgetsPage.map().getLivescoreSection().tournamentNamesList().get(2).validateTextIs(competitions.get(2).getName());
        widgetsPage.asserts().assertDisplayedTournamentsCount(competitions.size(), widgetsPage.map().getLivescoreSection().tournamentNamesList().size());
    }

    @Test
    public void displayNoGamesMessage_when_emptyCompetitionId_and_requestUrlIsValid() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataCompetitionIdsAttribute(" "));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(1, widgetsPage.getDataRequestUrl());
        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(EXPECTED_DATA_LABELS.noGames);
    }

    @Test
    public void displayNoGamesMessage_when_invalidCompetitionId_and_requestUrlIsValid() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataCompetitionIdsAttribute("INVALID53b63339-bb58-INVALID-8e1d-1db42c7c51deINVALID"));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateTextIs(EXPECTED_DATA_LABELS.noGames);
    }
}