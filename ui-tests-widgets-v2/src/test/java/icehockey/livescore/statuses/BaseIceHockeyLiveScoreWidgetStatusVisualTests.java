package icehockey.livescore.statuses;

import core.WidgetsBaseWebTest;
import data.constants.StatusEnum;
import data.constants.SupportedSports;
import data.models.searchapi.SportEntityModel;
import data.utils.DateUtils;
import data.widgets.attributes.DataDateAttribute;
import data.widgets.options.enums.DataPopularListEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.enums.DataThemeEnum;
import data.widgets.options.models.DataDate;
import repositories.searchapiV2.SearchV2EventHttpRepository;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.livescorewidgetpage.IceHockeyLivescoreWidgetPage;

import java.time.Instant;

public class BaseIceHockeyLiveScoreWidgetStatusVisualTests extends WidgetsBaseWebTest {

    protected final String competitionId = "e8479f7a-6248-48c3-bdc8-ee461cd45f04";
    protected final String eventStartTime = "2025-05-19T11:26:01.430Z";
    protected StatusEnum eventStatus;
    protected IceHockeyLivescoreWidgetPage widgetsPage;
    protected WidgetSettings widgetsOptions;
    protected SportEntityModel createdEvent;
    protected SearchV2EventHttpRepository searchV2EventHttpRepo;

    @Override
    protected void beforeEach() throws Exception {
        widgetsPage = new IceHockeyLivescoreWidgetPage();
        searchV2EventHttpRepo = new SearchV2EventHttpRepository(getCurrentTestProject());
        createdEvent = searchV2EventHttpRepo.createEvent(
                SupportedSports.ICE_HOCKEY,
                competitionId,
                Instant.parse(eventStartTime),
                eventStatus,
                DataTeamEnum.ICE_HOCKEY_DETROIT_RED_WINGS,
                DataTeamEnum.ICE_HOCKEY_PHILADELPHIA_FLYERS);

        widgetsPage.updateAttributeValue(new DataDateAttribute(DataDate.builder()
                .date(DateUtils.formatISODateTime(eventStartTime))
                .dateFormat("YYYY-MM-DD")
                .build()));
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigCompetitionList(DataPopularListEnum.MULTI_FE_STATIC_PROJECT.getValue());
    }

    protected void visualCheckOfStatusWithTheme(DataThemeEnum theme) {
        widgetsOptions.getIceHockeyWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "| Status: %s | Theme: %s".formatted(eventStatus, theme.name()));
    }

    @Override
    public void afterEach() {
        if (createdEvent != null) {
            String eventId = createdEvent.getId();
            searchV2EventHttpRepo.delete(eventId);
            createdEvent = null;
        }
        super.afterEach();
    }
}