package icehockey.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.CountryEnum;
import data.constants.Language;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.livescorewidgetpage.IceHockeyLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
public class IceHockeyLiveScoreWidgetDataConfigLangTests extends WidgetsBaseWebTest {

    private IceHockeyLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setDataConfigLang_bg() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigLang(Language.BULGARIAN.getCode());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.ICE_HOCKEY_NHL.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.USA.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_PHILADELPHIA_FLYERS.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_DETROIT_RED_WINGS.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("22 ЯНУ"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("Завършил след оувъртайм")
        );
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setDataConfigLang_en() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigLang(Language.ENGLISH.getCode());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.ICE_HOCKEY_NHL.getName()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.USA.getName()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_PHILADELPHIA_FLYERS.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_DETROIT_RED_WINGS.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("22 JAN"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("Finished OT")
        );
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setInvalidDataConfigLang() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigLang(StringConstants.INVALID_STRING);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateIsVisible();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetLangIsEn_when_setDataConfigLang_empty() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigLang(StringConstants.EMPTY_STRING);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.ICE_HOCKEY_NHL.getName()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.USA.getName()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_PHILADELPHIA_FLYERS.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamAnchor().validateTextIs(DataTeamEnum.ICE_HOCKEY_DETROIT_RED_WINGS.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("22 JAN"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("Finished OT")
        );
    }
}