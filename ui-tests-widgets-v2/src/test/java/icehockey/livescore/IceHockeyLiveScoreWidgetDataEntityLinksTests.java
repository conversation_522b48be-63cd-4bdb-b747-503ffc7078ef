package icehockey.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.enums.icehockey.IceHockeyTeamEnum;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.icehockey.livescorewidgetpage.IceHockeyLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.ICE_HOCKEY)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class IceHockeyLiveScoreWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private IceHockeyLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new IceHockeyLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void standingsAnchorUpdated_when_setDataEntityLinksStandingsUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/ice-hockey/auto-league-{competitionId}#standings";
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks.standings.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentStandingsAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.ICE_HOCKEY_NHL.getId()));
    }

    @Test
    public void competitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/ice-hockey/auto-league-{competitionId}";
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().tournamentAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.ICE_HOCKEY_NHL.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/ice-hockey/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        IceHockeyTeamEnum.DETROIT_RED_WINGS.getId() + "-" +
                                IceHockeyTeamEnum.PHILADELPHIA_FLYERS.getId() + "#" +
                                DataMatchIdEnum.ICE_HOCKEY_PHILADELPHIA_FLYERS_VS_DETROIT_RED_WINGS.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstMatchAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().getLivescoreSection().tournamentAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getIceHockeyWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().firstTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}