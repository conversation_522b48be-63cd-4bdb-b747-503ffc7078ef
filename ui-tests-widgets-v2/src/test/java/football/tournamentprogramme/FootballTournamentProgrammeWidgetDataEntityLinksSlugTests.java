package football.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataStageEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.tournamentprogrammewidgetpage.FootballTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TOURNAMENT_PROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTournamentProgrammeWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private FootballTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().firstMatchAnchor()
                .validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.FOOTBALL_CSKA_SOFIA_VS_CSKA_1948.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setDataEntityLinksCompetitionSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.FIRST_PROFESSIONAL_LEAGUE_ECL_PLAYOFF.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setCompetitionSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.FIRST_PROFESSIONAL_LEAGUE.getSlugEn()));
    }

    @Test
    @Issue("PLT-523")
    public void widgetStandingsUrlUpdatedWithSlug_when_setDataEntityLinksStandingsSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-standings-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .standings(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        //Navigate to page
        widgetsPage.open();

        //Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().standingsAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.FIRST_PROFESSIONAL_LEAGUE.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}