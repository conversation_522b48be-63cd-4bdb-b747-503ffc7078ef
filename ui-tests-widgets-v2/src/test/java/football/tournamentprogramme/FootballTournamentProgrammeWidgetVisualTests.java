package football.tournamentprogramme;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.tournamentprogrammewidgetpage.FootballTournamentProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TOURNAMENT_PROGRAMME)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTournamentProgrammeWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballTournamentProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTournamentProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_setDefaultData_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_setDefaultData_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_setDefaultData_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthMobileS_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthTablet_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthPercentage_desktop() {
        widgetsPage.setWidgetMaxWidth("40%");

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-40percent");
    }
}