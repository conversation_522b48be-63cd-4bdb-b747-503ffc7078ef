package football.knockout;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.knockoutwidgetpage.FootballKnockoutWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.KNOCKOUT)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Story(WidgetsStories.KNOCKOUT)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class FootballKnockoutSmallFinalWidgetTests extends WidgetsBaseWebTest {

    private FootballKnockoutWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballKnockoutWidgetPage().getShortFinalWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void knockoutSchemaDisplayed_when_setStageId() {
        // Apply configuration
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().finalStageHeader().validateTextIs("Final");
    }
}