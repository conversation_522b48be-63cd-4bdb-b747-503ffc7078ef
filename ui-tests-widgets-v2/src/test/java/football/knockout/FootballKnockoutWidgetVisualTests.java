package football.knockout;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.knockoutwidgetpage.FootballKnockoutWidgetPage;

import static data.constants.StringConstants.DARK_THEME_STRING;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.KNOCKOUT)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballKnockoutWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballKnockoutWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        viewPort = getViewPort();
        widgetsPage = new FootballKnockoutWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetMatchAggregateScoreInfoDisplayed_when_hoverAggregateBadge_desktopM() {
        widgetsPage.map().knockoutGamesSection.aggregateBadge().hover();

        assertSameAsBaseline(widgetsPage, "%s Aggregate badge hovered %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_setDefaultData_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetMatchAggregateScoreInfoDisplayed_when_hoverAggregateBadge_mobileS() {
        widgetsPage.map().knockoutGamesSection.tapToPreviousStage();
        widgetsPage.waitForSpinners();
        widgetsPage.map().knockoutGamesSection.holdOverAggregateBadge();

        assertSameAsBaseline(widgetsPage, "%s Aggregate badge hovered %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_setDefaultData_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_setDefaultData_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }
}