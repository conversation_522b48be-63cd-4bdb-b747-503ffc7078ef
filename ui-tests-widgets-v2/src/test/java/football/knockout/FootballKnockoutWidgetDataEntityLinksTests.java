package football.knockout;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.knockoutwidgetpage.FootballKnockoutWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(WidgetsTags.KNOCKOUT)
@Story(WidgetsStories.KNOCKOUT)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class FootballKnockoutWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballKnockoutWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballKnockoutWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorHrefEmpty_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().knockoutGamesSection.firstMatchHomeTeamNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().knockoutGamesSection.firstMatchAwayTeamNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    @Issue("PLT-232")
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().knockoutGamesSection.firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataTeamEnum.LAZIO.getId() + "-" + DataTeamEnum.BAYERN_MUNICH.getId() + "#" + DataMatchIdEnum.LAZIO_BAYERN_MUNICH.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().knockoutGamesSection.firstMatchHomeTeamNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().knockoutGamesSection.firstMatchAwayTeamNameAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
        widgetsPage.map().knockoutGamesSection.firstMatchAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().knockoutGamesSection.firstMatch().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().knockoutGamesSection.firstMatch().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}