package football.knockout;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.knockoutwidgetpage.FootballKnockoutWidgetPage;

import static data.constants.StringConstants.DARK_THEME_STRING;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.DARK_THEME)
@Tag(WidgetsTags.KNOCKOUT)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballKnockoutSmallFinalWidgetDarkThemeVisualTests extends WidgetsBaseWebTest {

    private FootballKnockoutWidgetPage widgetsPage;

    @Override
    protected void beforeEach() {
        viewPort = getViewPort();
        widgetsPage = new FootballKnockoutWidgetPage().getShortFinalWidgetPage();
        WidgetSettings widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(DataThemeEnum.DARK.getValue());
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetDisplayed_when_setDefaultData_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetMatchPenaltiesInfoDisplayed_when_hoverPenaltiesBadge_desktopM() {
        widgetsPage.map().knockoutGamesSection.penaltiesBadge().hover();
        assertSameAsBaseline(widgetsPage, "%s Penalties badge hovered %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetMatchPenaltiesInfoDisplayed_when_hoverPenaltiesBadge_mobileS() {
        widgetsPage.map().knockoutGamesSection.holdOverPenaltiesBadge();
        assertSameAsBaseline(widgetsPage, "%s Penalties badge hovered %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(DARK_THEME_STRING, viewPort.name()));
    }
}