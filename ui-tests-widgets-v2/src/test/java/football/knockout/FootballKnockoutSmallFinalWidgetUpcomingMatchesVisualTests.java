package football.knockout;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataCompetitionAttribute;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.attributes.DataStageAttribute;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataSeasonEuropeanChampionshipEnum;
import data.widgets.options.enums.DataStageEnum;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.knockoutwidgetpage.FootballKnockoutWidgetPage;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.KNOCKOUT)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballKnockoutSmallFinalWidgetUpcomingMatchesVisualTests extends WidgetsBaseWebTest {

    private static final String TEST_CASE_NAME = "Start time for upcoming matches | %s theme";
    private FootballKnockoutWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballKnockoutWidgetPage().getShortFinalWidgetPage();
        widgetsPage.updateAttributeValue(new DataCompetitionAttribute(DataCompetitionEnum.EUROPEAN_CHAMPIONSHIP));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonEuropeanChampionshipEnum.TWENTY_TWO_TWENTY_FOUR));
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.EUROPEAN_CHAMPIONSHIP_2024_FINAL_STAGE));

        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_desktopTall(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_desktopM(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_mobileS(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_mobileL(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_tablet(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.IPHONE_SE_MOBILE)
    public void widgetStartTimeInfoDisplayed_when_setUpcomingMatches_mobileDevice(DataThemeEnum theme) {
        visualCheckOfUpcomingMatches(theme);
    }

    private void visualCheckOfUpcomingMatches(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.generatePage(widgetsOptions);

        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, TEST_CASE_NAME.formatted(theme.name()));
    }
}