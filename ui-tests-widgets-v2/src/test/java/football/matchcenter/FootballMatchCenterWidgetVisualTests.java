package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.Language;
import data.constants.TypeCodeEnum;
import data.constants.VisualRegressionProject;
import data.constants.apiurls.football.v2.FootballApiUrlV2;
import data.constants.enums.football.FootballTeamPosition;
import data.models.footballapi.v2.matchevents.MatchEventsV2Model;
import data.models.footballapi.v2.matchevents.MatchEventsV2RequestBodyModel;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import j2html.attributes.Attribute;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Tag(SMPCategories.VISUAL)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballMatchCenterWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().sdkOptions.setDataConfigLang(Language.BULGARIAN.getCode());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_TALL.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void widgetDisplayed_when_openStats() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getStatsTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, " StatsView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void widgetDisplayed_when_openTimeline() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getTimelineTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, " TimelineView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void widgetDisplayed_when_openRanking() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getStandingsTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, " RankingView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void widgetDisplayed_when_openH2H() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getH2HTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, " H2HView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void respectiveTeamsHighlightedInStandingsTab_when_openStandingsTab_and_setLeagueStanding() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getStandingsTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, "Teams highlighted in Standings tab for League match");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void respectiveTeamsHighlightedInStandingsTab_when_openStandingsTab_and_setGroupStageStanding() {
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.LIVERPOOL_NAPOLI));
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getStandingsTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, "Teams highlighted in Standings tab for Group Stage match");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void respectiveTeamsNotHighlightedInStandingsTab_when_openStandingsTab_and_setPlayoffStanding() {
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.FOOTBALL_LIVERPOOL_VS_REAL_MADRID_CL_FINAL));
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getStandingsTab().click();

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, "Teams NOT highlighted in Standings tab for Knockout match");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void widgetLoadedCorrectly_when_openTimelineTab_and_primaryPlayerNullInResponseForEvents() {
        String url = FootballApiUrlV2.MATCHES_ID_EVENTS.getUrl().formatted(DataMatchIdEnum.ASTON_VILLA_VS_WOLVERHAMPTON.getId());
        String mockBody = MatchEventsV2RequestBodyModel.builder()
                .events(List.of(MatchEventsV2Model.builder()
                        .id("10467041")
                        .matchId(DataMatchIdEnum.ASTON_VILLA_VS_WOLVERHAMPTON.getId())
                        .typeCode(TypeCodeEnum.YELLOW_CARD.name())
                        .teamPosition(FootballTeamPosition.AWAY.name())
                        .minute(7)
                        .teamId(DataTeamEnum.WOLVERHAMPTON.getId())
                        .primaryPlayer(null)
                        .secondaryPlayer(null)
                        .score(null)
                        .build()
                ))
                .build()
                .toJson();
        ProxyServer.addRequestFilterWithModifiedResponse(url, mockBody, HttpStatus.SC_OK, HEADERS_AVOID_CORS_ISSUES);

        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().getTabsSection().getTimelineTab().click();

        assertSameAsBaseline(widgetsPage, "Widget correctly loaded when primary player is null in response");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth300px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.MOBILE_S.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.MOBILE_S.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth768px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.TABLET.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.TABLET.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }
}