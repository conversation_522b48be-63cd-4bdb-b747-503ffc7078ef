package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataPlayerEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
@Tag(WidgetsTags.EVENTS_WIDGETS)
//TODO: istoyanov 01/04/2025 update with Matches H2H link tests once they are merged to dev
public class FootballMatchCenterWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.ASTON_VILLA.getId()));
        widgetsPage.map().secondTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.WOLVERHAMPTON.getId()));
    }

    @Test
    public void lineupsPlayerAnchorUpdated_when_setDataEntityLinksPlayerUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-player-{playerId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.player.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLineupsMap().getFootballLineupsPlayerDataLabels().validateHrefIs(expectedUrlFormat.replace("{playerId}", DataPlayerEnum.OLLIE_WATKINS.getId()));
    }

    @Test
    public void teamH2HCompetitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{competitionId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getTeamH2HMatchMap().teamOneCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
        widgetsPage.map().getTeamH2HMatchMap().teamTwoCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
    }

    @Test
    public void standingsTeamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getStandingsMap().getGroupsMap().firstTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.LIVERPOOL.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().matchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataTeamEnum.ASTON_VILLA.getId() + "-" + DataTeamEnum.WOLVERHAMPTON.getId() + "#" + DataMatchIdEnum.ASTON_VILLA_VS_WOLVERHAMPTON.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().homeTeamAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().getSingleEventMap().awayTeamAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().matchAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().matchAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}