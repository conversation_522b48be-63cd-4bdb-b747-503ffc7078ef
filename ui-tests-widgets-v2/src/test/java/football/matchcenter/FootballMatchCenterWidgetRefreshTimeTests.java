package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataRefreshTimeAttribute;
import data.widgets.options.enums.DataRefreshTimeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.lineupswidgetpage.FootballLineupsWidgetPage;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;
import widgets.pages.football.singleeventwidgetpage.FootballSingleEventWidgetPage;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Tag(WidgetsTags.REFRESH_TIME_ATTRIBUTE)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballMatchCenterWidgetRefreshTimeTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private FootballSingleEventWidgetPage widgetsSingleEventPage;
    private FootballLineupsWidgetPage widgetsLineupsPage;
    private FootballStandingsWidgetPage widgetsStandingsPage;
    private FootballMatchesH2HWidgetPage widgetsMatchesH2HPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsSingleEventPage = new FootballSingleEventWidgetPage();
        widgetsLineupsPage = new FootballLineupsWidgetPage();
        widgetsStandingsPage = new FootballStandingsWidgetPage();
        widgetsMatchesH2HPage = new FootballMatchesH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void singleDataRequestIsMade_when_setRefreshTime_never() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied. Each widget has its own data request, but some requests are duplicated.
        widgetsPage.assertDataRequestsCount(7, widgetsSingleEventPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(7, widgetsLineupsPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(4, widgetsStandingsPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(7, widgetsMatchesH2HPage.getDataRequestUrl());
    }

    @Story(WidgetsStories.CORE_ATTRIBUTES)
    @ParameterizedTest
    @EnumSource(value = DataRefreshTimeEnum.class, names = {"FAST", "SUPER_FAST"}, mode = EnumSource.Mode.INCLUDE)
    public void singleDataRequestIsMade_when_setRefreshTime_superFast(DataRefreshTimeEnum attributeValue) {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(attributeValue));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        Wait.forMilliseconds(attributeValue.getSecondsValue() * 1000);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(12, widgetsSingleEventPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(12, widgetsLineupsPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(5, widgetsStandingsPage.getDataRequestUrl());
        widgetsPage.assertDataRequestsCount(12, widgetsMatchesH2HPage.getDataRequestUrl());
    }
}