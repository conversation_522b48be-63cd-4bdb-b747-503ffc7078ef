package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataOddsDisplayAttribute;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(project = Project.STATIC)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.ODDS)
@Story(WidgetsStories.H2H)
public class FootballMatchCenterWidgetDataOddsDisplayTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.FOOTBALL_SPAIN_SWITZERLAND_STATIC_PROJECT.getId()));
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @Test
    public void oddsTabDisplayedAfterStandings_setNotStartedMatchWithOdds_and_dataOddsDisplayAttributeTrue() {
        List<String> expectedTabs = List.of(
                WidgetSettingsFactory.getMatchCenterLabels().getMatchInformationTab(),
                WidgetSettingsFactory.getMatchCenterLabels().getMatchTimelineTab(),
                WidgetSettingsFactory.getMatchCenterLabels().getMatchStandingsTab(),
                WidgetSettingsFactory.getMatchCenterLabels().getMatchOddsTab(),
                WidgetSettingsFactory.getMatchCenterLabels().getMatchH2hTab()
        );

        openWidgetPage(widgetsPage, widgetsOptions);
        List<String> actualTabs = widgetsPage.map().getTabsSection().getTabNames();

        widgetsPage.asserts().assertMatchCenterTabs(expectedTabs, actualTabs);
    }

    @Test
    public void oddsTabNotDisplayed_when_setNotStartedMatchWithOdds_and_dataOddsDisplayAttributeFalse() {
        widgetsPage.updateAttributeValue(new DataOddsDisplayAttribute(BooleanEnum.FALSE));

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.asserts().assertOddsTabNotDisplayed();
    }

    @Test
    public void oddsTabNotDisplayed_when_setFinishedMatch() {
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.ASTON_VILLA_VS_BRENTFORD));

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.asserts().assertOddsTabNotDisplayed();
    }

    @Test
    @Tag(SMPCategories.VISUAL)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP)
    public void oddsDisplayedForCurrentlyLoadedMatch_when_openOddsTab() {
        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().getTabsSection().getOddsTab().click();

        assertSameAsBaseline(widgetsPage, "Odds displayed for currently loaded match");
    }
}