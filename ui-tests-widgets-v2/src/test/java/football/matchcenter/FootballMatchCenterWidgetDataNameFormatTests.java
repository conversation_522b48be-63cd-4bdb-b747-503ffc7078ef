package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataMatchCenterMainEventsDisplayAttribute;
import data.widgets.attributes.DataNameFormatAttribute;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataNameFormatEnum;
import data.widgets.options.enums.DataPlayerEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
public class FootballMatchCenterWidgetDataNameFormatTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDataDisplayed_when_setDataNameFormat_defaultValues() {
        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().mainEventFirstParticipantName().validateTextIs(DataPlayerEnum.OLLIE_WATKINS.getLastName());
    }

    @Test
    public void widgetDataDisplayed_when_setDataNameFormat_initials() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchCenterMainEventsDisplayAttribute(BooleanEnum.TRUE));
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));

        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().mainEventFirstParticipantName().validateTextIs(DataPlayerEnum.OLLIE_WATKINS.getInitials());
    }

    @Test
    public void widgetDisplayed_when_setDataNameFormat_lastName() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchCenterMainEventsDisplayAttribute(BooleanEnum.TRUE));
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));

        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().mainEventFirstParticipantName().validateTextIs(DataPlayerEnum.OLLIE_WATKINS.getLastName());
    }
}