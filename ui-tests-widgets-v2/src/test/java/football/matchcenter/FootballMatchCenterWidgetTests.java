package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataWidgetIdAttribute;
import data.widgets.attributes.DataWidgetSportAttribute;
import data.widgets.attributes.DataWidgetTypeAttribute;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballMatchCenterWidgetTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.MATCH_CENTER));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().homeTeamAnchor().validateTextIs(DataTeamEnum.ASTON_VILLA.getFullName());
        widgetsPage.map().getSingleEventMap().awayTeamAnchor().validateTextIs(DataTeamEnum.WOLVERHAMPTON.getFullName());
        widgetsPage.map().getSingleEventMap().scoreAnchor().validateTextIs("3 : 1");
    }

    @Test
    public void widgetDataDisplayed_when_setSportData_dataMatchId() {
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.SC_LYON_VS_ANNECY_FC));
        // Apply configuration

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().homeTeamAnchor().validateTextIs(DataTeamEnum.SC_LYON.getFullName());
        widgetsPage.map().getSingleEventMap().awayTeamAnchor().validateTextIs(DataTeamEnum.ANNECY_FC.getFullName());
        widgetsPage.map().getSingleEventMap().scoreAnchor().validateTextIs("2 : 3");
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.MATCH_CENTER));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.MATCH_CENTER));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs("");
        app().browser().assertNoConsoleErrorsLogged();
    }

    @Test
    public void dateAndTimeDisplayedWithoutGMTXValue_when_checkDateAndTimeValueInInformationTab() {
        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().getMatchInfoMap().getDateAndTimeValue().validateTextNotContains("GMT +");
    }
}