package football.matchcenter;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataMatchCenterMainEventsDisplayAttribute;
import data.widgets.options.enums.BooleanEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchcenterwidgetpage.FootballMatchCenterWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCH_CENTER)
@Story(WidgetsStories.SINGLEEVENT)
@Story(WidgetsStories.LINEUPS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.H2H)
public class FootballMatchCenterWidgetDataMatchCenterMainEventsDisplayTests extends WidgetsBaseWebTest {

    private FootballMatchCenterWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchCenterWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDataDisplayed_when_setDataMatchCenterMainEventsDisplay_defaultValues() {
        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().mainEvents().validateIsVisible();
    }

    @Test
    public void widgetDisplayed_when_setDataMatchCenterMainEventsDisplay_true() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchCenterMainEventsDisplayAttribute(BooleanEnum.TRUE));

        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().mainEvents().validateIsVisible();
    }

    @Test
    public void widgetDataDisplayed_when_setDataMatchCenterMainEventsDisplay_false() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchCenterMainEventsDisplayAttribute(BooleanEnum.FALSE));

        // Navigate to widget page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getSingleEventMap().mainEvents().validateNotVisible();
    }
}