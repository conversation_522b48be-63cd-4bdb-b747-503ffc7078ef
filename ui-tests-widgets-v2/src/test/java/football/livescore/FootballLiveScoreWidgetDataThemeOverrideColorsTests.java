package football.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.livescorewidgetpage.FootballLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballLiveScoreWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillActiveBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().firstTeamName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().livePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPillActiveBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().firstTeamName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().livePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPillActiveBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().firstTeamName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getLivescoreSection().matchRowContainers().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().livePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().allGamesPillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }
}