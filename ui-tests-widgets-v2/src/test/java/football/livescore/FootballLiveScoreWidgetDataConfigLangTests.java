package football.livescore;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.CountryEnum;
import data.constants.Language;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.livescorewidgetpage.FootballLivescoreWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.LIVESCORE)
@Story(WidgetsStories.LIVESCORE)
public class FootballLiveScoreWidgetDataConfigLangTests extends WidgetsBaseWebTest {

    private FootballLivescoreWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballLivescoreWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setDataConfigLang_bg() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigLang(Language.BULGARIAN.getCode());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.PREMIER_LEAGUE.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.ENGLAND.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamName().validateTextIs(DataTeamEnum.CRYSTAL_PALACE.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getNameBg()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("18 ЯНУ"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("КР")
        );
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setDataConfigLang_en() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigLang(Language.ENGLISH.getCode());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.PREMIER_LEAGUE.getName()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.ENGLAND.getName()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamName().validateTextIs(DataTeamEnum.CRYSTAL_PALACE.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("18 JAN"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("FIN")
        );
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetDisplayed_when_setInvalidDataConfigLang() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigLang(StringConstants.INVALID_STRING);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getLivescoreSection().centerMessage().validateIsVisible();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SDK_OPTIONS)
    public void widgetLangIsEn_when_setDataConfigLang_empty() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigLang(StringConstants.EMPTY_STRING);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getLivescoreSection().tournamentName().validateTextIs(DataCompetitionEnum.PREMIER_LEAGUE.getName()),
                () -> widgetsPage.map().getLivescoreSection().countryName().validateTextIs(CountryEnum.ENGLAND.getName()),
                () -> widgetsPage.map().getLivescoreSection().firstTeamName().validateTextIs(DataTeamEnum.CRYSTAL_PALACE.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().secondTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getFullName()),
                () -> widgetsPage.map().getLivescoreSection().firstMatchDate().validateTextIs("18 JAN"),
                () -> widgetsPage.map().getLivescoreSection().firstMatchStatus().validateTextIs("FIN")
        );
    }
}