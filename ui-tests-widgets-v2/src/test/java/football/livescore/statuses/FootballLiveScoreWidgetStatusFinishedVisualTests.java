package football.livescore.statuses;

import categories.SMPCategories;
import categories.WidgetsTags;
import data.constants.VisualRegressionProject;
import data.constants.enums.football.FootballMatchStatus;
import data.widgets.options.enums.DataThemeEnum;
import facades.FootballApiFacade;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.LIVESCORE)
@Tag(WidgetsTags.EVENT_STATUS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballLiveScoreWidgetStatusFinishedVisualTests extends BaseFootballLiveScoreWidgetStatusVisualTests {

    @Override
    public void beforeAll() {
        super.beforeAll();
        new FootballApiFacade(getCurrentTestProject()).updateMatchToFinished(matchId, kickoffDate);
        matchStatus = FootballMatchStatus.FINISHED;
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void expectedWidgetStatusMatchDisplayed_when_finishedStatus_theme_desktopM(DataThemeEnum theme) {
        visualCheckOfStatusWithTheme(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void expectedWidgetStatusMatchDisplayed_when_finishedStatus_theme_mobileS(DataThemeEnum theme) {
        visualCheckOfStatusWithTheme(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void expectedWidgetStatusMatchDisplayed_when_finishedStatus_theme_mobileL(DataThemeEnum theme) {
        visualCheckOfStatusWithTheme(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void expectedWidgetStatusMatchDisplayed_when_finishedStatus_theme_tablet(DataThemeEnum theme) {
        visualCheckOfStatusWithTheme(theme);
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.IPHONE_SE_MOBILE)
    public void expectedWidgetStatusMatchDisplayed_when_finishedStatus_theme_mobileDevice(DataThemeEnum theme) {
        visualCheckOfStatusWithTheme(theme);
    }
}