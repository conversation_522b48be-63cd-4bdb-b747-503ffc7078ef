package football.odds;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.oddswidgetpage.FootballOddsWidgetPage;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.ODDS)
@Story(WidgetsStories.ODDS)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballOddsWidgetDataThemeTests extends WidgetsBaseWebTest {

    private FootballOddsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballOddsWidgetPage(DataMatchIdEnum.FOOTBALL_SPAIN_SWITZERLAND_STATIC_PROJECT.getId());
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITE.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = null;

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
    }
}