package football.odds;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.oddswidgetpage.FootballOddsWidgetPage;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.ODDS)
@Story(WidgetsStories.ODDS)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballOddsWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballOddsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballOddsWidgetPage(DataMatchIdEnum.FOOTBALL_SPAIN_SWITZERLAND_STATIC_PROJECT.getId());
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
        widgetsPage.map().selectedMarketTypeOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().oddOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().odds().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().selectedMarketTypeOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().oddOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().odds().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        openWidgetPage(widgetsPage, widgetsOptions);

        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().selectedMarketTypeOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().oddOption().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().odds().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }
}