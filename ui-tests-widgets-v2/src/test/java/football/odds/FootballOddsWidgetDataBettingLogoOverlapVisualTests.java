package football.odds;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataBettingLogoOverlapAttribute;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import io.visual_regression_tracker.sdk_java.TestRunResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.oddswidgetpage.FootballOddsWidgetPage;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.ODDS)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballOddsWidgetDataBettingLogoOverlapVisualTests extends WidgetsBaseWebTest {

    private static final String ODDS_DISPLAYED = "Odds displayed";
    private static final String ODD_PROVIDER_LOGO_DISPLAYED = "Odd provider logo displayed";
    private FootballOddsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private TestRunResult oddsScreenshot;
    private TestRunResult oddProviderLogoScreenshot;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballOddsWidgetPage(DataMatchIdEnum.FOOTBALL_SPAIN_SWITZERLAND_STATIC_PROJECT.getId());
        widgetsPage.updateAttributeValue(new DataBettingLogoOverlapAttribute(BooleanEnum.TRUE));

        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void oddsAndOddProviderLogoSwitched_when_openFootballOddsWidget_and_setDataBettingLogoOverlapTrue_desktopM() {
        oddProviderLogoScreenshot = takeScreenshot(widgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 0);
        oddsScreenshot = takeScreenshot(widgetsPage, ODDS_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddProviderLogoScreenshot),
                () -> assertSameAsBaseline(oddsScreenshot));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void oddsAndOddProviderLogoSwitched_when_openFootballOddsWidget_and_setDataBettingLogoOverlapTrue_mobileS() {
        oddProviderLogoScreenshot = takeScreenshot(widgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 500);
        oddsScreenshot = takeScreenshot(widgetsPage, ODDS_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddProviderLogoScreenshot),
                () -> assertSameAsBaseline(oddsScreenshot));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void oddsAndOddProviderLogoSwitched_when_openFootballOddsWidget_and_setDataBettingLogoOverlapTrue_mobileL() {
        oddProviderLogoScreenshot = takeScreenshot(widgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 100);
        oddsScreenshot = takeScreenshot(widgetsPage, ODDS_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddProviderLogoScreenshot),
                () -> assertSameAsBaseline(oddsScreenshot));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void oddsAndOddProviderLogoSwitched_when_openFootballOddsWidget_and_setDataBettingLogoOverlapTrue_tablet() {
        oddProviderLogoScreenshot = takeScreenshot(widgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 500);
        oddsScreenshot = takeScreenshot(widgetsPage, ODDS_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddProviderLogoScreenshot),
                () -> assertSameAsBaseline(oddsScreenshot));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.IPHONE_SE_MOBILE)
    public void oddsAndOddProviderLogoSwitched_when_openFootballOddsWidget_and_setDataBettingLogoOverlapTrue_mobileDevice() {
        oddProviderLogoScreenshot = takeScreenshot(widgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 0);
        oddsScreenshot = takeScreenshot(widgetsPage, ODDS_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddProviderLogoScreenshot),
                () -> assertSameAsBaseline(oddsScreenshot));
    }
}