package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataRefreshTimeAttribute;
import data.widgets.options.enums.DataRefreshTimeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Tag(WidgetsTags.REFRESH_TIME_ATTRIBUTE)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetRefreshTimeTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void singleDataRequestIsMade_when_setRefreshTime_never() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(1, widgetsPage.getDataRequestUrl());
    }

    @Story(WidgetsStories.CORE_ATTRIBUTES)
    @ParameterizedTest
    @EnumSource(value = DataRefreshTimeEnum.class, names = {"FAST", "SUPER_FAST"}, mode = EnumSource.Mode.INCLUDE)
    public void singleDataRequestIsMade_when_setRefreshTime_superFast(DataRefreshTimeEnum attributeValue) {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(attributeValue));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        Wait.forMilliseconds(attributeValue.getSecondsValue() * 1000);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(2, widgetsPage.getDataRequestUrl());
    }
}