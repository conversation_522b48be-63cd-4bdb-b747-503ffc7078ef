package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.models.footballapi.teams.TeamModel;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.football.FootballTeamsHttpRepository;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_LABELS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private FootballTeamsHttpRepository footballTeamsHttpRepo = new FootballTeamsHttpRepository(getCurrentTestProject());
    private TeamModel teamResponse = footballTeamsHttpRepo.getById(DataTeamEnum.BAYERN_MUNICH.getId()).getResult();

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    // Info
    @Test
    public void widgetInfoLabelUpdated_when_setDataLabels_founded() {
        // Get founded year
        String foundedYear = teamResponse.getFounded();
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().foundedInfoLabel().validateTextIs(expectedDataLabels.est + ": " + foundedYear);
    }

    // Stats
    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_wins() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(1).validateHtmlIs(expectedDataLabels.win);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_draws() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(2).validateHtmlIs(expectedDataLabels.draw);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_played() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(0).validateHtmlIs(expectedDataLabels.playedShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_goalsConceded() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(5).validateHtmlIs(expectedDataLabels.goalsConceded);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_goalsScored() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(4).validateHtmlIs(expectedDataLabels.goalsScored);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_defeats() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(3).validateHtmlIs(expectedDataLabels.defeats);
    }

    @Test
    public void widgetInfoLabelUpdated_when_setDataLabelsDefaultValues() {
        // Get founded year
        String foundedYear = teamResponse.getFounded();
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().foundedInfoLabel().validateTextIs(expectedDataLabels.est + ": " + foundedYear);
    }

    // Stats
    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().statsLabels().get(0).validateHtmlIs(expectedDataLabels.playedShort);
        widgetsPage.map().statsLabels().get(1).validateHtmlIs(expectedDataLabels.win);
        widgetsPage.map().statsLabels().get(2).validateHtmlIs(expectedDataLabels.draw);
        widgetsPage.map().statsLabels().get(3).validateHtmlIs(expectedDataLabels.defeats);
        widgetsPage.map().statsLabels().get(4).validateHtmlIs(expectedDataLabels.goalsScored);
        widgetsPage.map().statsLabels().get(5).validateHtmlIs(expectedDataLabels.goalsConceded);
    }
}