package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.enums.TeamDataElementsStatisticsEnum;
import data.widgets.attributes.DataElementsAttribute;
import data.widgets.options.models.DataElements;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetDataElementsTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void expectedStatisticsDisplayed_when_setMinimumNumberOfVisibleDataElements() {
        // Apply configuration
        DataElements expectedDataElements = WidgetSettingsFactory.getTeamDataElements();
        expectedDataElements.setStats(List.of(
                TeamDataElementsStatisticsEnum.RANK.getLabel(),
                TeamDataElementsStatisticsEnum.GOALS_SCORED.getLabel(),
                TeamDataElementsStatisticsEnum.WIN.getLabel()
        ));
        widgetsPage.updateAttributeValue(new DataElementsAttribute(expectedDataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts()
                .assertCountOfDisplayedStatisticsIs(expectedDataElements.getStats().size())
                .assertDisplayedStatisticsAre(expectedDataElements);
    }

    @Test
    public void defaultStatisticsDisplayed_when_setUnderMinimumNumberOfVisibleDataElements() {
        int expectedStatisticsCount = 6;

        // Apply configuration
        DataElements expectedDataElements = WidgetSettingsFactory.getTeamDataElements();
        expectedDataElements.setStats(List.of(
                TeamDataElementsStatisticsEnum.RANK.getLabel(),
                TeamDataElementsStatisticsEnum.GOALS_SCORED.getLabel()
        ));
        widgetsPage.updateAttributeValue(new DataElementsAttribute(expectedDataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts()
                .assertCountOfDisplayedStatisticsIs(expectedStatisticsCount)
                .assertDisplayedStatisticsAre(WidgetSettingsFactory.getTeamDefaultDataElements());
    }

    @Test
    public void defaultStatisticsDisplayed_when_setAboveMaximumNumberOfVisibleDataElements() {
        int expectedStatisticsCount = 6;

        // Apply configuration
        DataElements expectedDataElements = WidgetSettingsFactory.getTeamDataElements();
        widgetsPage.updateAttributeValue(new DataElementsAttribute(expectedDataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.asserts()
                .assertCountOfDisplayedStatisticsIs(expectedStatisticsCount)
                .assertDisplayedStatisticsAre(WidgetSettingsFactory.getTeamDefaultDataElements());
    }
}