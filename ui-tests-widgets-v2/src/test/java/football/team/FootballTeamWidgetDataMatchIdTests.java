package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.attributes.DataTeamAttribute;
import data.widgets.options.enums.DataMatchIdEnum;
import facades.FootballApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetDataMatchIdTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void nextMatchWithOddsDisplayed_when_dataMatchIdAttributeNotSet() {
        // Apply configuration
        MatchV2Model matchWithOdds = new FootballApiFacade(getCurrentTestProject()).getFootballEventWithOdds();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigOddClient(StringConstants.SPORTAL_365_STRING);
        widgetsPage.updateAttributeValue(new DataTeamAttribute(matchWithOdds.getHomeTeam().getId()));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(matchWithOdds.getSeason().getId()));
        widgetsPage.removeAttribute(new DataMatchIdAttribute(DataMatchIdEnum.SC_LYON_VS_ANNECY_FC));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().matchTournament().validateTextIs(matchWithOdds.getSeason().getTournament().getName());
        widgetsPage.map().homeTeamName().validateTextIs(matchWithOdds.getHomeTeam().getName());
        widgetsPage.map().awayTeamName().validateTextIs(matchWithOdds.getAwayTeam().getName());
        widgetsPage.map().oddsContainer().validateIsVisible();
    }
}