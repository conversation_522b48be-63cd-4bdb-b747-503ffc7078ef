package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetDataTeamShortNameTypeTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDisplayed_when_setDataTeamShortStatusType_shortName() {
        String expectedHomeTeam = DataTeamEnum.SC_LYON.getShortName();
        String expectedAwayTeam = DataTeamEnum.ANNECY_FC.getShortName();
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.SHORT_NAME));
        widgetsPage.updateAttributeValue(new DataDisplaySocialIconsAttribute(Boolean.TRUE));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().validateTextIs(expectedHomeTeam);
        widgetsPage.map().awayTeamName().validateTextIs(expectedAwayTeam);
    }

    @Test
//    @Issue("SFE-4306")
    public void widgetDataDisplayed_when_setDataTeamShortStatusType_threeLetterCode() {
        String expectedHomeTeam = DataTeamEnum.SC_LYON.getThreeLetterCode();
        String expectedAwayTeam = DataTeamEnum.ANNECY_FC.getThreeLetterCode();
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));
        widgetsPage.updateAttributeValue(new DataDisplaySocialIconsAttribute(Boolean.FALSE));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamName().validateTextIs(expectedHomeTeam);
        widgetsPage.map().awayTeamName().validateTextIs(expectedAwayTeam);
    }

    @Test
//    @Issue("SFE-4306")
    public void widgetDataDisplayed_when_setDataTeamShortStatusType_defaultValues() {
        String expectedValueTeam = DataTeamEnum.MANCHESTER_UNITED.getFullName();
        String expectedHomeTeam = DataTeamEnum.SC_LYON.getFullName();
        String expectedAwayTeam = DataTeamEnum.ANNECY_FC.getFullName();
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.removeAttribute(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamName().validateTextIs(expectedValueTeam);
        widgetsPage.map().homeTeamName().validateTextIs(expectedHomeTeam);
        widgetsPage.map().awayTeamName().validateTextIs(expectedAwayTeam);
    }
}