package football.team;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.CountryEnum;
import data.constants.StringConstants;
import data.constants.enums.VenueEnum;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamwidgetpage.FootballTeamWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAM)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamWidgetTests extends WidgetsBaseWebTest {

    private FootballTeamWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamName().validateTextIs(DataTeamEnum.BAYERN_MUNICH.getFullName());
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData_dataTeamId() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.BAYERN_MUNICH));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamName().validateTextIs(DataTeamEnum.BAYERN_MUNICH.getFullName());
    }

    @Test
    public void widgetDataDisplayed_when_setSportData_dataMatchId() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.SC_LYON_VS_ANNECY_FC));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.matchPage.map().homeTeam().validateTextIs(DataTeamEnum.SC_LYON.getFullName());
        widgetsPage.matchPage.map().awayTeamName().validateTextIs(DataTeamEnum.ANNECY_FC.getFullName());
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs(StringConstants.EMPTY_STRING);
        app().browser().assertNoConsoleErrorsLogged();
    }

    @Test
    public void widgetTeamCountryDisplayedCorrectly_when_setSportData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertEquals(widgetsPage.map().countryInfoLabel().getText(), CountryEnum.ENGLAND.getName(), "Team country should be displayed");
    }

    @Test
    public void widgetTeamStadiumDisplayedCorrectly_when_setSportData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertEquals(widgetsPage.map().venueInfoLabel().getText(), VenueEnum.OLD_TRAFFORD.getName(), "Team stadium should be displayed");
    }


    @Test
    public void widgetTeamFoundedYearDisplayedCorrectly_when_setSportData() {
        String foundedYear = "Est: 1878";
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertEquals(foundedYear, widgetsPage.map().foundedInfoLabel().getText(), "Founded team year should be displayed");
    }

    @Test
    public void widgetTeamLogoDisplayedCorrectly_when_setSportData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamImageLogo().validateIsVisible();
    }

    @Test
    public void widgetTeamLastFiveMatchesFormDisplayed_when_setSportData_and_lastFiveMatchesDisplayed() {
        int expectedFormMatchesCount = 5;
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));
        widgetsPage.updateAttributeValue(new DataTeamAttribute(DataTeamEnum.MANCHESTER_UNITED));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedFormMatchesCount, widgetsPage.map().teamFormDesktopLabel().size(),
                        "The number of the last 5 matches should be displayed.\nExpected: %s Actual: %s"
                                .formatted(expectedFormMatchesCount, widgetsPage.map().teamFormDesktopLabel().size())),
                () -> Assertions.assertEquals(expectedFormMatchesCount, widgetsPage.map().teamFormMobileLabel().size(),
                        "The number of the last 5 matches should be displayed.\nExpected: %s Actual: %s"
                                .formatted(expectedFormMatchesCount, widgetsPage.map().teamFormMobileLabel().size())));
    }

    @Test
    public void widgetTeamActiveCompetitionsDisplayed_when_setSportData_and_currentCompetitionDisplayed() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().currentSeasonLabel().validateIsVisible();
    }
}