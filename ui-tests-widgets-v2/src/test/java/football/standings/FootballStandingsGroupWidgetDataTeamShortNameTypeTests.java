package football.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.attributes.DataStageAttribute;
import data.widgets.attributes.DataTeamNameTypeAttribute;
import data.widgets.options.enums.DataSeasonPremierLeagueEnum;
import data.widgets.options.enums.DataStageEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.enums.DataTeamNameTypeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Story(WidgetsStories.STANDINGS)
public class FootballStandingsGroupWidgetDataTeamShortNameTypeTests extends WidgetsBaseWebTest {

    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDisplayed_when_setDataTeamNameType_threeLetterCode() {
        String expectedValueThirdTeam = DataTeamEnum.MANCHESTER_UNITED.getThreeLetterCode();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.PREMIER_LEAGUE_2022_2023));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().thirdTeamNameAnchor().validateTextIs(expectedValueThirdTeam);
    }

    @Test
    public void widgetDisplayed_when_setDataTeamNameType_shortName() {
        String expectedValueThirdTeam = DataTeamEnum.MANCHESTER_UNITED.getShortName();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.PREMIER_LEAGUE_2022_2023));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.SHORT_NAME));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().thirdTeamNameAnchor().validateTextIs(expectedValueThirdTeam);
    }

    @Test
//    @Issue("SFE-4306")
    public void widgetDataDisplayed_when_setDataTeamNameType_defaultValues() {
        String expectedValueThirdTeam = DataTeamEnum.MANCHESTER_UNITED.getFullName();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.PREMIER_LEAGUE_2022_2023));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.removeAttribute(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().thirdTeamNameAnchor().validateTextIs(expectedValueThirdTeam);
    }
}