package football.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(WidgetsTags.STANDINGS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class FootballStandingsGroupsWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage().getGroupStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied

        widgetsPage.groupsMap().firstTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.SSC_NAPOLI.getId()));
    }

    @Test
    public void leagueAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{competitionId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().tournamentAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.CHAMPIONS_LEAGUE.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().firstTeamAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().tournamentAnchor().validateHrefIs(StringConstants.EMPTY_STRING);
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().firstTeamName().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.groupsMap().firstTeamName().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}