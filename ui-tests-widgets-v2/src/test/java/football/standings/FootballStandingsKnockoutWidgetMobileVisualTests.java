package football.standings;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballStandingsKnockoutWidgetMobileVisualTests extends WidgetsBaseWebTest {

    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage().getKnockoutStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void widgetDisplayed_when_setDefaultData_mobileSwipe() {
        widgetsPage.knockoutMap().getKnockoutGamesSection().swipeToNextStage();

        assertSameAsBaseline(widgetsPage, DeviceName.IPHONE_SE_MOBILE + " Swipe");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void widgetDisplayed_when_setDefaultData_mobileTap() {
        widgetsPage.knockoutMap().getKnockoutGamesSection().tapToPreviousStage();
        widgetsPage.knockoutMap().getKnockoutGamesSection().tapToPreviousStage();

        assertSameAsBaseline(widgetsPage, DeviceName.IPHONE_SE_MOBILE + " Tap");
    }
}