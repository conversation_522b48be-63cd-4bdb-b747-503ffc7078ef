package football.standings;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballStandingsGroupsWidgetVisualTests extends WidgetsBaseWebTest {

    private static final String STANDINGS_GROUPS_STRING = "StandingsGroups";
    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage().getGroupStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(ViewportSize.DESKTOP_M, STANDINGS_GROUPS_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(ViewportSize.MOBILE_S, STANDINGS_GROUPS_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(ViewportSize.TABLET, STANDINGS_GROUPS_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s %s".formatted(ViewportSize.MOBILE_L, STANDINGS_GROUPS_STRING));
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_mobile() {
        widgetsPage.map().getSeasonSelect().selectButton().click();
        widgetsPage.waitForSpinners();

        assertSameAsBaseline(widgetsPage, "%s Dropdown State Mobile (%s) ".formatted(STANDINGS_GROUPS_STRING,
                DeviceName.IPHONE_SE_MOBILE.getName()));
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_selectValue_mobile() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();

        assertSameAsBaseline(widgetsPage, "%s Dropdown Selection Mobile (%s) ".formatted(STANDINGS_GROUPS_STRING,
                DeviceName.IPHONE_SE_MOBILE.getName()));
    }
}