package football.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Story(WidgetsStories.KNOCKOUT)
public class FootballStandingsKnockoutSmallFinalWidgetTests extends WidgetsBaseWebTest {

    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage().getKnockoutSmallFinalStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.STANDINGS));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs("2022");
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataStageAttribute(DataStageEnum.KNOCKOUT_SMALL_FINAL_STAGE));
        widgetsPage.updateAttributeValue(new DataGroupsAttribute(DataGroupsEnum.EMPTY));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs("2022");
        widgetsPage.map().tournamentAnchor().validateTextIs("World Cup\nInternational");
    }
}