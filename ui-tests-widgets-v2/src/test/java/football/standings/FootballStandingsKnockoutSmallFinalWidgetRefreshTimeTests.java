package football.standings;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataRefreshTimeAttribute;
import data.widgets.options.enums.DataRefreshTimeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.standingswidgetpage.FootballStandingsWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Tag(WidgetsTags.REFRESH_TIME_ATTRIBUTE)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Story(WidgetsStories.KNOCKOUT)
public class FootballStandingsKnockoutSmallFinalWidgetRefreshTimeTests extends WidgetsBaseWebTest {

    private FootballStandingsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStandingsWidgetPage().getKnockoutSmallFinalStandingsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void singleDataRequestIsMade_when_setRefreshTime_never() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(4, widgetsPage.getDataRequestUrl());
    }

    @Story(WidgetsStories.CORE_ATTRIBUTES)
    @ParameterizedTest
    @EnumSource(value = DataRefreshTimeEnum.class, names = {"FAST", "SUPER_FAST"}, mode = EnumSource.Mode.INCLUDE)
    public void singleDataRequestIsMade_when_setRefreshTime_superFast(DataRefreshTimeEnum attributeValue) {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(attributeValue));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        Wait.forMilliseconds(attributeValue.getSecondsValue() * 1000);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(5, widgetsPage.getDataRequestUrl());
    }
}