package football.teamh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataElementsAttribute;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamh2hwidgetpage.FootballTeamH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballTeamH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.H2H)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTeamH2HDataElements()));

        var expectedDataLabels = WidgetSettingsFactory.getTeamH2HDataTabsInfoLabels();

        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.defeats)), "defeats header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.draw)), "draw header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.goalsConceded)), "goalsConceded header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.goalsScored)), "goalsScored header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.playedShort)), "played header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.win)), "win header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.pointsShort)), "labelPoints header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.rank)), "rank header not found");

        assertSameAsBaseline(widgetsPage, " Team H2H Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    @Story(WidgetsStories.H2H)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_mobileL() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTeamH2HDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L + " Team H2H Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    @Story(WidgetsStories.H2H)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_tablet() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTeamH2HDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET + " Team H2H Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    @Story(WidgetsStories.H2H)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_mobileS() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTeamH2HDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S + " Team H2H Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.H2H)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void allStatsDisplayed_when_setDataElementsNull() {
        widgetsPage.removeAttribute(new DataElementsAttribute(WidgetSettingsFactory.getTeamDataElements()));

        // Expected Headers
        assertSameAsBaseline(widgetsPage, " All Stats displayed when Data Elements Null");
    }
}