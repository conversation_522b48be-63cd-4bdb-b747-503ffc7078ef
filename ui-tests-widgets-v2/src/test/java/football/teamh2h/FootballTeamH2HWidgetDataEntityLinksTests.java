package football.teamh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamh2hwidgetpage.FootballTeamH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballTeamH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
//    @Issue("SFE-4269")
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamOneAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.ARSENAL.getId()));
        widgetsPage.map().teamTwoAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", DataTeamEnum.ASTON_VILLA.getId()));
    }

    @Test
    public void teamCompetitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{competitionId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamOneCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
        widgetsPage.map().teamTwoCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
    }

    @Test
//    @Issue("SFE-4269")
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamOneCompetitionAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().teamOneAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");

        widgetsPage.map().teamTwoCompetitionAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
        widgetsPage.map().teamTwoAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#");
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamOne().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().teamOne().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}