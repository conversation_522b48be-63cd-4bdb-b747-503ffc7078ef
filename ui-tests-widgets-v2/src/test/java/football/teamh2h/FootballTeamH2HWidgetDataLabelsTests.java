package football.teamh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.enums.TeamDataElementsStatisticsEnum;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamh2hwidgetpage.FootballTeamH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_LABELS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballTeamH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @ParameterizedTest
    @EnumSource(TeamDataElementsStatisticsEnum.class)
    public void widgetStatsLabelUpdated_when_setDataLabels(TeamDataElementsStatisticsEnum statistic) {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getTeamH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertNotNull(widgetsPage.map().getStatsLabelByText(statistic.getValue()),
                "Statistic not found: %s".formatted(statistic.getValue()));
    }
}