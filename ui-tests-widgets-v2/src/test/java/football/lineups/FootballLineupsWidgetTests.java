package football.lineups;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataWidgetIdAttribute;
import data.widgets.attributes.DataWidgetSportAttribute;
import data.widgets.attributes.DataWidgetTypeAttribute;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.lineupswidgetpage.FootballLineupsWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.LINEUPS)
@Story(WidgetsStories.PROFILE)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballLineupsWidgetTests extends WidgetsBaseWebTest {

    private FootballLineupsWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballLineupsWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultDataClubTeams() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstTeamName().validateTextIs(DataTeamEnum.BARCELONA.getFullName());
        widgetsPage.map().secondTeamName().validateTextIs(DataTeamEnum.BSC_YOUNG_BOYS.getFullName());
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultDataNationalTeams() {
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.FOOTBALL_SPAIN_VS_SWITZERLAND));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstTeamName().validateTextIs(DataTeamEnum.FOOTBALL_SWITZERLAND.getFullName());
        widgetsPage.map().secondTeamName().validateTextIs(DataTeamEnum.FOOTBALL_SPAIN.getFullName());
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.LINEUPS));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.LINEUPS));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.EVENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs(StringConstants.EMPTY_STRING);
        app().browser().assertNoConsoleErrorsLogged();
    }
}