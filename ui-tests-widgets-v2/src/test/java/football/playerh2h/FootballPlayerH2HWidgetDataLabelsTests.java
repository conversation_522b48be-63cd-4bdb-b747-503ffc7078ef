package football.playerh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.enums.football.FootballStatisticsEnum;
import data.widgets.attributes.DataElementsAttribute;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.playerh2hwidgetpage.FootballPlayerH2HWidgetPage;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYERH2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_LABELS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballPlayerH2HWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballPlayerH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballPlayerH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    // Stats
    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_matchesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getMatchesPlayedLabel().validateHtmlIs(expectedDataLabels.played);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_minutesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getMinutesPlayedLabel().validateHtmlIs(expectedDataLabels.minutes);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_goals() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.GOALS.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getGoalsLabel().validateHtmlIs(expectedDataLabels.goals);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_assists() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getAssistsLabel().validateHtmlIs(expectedDataLabels.assists);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_foulsCommitted() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.FOULS_COMMITTED.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getFoulsLabel().validateHtmlIs(expectedDataLabels.foulsCommitted);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_shots() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.SHOTS.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getShotsLabel().validateHtmlIs(expectedDataLabels.shots);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_shotsOnTarget() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.SHOTS_ON_TARGET.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getShotsOnTargetLabel().validateHtmlIs(expectedDataLabels.shotsOnTarget);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_redCards() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getRedCardsLabel().validateHtmlIs(expectedDataLabels.redCards);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabels_yellowCards() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getYellowCardsLabel().validateHtmlIs(expectedDataLabels.yellowCards);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_matchesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getMatchesPlayedLabel().validateHtmlIs(expectedDataLabels.playedShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_minutesPlayed() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getMinutesPlayedLabel().validateHtmlIs(expectedDataLabels.minutesShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_goals() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.GOALS.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getGoalsLabel().validateHtmlIs(expectedDataLabels.goalsShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_assists() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getAssistsLabel().validateHtmlIs(expectedDataLabels.assistsShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_foulsCommitted() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.FOULS_COMMITTED.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getFoulsLabel().validateHtmlIs(expectedDataLabels.foulsCommitted);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_shots() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.SHOTS.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getShotsLabel().validateHtmlIs(expectedDataLabels.shots);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_shotsOnTarget() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        List<String> dataElements = WidgetSettingsFactory.getPlayerH2HDataElements();
        dataElements.add(FootballStatisticsEnum.SHOTS_ON_TARGET.name().toLowerCase());
        widgetsPage.updateAttributeValue(new DataElementsAttribute(dataElements));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getShotsOnTargetLabel().validateHtmlIs(expectedDataLabels.shotsOnTarget);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_redCards() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getRedCardsLabel().validateHtmlIs(expectedDataLabels.redCardsShort);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_yellowCards() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getPlayerH2HDataTabsInfoLabelsDefaultValues();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getYellowCardsLabel().validateHtmlIs(expectedDataLabels.yellowCardsShort);
    }
}