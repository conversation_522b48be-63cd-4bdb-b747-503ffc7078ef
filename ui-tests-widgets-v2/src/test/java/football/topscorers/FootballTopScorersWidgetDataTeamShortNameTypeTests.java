package football.topscorers;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.attributes.DataTeamIdsAttribute;
import data.widgets.attributes.DataTeamNameTypeAttribute;
import data.widgets.options.enums.DataSeasonPremierLeagueEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.enums.DataTeamNameTypeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.topscorerswidgetpage.FootballTopScorersWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STANDINGS)
@Story(WidgetsStories.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTopScorersWidgetDataTeamShortNameTypeTests extends WidgetsBaseWebTest {

    private FootballTopScorersWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTopScorersWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDisplayed_when_setDataTeamShortStatusType_threeLetterCode() {
        String expectedValue = DataTeamEnum.MANCHESTER_UNITED.getThreeLetterCode();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamIdsAttribute(WidgetSettingsFactory.getDataTeamIds(DataTeamEnum.MANCHESTER_UNITED.getId())));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().playerOneTeam().validateTextIs(expectedValue);
    }

    @Test
    public void widgetDisplayed_when_setDataTeamShortStatusType_shortName() {
        String expectedValue = DataTeamEnum.MANCHESTER_UNITED.getShortName();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamIdsAttribute(WidgetSettingsFactory.getDataTeamIds(DataTeamEnum.MANCHESTER_UNITED.getId())));
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.SHORT_NAME));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().playerOneTeam().validateTextIs(expectedValue);
    }

    @Test
//    @Issue("SFE-4306")
    public void widgetDataDisplayed_when_setDataTeamShortNameType_defaultValues() {
        String expectedValue = DataTeamEnum.MANCHESTER_UNITED.getFullName();

        // Apply configuration
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE));
        widgetsPage.updateAttributeValue(new DataTeamIdsAttribute(WidgetSettingsFactory.getDataTeamIds(DataTeamEnum.MANCHESTER_UNITED.getId())));
        widgetsPage.removeAttribute(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().playerOneTeam().validateTextIs(expectedValue);
    }
}