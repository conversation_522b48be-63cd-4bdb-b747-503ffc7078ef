package football.topscorers;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.topscorerswidgetpage.FootballTopScorersWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TOPSCORERS)
@Story(WidgetsStories.STANDINGS)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTopScorersWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballTopScorersWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTopScorersWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHighLighted(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPlayerHeader().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(0, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(1, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHighLighted(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPlayerHeader().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(0, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(1, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHighLighted(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getPlayerHeader().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(0, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTableSection().getCellByIndex(1, 0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }
}