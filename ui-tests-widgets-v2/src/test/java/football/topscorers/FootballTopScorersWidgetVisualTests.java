package football.topscorers;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataElementsAttribute;
import data.widgets.attributes.DataHeaderDisplayAttribute;
import data.widgets.options.enums.BooleanEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.topscorerswidgetpage.FootballTopScorersWidgetPage;

import java.util.ArrayList;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TOPSCORERS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballTopScorersWidgetVisualTests extends WidgetsBaseWebTest {
    private FootballTopScorersWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTopScorersWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_desktop() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTopScorersDataElements()));

        var expectedDataLabels = WidgetSettingsFactory.getTopScorersDataLabels();
        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.missedPenaltiesShort)), "missedPenalties header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.assistsShort)), "assists header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.goalsShort)), "goals header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.minutesShort)), "minutes header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.penaltiesShort)), "penalties header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.playedShort)), "played header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.redCardsShort)), "redCards header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.scoredFirstShort)), "scoredFirst header not found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.yellowCardsShort)), "yellowCards header not found");

        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M + " Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_mobileL() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTopScorersDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L + " Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_tablet() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTopScorersDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET + " Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_mobileS() {
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getTopScorersDataElements()));

        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S + " Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsNotDisplayed_when_setDataElementsNull() {
        widgetsPage.removeAttribute(new DataElementsAttribute(WidgetSettingsFactory.getTopScorersDataElements()));

        var expectedDataLabels = WidgetSettingsFactory.getTopScorersDataLabels();

        // Expected Headers
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.missedPenaltiesShort)), "missed_penalties header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.assistsShort)), "assists header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.goalsShort)), "goals header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.minutesShort)), "minutes header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.penaltiesShort)), "penalties header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.playedShort)), "played header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.redCardsShort)), "red_cards header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.scoredFirstShort)), "scored_first header was found");
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.yellowCardsShort)), "yellow_cards header was found");

        assertSameAsBaseline(widgetsPage, "Data Elements Null");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setDataElementsValues_singleProperty() {
        // Apply configuration
        var element = new ArrayList<String>();
        element.add("yellow_cards");

        widgetsPage.updateAttributeValue(new DataElementsAttribute(element));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        var expectedDataLabels = WidgetSettingsFactory.getTopScorersDataLabels();
        // Expected Header
        Assertions.assertTrue(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.yellowCardsShort)), "Yellow Cards header not found");

        // Not Expected Headers
        Assertions.assertFalse(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.firstYellowCardsShort)), "First Yellow Cards header was found");
        Assertions.assertFalse(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.redCardsShort)), "Red Cards header was found");
        Assertions.assertFalse(widgetsPage.map().getTableSection().getTableHeaders().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.totalCardsShort)), "Total Cards header was found");

        assertSameAsBaseline(widgetsPage, "Single Property");
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_mobile() {
        widgetsPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        widgetsPage.generatePage(widgetsOptions);

        widgetsPage.open();
        widgetsPage.map().getSeasonSelect().selectButton().click();

        assertSameAsBaseline(widgetsPage, "Dropdown State Mobile (%s) ".formatted(DeviceName.IPHONE_SE_MOBILE.getName()));
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_selectValue_mobile() {
        widgetsPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.TRUE));
        widgetsPage.generatePage(widgetsOptions);

        widgetsPage.open();
        widgetsPage.map().getSeasonSelect().selectPreviousSeason();
        widgetsPage.waitForSpinners();

        assertSameAsBaseline(widgetsPage, "Dropdown Selection Mobile (%s) ".formatted(DeviceName.IPHONE_SE_MOBILE.getName()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthMobileS_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.MOBILE_S.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthTablet_desktop() {
        widgetsPage.setWidgetMaxWidth(ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-" + ViewportSize.TABLET.getWidth() + StringConstants.PX_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthPercentage_desktop() {
        widgetsPage.setWidgetMaxWidth("40%");

        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, StringConstants.MAX_WIDTH_STRING + "-40percent");
    }
}