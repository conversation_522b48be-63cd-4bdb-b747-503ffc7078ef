package football.topscorers;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.topscorerswidgetpage.FootballTopScorersWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TOPSCORERS)
@Story(WidgetsStories.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTopScorersWidgetTests extends WidgetsBaseWebTest {

    private FootballTopScorersWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTopScorersWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TOP_SCORERS));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().playerOneName().validateTextIs(DataPlayerEnum.RADOSLAV_KIRILOV.getNameEn());
        widgetsPage.map().playerOneTeam().validateTextIs(DataTeamEnum.SLAVIA_SOFIA.getFullName());
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
    public void widgetDataDisplayed_when_setSportData_dataTeamIds_dataSeason() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataTeamIdsAttribute(WidgetSettingsFactory.getDataTeamIds(DataTeamEnum.SLAVIA_SOFIA.getId())));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonEfbetLeagueEnum.TWENTY_ONE_TWENTY_TWO));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().playerOneName().validateTextIs(DataPlayerEnum.RADOSLAV_KIRILOV.getNameEn());
        widgetsPage.map().playerOneTeam().validateTextIs(DataTeamEnum.SLAVIA_SOFIA.getFullName());
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TOP_SCORERS));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TOP_SCORERS));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs(StringConstants.EMPTY_STRING);
        app().browser().assertNoConsoleErrorsLogged();
    }
}