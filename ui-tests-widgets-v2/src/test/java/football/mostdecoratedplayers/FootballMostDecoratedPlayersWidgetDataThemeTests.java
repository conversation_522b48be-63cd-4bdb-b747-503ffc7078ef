package football.mostdecoratedplayers;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.mostdecoratedplayerswidgetpage.FootballMostDecoratedPlayersWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MOSTDECORATEDPLAYERS)
@Story(WidgetsStories.STANDINGS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Story(WidgetsStories.DATA_THEME)
public class FootballMostDecoratedPlayersWidgetDataThemeTests extends WidgetsBaseWebTest {

    private FootballMostDecoratedPlayersWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMostDecoratedPlayersWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.BLACK.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITESMOKE.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.DARKRED.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.WHITESMOKE.getColorValue().asRgba());
    }
}