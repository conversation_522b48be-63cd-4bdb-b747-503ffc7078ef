package football.player;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballPlayerWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballPlayerWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPlayerPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().playerName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getSeasonDropdownValueByIndex(0).validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPlayerPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().playerName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getSeasonDropdownValueByIndex(0).validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPlayerPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setTabRowBackgroundColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().playerName().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getSeasonDropdownValueByIndex(0).validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }
}