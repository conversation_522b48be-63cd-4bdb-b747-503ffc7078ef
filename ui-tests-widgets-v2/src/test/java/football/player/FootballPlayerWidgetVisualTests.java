package football.player;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataCompetitionAttribute;
import data.widgets.attributes.DataElementsAttribute;
import data.widgets.attributes.DataPlayerAttribute;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataPlayerEnum;
import data.widgets.options.enums.DataSeasonChampionsLeagueEnum;
import io.qameta.allure.Story;
import j2html.attributes.Attribute;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballPlayerWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballPlayerWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_setDefaultData_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_setDefaultData_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_setDefaultData_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_openStats_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M + " StatsView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET + " StatsView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L + " StatsView");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetInfoStatsUpdated_when_setForwardDataElementsValues() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));
        widgetsPage.updateAttributeValue(new DataPlayerAttribute(DataPlayerEnum.CRISTIANO_RONALDO));

        var expectedDataLabels = WidgetSettingsFactory.getPlayerDataLabels();
        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.position)), "position head not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.shirtNumber)), "shirt number header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.dateOfBirth)), "age header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.height + " / " + expectedDataLabels.weight)), "height / weight header not found");

        assertSameAsBaseline(widgetsPage, " Forward Player Info Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setForwardDataElementsValues() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));
        widgetsPage.updateAttributeValue(new DataPlayerAttribute(DataPlayerEnum.CRISTIANO_RONALDO));

        var expectedDataLabels = WidgetSettingsFactory.getPlayerDataLabels();
        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.playedShort)), "played header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.goals)), "goals header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.shots)), "assists header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.assistsShort)), "foulsCommitted header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.minutes)), "shots header not found");
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.yellowCardsShort)), "shotsOnTarget header not found");

        assertSameAsBaseline(widgetsPage, " Forward Player Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetInfoStatsUpdated_when_setGoalkeeperDataElementsValues() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));
        widgetsPage.updateAttributeValue(new DataPlayerAttribute(DataPlayerEnum.TOM_HEATON_GOALKEEPER));
        widgetsPage.updateAttributeValue(new DataCompetitionAttribute(DataCompetitionEnum.CHAMPIONS_LEAGUE));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonChampionsLeagueEnum.TWENTY_ONE_TWENTY_TWO));

        var expectedDataLabels = WidgetSettingsFactory.getPlayerDataLabels();
        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.playerBio + ":")), "country header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.position + ":")), "position header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.dateOfBirth + ":")), "dateOfBirth header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.height + ":")), "height header not found");
        Assertions.assertTrue(widgetsPage.map().getInfoLabels().stream().anyMatch(header -> header.getText().equals(expectedDataLabels.weight + ":")), "weight header not found");

        assertSameAsBaseline(widgetsPage, " Keeper Player Info Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void widgetStatsUpdated_when_setGoalkeeperDataElementsValues() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));
        widgetsPage.updateAttributeValue(new DataPlayerAttribute(DataPlayerEnum.TOM_HEATON_GOALKEEPER));
        widgetsPage.updateAttributeValue(new DataCompetitionAttribute(DataCompetitionEnum.CHAMPIONS_LEAGUE));
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonChampionsLeagueEnum.TWENTY_ONE_TWENTY_TWO));

        var expectedDataLabels = WidgetSettingsFactory.getPlayerDataLabels();

        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().getStatsLabels().stream()
                .anyMatch(header -> header.getText().equals(expectedDataLabels.playedShort)), "played header not found");

        assertSameAsBaseline(widgetsPage, " Keeper Player Stats Usual Data Elements set");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void allPlayerStatsDisplayed_when_setDataElementsNull() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));

        // Expected Headers
        assertSameAsBaseline(widgetsPage, "All Stats displayed when Data Elements Null");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    @Story(WidgetsStories.PROFILE)
    @Story(WidgetsStories.DATA_ELEMENTS)
    public void allPlayerInfoDisplayed_when_setDataElementsNull() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataElementsAttribute(WidgetSettingsFactory.getPlayerDataElements()));

        // Expected Headers
        assertSameAsBaseline(widgetsPage, "All Info displayed when Data Elements Null");
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth300px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.MOBILE_S.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.MOBILE_S.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth768px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.TABLET.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.TABLET.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }
}