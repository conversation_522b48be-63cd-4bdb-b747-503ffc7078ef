package football.player;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class FootballPlayerWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballPlayerWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setDataEntityLinksMatchScoreSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-match-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.getMatchPage().map().matchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.LIVERPOOL_NAPOLI.getSlugEn()));
    }

    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setMatchScoreSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-match-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.getMatchPage().map().matchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.LIVERPOOL_NAPOLI.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().clubTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().clubTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}