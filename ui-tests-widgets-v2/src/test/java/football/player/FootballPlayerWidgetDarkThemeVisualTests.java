package football.player;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.constants.enums.TeamDataTabsParametersEnum;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;

import static data.constants.StringConstants.DARK_THEME_STRING;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Tag(WidgetsTags.DARK_THEME)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballPlayerWidgetDarkThemeVisualTests extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballPlayerWidgetPage();
        WidgetSettings widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(DataThemeEnum.DARK.getValue());
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openInfoTab_and_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.INFO.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openStatsTab_and_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.STATS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openMatchesTab_and_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.MATCHES.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openPredictionsTab_and_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.PREDICTIONS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openInfoTab_and_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.INFO.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openStatsTab_and_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.STATS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openMatchesTab_and_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.MATCHES.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openPredictionsTab_and_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.PREDICTIONS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openInfoTab_and_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.INFO.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openStatsTab_and_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.STATS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openMatchesTab_and_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.MATCHES.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openPredictionsTab_and_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.PREDICTIONS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openInfoTab_and_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.INFO.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openStatsTab_and_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.STATS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openMatchesTab_and_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.MATCHES.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openPredictionsTab_and_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.PREDICTIONS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openInfoTab_and_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.INFO.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openStatsTab_and_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.STATS.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openMatchesTab_and_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.MATCHES.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openPredictionsTab_and_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, TeamDataTabsParametersEnum.PREDICTIONS.name()));
    }
}