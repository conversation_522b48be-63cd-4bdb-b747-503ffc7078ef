package football.player;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballPlayerWidgetDataThemeTests extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballPlayerWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().themes.client.setColors(widgetsPage.getClientColorsPlayer(SportalColors.RED.name().toLowerCase()));
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, Colors.RED.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().playerBackground().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
    }
}