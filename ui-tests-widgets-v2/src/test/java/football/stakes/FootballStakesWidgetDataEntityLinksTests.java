package football.stakes;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataOddsBettingIdAttribute;
import data.widgets.options.models.Slug;
import facades.FootballApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.stakeswidgetpage.FootballStakesWidgetPage;

import java.util.Collections;
import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STAKES)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Story(WidgetsStories.STAKES)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballStakesWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballStakesWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private MatchV2Model matchWithOdds;
    private String bookmakerId;

    @Override
    public void beforeAll() {
        super.beforeAll();
        matchWithOdds = new FootballApiFacade(getCurrentTestProject()).getFootballEventWithOdds(StringConstants.SPORTAL_STRING);
        bookmakerId = matchWithOdds.getOdds().get(0).getBookmaker().getId();
    }

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStakesWidgetPage();
        widgetsPage.attributesList.add(new DataMatchIdAttribute(matchWithOdds.getId()));
        widgetsPage.updateAttributeValue(new DataOddsBettingIdAttribute(bookmakerId));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{teamId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", matchWithOdds.getHomeTeam().getId()));
        widgetsPage.map().awayTeamAnchor().validateHrefIs(expectedUrlFormat.replace("{teamId}", matchWithOdds.getAwayTeam().getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().scoreAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        getTeamIds().get(0) + "-" + getTeamIds().get(1) + "#" + matchWithOdds.getId()));
    }

    @Test
    public void competitionAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/league-{competitionId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().competitionAnchor().validateHrefIs(
                expectedUrlFormat.replace("{competitionId}", matchWithOdds.getSeason().getTournament().getEntityId()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    private List<String> getTeamIds() {
        String firstTeamName = matchWithOdds.getHomeTeam().getName();
        String secondTeamName = matchWithOdds.getAwayTeam().getName();

        if (firstTeamName.compareTo(secondTeamName) < 0) {
            return List.of(matchWithOdds.getHomeTeam().getId(), matchWithOdds.getAwayTeam().getId());
        } else if (firstTeamName.compareTo(secondTeamName) > 0) {
            return List.of(matchWithOdds.getAwayTeam().getId(), matchWithOdds.getHomeTeam().getId());
        }
        return Collections.emptyList();
    }
}