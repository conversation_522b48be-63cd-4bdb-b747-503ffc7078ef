package football.stakes;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataRefreshTimeAttribute;
import data.widgets.options.enums.DataRefreshTimeEnum;
import facades.FootballApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.utilities.Wait;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.stakeswidgetpage.FootballStakesWidgetPage;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STAKES)
@Tag(WidgetsTags.REFRESH_TIME_ATTRIBUTE)
@Story(WidgetsStories.STAKES)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballStakesWidgetRefreshTimeTests extends WidgetsBaseWebTest {

    private FootballStakesWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private MatchV2Model matchWithOdds;

    @Override
    public void beforeAll() {
        super.beforeAll();
        matchWithOdds = new FootballApiFacade(getCurrentTestProject()).getFootballEventWithOdds();
    }

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStakesWidgetPage();
        widgetsPage.attributesList.add(new DataMatchIdAttribute(matchWithOdds.getId()));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void singleDataRequestIsMade_when_setRefreshTime_never() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(DataRefreshTimeEnum.NEVER));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(1, widgetsPage.getDataRequestUrl());
    }

    @Story(WidgetsStories.CORE_ATTRIBUTES)
    @ParameterizedTest
    @EnumSource(value = DataRefreshTimeEnum.class, names = {"FAST", "SUPER_FAST"}, mode = EnumSource.Mode.INCLUDE)
    public void singleDataRequestIsMade_when_setRefreshTime_superFast(DataRefreshTimeEnum attributeValue) {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataRefreshTimeAttribute(attributeValue));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        Wait.forMilliseconds(attributeValue.getSecondsValue() * 1000);

        // Assert Configuration Applied
        widgetsPage.assertDataRequestsCount(2, widgetsPage.getDataRequestUrl());
    }
}