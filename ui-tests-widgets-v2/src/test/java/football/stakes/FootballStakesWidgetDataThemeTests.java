package football.stakes;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.constants.StringConstants;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataOddsBettingIdAttribute;
import data.widgets.attributes.DataThemeAttribute;
import data.widgets.options.enums.DataThemeEnum;
import facades.FootballApiFacade;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.stakeswidgetpage.FootballStakesWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STAKES)
@Story(WidgetsStories.STAKES)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballStakesWidgetDataThemeTests extends WidgetsBaseWebTest {

    private FootballStakesWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private MatchV2Model matchWithOdds;
    private String bookmakerId;

    @Override
    public void beforeAll() {
        super.beforeAll();
        matchWithOdds = new FootballApiFacade(getCurrentTestProject()).getFootballEventWithOdds(StringConstants.SPORTAL_STRING);
        bookmakerId = matchWithOdds.getOdds().get(0).getBookmaker().getId();
    }

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballStakesWidgetPage();
        widgetsPage.attributesList.add(new DataMatchIdAttribute(matchWithOdds.getId()));
        widgetsPage.updateAttributeValue(new DataOddsBettingIdAttribute(bookmakerId));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Issue("PLT-527")
    public void widgetThemeUpdated_when_setDataThemeToDark() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.EERIE_BLACK.getColorValue().asRgba());
        widgetsPage.map().homeTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, Colors.WHITE.getColorValue().asRgba());
    }


    @Test
    public void widgetThemeUpdated_when_setDataThemeToLight() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().homeTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
    }

    @Test
    @Issue("PLT-528")
    public void sportEntityContainerPrimaryBgColor() {
        // Apply configuration
        var expectedColour = Colors.RED;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.client.setColors(widgetsPage.getClientColorsStakes(expectedColour.name().toLowerCase()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().sportEntityContainer().validateStyle(CssStyle.BORDER_COLOR, expectedColour.getColorValue().asRgba());
    }

    @Test
    @Issue("PLT-528")
    public void oddsContainerPrimaryBgColor() {
        // Apply configuration
        var expectedColour = Colors.RED;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.client.setColors(widgetsPage.getClientColorsStakes(expectedColour.name().toLowerCase()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().oddsContainer().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColour.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeToClient() {
        // Apply configuration
        var expectedColour = Colors.RED;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();
        widgetsOptions.getFootballWidgetOptions().themes.client.setColors(widgetsPage.getClientColorsStakes(expectedColour.name().toLowerCase()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().homeTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColour.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeUpdated_when_setDataThemeDefaultValues() {
        // Apply configuration
        widgetsPage.removeAttribute(new DataThemeAttribute(DataThemeEnum.LIGHT));
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, SportalColors.WHITE_255.getColorValue().asRgba());
        widgetsPage.map().homeTeamAnchor().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_800.getColorValue().asRgba());
    }
}