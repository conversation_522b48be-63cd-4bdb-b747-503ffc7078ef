package football.stakes;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.options.enums.DataMatchIdEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;

import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.stakeswidgetpage.FootballStakesWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.STATIC)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.STAKES)
@Story(WidgetsStories.STAKES)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballStakesWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballStakesWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    public void beforeAll() {
        widgetsPage = new FootballStakesWidgetPage();
        widgetsPage.attributesList.add(new DataMatchIdAttribute(DataMatchIdEnum.FOOTBALL_SPAIN_SWITZERLAND_STATIC_PROJECT));
    }

    @Override
    protected void beforeEach() {
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }
}