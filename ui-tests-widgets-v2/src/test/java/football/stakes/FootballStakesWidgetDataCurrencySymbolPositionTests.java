package football.stakes;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.models.footballapi.v2.MatchV2Model;
import data.widgets.attributes.*;
import data.widgets.options.enums.DataCurrencySymbolPositionEnum;
import facades.FootballApiFacade;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.stakeswidgetpage.FootballStakesWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.STAKES)
@Story(WidgetsStories.STAKES)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballStakesWidgetDataCurrencySymbolPositionTests extends WidgetsBaseWebTest {

    private static final String CURRENCY_STRING = "€";
    private FootballStakesWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private MatchV2Model matchWithOdds;
    private String bookmakerId;

    @Override
    public void beforeAll() {
        super.beforeAll();
        matchWithOdds = new FootballApiFacade(getCurrentTestProject()).getFootballEventWithOdds(StringConstants.SPORTAL_STRING);
        bookmakerId = matchWithOdds.getOdds().get(0).getBookmaker().getId();
    }

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballStakesWidgetPage();
        widgetsPage.attributesList.add(new DataMatchIdAttribute(matchWithOdds.getId()));
        widgetsPage.updateAttributeValue(new DataOddsBettingIdAttribute(bookmakerId));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void currencySymbolPrefix_when_prefixPositionIsSet() {
        widgetsPage.updateAttributeValue(new DataCurrencySymbolPosition(DataCurrencySymbolPositionEnum.PREFIX.name().toLowerCase()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().positionCurrencySymbolLabel().getText().startsWith(CURRENCY_STRING),
                "Currency symbol should be prefix (e.g., €100)");
    }

    @Test
    public void currencySymbolSuffix_when_suffixPositionIsSet() {
        widgetsPage.updateAttributeValue(new DataCurrencySymbolPosition(DataCurrencySymbolPositionEnum.SUFFIX.name().toLowerCase()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().positionCurrencySymbolLabel().getText().endsWith(CURRENCY_STRING),
                "Currency symbol should be suffix (e.g., 100€)");
    }

    @Test
    public void currencySymbolPrefix_when_positionIsNotSet() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertTrue(widgetsPage.map().positionCurrencySymbolLabel().getText().startsWith(CURRENCY_STRING),
                "Currency symbol should default to prefix position (e.g., €100)");
    }
}