package football.matchesh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.*;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCHES_H2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
public class FootballMatchesH2HWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private FootballMatchesH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchesH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetTeamAnchorHrefEmpty_when_setDataEntityLinksTeamSlugUrl() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team("https://dev.sportal.bg/football/auto-team-{slug}")
                        .build();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().firstMatchHomeTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().firstMatchAwayTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING)
        );
    }

    @Test
    public void teamAnchorHrefEmpty_when_setSlugWithoutEntityLinksUrl() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team("https://dev.sportal.bg/football/auto-team-{slug}")
                        .build();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().firstMatchHomeTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().firstMatchAwayTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING)
        );
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setDataEntityLinksCompetitionSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getCompetitionLink().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setCompetitionSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().getCompetitionLink().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
    }

    @Test
    public void standingsAnchorUpdated_when_setDataEntityLinksCompetitionStandingsSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}#standings";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .standings(expectedUrlFormat)
                        .build();

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getCompetitionStandingsLink().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
    }

    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setDataEntityLinksMatchScoreSlugUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-match-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().firstMatchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.MANCHESTER_UNITED_VS_ASTON_VILLA.getSlugEn()));
    }

    @Test
    public void widgetMatchUrlUpdatedWithSlug_when_setMatchScoreSlugWithoutEntityLinksUrl() {
        //Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-match-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match(expectedUrlFormat)
                        .build();

        //Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        widgetsPage.map().firstMatchAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataMatchIdEnum.MANCHESTER_UNITED_VS_ASTON_VILLA.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .match("https://dev.sportal.bg/football/auto-match-{slug}")
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team("https://dev.sportal.bg/football/auto-team-{slug}")
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}