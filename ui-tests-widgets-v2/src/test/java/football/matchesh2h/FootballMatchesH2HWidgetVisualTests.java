package football.matchesh2h;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataSportEntityOneAttribute;
import data.widgets.options.enums.DataTeamEnum;
import j2html.attributes.Attribute;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCHES_H2H)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballMatchesH2HWidgetVisualTests extends WidgetsBaseWebTest {

    private static final String PILLS_NOT_DISPLAYED_TEST_CASE = "Pills for teams not displayed when no data is available";
    private FootballMatchesH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballMatchesH2HWidgetPage();
        widgetsPage.updateAttributeValue(new DataSportEntityOneAttribute(DataTeamEnum.AJAX.getId()));
        widgetsPage.updateAttributeValue(new DataSportEntityOneAttribute(DataTeamEnum.LEVSKI_SOFIA.getId()));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void pillsForTeamsNotDisplayed_when_noDataAvailableForMatches_desktopM() {
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, PILLS_NOT_DISPLAYED_TEST_CASE);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void pillsForTeamsNotDisplayed_when_noDataAvailableForMatches_mobileS() {
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, PILLS_NOT_DISPLAYED_TEST_CASE);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void pillsForTeamsNotDisplayed_when_noDataAvailableForMatches_mobileL() {
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, PILLS_NOT_DISPLAYED_TEST_CASE);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void pillsForTeamsNotDisplayed_when_noDataAvailableForMatches_tablet() {
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, PILLS_NOT_DISPLAYED_TEST_CASE);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.IPHONE_SE_MOBILE)
    public void pillsForTeamsNotDisplayed_when_noDataAvailableForMatches_mobileDevice() {
        openWidgetPage(widgetsPage, widgetsOptions);
        assertSameAsBaseline(widgetsPage, PILLS_NOT_DISPLAYED_TEST_CASE);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth300px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.MOBILE_S.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.MOBILE_S.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth768px_desktop() {
        // Apply max-width style to test responsive behavior on desktop
        widgetsPage.updateAttributeValue(new Attribute("style", "max-width:" + ViewportSize.TABLET.getWidth() + "px"));
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.TABLET.getWidth() + "px - " + ViewportSize.DESKTOP_M);
    }
}