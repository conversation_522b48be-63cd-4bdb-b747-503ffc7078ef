package football.matchesh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataTeamNameTypeAttribute;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.enums.DataTeamNameTypeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCHES_H2H)
@Story(WidgetsStories.H2H)
public class FootballMatchesH2HWidgetDataTeamNameTypeTests extends WidgetsBaseWebTest {

    private FootballMatchesH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchesH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetDisplayed_when_setDataTeamNameType_threeLetterCode() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.THREE_LETTER_CODE));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getThreeLetterCode());
        widgetsPage.map().firstMatchAwayTeamName().validateTextIs(DataTeamEnum.ASTON_VILLA.getThreeLetterCode());
    }

    @Test
    public void widgetDisplayed_when_setDataTeamNameType_shortName() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.SHORT_NAME));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getShortName());
        widgetsPage.map().firstMatchAwayTeamName().validateTextIs(DataTeamEnum.ASTON_VILLA.getFullName());
    }

    @Test
    public void widgetDataDisplayed_when_setDataTeamNameType_defaultValues() {
        widgetsPage.removeAttribute(new DataTeamNameTypeAttribute(DataTeamNameTypeEnum.FULL_NAME));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamName().validateTextIs(DataTeamEnum.MANCHESTER_UNITED.getFullName());
        widgetsPage.map().firstMatchAwayTeamName().validateTextIs(DataTeamEnum.ASTON_VILLA.getFullName());
    }
}