package football.matchesh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataTeamEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCHES_H2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
public class FootballMatchesH2HWidgetDataEntityLinksTests extends WidgetsBaseWebTest {

    private FootballMatchesH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchesH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorHrefEmpty_when_setDataEntityLinksTeamUrl() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team.setUrl("https://dev.sportal.bg/football/auto-team-{teamId}");

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().firstMatchHomeTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().firstMatchAwayTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING)
        );
    }

    @Test
    public void competitionAnchorUpdated_when_setDataEntityLinksCompetitionUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{competitionId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getCompetitionLink().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
    }

    @Test
    public void standingsAnchorUpdated_when_setDataEntityLinksCompetitionStandingsUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{competitionId}#standings";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.standings.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().getCompetitionStandingsLink().validateHrefIs(expectedUrlFormat.replace("{competitionId}", DataCompetitionEnum.PREMIER_LEAGUE.getId()));
    }

    @Test
    public void matchAnchorUpdated_when_setDataEntityLinksMatchUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://sportal.bg/football/auto-match-{teamId}-{teamId}#{matchId}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.match.setUrl(expectedUrlFormat);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchAnchor().validateHrefIs(
                expectedUrlFormat.replace("{teamId}-{teamId}#{matchId}",
                        DataTeamEnum.ASTON_VILLA.getId() + "-" + DataTeamEnum.MANCHESTER_UNITED.getId() + "#" + DataMatchIdEnum.MANCHESTER_UNITED_VS_ASTON_VILLA.getId()));
    }

    @Test
    public void widgetLoaded_when_dataEntityLinksNotSet() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().firstMatchHomeTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().firstMatchAwayTeamAnchor().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().firstMatchAnchor().validateHrefIs(widgetsPage.browser().getUrl() + "#"),
                () -> widgetsPage.map().getCompetitionLink().validateHrefIs(StringConstants.EMPTY_STRING),
                () -> widgetsPage.map().getCompetitionStandingsLink().validateHrefIs(StringConstants.EMPTY_STRING)
        );
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamAnchor().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void linksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().firstMatchHomeTeamAnchor().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}