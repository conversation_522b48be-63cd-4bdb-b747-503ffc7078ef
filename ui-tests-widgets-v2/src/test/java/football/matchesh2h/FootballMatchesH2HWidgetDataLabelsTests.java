package football.matchesh2h;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataLabelsAttribute;
import data.widgets.options.models.DataLabels;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.matchesh2hwidgetpage.FootballMatchesH2HWidgetPage;

@Authenticate(project = Project.STATIC, user = User.FULLSETUP)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.MATCHES_H2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_LABELS)
public class FootballMatchesH2HWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballMatchesH2HWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballMatchesH2HWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @Test
    public void widgetH2HTabLabelUpdated_when_setDataLabels() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getMatchesH2HDataLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().tabPillsSection.h2hPillButton().validateTextIs(
                expectedDataLabels.h2h);
    }

    @Test
//    @Issue("PLT-102")
    public void widgetLabelUpdated_when_setDataLabels_and_firstTeamClicked() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getMatchesH2HDataLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().tabPillsSection.teamOnePillButton().click();

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getMatchResult(1).validateTextIs(expectedDataLabels.w),
                () -> widgetsPage.map().getMatchResult(4).validateTextIs(expectedDataLabels.l),
                () -> widgetsPage.map().getMatchResult(6).validateTextIs(expectedDataLabels.d)
        );
    }

    @Test
//    @Issue("PLT-102")
    public void widgetLabelUpdated_when_setDataLabels_and_secondTeamClicked() {
        // Apply configuration
        DataLabels expectedDataLabels = WidgetSettingsFactory.getMatchesH2HDataLabels();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().tabPillsSection.teamTwoPillButton().click();

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getMatchResult(1).validateTextIs(expectedDataLabels.l),
                () -> widgetsPage.map().getMatchResult(2).validateTextIs(expectedDataLabels.d),
                () -> widgetsPage.map().getMatchResult(7).validateTextIs(expectedDataLabels.w)
        );
    }

    @Test
    public void widgetH2HTabLabelDisplayed_when_setDataLabelsWithDefaultValues() {
        DataLabels expectedDataLabels = WidgetSettingsFactory.getMatchesH2HDataLabelsDefaultValues();

        // Apply configuration
        widgetsPage.removeAttribute(new DataLabelsAttribute(
                DataLabels.builder()
                        .build()));
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().tabPillsSection.teamTwoPillButton().click();

        // Assert Configuration Applied
        Assertions.assertAll(
                () -> widgetsPage.map().getMatchResult(1).validateTextIs(expectedDataLabels.l),
                () -> widgetsPage.map().getMatchResult(2).validateTextIs(expectedDataLabels.d),
                () -> widgetsPage.map().getMatchResult(7).validateTextIs(expectedDataLabels.w),
                () -> widgetsPage.map().tabPillsSection.h2hPillButton().validateTextIs(expectedDataLabels.h2h)
        );
    }
}