package football.teamh2hmatch;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataCompetitionEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamh2hmatchwidgetpage.FootballTeamH2HMatchWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Story(WidgetsStories.H2H)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HMatchWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private FootballTeamH2HMatchWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamH2HMatchWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
//    @Issue("SFE-4269")
    public void widgetTeamUrlUpdatedWithSlug_when_setDataEntityLinksTeamSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataTeamEnum.MANCHESTER_CITY.getSlugEn()));
        widgetsPage.map().teamTwoAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataTeamEnum.ASTON_VILLA.getSlugEn()));
    }

    @Test
    public void widgetTeamUrlUpdatedWithSlug_when_setSlugWithoutEntityLinksUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.team = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataTeamEnum.MANCHESTER_CITY.getSlugEn()));
        widgetsPage.map().teamTwoAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataTeamEnum.ASTON_VILLA.getSlugEn()));
    }

    @Test
    public void widgetCompetitionUrlUpdatedWithSlug_when_setDataEntityLinksTeamSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
        widgetsPage.map().teamTwoCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
    }

    @Test
//    @Issue("SFE-4269")
    public void widgetCompetitionUrlUpdatedWithSlug_when_setSlugWithoutEntityLinksUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-league-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.competition = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
        widgetsPage.map().teamTwoCompetitionAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataCompetitionEnum.PREMIER_LEAGUE.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
//    @Issue("SFE-4269")
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOne().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOne().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}