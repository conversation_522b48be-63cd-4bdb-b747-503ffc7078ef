package football.teamh2hmatch;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.*;
import data.widgets.options.enums.*;
import data.widgets.options.models.DataSportEntity;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamh2hmatchwidgetpage.FootballTeamH2HMatchWidgetPage;

import java.util.logging.Level;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Story(WidgetsStories.H2H)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HMatchWidgetTests extends WidgetsBaseWebTest {

    private FootballTeamH2HMatchWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamH2HMatchWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    @Tag(SMPCategories.SMOKE)
    @Story(WidgetsStories.CORE_ATTRIBUTES)
//    @Issue("SFE-4269")
    public void widgetDisplayed_when_setDefaultData() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_H2H_MATCH));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneNameAnchor().validateTextIs(DataTeamEnum.MANCHESTER_CITY.getFullName());
        widgetsPage.map().teamTwoNameAnchor().validateTextIs(DataTeamEnum.ASTON_VILLA.getFullName());
    }

    @Test
    @Story(WidgetsStories.SPORT_DATA_ATTRIBUTES)
//    @Issue("SFE-4269")
    public void widgetDataDisplayed_when_setSportData_dataSportEntity() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataSportEntityOneAttribute(DataSportEntity
                .builder()
                .id(DataTeamEnum.ARSENAL.getId())
                .seasonId(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE.getValue())
                .build()));
        widgetsPage.updateAttributeValue(new DataSportEntityTwoAttribute(DataSportEntity
                .builder()
                .id(DataTeamEnum.TOTTENHAM.getId())
                .seasonId(DataSeasonPremierLeagueEnum.TWENTY_TWO_TWENTY_THREE.getValue())
                .build()));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamOneNameAnchor().validateTextIs(DataTeamEnum.ARSENAL.getFullName());
        widgetsPage.map().teamTwoNameAnchor().validateTextIs(DataTeamEnum.TOTTENHAM.getFullName());
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidSportIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_H2H_MATCH));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_SPORT.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetTypeIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.INVALID));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.TEAM_H2H_MATCH));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        app().browser().assertConsoleErrorLogged(WidgetConsoleErrorsEnum.INVALID_WIDGET_TYPE.getValue(), Level.WARNING);
    }

    @Test
    @Story(WidgetsStories.CORE_ATTRIBUTES)
    public void consoleErrorDisplayed_when_invalidWidgetIdIsSet() {
        // Apply configuration
        widgetsPage.updateAttributeValue(new DataWidgetSportAttribute(DataWidgetSportEnum.FOOTBALL));
        widgetsPage.updateAttributeValue(new DataWidgetTypeAttribute(DataWidgetTypeEnum.TOURNAMENT));
        widgetsPage.updateAttributeValue(new DataWidgetIdAttribute(DataWidgetIdEnum.INVALID));

        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().shadowHost().validateTextIs("");
        app().browser().assertNoConsoleErrorsLogged();
    }
}