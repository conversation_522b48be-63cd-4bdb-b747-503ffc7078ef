package football.teamh2hmatch;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamh2hmatchwidgetpage.FootballTeamH2HMatchWidgetPage;

import static data.constants.StringConstants.DARK_THEME_STRING;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMH2H)
@Tag(WidgetsTags.DARK_THEME)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class FootballTeamH2HMatchWidgetDarkThemeVisualTests extends WidgetsBaseWebTest {

    private FootballTeamH2HMatchWidgetPage widgetsPage;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballTeamH2HMatchWidgetPage();
        WidgetSettings widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(DataThemeEnum.DARK.getValue());
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, DARK_THEME_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, DARK_THEME_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, DARK_THEME_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, DARK_THEME_STRING);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, DARK_THEME_STRING);
    }
}