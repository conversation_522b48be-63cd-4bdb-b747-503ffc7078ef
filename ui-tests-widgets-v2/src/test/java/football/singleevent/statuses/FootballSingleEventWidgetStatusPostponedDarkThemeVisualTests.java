package football.singleevent.statuses;

import categories.SMPCategories;
import categories.WidgetsTags;
import data.constants.VisualRegressionProject;
import data.constants.enums.football.FootballMatchStatus;
import data.widgets.options.enums.DataShortStatusTypeEnum;
import facades.FootballApiFacade;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.SINGLEEVENT)
@Tag(WidgetsTags.EVENT_STATUS_WIDGETS)
@Tag(WidgetsTags.DARK_THEME)
@Tag(SMPCategories.VISUAL)
public class FootballSingleEventWidgetStatusPostponedDarkThemeVisualTests extends BaseFootballSingleEventWidgetStatusVisualTests {

    @Override
    public void beforeAll() {
        super.beforeAll();
        new FootballApiFacade(getCurrentTestProject()).updateMatchToPostponed(matchId, kickoffDate);
        matchStatus = FootballMatchStatus.POSTPONED;
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_setAdditionalInfo_desktopM(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(true, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_noAdditionalInfo_desktopM(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(false, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_setAdditionalInfo_mobileS(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(true, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_noAdditionalInfo_mobileS(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(false, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_setAdditionalInfo_mobileL(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(true, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_noAdditionalInfo_mobileL(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(false, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_setAdditionalInfo_tablet(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(true, shortStatusType);
    }

    @ParameterizedTest
    @EnumSource(DataShortStatusTypeEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void expectedWidgetStateDisplayed_when_postponedStatus_darkTheme_and_noAdditionalInfo_tablet(DataShortStatusTypeEnum shortStatusType) {
        visualCheckOfWidget(false, shortStatusType);
    }
}