package football.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataCompetitionEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.singleeventwidgetpage.FootballSingleEventWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.SINGLEEVENT)
@Story(WidgetsStories.SINGLEEVENT)
public class FootballSingleEventWidgetDataAdditionalInfoTests extends WidgetsBaseWebTest {

    private FootballSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void additionalInfoDisplayed_when_setDataAdditionalInfoTrue() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataAdditionalInfo(BooleanEnum.TRUE.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
          widgetsPage.map().competitionInfoAnchor().isVisible();
          widgetsPage.map().competitionName().validateTextIs(DataCompetitionEnum.CLUB_FRIENDLIES.getName());
          widgetsPage.map().roundInfo().validateTextIs("Round 1, 30 July 2023");
    }

    @Test
    public void additionalInfoHidden_when_setDataAdditionalInfoFalse() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataAdditionalInfo(BooleanEnum.FALSE.getValue());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().competitionInfoAnchor().validateNotVisible();
    }

    @Test
    public void additionalInfoHidden_when_noDataAdditionalInfoDefault() {
        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().competitionInfoAnchor().validateNotVisible();
    }
}