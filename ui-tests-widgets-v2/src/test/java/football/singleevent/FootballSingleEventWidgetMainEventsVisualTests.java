package football.singleevent;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.DataMatchIdAttribute;
import data.widgets.attributes.DataNameFormatAttribute;
import data.widgets.attributes.DataSingleEventMainEventsDisplayAttribute;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataMatchIdEnum;
import data.widgets.options.enums.DataNameFormatEnum;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.singleeventwidgetpage.FootballSingleEventWidgetPage;

@Authenticate(project = Project.STATIC)
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.SINGLEEVENT)
@Tag(SMPCategories.VISUAL)
@Story(WidgetsStories.SINGLEEVENT)
@Tag(WidgetsTags.EVENTS_WIDGETS)
public class FootballSingleEventWidgetMainEventsVisualTests extends WidgetsBaseWebTest {

    private FootballSingleEventWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballSingleEventWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsPage.updateAttributeValue(new DataMatchIdAttribute(DataMatchIdEnum.FOOTBALL_MANCHESTER_CITY_VS_WEST_HAM_UNITED_STATIC_PROJECT));
        widgetsPage.updateAttributeValue(new DataSingleEventMainEventsDisplayAttribute(BooleanEnum.TRUE));
        widgetsOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetMainEventsDisplayed_mainEventsDisabled_desktopTall(DataThemeEnum theme) {
        widgetsPage.updateAttributeValue(new DataSingleEventMainEventsDisplayAttribute(BooleanEnum.FALSE));
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        openWidgetPage(widgetsPage, widgetsOptions);

        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_TALL.getName(), "%s - main events: %s".formatted(theme, BooleanEnum.FALSE.getValue()));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetMainEventsDisplayed_allMainEvents_initials_desktopTall(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_TALL.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.INITIALS));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetMainEventsDisplayed_allMainEvents_lastName_desktopTall(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_TALL.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.LAST_NAME));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetMainEventsDisplayed_allMainEvents_initials_desktopM(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.INITIALS));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetMainEventsDisplayed_allMainEvents_lastName_desktopM(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.LAST_NAME));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    public void widgetMainEventsDisplayed_allMainEvents_initials_tablet(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.INITIALS));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    public void widgetMainEventsDisplayed_allMainEvents_lastName_tablet(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.LAST_NAME));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetMainEventsDisplayed_allMainEvents_initials_mobileL(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.MOBILE_L.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.INITIALS));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetMainEventsDisplayed_allMainEvents_lastName_mobileL(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.MOBILE_L.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.LAST_NAME));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetMainEventsDisplayed_allMainEvents_initials_mobileS(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.INITIALS));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.MOBILE_S.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.INITIALS));
    }

    @ParameterizedTest
    @EnumSource(value = DataThemeEnum.class, names = {"LIGHT", "DARK"}, mode = EnumSource.Mode.INCLUDE)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetMainEventsDisplayed_allMainEvents_lastName_mobileS(DataThemeEnum theme) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(theme.getValue());
        widgetsPage.updateAttributeValue(new DataNameFormatAttribute(DataNameFormatEnum.LAST_NAME));
        openWidgetPage(widgetsPage, widgetsOptions);

        // Verify page looks as expected
        assertSameAsBaseline(widgetsPage, DeviceName.MOBILE_S.getName(), "%s - main events enabled: %s".formatted(theme, DataNameFormatEnum.LAST_NAME));
    }
}