package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import j2html.attributes.Attribute;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetVisualTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData_desktop() {
        assertSameAsBaseline(widgetsPage, ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_S.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        assertSameAsBaseline(widgetsPage, ViewportSize.TABLET.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        assertSameAsBaseline(widgetsPage, ViewportSize.MOBILE_L.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthMobileS_desktop() {
        // Apply max-width constraint using ViewportSize.MOBILE_S width (300px)
        widgetsPage.setWidgetMaxWidth(ViewportSize.MOBILE_S.getWidth() + "px");
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.MOBILE_S.getWidth() + "px - " + ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthMobileL_desktop() {
        // Test with ViewportSize.MOBILE_L width (393px)
        widgetsPage.setWidgetMaxWidth(ViewportSize.MOBILE_L.getWidth() + "px");
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.MOBILE_L.getWidth() + "px - " + ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidthTablet_desktop() {
        // Test with ViewportSize.TABLET width (768px)
        widgetsPage.setWidgetMaxWidth(ViewportSize.TABLET.getWidth() + "px");
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "max-width-" + ViewportSize.TABLET.getWidth() + "px - " + ViewportSize.DESKTOP_M.toString());
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setCustomStylesWithMobileSWidth_desktop() {
        // Test with custom CSS styles using ViewportSize.MOBILE_S width
        widgetsPage.setCustomWidgetStyles("max-width: " + ViewportSize.MOBILE_S.getWidth() + "px; border: 2px solid red; padding: 10px;");
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "custom-styles-" + ViewportSize.MOBILE_S.getWidth() + "px - " + ViewportSize.DESKTOP_M.toString());
    }
}