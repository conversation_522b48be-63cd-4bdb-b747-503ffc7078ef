package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.AssertMessages;
import data.utils.DateUtils;
import data.widgets.attributes.DataDateFromAttribute;
import data.widgets.attributes.DataDateToAttribute;
import data.widgets.options.models.DataDate;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

import java.time.LocalDate;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetDataDateFromToTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;
    private static final LocalDate EXPECTED_DATE_FROM = LocalDate.of(2022, 1, 3);
    private static final LocalDate EXPECTED_DATE_TO = LocalDate.of(2022, 3, 12);
    private static final String DATE_FORMAT = "YYYY-MM-DD";

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void footballTeamProgrammeDataDateTimeFrom_when_setDateFrom() {
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_FROM);

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        String lastEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(widgetsPage.map().getProgrammeSection().eventDate().size() - 1).getText();
        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
    }

    @Test
    public void footballTeamProgrammeDataDateTimeTo_when_setDateTo() {
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_TO);

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        String firstEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(0).getText();
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
    }

    @Test
    public void footballTeamProgrammeDataDateTimeFromTo_when_setDateFromTo() {
        String convertedExpectedDateFrom = DateUtils.convertDateFormat(EXPECTED_DATE_FROM);
        String convertedExpectedDateTo = DateUtils.convertDateFormat(EXPECTED_DATE_TO);

        widgetsPage.updateAttributeValue(new DataDateFromAttribute(DataDate.builder()
                .date(EXPECTED_DATE_FROM.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        widgetsPage.updateAttributeValue(new DataDateToAttribute(DataDate.builder()
                .date(EXPECTED_DATE_TO.toString())
                .dateFormat(DATE_FORMAT)
                .build()));

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        //Assert Configuration Applied
        String firstEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(0).getText();
        String lastEventDate = widgetsPage.map().getProgrammeSection().eventDate().get(widgetsPage.map().getProgrammeSection().eventDate().size() - 1).getText();

        Assertions.assertEquals(convertedExpectedDateFrom, lastEventDate, AssertMessages.expectedEventDate(convertedExpectedDateFrom, lastEventDate));
        Assertions.assertEquals(convertedExpectedDateTo, firstEventDate, AssertMessages.expectedEventDate(convertedExpectedDateTo, firstEventDate));
    }
}