package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import com.github.javafaker.Faker;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.options.enums.DataSeasonPremierLeagueEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_LABELS)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
//    @Issue("PLT-522")
    public void currentSeasonLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelCurrentSeason = "CSeason- " + Faker.instance().animal().name();
        widgetsPage.removeAttribute(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_ONE_TWENTY_TWO));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs(
                widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelCurrentSeason);
    }

    @Test
    public void fixturesLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelFixture = "Fixture-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().programmePillButton().validateTextIs(
                widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelFixture.trim());
    }

    @Test
    public void resultsLabelChanged_when_setDataLabelsText() {
        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelResults = "Result-" + Faker.instance().animal().name();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateTextIs(
                widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels.labelResults.trim());
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_currentSeason() {
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.removeAttribute(new DataSeasonAttribute(DataSeasonPremierLeagueEnum.TWENTY_ONE_TWENTY_TWO));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getSeasonSelect().selectedOption().validateTextIs(expectedDataLabels.labelCurrentSeason);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_fixtures() {
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().programmePillButton().validateTextIs(expectedDataLabels.labelFixture);
    }

    @Test
    public void widgetStatsLabelUpdated_when_setDataLabelsDefaultValues_results() {
        var expectedDataLabels = WidgetSettingsFactory.getTeamProgrammeDataLabelsDefaultValues();

        // Apply configuration
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTabPillsSection().resultsPillButton().validateTextIs(expectedDataLabels.labelResults);
    }
}