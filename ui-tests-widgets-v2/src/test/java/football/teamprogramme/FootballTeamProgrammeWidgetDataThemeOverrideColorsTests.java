package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        // Set all dark theme colors with expectedColor using optimized helper method
        widgetsPage.setAllDarkThemeColors(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().setThemes(widgetsPage.getTeamProgrammeThemeOptions(expectedColor.getColorValue().asHex()));
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        // Set all light theme colors with expectedColor using optimized helper method
        widgetsPage.setAllLightThemeColors(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        // Set all client theme colors with expectedColor using optimized helper method
        widgetsPage.setAllClientThemeColors(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }
}