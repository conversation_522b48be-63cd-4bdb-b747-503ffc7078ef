package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.SportalColors;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.Div;
import solutions.bellatrix.web.components.Span;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.PROGRAMME)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        // Set all color properties with expectedColor
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setDropdownBorderColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillActiveBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setSelectButtonSecondaryColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setSelectOptionBorderColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setDropdownActiveBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setDropdownBtnColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillBorderColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillActiveTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPillTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setDisabledPillTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setHeaderBoxShadowColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setOddsContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setOddsSelectionBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setOddItemHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setOddsSelectionBorderColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setStandingsLinkColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.dark.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_400.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.light.colors.setPillColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_890.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setHiContrast(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPrimaryBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setSportEntityContainerPrimaryBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setRowBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPillBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getFootballWidgetOptions().themes.client.colors.setPillColor(expectedColor.getColorValue().asHex());
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getProgrammeSection().tournamentAnchor().createByCss(Span.class, "p").validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getProgrammeSection().matchesList().get(0).validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().getTabPillsSection().programmePillButton().createByCss(Span.class, "span").validateStyle(CssStyle.COLOR, SportalColors.GREY_890.getColorValue().asRgba());
    }
}