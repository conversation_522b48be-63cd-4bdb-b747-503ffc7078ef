package football.teamprogramme;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.utils.StringUtils;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamprogrammewidgetpage.FootballTeamProgrammeWidgetPage;

import static data.constants.StringConstants.*;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMPROGRAMME)
@Tag(WidgetsTags.DARK_THEME)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class FootballTeamProgrammeWidgetDarkThemeVisualTests extends WidgetsBaseWebTest {

    private FootballTeamProgrammeWidgetPage widgetsPage;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballTeamProgrammeWidgetPage();
        WidgetSettings widgetsOptions = widgetsPage.getWidgetOptions();
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(DataThemeEnum.DARK.getValue());
        openWidgetPage(widgetsPage, widgetsOptions);
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openResults_and_setDataThemeToDark_desktopTall() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, StringUtils.capitalizeFirstLetter(RESULTS_STRING)));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetThemeUpdated_when_openFixtures_and_setDataThemeToDark_desktopTall() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, FIXTURES_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openResults_and_setDataThemeToDark_desktopM() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, StringUtils.capitalizeFirstLetter(RESULTS_STRING)));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetThemeUpdated_when_openFixtures_and_setDataThemeToDark_desktopM() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, FIXTURES_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openResults_and_setDataThemeToDark_mobileS() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, StringUtils.capitalizeFirstLetter(RESULTS_STRING)));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetThemeUpdated_when_openFixtures_and_setDataThemeToDark_mobileS() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, FIXTURES_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openResults_and_setDataThemeToDark_mobileL() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, StringUtils.capitalizeFirstLetter(RESULTS_STRING)));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetThemeUpdated_when_openFixtures_and_setDataThemeToDark_mobileL() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, FIXTURES_STRING));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openResults_and_setDataThemeToDark_tablet() {
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, StringUtils.capitalizeFirstLetter(RESULTS_STRING)));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetThemeUpdated_when_openFixtures_and_setDataThemeToDark_tablet() {
        widgetsPage.map().getSeasonSelect().selectCurrentSeason();
        widgetsPage.waitForSpinners();
        assertSameAsBaseline(widgetsPage, "%s - %s".formatted(DARK_THEME_STRING, FIXTURES_STRING));
    }
}
