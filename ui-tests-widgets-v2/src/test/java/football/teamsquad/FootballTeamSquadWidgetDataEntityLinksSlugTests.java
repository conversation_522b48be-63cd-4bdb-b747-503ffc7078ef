package football.teamsquad;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.attributes.DataSeasonAttribute;
import data.widgets.options.enums.DataPlayerEnum;
import data.widgets.options.enums.DataSeasonEfbetLeagueEnum;
import data.widgets.options.enums.DataTeamEnum;
import data.widgets.options.models.Slug;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMSQUAD)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsStories.DATA_ENTITY_LINKS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTeamSquadWidgetDataEntityLinksSlugTests extends WidgetsBaseWebTest {

    private FootballTeamSquadWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamSquadWidgetPage();
        widgetsPage.updateAttributeValue(new DataSeasonAttribute(DataSeasonEfbetLeagueEnum.TWENTY_THREE_TWENTY_FOUR));
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void teamAnchorUpdated_when_setDataEntityLinksTeamSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-team-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .team(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);


        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamNameAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataTeamEnum.PIRIN_BLAGOEVGRAD.getSlugEn()));
    }

    @Test
    public void widgetPlayerUrlUpdatedWithSlug_when_setDataEntityLinksTeamSlugUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-player-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .player(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().goalkeeperOneAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataPlayerEnum.MARIO_KIREV.getSlugEn()));
        widgetsPage.map().goalkeeperTwoAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataPlayerEnum.MAKSIM_KOVALYOV.getSlugEn()));
    }

    @Test
    public void widgetPlayerUrlUpdatedWithSlug_when_setSlugWithoutEntityLinksUrl() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-player-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.player = null;
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .player(expectedUrlFormat)
                        .build();
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().goalkeeperOneAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataPlayerEnum.MARIO_KIREV.getSlugEn()));
        widgetsPage.map().goalkeeperTwoAnchor().validateHrefIs(expectedUrlFormat.replace("{slug}", DataPlayerEnum.MAKSIM_KOVALYOV.getSlugEn()));
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInSameWindow_when_dataEntityLinksConfigurationIsSetToFalse() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(false);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamName().click();
        Assertions.assertEquals(1, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }

    @Test
    @ExecutionBrowser(lifecycle = Lifecycle.RESTART_EVERY_TIME, browser = Browser.CHROME)
    public void slugLinksOpenedInNewWindow_when_dataEntityLinksConfigurationIsSetToTrue() {
        // Apply configuration
        String expectedUrlFormat = "https://dev.sportal.bg/football/auto-competition-{slug}";
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.slug =
                Slug.builder()
                        .competition(expectedUrlFormat)
                        .build();
        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataEntityLinks.configuration.setNewWindow(true);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().teamName().click();
        Assertions.assertEquals(2, widgetsPage.browser().getWrappedDriver().getWindowHandles().size());
    }
}