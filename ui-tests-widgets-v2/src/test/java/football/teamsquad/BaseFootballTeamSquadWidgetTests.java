package football.teamsquad;

import core.WidgetsBaseWebTest;
import data.constants.enums.TeamSquadDataElementsStatisticsEnum;
import data.widgets.attributes.DataElementsAttribute;
import widgets.pages.WidgetSettings;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static data.constants.StringConstants.*;

public class BaseFootballTeamSquadWidgetTests extends WidgetsBaseWebTest {

    protected FootballTeamSquadWidgetPage footballTeamSquadWidgetPage;
    protected WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        viewPort = getViewPort();
        footballTeamSquadWidgetPage = new FootballTeamSquadWidgetPage();
        widgetsOptions = footballTeamSquadWidgetPage.getWidgetOptions();
    }

    protected void visualCheckDataElements(FootballTeamSquadWidgetPage.ViewEnum view, TeamSquadDataElementsStatisticsEnum... dataElements) {
        String dataElementsString;

        if (dataElements.length == 1 && dataElements[0] == TeamSquadDataElementsStatisticsEnum.EMPTY) {
            dataElementsString = "data-elements attribute not provided";
            footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(dataElements[0].getValue()));
        } else {
            List<String> elements = Arrays.stream(dataElements).map(TeamSquadDataElementsStatisticsEnum::getValue).toList();

            dataElementsString = elements.stream()
                    .map(element -> "'" + element + "'")
                    .collect(Collectors.collectingAndThen(
                            Collectors.joining(", ", "[", "]"),
                            s -> "data-elements=\"" + s + "\""
                    ));

            footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(elements));
        }

        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        if (view == FootballTeamSquadWidgetPage.ViewEnum.CARD) {
            footballTeamSquadWidgetPage.map().getDataHeaderPills().cardViewPillButton().click();
        }

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s | %s | %s".formatted(view.getValue(), DARK_THEME_STRING, dataElementsString));
    }

    protected void visualCheckOfDefaultViews(FootballTeamSquadWidgetPage.ViewEnum view, String device) {
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataHeaderDefaultOption(null);

        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage,
                "%s opened by default for %s devices".formatted(view.getValue(), device));
    }
}