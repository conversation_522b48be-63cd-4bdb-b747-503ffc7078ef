package football.teamsquad;

import categories.SMPCategories;
import categories.WidgetsTags;
import data.constants.VisualRegressionProject;
import data.constants.enums.TeamSquadDataElementsStatisticsEnum;
import data.widgets.attributes.DataElementsAttribute;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMSQUAD)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTeamSquadWidgetVisualTests extends BaseFootballTeamSquadWidgetTests {

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setDefaultData() {
        footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(TeamSquadDataElementsStatisticsEnum.EMPTY.getValue()));
        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s %s".formatted("Default values", viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetDisplayed_when_openStats_mobileS() {
        footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(TeamSquadDataElementsStatisticsEnum.EMPTY.getValue()));
        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s %s".formatted("Default values", viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetDisplayed_when_openStats_tablet() {
        footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(TeamSquadDataElementsStatisticsEnum.EMPTY.getValue()));
        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s %s".formatted("Default values", viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetDisplayed_when_openStats_mobileL() {
        footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(TeamSquadDataElementsStatisticsEnum.EMPTY.getValue()));
        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s %s".formatted("Default values", viewPort.name()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.IPHONE_SE_MOBILE)
    public void widgetDisplayed_when_openStats_mobileDevice() {
        footballTeamSquadWidgetPage.updateAttributeValue(new DataElementsAttribute(TeamSquadDataElementsStatisticsEnum.EMPTY.getValue()));
       openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);

        assertSameAsBaseline(footballTeamSquadWidgetPage, "%s %s".formatted("Default values", viewPort.name()));
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_mobile() {
       openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);
        footballTeamSquadWidgetPage.map().getSeasonSelect().selectButton().click();

        assertSameAsBaseline(footballTeamSquadWidgetPage, "Dropdown State Mobile (%s) ".formatted(DeviceName.IPHONE_SE_MOBILE.getName()));
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void dropdownCorrectlyDisplayed_when_selectValue_mobile() {
        openWidgetPage(footballTeamSquadWidgetPage, widgetsOptions);
        footballTeamSquadWidgetPage.map().getSeasonSelect().selectPreviousSeason();

        assertSameAsBaseline(footballTeamSquadWidgetPage, "Dropdown Selection Mobile (%s) ".formatted(DeviceName.IPHONE_SE_MOBILE.getName()));
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDefaultListViewDisplayed_when_openWidget_desktop() {
        visualCheckOfDefaultViews(FootballTeamSquadWidgetPage.ViewEnum.LIST, ViewportSize.DESKTOP_M.name());
    }

    @Test
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, deviceName = DeviceName.IPHONE_SE_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.IPHONE_SE)
    public void widgetDefaultCardViewDisplayed_when_openWidget_mobileDevice() {
        visualCheckOfDefaultViews(FootballTeamSquadWidgetPage.ViewEnum.CARD, ViewportSize.IPHONE_SE.name());
    }
}