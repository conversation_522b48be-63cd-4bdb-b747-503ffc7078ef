package football.teamsquad;

import categories.SMPCategories;
import categories.WidgetsTags;
import data.constants.VisualRegressionProject;
import data.constants.enums.TeamSquadDataElementsStatisticsEnum;
import data.widgets.options.enums.DataThemeEnum;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.Browser;
import solutions.bellatrix.web.infrastructure.DeviceName;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMSQUAD)
@Tag(WidgetsTags.DARK_THEME)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class FootballTeamSquadWidgetDarkThemeVisualTests extends BaseFootballTeamSquadWidgetTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataLabels(null);
        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataTheme(DataThemeEnum.DARK.getValue());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setAllDataElements_desktopTall(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.getValues());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setDefaultDataElements_desktopTall(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.EMPTY);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setConcededDataElements_desktopTall(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.CONCEDED_GOALS);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setAllDataElements_desktopM(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.getValues());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setDefaultDataElements_desktopM(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.EMPTY);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setConcededDataElements_desktopM(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.CONCEDED_GOALS);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setAllDataElements_mobileS(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.getValues());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setDefaultDataElements_mobileS(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.EMPTY);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_S)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_S)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setConcededDataElements_mobileS(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.CONCEDED_GOALS);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setAllDataElements_mobileL(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.getValues());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setDefaultDataElements_mobileL(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.EMPTY);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.MOBILE_L)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.MOBILE_L)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setConcededDataElements_mobileL(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.CONCEDED_GOALS);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setAllDataElements_tablet(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.getValues());
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setDefaultDataElements_tablet(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.EMPTY);
    }

    @ParameterizedTest
    @EnumSource(FootballTeamSquadWidgetPage.ViewEnum.class)
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.TABLET)
    @ExecutionBrowser(browser = Browser.CHROME_MOBILE, lifecycle = Lifecycle.REUSE_IF_STARTED, deviceName = DeviceName.TABLET)
    public void widgetCorrectlyDisplayed_when_openView_setDarkTheme_and_setConcededDataElements_tablet(FootballTeamSquadWidgetPage.ViewEnum view) {
        visualCheckDataElements(view, TeamSquadDataElementsStatisticsEnum.CONCEDED_GOALS);
    }
}