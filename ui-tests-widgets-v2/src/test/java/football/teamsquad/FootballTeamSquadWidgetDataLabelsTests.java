package football.teamsquad;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.WidgetSettingsFactory;
import widgets.pages.football.teamsquadwidgetpage.FootballTeamSquadWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.TEAMSQUAD)
@Story(WidgetsStories.PROFILE)
@Story(WidgetsStories.DATA_LABELS)
@Tag(WidgetsTags.STATISTICS_WIDGETS)
public class FootballTeamSquadWidgetDataLabelsTests extends WidgetsBaseWebTest {

    private FootballTeamSquadWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new FootballTeamSquadWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_name() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.labelNameShort);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_age() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getAgeHeader().validateTextIs(expectedDataLabels.ageShort);
        widgetsPage.map().getTableSection().getAgeFullHeader().validateHtmlIs(expectedDataLabels.age);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_matchesPlayed() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesPlayedHeader().validateTextIs(expectedDataLabels.matchesPlayedShort);
        widgetsPage.map().getTableSection().getMatchesPlayedFullHeader().validateHtmlIs(expectedDataLabels.matchesPlayed);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_goals() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataLabels(null);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getGoalsHeader().validateTextIs(expectedDataLabels.goalsShort);
        widgetsPage.map().getTableSection().getGoalsFullHeader().validateHtmlIs(expectedDataLabels.goals);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_assists() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().getWidgetAttributes().setDataLabels(null);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getAssistsHeader().validateTextIs(expectedDataLabels.assistsShort);
        widgetsPage.map().getTableSection().getAssistsFullHeader().validateHtmlIs(expectedDataLabels.assists);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_yellowCards() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getYellowCardsHeader().validateTextIs(expectedDataLabels.yellowCardsShort);
        widgetsPage.map().getTableSection().getYellowCardsFullHeader().validateHtmlIs(expectedDataLabels.yellowCards);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabelsDefaultValues_redCards() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabelsDefaultValues();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.dataLabels = null;
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getRedCardsHeader().validateTextIs(expectedDataLabels.redCardsShort);
        widgetsPage.map().getTableSection().getRedCardsFullHeader().validateHtmlIs(expectedDataLabels.redCards);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_name() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getNameHeader().validateTextIs(expectedDataLabels.labelName);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_age() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getAgeHeader().validateTextIs(expectedDataLabels.ageShort);
        widgetsPage.map().getTableSection().getAgeFullHeader().validateHtmlIs(expectedDataLabels.age);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_matchesPlayed() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMatchesPlayedHeader().validateTextIs(expectedDataLabels.matchesPlayedShort);
        widgetsPage.map().getTableSection().getMatchesPlayedFullHeader().validateHtmlIs(expectedDataLabels.matchesPlayed);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_goals() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getGoalsHeader().validateTextIs(expectedDataLabels.goalsShort);
        widgetsPage.map().getTableSection().getGoalsFullHeader().validateHtmlIs(expectedDataLabels.goals);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_assists() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getAssistsHeader().validateTextIs(expectedDataLabels.assistsShort);
        widgetsPage.map().getTableSection().getAssistsFullHeader().validateHtmlIs(expectedDataLabels.assists);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_yellowCards() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getYellowCardsHeader().validateTextIs(expectedDataLabels.yellowCardsShort);
        widgetsPage.map().getTableSection().getYellowCardsFullHeader().validateHtmlIs(expectedDataLabels.yellowCards);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_redCards() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getRedCardsHeader().validateTextIs(expectedDataLabels.redCardsShort);
        widgetsPage.map().getTableSection().getRedCardsFullHeader().validateHtmlIs(expectedDataLabels.redCards);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_conceded() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getConcededHeader().validateTextIs(expectedDataLabels.concededShort);
        widgetsPage.map().getTableSection().getConcededFullHeader().validateHtmlIs(expectedDataLabels.conceded);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_cleanSheets() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getCleansheetsHeader().validateTextIs(expectedDataLabels.cleanSheetsShort);
        widgetsPage.map().getTableSection().getCleansheetsFullHeader().validateHtmlIs(expectedDataLabels.cleanSheets);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_minutesSubstitute() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMinutesSubstituteHeader().validateTextIs(expectedDataLabels.minutesSubstituteShort);
        widgetsPage.map().getTableSection().getMinutesSubstituteFullHeader().validateHtmlIs(expectedDataLabels.minutesSubstitute);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_minutes() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getMinutesHeader().validateTextIs(expectedDataLabels.minutesShort);
        widgetsPage.map().getTableSection().getMinutesFullHeader().validateHtmlIs(expectedDataLabels.minutes);
    }

    @Test
    public void widgetTableHeadersUpdated_when_setDataLabels_started() {
        // Apply configuration
        var expectedDataLabels = WidgetSettingsFactory.getTeamSquadDataLabels();

        widgetsOptions.getFootballWidgetOptions().widgetAttributes.setDataLabels(expectedDataLabels);
        widgetsPage.generatePage(widgetsOptions);

        // Navigate to page
        widgetsPage.open();

        // Assert Configuration Applied
        widgetsPage.map().getTableSection().getStartedHeader().validateTextIs(expectedDataLabels.startedShort);
        widgetsPage.map().getTableSection().getStartedFullHeader().validateHtmlIs(expectedDataLabels.started);
    }
}