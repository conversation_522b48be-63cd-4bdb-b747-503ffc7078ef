package multisport;

import categories.SMPCategories;
import categories.WidgetsStories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.widgets.options.enums.DataThemeEnum;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.support.Colors;
import solutions.bellatrix.web.components.enums.CssStyle;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.WidgetSettings;
import widgets.pages.multisport.multisportwidgetpage.MultisportWidgetPage;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.MULTISPORT)
@Story(WidgetsStories.MULTISPORT)
@Story(WidgetsStories.DATA_THEME)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
public class MultisportWidgetDataThemeOverrideColorsTests extends WidgetsBaseWebTest {

    private MultisportWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        // Generate html for page with configuration required
        widgetsPage = new MultisportWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().border().validateStyle(CssStyle.BORDER_RIGHT, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().text().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().commonText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dateText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().statusText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamLost().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamWin().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdatedInDropdown_when_overrideDarkThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.DARK.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.dark.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().dropDownButton().click();

        // Assert Configuration Applied
        widgetsPage.map().dropDown().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dropDown().validateStyle(CssStyle.BORDER, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().dopDownActivePills().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().border().validateStyle(CssStyle.BORDER_RIGHT, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().text().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().commonText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dateText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().statusText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamLost().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamWin().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdatedInDropdown_when_overrideLightThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.LIGHT.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.light.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().dropDownButton().click();

        // Assert Configuration Applied
        widgetsPage.map().dropDown().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dropDown().validateStyle(CssStyle.BORDER, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().dopDownActivePills().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdated_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);

        // Assert Configuration Applied
        widgetsPage.map().widgetDiv().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().border().validateStyle(CssStyle.BORDER_RIGHT, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().text().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().commonText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dateText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().statusText().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamLost().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().teamWin().validateStyle(CssStyle.COLOR, expectedColor.getColorValue().asRgba());
    }

    @Test
    public void widgetThemeColorsUpdatedInDropdown_when_overrideClientThemeColors() {
        // Apply configuration
        Colors expectedColor = Colors.YELLOW;
        widgetsOptions.getMultisportWidgetOptions().widgetAttributes.dataTheme = DataThemeEnum.CLIENT.getValue();

        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setBorderMultiSportColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportCommonColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setTeamLostColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setGamePartColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportWinnerColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportHoverColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setDropdownBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setLiveIndicatorColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setPostponedMatchColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setProgressContainerBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setSurfaceBackgroundColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultisportHighlightColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setAgeRestrictTextColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setMultiSportDropdownButtonBgColor(expectedColor.getColorValue().asHex());
        widgetsOptions.getMultisportWidgetOptions().themes.client.colors.setDropdownActiveDateBgColor(expectedColor.getColorValue().asHex());

        // Navigate to page
        openWidgetPage(widgetsPage, widgetsOptions);
        widgetsPage.map().dropDownButton().click();

        // Assert Configuration Applied
        widgetsPage.map().dropDown().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
        widgetsPage.map().dropDown().validateStyle(CssStyle.BORDER, "1px solid " + expectedColor.getColorValue().asRgb());
        widgetsPage.map().dopDownActivePills().validateStyle(CssStyle.BACKGROUND_COLOR, expectedColor.getColorValue().asRgba());
    }
}
