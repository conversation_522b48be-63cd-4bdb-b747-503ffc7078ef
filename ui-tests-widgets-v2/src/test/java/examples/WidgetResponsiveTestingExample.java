package examples;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.*;
import widgets.pages.WidgetSettings;
import widgets.pages.football.playerwidgetpage.FootballPlayerWidgetPage;
import widgets.utils.WidgetStyleUtils;

/**
 * Example test class demonstrating how to use the new widget styling utilities
 * for responsive testing across different widgets.
 * 
 * This shows the improved approach for testing widgets with container constraints.
 */
@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.FOOTBALL)
@Tag(WidgetsTags.PLAYER)
@Tag(SMPCategories.VISUAL)
@Tag(WidgetsTags.PROFILE_WIDGETS)
public class WidgetResponsiveTestingExample extends WidgetsBaseWebTest {

    private FootballPlayerWidgetPage widgetsPage;
    private WidgetSettings widgetsOptions;

    @Override
    protected void beforeEach() {
        widgetsPage = new FootballPlayerWidgetPage();
        widgetsOptions = widgetsPage.getWidgetOptions();
    }

    /**
     * Example 1: Basic max-width testing using the utility class
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_setMaxWidth300px_usingUtility() {
        // Method 1: Using utility class with predefined constant
        WidgetStyleUtils.applyMaxWidth(widgetsPage, WidgetStyleUtils.MaxWidth.CONTAINER_300);
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "utility-max-width-300px");
    }

    /**
     * Example 2: Mobile constraints testing
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_applyMobileConstraints() {
        // Method 2: Using predefined mobile constraints
        WidgetStyleUtils.applyMobileConstraints(widgetsPage);
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "mobile-constraints-desktop");
    }

    /**
     * Example 3: Container testing with visual enhancements
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_applyContainerTesting() {
        // Method 3: Container testing with border and padding
        WidgetStyleUtils.applyContainerTesting(widgetsPage, WidgetStyleUtils.MaxWidth.CONTAINER_400);
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "container-testing-400px");
    }

    /**
     * Example 4: Complete responsive testing setup
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_applyResponsiveTestingSetup() {
        // Method 4: Complete responsive setup with visual enhancements
        WidgetStyleUtils.applyResponsiveTestingSetup(widgetsPage, WidgetStyleUtils.MaxWidth.CONTAINER_500);
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "responsive-testing-setup-500px");
    }

    /**
     * Example 5: Custom styles with max-width
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_applyCustomStyles() {
        // Method 5: Custom styles with max-width
        WidgetStyleUtils.applyMaxWidthWithStyles(
            widgetsPage, 
            WidgetStyleUtils.MaxWidth.CONTAINER_300,
            "background-color: #f5f5f5; border-radius: 10px; padding: 20px;"
        );
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "custom-styles-300px");
    }

    /**
     * Example 6: Direct method usage (original approach improved)
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_useDirectMethods() {
        // Method 6: Direct usage of the base class methods
        widgetsPage.setWidgetMaxWidth("300px");
        // or
        // widgetsPage.setCustomWidgetStyles("max-width: 300px; border: 1px solid #ccc;");
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "direct-methods-300px");
    }

    /**
     * Example 7: Percentage-based responsive testing
     */
    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_M)
    public void widgetDisplayed_when_usePercentageWidth() {
        // Method 7: Percentage-based width testing
        WidgetStyleUtils.applyMaxWidth(widgetsPage, WidgetStyleUtils.MaxWidth.HALF_WIDTH);
        
        widgetsPage.generatePage(widgetsOptions);
        widgetsPage.open();

        assertSameAsBaseline(widgetsPage, "percentage-width-50percent");
    }
}
