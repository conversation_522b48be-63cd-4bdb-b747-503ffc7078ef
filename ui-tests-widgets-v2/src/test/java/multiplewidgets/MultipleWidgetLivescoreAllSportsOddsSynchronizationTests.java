package multiplewidgets;

import categories.SMPCategories;
import categories.WidgetsTags;
import core.WidgetsBaseWebTest;
import data.constants.VisualRegressionProject;
import data.widgets.attributes.*;
import data.widgets.options.enums.BooleanEnum;
import data.widgets.options.enums.DataHeaderDefaultOptionEnum;
import data.widgets.options.enums.DataOddsBettingIdEnum;
import data.widgets.options.enums.DataPopularListEnum;
import data.widgets.options.models.DataDate;
import io.visual_regression_tracker.sdk_java.TestRunResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.visualregression.ViewportSize;
import plugins.visualregression.VisualRegression;
import solutions.bellatrix.web.infrastructure.ExecutionBrowser;
import solutions.bellatrix.web.infrastructure.Lifecycle;
import widgets.pages.MultipleWidgetsPage;
import widgets.pages.WidgetSettings;
import widgets.pages.basketball.livescorewidgetpage.BasketballLivescoreWidgetPage;
import widgets.pages.football.livescorewidgetpage.FootballLivescoreWidgetPage;
import widgets.pages.icehockey.livescorewidgetpage.IceHockeyLivescoreWidgetPage;
import widgets.pages.tennis.livescorewidgetpage.TennisLivescoreWidgetPage;

import java.util.List;

@ExecutionBrowser(lifecycle = Lifecycle.REUSE_IF_STARTED)
@Authenticate(user = User.FULLSETUP, project = Project.STATIC)
@Tag(SMPCategories.WIDGETSV2)
@Tag(WidgetsTags.MULTI_WIDGETS)
@Tag(WidgetsTags.PROGRAMME_WIDGETS)
@Tag(SMPCategories.VISUAL)
public class MultipleWidgetLivescoreAllSportsOddsSynchronizationTests extends WidgetsBaseWebTest {

    private static final String LIVESCORE_ALL_SPORTS_STRING = "Livescore All Sports ";
    private static final String ODDS_DISPLAYED = LIVESCORE_ALL_SPORTS_STRING + "Odds displayed";
    private static final String ODD_PROVIDER_LOGO_DISPLAYED = LIVESCORE_ALL_SPORTS_STRING + "Odd provider logo displayed";
    private WidgetSettings footballLivescoreWidgetOptions;
    private BasketballLivescoreWidgetPage basketballLivescoreWidgetPage;
    private WidgetSettings basketballLivescoreWidgetOptions;
    private TennisLivescoreWidgetPage tennisLivescoreWidgetPage;
    private WidgetSettings tennisLivescoreWidgetOptions;
    private WidgetSettings iceHockeyLivescoreWidgetOptions;
    private MultipleWidgetsPage multipleWidgetsPage;
    private TestRunResult oddsScreenshot;
    private TestRunResult oddProviderLogoScreenshot;

    @Override
    protected void beforeEach() {
        FootballLivescoreWidgetPage footballLivescoreWidgetPage = new FootballLivescoreWidgetPage();
        footballLivescoreWidgetPage.updateAttributeValue(new DataDateAttribute(
                DataDate.builder()
                        .date("17-06-2024")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
        footballLivescoreWidgetPage.updateAttributeValue(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.ODDS));
        footballLivescoreWidgetPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.FALSE));
        footballLivescoreWidgetPage.updateAttributeValue(new DataOptionsDisplayAttribute(BooleanEnum.FALSE));
        footballLivescoreWidgetPage.updateAttributeValue(new DataBettingLogoOverlapAttribute(BooleanEnum.TRUE));
        footballLivescoreWidgetOptions = footballLivescoreWidgetPage.getWidgetOptions();
        footballLivescoreWidgetOptions.getFootballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());

        basketballLivescoreWidgetPage = new BasketballLivescoreWidgetPage();
        basketballLivescoreWidgetPage.updateAttributeValue(new DataDateAttribute(
                DataDate.builder()
                        .date("12-06-2024")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
        basketballLivescoreWidgetPage.updateAttributeValue(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.ODDS));
        basketballLivescoreWidgetPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.FALSE));
        basketballLivescoreWidgetPage.updateAttributeValue(new DataOptionsDisplayAttribute(BooleanEnum.FALSE));
        basketballLivescoreWidgetPage.updateAttributeValue(new DataBettingLogoOverlapAttribute(BooleanEnum.TRUE));
        basketballLivescoreWidgetOptions = basketballLivescoreWidgetPage.getWidgetOptions();
        basketballLivescoreWidgetOptions.getBasketballWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());

        tennisLivescoreWidgetPage = new TennisLivescoreWidgetPage();
        tennisLivescoreWidgetPage.updateAttributeValue(new DataDateAttribute(
                DataDate.builder()
                        .date("12-06-2024")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
        tennisLivescoreWidgetPage.updateAttributeValue(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.ODDS));
        tennisLivescoreWidgetPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.FALSE));
        tennisLivescoreWidgetPage.updateAttributeValue(new DataOptionsDisplayAttribute(BooleanEnum.FALSE));
        tennisLivescoreWidgetPage.updateAttributeValue(new DataBettingLogoOverlapAttribute(BooleanEnum.TRUE));
        tennisLivescoreWidgetOptions = tennisLivescoreWidgetPage.getWidgetOptions();
        tennisLivescoreWidgetOptions.getTennisWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());

        IceHockeyLivescoreWidgetPage iceHockeyLivescoreWidgetPage = new IceHockeyLivescoreWidgetPage();
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataDateAttribute(
                DataDate.builder()
                        .date("20-04-2025")
                        .dateFormat("DD-MM-YYYY")
                        .build()));
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataOddsBettingIdAttribute(DataOddsBettingIdEnum.ONE_HUNDRED_AND_TWENTY));
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataHeaderDefaultOptionAttribute(DataHeaderDefaultOptionEnum.ODDS));
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataHeaderDisplayAttribute(BooleanEnum.FALSE));
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataOptionsDisplayAttribute(BooleanEnum.FALSE));
        iceHockeyLivescoreWidgetPage.updateAttributeValue(new DataBettingLogoOverlapAttribute(BooleanEnum.TRUE));
        iceHockeyLivescoreWidgetOptions = iceHockeyLivescoreWidgetPage.getWidgetOptions();
        iceHockeyLivescoreWidgetOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigProject(getCurrentTestProject().getDomain());
        iceHockeyLivescoreWidgetOptions.getIceHockeyWidgetOptions().getSdkOptions().setDataConfigCompetitionList(DataPopularListEnum.ALL.getValue());

        multipleWidgetsPage = new MultipleWidgetsPage();
    }

    @Test
    @VisualRegression(projectName = VisualRegressionProject.WIDGETSV2, viewportSize = ViewportSize.DESKTOP_TALL)
    public void oddsAndBettingProviderLogoSynchronized_when_openMultipleWidgetsLivescoreAllSports() {
        multipleWidgetsPage.generatePage(
                List.of(
                        footballLivescoreWidgetOptions,
                        basketballLivescoreWidgetOptions,
                        tennisLivescoreWidgetOptions,
                        iceHockeyLivescoreWidgetOptions
                )
        );

        multipleWidgetsPage.open();
        basketballLivescoreWidgetPage.waitForPageLoad();
        tennisLivescoreWidgetPage.waitForPageLoad();

        oddsScreenshot = takeScreenshot(multipleWidgetsPage, ODDS_DISPLAYED, 2000);
        oddProviderLogoScreenshot = takeScreenshot(multipleWidgetsPage, ODD_PROVIDER_LOGO_DISPLAYED, 2000);

        Assertions.assertAll(
                () -> assertSameAsBaseline(oddsScreenshot),
                () -> assertSameAsBaseline(oddProviderLogoScreenshot));
    }
}