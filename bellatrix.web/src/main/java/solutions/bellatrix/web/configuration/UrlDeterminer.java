/*
 * Copyright 2022 Automate The Planet Ltd.
 * Author: <PERSON>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * You may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package solutions.bellatrix.web.configuration;

import org.apache.http.client.utils.URIBuilder;
import solutions.bellatrix.core.configuration.ConfigurationService;

import java.net.URI;
import java.net.URISyntaxException;

public class UrlDeterminer {
    public static String getShopUrl(String urlPart) {
        return contactUrls(ConfigurationService.get(UrlSettings.class).getShopUrl(), urlPart);
    }

    public static String getAccountUrl(String urlPart) {
        return contactUrls(ConfigurationService.get(UrlSettings.class).getAccountUrl(), urlPart);
    }

    private static String contactUrls(String url, String part) {
        try {
            var uriBuilder = new URIBuilder(url);
            URI uri = uriBuilder.setPath(uriBuilder.getPath() + part)
                    .build()
                    .normalize();
            return uri.toString();
        } catch (URISyntaxException ex) {
            return null;
        }
    }
}
