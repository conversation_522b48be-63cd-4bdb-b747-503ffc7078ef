package basketballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.MethodParameterType;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.models.basketball.season.SeasonDetailsModel;
import data.models.searchapi.ResultModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.openqa.selenium.NotFoundException;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.UUID;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.BASKETBALL_API)
@Story(APITags.BASKETBALL_API)
public class BasketballApiSeasonsTests extends BaseBasketballApiTests {

    @Test
    public void basketballSeasonsReturned_when_getRequestAll() {
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);

        seasonListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        Assertions.assertFalse(seasonListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Basketball Seasons list"));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setNoQueryParams() {
        basketballSeasonsRepo.getAll().getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        Matchers.containsString(AssertMessages.requiredRequestParameter(StringConstants.OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setLimitQueryParamOnly() {
        defaultQueryParams.remove(StringConstants.OFFSET_STRING);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        Matchers.containsString(AssertMessages.requiredRequestParameter(StringConstants.OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setOffsetQueryParamOnly() {
        defaultQueryParams.remove(StringConstants.LIMIT_STRING);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        Matchers.containsString(AssertMessages.requiredRequestParameter(StringConstants.LIMIT_STRING, MethodParameterType.INTEGER)));
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3, 5, 10, 30, 100, 200, 1000})
    public void expectedCountOfBasketballSeasonsReturned_when_getRequestAll_and_setLimitQueryParam(int limit) {
        defaultQueryParams.put(StringConstants.LIMIT_STRING, limit);

        var basketballSeasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);
        basketballSeasonListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Long pageMetaActualLimit = basketballSeasonsRepo.getMetaData().getResult().getLimit();
        long actualCountOfSeasons = basketballSeasonListResponse.getResult().size();

        Assertions.assertEquals(limit, pageMetaActualLimit, AssertMessages.responseNotContains(StringConstants.LIMIT_STRING));
        Assertions.assertEquals(limit, actualCountOfSeasons, AssertMessages.responseNotContains("basketball seasons count"));
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3, 5, 10, 30, 100})
    public void expectedOffsetApplied_when_getRequestAll_and_setOffsetQueryParam(int offset) {
        defaultQueryParams.put(StringConstants.OFFSET_STRING, offset);

        var basketballSeasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);
        basketballSeasonListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Long pageMetaActualOffset = basketballSeasonsRepo.getMetaData().getResult().getOffset();

        Assertions.assertEquals(offset, pageMetaActualOffset, AssertMessages.responseNotContains(StringConstants.LIMIT_STRING));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidOffsetValueQueryParam() {
        defaultQueryParams.put(StringConstants.OFFSET_STRING, INVALID_OFFSET);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.OFFSET_STRING)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidLimitValueQueryParam() {
        defaultQueryParams.put(StringConstants.LIMIT_STRING, INVALID_LIMIT);

        basketballTeamRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.LIMIT_STRING)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidOffset_and_invalidLimitValuesQueryParams() {
        defaultQueryParams.put(StringConstants.OFFSET_STRING, INVALID_OFFSET);
        defaultQueryParams.put(StringConstants.LIMIT_STRING, INVALID_LIMIT);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.OFFSET_STRING)));
    }

    @Test
    public void basketballSeasonsReturned_when_getRequestAll_and_setCompetitionIdQueryParam() {
        var season = basketballSeasonsRepo.getAll(defaultQueryParams).getResult().get(0);
        defaultQueryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, season.getCompetition().getId());

        var seasonsListResponseByCompetitionId = basketballSeasonsRepo.getAll(defaultQueryParams);
        seasonsListResponseByCompetitionId.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(seasonsListResponseByCompetitionId.getResult().isEmpty(), AssertMessages.responseNotContains("Basketball Seasons list"));
        Assertions.assertEquals(season, seasonsListResponseByCompetitionId.getResult().get(0), AssertMessages.responseNotContains("Basketball Season object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidCompetitionIdQueryParam() {
        String invalidCompetitionId = "test-invalid-competition-id";

        defaultQueryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, invalidCompetitionId);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Competition with ID %s does not exist!".formatted(invalidCompetitionId)));
    }

    @Test
    public void basketballSeasonDetailsReturned_when_getRequestAll_and_setTeamIdQueryParam() {
        ResultModel laLakersTeam = searchV2SuggestRepo.getTeamsByName(BasketballTeamEnum.LA_LAKERS.getName(), SupportedSports.BASKETBALL).get(0);
        defaultQueryParams.put(StringConstants.TEAM_ID_STRING, laLakersTeam.getId());

        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);
        seasonListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(seasonListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Basketball Seasons list"));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidTeamIdQueryParam() {
        String invalidTeamId = "invalid-team-id";
        defaultQueryParams.put(StringConstants.TEAM_ID_STRING, invalidTeamId);

        basketballSeasonsRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Team with ID %s does not exist!".formatted(invalidTeamId)));
    }

    @Test
    public void basketballSeasonReturned_when_getRequestBySeasonId() {
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);
        var firstSeason = seasonListResponse.getResult().get(0);

        var seasonResponse = basketballSeasonsRepo.getById(firstSeason.getId());
        var actualStatusCode = seasonResponse.getResponse().getStatusCode();

        Assertions.assertEquals(HttpStatus.SC_OK, actualStatusCode, AssertMessages.statusCodeNotExpected(String.valueOf(actualStatusCode)));
        Assertions.assertEquals(firstSeason, seasonResponse.getResult(), AssertMessages.responseNotContains("First Season object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestBySeasonId_and_setInvalidSeasonId() {
        String invalidId = String.valueOf(UUID.randomUUID());

        basketballSeasonsRepo.getById(invalidId).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Season with ID %s does not exist!".formatted(invalidId)));
    }

    @Test
    public void basketballSeasonReturned_when_getRequestBySeasonSlug() {
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams);
        var firstSeason = seasonListResponse.getResult().get(0);

        var seasonResponse = basketballSeasonsRepo.getById(firstSeason.getSlug());
        var actualStatusCode = seasonResponse.getResponse().getStatusCode();

        Assertions.assertEquals(HttpStatus.SC_OK, actualStatusCode, AssertMessages.statusCodeNotExpected(String.valueOf(actualStatusCode)));
        Assertions.assertEquals(firstSeason, seasonResponse.getResult(), AssertMessages.responseNotContains("First Season object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestBySeasonId_and_setInvalidSeasonSlug() {
        String invalidSlug = "chicago-bulls-123-team";

        basketballSeasonsRepo.getById(invalidSlug).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Season with ID 00000000-0000-0000-0000-000000ca64d0 does not exist!"));
    }

    @Test
    public void errorMessageReturned_when_getRequestSeasonDetails_and_setNoQueryParams() {
        basketballSeasonsRepo.get("details", queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Either %s or %s parameter should be provided".formatted(
                        StringConstants.SEASON_ID_UNDERSCORED_STRING,
                        StringConstants.COMPETITION_ID_UNDERSCORED_STRING)));
    }

    @Test
    public void seasonsDetailsReturned_when_getRequestSeasonDetails_andSetSeasonId() {
        var firstSeason = basketballSeasonsRepo.getAll(defaultQueryParams).getResult().get(0);

        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, firstSeason.getId());
        var seasonDetailsResponse = basketballSeasonsRepo.get("details", queryParams);
        seasonDetailsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        firstSeason.setStages(seasonDetailsResponse.getResult().getStages());

        Assertions.assertEquals(firstSeason, seasonDetailsResponse.getResult(), AssertMessages.responseNotContains("Basketball season details object"));
    }

    @Test
    public void seasonsDetailsReturnedWithStatusInactive_when_getRequestSeasonDetails_andInactiveSetSeasonId() {
        var inactiveSeason = basketballSeasonsRepo.getAll(defaultQueryParams).getResult().stream()
                .filter(s -> s.getStatus().equals(StringConstants.INACTIVE.toUpperCase()))
                .findFirst()
                .orElseThrow(() -> new NotFoundException("Inactive season not found"));

        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, inactiveSeason.getId());
        var seasonDetailsResponse = basketballSeasonsRepo.get("details", queryParams);
        seasonDetailsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        inactiveSeason.setStages(seasonDetailsResponse.getResult().getStages());

        Assertions.assertEquals(inactiveSeason, seasonDetailsResponse.getResult(), AssertMessages.responseNotContains("Basketball season details object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestSeasonDetails_and_setInvalidSeasonIdQueryParam() {
        String invalidSeasonId = String.valueOf(UUID.randomUUID());
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, invalidSeasonId);

        basketballSeasonsRepo.get("details", queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Season with ID %s does not exist!".formatted(invalidSeasonId)));
    }

    @Test
    public void errorMessageReturned_when_getRequestSeasonDetails_and_setCompetitionIdQueryParamOnly() {
        var firstSeason = basketballSeasonsRepo.getAll(defaultQueryParams).getResult().get(0);

        queryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, firstSeason.getCompetition().getId());
        basketballSeasonsRepo.get("details", queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("%s and season parameters should be submitted together"
                        .formatted(StringConstants.COMPETITION_ID_UNDERSCORED_STRING)));
    }

    @Test
    public void basketballSeasonDetailsReturned_when_getRequestSeasonDetails_and_setCompetitionId_and_seasonQueryParams() {
        var firstSeason = basketballSeasonsRepo.getAll(defaultQueryParams).getResult().get(0);
        seasonsQueryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, firstSeason.getCompetition().getId());

        var seasonDetailsResponse = basketballSeasonsRepo.get("details", seasonsQueryParams);
        seasonDetailsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        firstSeason.setStages(seasonDetailsResponse.getResult().getStages());

        Assertions.assertFalse(seasonDetailsResponse.getResult().getStages().isEmpty(), AssertMessages.responseNotContains("Basketball Season Stages list"));
        Assertions.assertEquals(firstSeason, seasonDetailsResponse.getResult(), AssertMessages.responseNotContains("Basketball season details object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestSeasonDetails_and_setInvalidCompetitionId_and_seasonQueryParams() {
        String invalidCompetitionId = String.valueOf(UUID.randomUUID());
        seasonsQueryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, invalidCompetitionId);

        basketballSeasonsRepo.get("details", seasonsQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Competition with ID %s does not exist!".formatted(invalidCompetitionId)));
    }

    @Test
//    @Issue("SBE-3012")
    public void errorMessageReturned_when_getRequestSeasonDetails_and_setSeasonQueryParamOnly() {
        basketballSeasonsRepo.get("details", seasonsQueryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Either %s or %s parameter should be provided".formatted(
                        StringConstants.SEASON_ID_UNDERSCORED_STRING,
                        StringConstants.COMPETITION_ID_UNDERSCORED_STRING)));
    }


    @Test
    public void basketballSeasonDetailsReturned_when_getRequestAll_and_setTranslationLanguage() {
        String expectedLanguage = "bg";
        var customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, "all");

        var seasonsListResponse = basketballSeasonsRepo.getAll(customQueryParams);

        String countryNameTranslated = seasonsListResponse.getResult().stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations().stream().anyMatch(t -> t.getLanguage().equals(expectedLanguage) && t.getName() != null))
                .findFirst()
                .flatMap(c -> c.getCompetition().getCountry().getTranslations().stream().filter(t -> t.getLanguage().equals(expectedLanguage)).findFirst())
                .orElseThrow()
                .getName();

        customQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, expectedLanguage);
        var seasonListResponse = basketballSeasonsRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, seasonListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected("200"));
        Assertions.assertTrue(seasonListResponse.getResult().stream().anyMatch(e -> e.getCompetition().getCountry().getName().contains(countryNameTranslated)),
                AssertMessages.responseNotContains("Country name is not translated"));
    }

    @Test
    public void basketballSeasonDetailsReturned_when_getRequestById_and_setTranslationLanguage() {
        String expectedLanguage = "bg";
        var customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, "all");

        var seasonsListResponse = basketballSeasonsRepo.getAll(customQueryParams);

        SeasonDetailsModel seasonDetails = seasonsListResponse.getResult().stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations().stream().anyMatch(t -> t.getLanguage().equals(expectedLanguage) && t.getName() != null))
                .findFirst()
                .orElseThrow();

        var translatedCountryName = seasonDetails.getCompetition().getCountry().getTranslations().stream()
                .filter(t -> t.getLanguage().equals(expectedLanguage))
                .findFirst()
                .orElseThrow()
                .getName();

        var seasonId = seasonDetails.getId();
        var seasonDetailsResponse = basketballSeasonsRepo.getSeasonsDetails(seasonId, expectedLanguage);

        Assertions.assertEquals(HttpStatus.SC_OK, seasonDetailsResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected("200"));
        Assertions.assertTrue(seasonDetailsResponse.getResult().getCompetition().getCountry().getName().contains(translatedCountryName), AssertMessages.responseNotContains("Country name is not translated"));
    }
}