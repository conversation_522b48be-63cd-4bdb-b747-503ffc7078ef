package basketballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.MethodParameterType;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.SportEntityEnum;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.constants.enums.odds.OddsScopeTypeEnum;
import data.models.basketball.game.GameModel;
import data.widgets.options.enums.DataOddsMarketValueTypeEnum;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.BASKETBALL_API)
@Story(APITags.BASKETBALL_API)
public class BasketballApiGamesTests extends BaseBasketballApiTests {

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setNoQueryParams() {
        basketballGamesRepo.getAll().getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.containsString(AssertMessages.requiredRequestParameter(StringConstants.OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setOffsetQueryParamOnly() {
        defaultQueryParams.remove(StringConstants.LIMIT_STRING);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(
                        AssertMessages.requiredRequestParameter(StringConstants.LIMIT_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setLimitQueryParamOnly() {
        defaultQueryParams.remove(StringConstants.OFFSET_STRING);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(
                        AssertMessages.requiredRequestParameter(StringConstants.OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidOffsetValueQueryParam() {
        defaultQueryParams.put(StringConstants.OFFSET_STRING, INVALID_OFFSET);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.OFFSET_STRING)));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidLimitValueQueryParam() {
        defaultQueryParams.put(StringConstants.LIMIT_STRING, INVALID_LIMIT);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.LIMIT_STRING)));
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3, 5, 10, 30, 100, 200, 1000})
    public void expectedCountOfBasketballTeamsReturned_when_getRequestAll_and_setLimitQueryParam(int limit) {
        defaultQueryParams.put(StringConstants.LIMIT_STRING, limit);

        var basketballGamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);
        basketballGamesListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Long pageMetaActualLimit = basketballGamesRepo.getMetaData().getResult().getLimit();
        long actualCountOfGames = basketballGamesListResponse.getResult().size();

        Assertions.assertEquals(limit, pageMetaActualLimit, AssertMessages.responseNotContains(StringConstants.LIMIT_STRING));
        Assertions.assertEquals(limit, actualCountOfGames, AssertMessages.responseNotContains("basketball games count"));
    }

    @ParameterizedTest
    @ValueSource(strings = {StringConstants.ASC_UPPERCASE_STRING, StringConstants.DESC_UPPERCASE_STRING})
    public void basketballGamesReturned_when_getRequestAll_and_setSortOrder(String sortOrder) {
        defaultQueryParams.put(StringConstants.SORT_ORDER_STRING, sortOrder);

        var gamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);
        gamesListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<GameModel> games = gamesListResponse.getResult();
        Assertions.assertFalse(games.isEmpty(), AssertMessages.responseNotContains("Games objects list"));

        assertSortOrderByGameTimeProperty(sortOrder, games);
    }

    @Test
    public void basketballGamesReturnedInDescOrder_when_getRequestAll_and_setNoSortOrderQueryParam() {
        var gamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);
        gamesListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<GameModel> games = gamesListResponse.getResult();
        Assertions.assertFalse(games.isEmpty(), AssertMessages.responseNotContains("Games objects list"));

        assertSortOrderByGameTimeProperty(StringConstants.DESC_UPPERCASE_STRING, games);
    }

    @ParameterizedTest
    @ValueSource(strings = {StringConstants.ASCENDING_STRING, StringConstants.DESC_STRING})
    public void errorMessageReturned_when_getRequestAll_and_setSortOrderQueryParamValueLowerCased(String sortOrder) {
        defaultQueryParams.put(StringConstants.SORT_ORDER_STRING, sortOrder);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.failedToParseParameter(StringConstants.SORT_ORDER_STRING)));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setCompetitionsId() {
        var competitionsListResponse = basketballCompetitionsRepo.getAll(defaultQueryParams).getResult();
        defaultQueryParams.put(StringConstants.COMPETITION_IDS_STRING, List.of(competitionsListResponse.get(0).getId()));

        var gamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(competitionsListResponse.get(0), gamesListResponse.getResult().get(0).getSeason().getCompetition(), AssertMessages.responseNotContains("Season Competition"));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setInvalidCompetitionIds() {
        String invalidCompetitionId = String.valueOf(UUID.randomUUID());

        defaultQueryParams.put(StringConstants.COMPETITION_IDS_STRING, List.of(invalidCompetitionId));

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Competition with ID %s does not exist!".formatted(invalidCompetitionId)));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setSeasonId() {
        var selectedSeasonIndex = 0;
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams).getResult();
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("season_ids", List.of(seasonListResponse.get(0).getId()));

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(seasonListResponse.get(selectedSeasonIndex).getId(), gamesListResponse.getResult().get(selectedSeasonIndex).getSeason().getId(), AssertMessages.responseNotContains("Season"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll() {
        var gamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Players objects list"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestById() {
        var gamesListResponse = basketballGamesRepo.getAll(defaultQueryParams);
        var firstGame = gamesListResponse.getResult().get(0);

        var gameResponse = basketballGamesRepo.getById(firstGame.getId());

        Assertions.assertEquals(HttpStatus.SC_OK, gameResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(firstGame, gameResponse.getResult(), AssertMessages.responseNotContains("First Player object"));
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_removeQueryParameters() {
        var gamesListResponse = basketballGamesRepo.getAll();

        Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_BAD_REQUEST));
        Assertions.assertTrue(gamesListResponse.getResponse().getBody().print().contains("Required request parameter 'offset' for method parameter type Integer is not present"), AssertMessages.responseNotExpected());
    }

    @Test
    public void errorMessageReturned_when_getRequestAll_and_setUnexpectedSortOrder() {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("sort_order", "test");

        basketballGamesRepo.getAll(customQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("Failed to parse parameter sort_order"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setStageId() {
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams).getResult();
        var seasonDetailsResponse = basketballSeasonsRepo.getSeasonsDetails(seasonListResponse.get(0).getId()).getResult();
        var stageId = seasonDetailsResponse.getStages().get(0).getId();

        var customQueryParams = defaultQueryParams;
        customQueryParams.put("stage_ids", List.of(stageId));

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(stageId, gamesListResponse.getResult().get(0).getStage().getId(), AssertMessages.responseNotContains("Stage"));
    }

    //TODO: Add test for LIVE status on the static database
    @ParameterizedTest
    @ValueSource(strings = {"NOT_STARTED", "FINISHED", "CANCELLED", "INTERRUPTED", "POSTPONED"})
    public void basketballGamesReturned_when_getRequestAll_and_setStatusType(String statusType) {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("status_type", List.of(statusType));

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games objects list"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setFromGameTime() {
        var dateTimeFormatter = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'");
        var timeFrom = LocalDateTime.now().format(dateTimeFormatter);
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("from_game_time", timeFrom);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games objects list"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setToGameTime() {
        var dateTimeFormatter = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'");
        var timeTo = LocalDateTime.now().plusMonths(1).format(dateTimeFormatter);
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("to_game_time", timeTo);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games objects list"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setRoundFilter() {
        var seasonListResponse = basketballSeasonsRepo.getAll(defaultQueryParams).getResult();
        var seasonDetailsResponse = basketballSeasonsRepo.getSeasonsDetails(seasonListResponse.get(0).getId()).getResult();
        var stageId = seasonDetailsResponse.getStages().get(0).getId();
        var roundId = seasonDetailsResponse.getStages().get(0).getRounds().get(0).getId();

        var customQueryParams = defaultQueryParams;
        customQueryParams.put("round_filter", stageId + ":" + roundId);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(stageId, gamesListResponse.getResult().get(0).getStage().getId(), AssertMessages.responseNotContains("Stage"));
        Assertions.assertEquals(roundId, gamesListResponse.getResult().get(0).getRound().getId(), AssertMessages.responseNotContains("Round"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setTeamId() {
        var searchResponse = searchV2SuggestRepo.searchEntities(BasketballTeamEnum.CHICAGO_BULLS.getName(), SupportedSports.BASKETBALL, SportEntityEnum.TEAM);
        var selectedTeamId = searchResponse.getResult().get(0).getId();

        var customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.TEAM_IDS_UNDERSCORED_STRING, selectedTeamId);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertEquals(selectedTeamId, gamesListResponse.getResult().get(0).getTeam(selectedTeamId).getId(), AssertMessages.responseNotContains("Games objects list"));
    }

    @Test
    //@Issue("SBE-3012")
    public void basketballGamesReturned_when_getRequestAll_and_setUnexpectedCompetitionList() {
        String competitionList = "sportalios1";
        Map<String, Object> customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionList);

        basketballGamesRepo.getAll(customQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo("%s competition_list does not exist or doesn't contain any competitions".formatted(competitionList)));
    }

    @Test
    @Disabled("All odds are null")
    public void basketballGamesReturned_when_getRequestAll_and_setOddClient() {
        var oddClient = "sportal";
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("odd_client", "sportal");

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertTrue(gamesListResponse.getResult().stream().anyMatch(e -> e.getOdds().contains(oddClient)), AssertMessages.entityNotExpected("Odd client", oddClient, ""));
    }

    @ParameterizedTest
    @ValueSource(strings = {"LIVE", "PRE_EVENT", "ALL"})
    public void basketballGamesReturned_when_getRequestAll_and_setOddType(String oddType) {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("odd_type", oddType);
        customQueryParams.put("odd_client", "sportal");

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games objects list"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"12", "1x2", "OVER_UNDER", "DOUBLE_CHANCE", "BOTH_TO_SCORE", "DRAW_NO_BET", "FIRST_TEAM_TO_SCORE"})
    public void basketballGamesReturned_when_getRequestAll_and_setMarketType(String marketType) {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("odd_client", "sportal");
        customQueryParams.put("market_types", List.of(marketType));

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games list"));
    }

    @Test
    @Issue("OFA-208")
    public void errorMessageReturned_when_getRequestAll_and_setMarketTypeGameHandicap() {
        String expectedMarketType = "GAME_HANDICAP";

        defaultQueryParams.put("odd_client", "sportal");
        defaultQueryParams.put("market_types", expectedMarketType);

        basketballGamesRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo("Invalid market_type: %s".formatted(expectedMarketType)));
    }

    @Test
    @Issue("SBE-3012")
    public void errorMessageReturned_when_getRequestAll_and_setMarketTypeWithoutOddClient() {
        var marketType = "12";
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("market_types", List.of(marketType));

        basketballGamesRepo.getAll(customQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("Odds can only be requested with an odd client specified"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"ORDINARY_TIME", "FULL_TIME"})
    public void basketballGamesReturned_when_getRequestAll_and_setScopeType(String scopeType) {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("odd_client", "sportal");
        customQueryParams.put("scope_type", scopeType);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games list"));
    }

    @Test
    //@Issue("SBE-3012")
    public void errorMessageReturned_when_getRequestAll_and_setScopeTypeWithoutOddClient() {
        Map<String, Object> customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());

        basketballGamesRepo.getAll(customQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.containsString("Odds can only be requested with an odd client specified"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"FRACTIONAL", "DECIMAL", "MONEYLINE"})
    public void basketballGamesReturned_when_getRequestAll_and_setOddFormat(String oddFormat) {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("odd_client", "sportal");
        customQueryParams.put("odd_format", oddFormat);

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertFalse(gamesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Games list"));

    }

    @Test
    //@Issue("SBE-3012")
    public void errorMessageReturned_when_getRequestAll_and_setOddFormatWithoutOddClient() {
        Map<String, Object> customQueryParams = defaultQueryParams;
        customQueryParams.put(StringConstants.ODD_FORMAT_STRING, DataOddsMarketValueTypeEnum.FRACTIONAL.name());

        basketballGamesRepo.getAll(customQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.containsString("Odds can only be requested with an odd client specified"));
    }

    @Test
    public void basketballGamesReturned_when_getRequestAll_and_setTranslationLanguage() {
        var customQueryParams = defaultQueryParams;
        customQueryParams.put("translation_language", "bg");

        var gamesListResponse = basketballGamesRepo.getAll(customQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, gamesListResponse.getResponse().getStatusCode(), AssertMessages.statusCodeNotExpected(HttpStatus.SC_OK));
        Assertions.assertTrue(gamesListResponse.getResult().stream().anyMatch(e -> e.getStatus().getName().contains("Не е започнал")), AssertMessages.responseNotContains("Game status is not translated"));
    }

    private void assertSortOrderByGameTimeProperty(String sortOrder, List<GameModel> games) {
        if (sortOrder.equalsIgnoreCase(StringConstants.ASCENDING_STRING)) {
            List<GameModel> gamesSortedByGameTimeAscending = games.stream().sorted(Comparator.comparing(GameModel::getGameTime)).toList();
            Assertions.assertEquals(gamesSortedByGameTimeAscending, games, AssertMessages.responseNotContains("Games sorted by game time"));
        } else {
            List<GameModel> gamesSortedByGameTimeDescending = games.stream().sorted(Comparator.comparing(GameModel::getGameTime).reversed()).toList();
            Assertions.assertEquals(gamesSortedByGameTimeDescending, games, AssertMessages.responseNotContains("Games sorted by game time"));
        }
    }
}