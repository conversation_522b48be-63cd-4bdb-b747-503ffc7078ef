package searchapiV2;

import categories.APITags;
import categories.SMPCategories;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.searchapi.ResultModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2SuggestTests extends BaseSearchApiV2Tests {

    private static final List<SupportedSports> SPORTS = List.of(
            SupportedSports.FOOTBALL, SupportedSports.BASKETBALL, SupportedSports.TENNIS, SupportedSports.ICE_HOCKEY);

    @ParameterizedTest
    @MethodSource("invalidEventDatePerSport")
    public void errorMessageReturned_when_getSuggestRequest_and_setInvalidEventStartTimeValueQueryParam(SupportedSports sport, String eventStartTime) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.EVENT_START_TIME_STRING, eventStartTime);

        searchV2SuggestRepo.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid `%s` format".formatted(StringConstants.EVENT_START_TIME_STRING)));
    }

    @ParameterizedTest
    @MethodSource("tenYearInFutureEventDatePerSport")
    public void noResultsReturned_when_getSuggestRequest_and_setEventStartTimeQueryParam_valueTenYearInFuture(SupportedSports sport, String eventStarTime) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.EVENT_START_TIME_STRING, eventStarTime);

        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<ResultModel> suggestListResult = suggestListResponse.getResult();
        Assertions.assertTrue(suggestListResult.isEmpty(), "No results should be returned for the future date - ten year in future");
    }

    @ParameterizedTest
    @MethodSource("pastEventDatePerSport")
    public void eventsReturned_when_getSuggestRequest_and_setEventStartTimeQueryParam_valueInPast(SupportedSports sport, String eventStartTIme) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.EVENT_START_TIME_STRING, eventStartTIme);

        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<ResultModel> suggestListResult = suggestListResponse.getResult();
        Assertions.assertFalse(suggestListResult.isEmpty(), "Events should be returned for the past date where events are available");
    }

    private static Stream<Object[]> invalidEventDatePerSport() {
        List<String> eventStartTime = List.of("2024-13-24", "2024-09-2418:45:00Z", "2024-13-24T18:45:00Z",
                "2024-01-32T18:45:00Z", "2023-02-29T18:45:00Z", "2023-02-29", "2024-09-2418:45:00");

        return SPORTS.stream().flatMap(platform ->
                eventStartTime.stream().map(url -> new Object[]{platform, url})
        );
    }

    private static Stream<Object[]> tenYearInFutureEventDatePerSport() {
        int nextYear = LocalDate.now().getYear() + 10;
        String dateTenYearInFuture = "%s-12-29".formatted(nextYear);
        List<String> eventStartTime = List.of(dateTenYearInFuture, "%sT18:45:00Z".formatted(dateTenYearInFuture));

        return SPORTS.stream().flatMap(platform ->
                eventStartTime.stream().map(url -> new Object[]{platform, url})
        );
    }

    private static Stream<Object[]> pastEventDatePerSport() {
        List<String> eventStartTime = List.of("2024-09-24", "2024-09-24T18:45:00Z");

        return SPORTS.stream().flatMap(platform ->
                eventStartTime.stream().map(url -> new Object[]{platform, url})
        );
    }
}