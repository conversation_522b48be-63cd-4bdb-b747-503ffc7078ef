package searchapiV2;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.SupportedSports;
import data.models.clientapi.ProjectModel;
import data.models.clientapi.SportModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.client.ClientProjectsHttpRepository;
import repositories.client.ClientSportsProjectsHttpRepository;
import repositories.searchapiV2.SearchV2SportsHttpRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2SportsTests extends BaseSearchApiV2Tests {

    @ParameterizedTest
    @EnumSource(value = Project.class, names = {"FULLSETUP", "FULLSETUP_API"}, mode = EnumSource.Mode.INCLUDE)
    public void expectedSportsReturned_when_checkSportsPerProject(Project project) {
        ClientProjectsHttpRepository clientProjectsHttpRepository = new ClientProjectsHttpRepository(Project.NONE);
        ClientSportsProjectsHttpRepository clientSportsProjectsHttpRepository = new ClientSportsProjectsHttpRepository(Project.NONE);
        searchV2SportsHttpRepository = new SearchV2SportsHttpRepository(project);

        ProjectModel result = clientProjectsHttpRepository.getById(project.getDomain()).getResult();
        String sportProject = result.getConfiguration().getSportProject();

        Set<String> expectedAvailableSports = clientSportsProjectsHttpRepository.getById(sportProject).getResult().getSports()
                .stream()
                .map(SportModel::getSport)
                .collect(Collectors.toSet());

        var searchSportsResponse = searchV2SportsHttpRepository.getAll();
        searchSportsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Set<String> actualAvailableSportsForProject = searchSportsResponse.getResult()
                .stream()
                .map(data.models.searchapi.sports.SportModel::getSport)
                .collect(Collectors.toSet());

        Assertions.assertEquals(expectedAvailableSports, actualAvailableSportsForProject, "Available sports for project are not as expected");
    }

    @Test
    public void sportsReturned_when_getSportsRequest() {
        var sportsListResponse = searchV2SportsHttpRepository.getAll();

        Assertions.assertEquals(HttpStatus.SC_OK, sportsListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(sportsListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Sport object"));
    }

    @Test
    public void validSupportedSportsReturned_when_getSportsRequest_and_checkSportsValues() {
        var sportsListResponse = searchV2SportsHttpRepository.getAll();

        Assertions.assertEquals(HttpStatus.SC_OK, sportsListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        List<SupportedSports> expectedSports = Arrays.stream(SupportedSports.values()).filter(e -> !e.equals(SupportedSports.INVALID)).toList();
        List<data.models.searchapi.sports.SportModel> actualSports = sportsListResponse.getResult();

        Assertions.assertEquals(expectedSports.size(), actualSports.size(), "Number of sports returned is not as expected");
        assertSupportedSports(expectedSports, actualSports);

    }

    private void assertSupportedSports(List<SupportedSports> expectedSports, List<data.models.searchapi.sports.SportModel> actualSports) {
        for (int i = 0; i < expectedSports.size(); i++) {
            Assertions.assertEquals(expectedSports.get(i).getValue(), actualSports.get(i).getSport(), "Sport is not correct");
            Assertions.assertEquals(expectedSports.get(i).getTitle(), actualSports.get(i).getTitle(), "Sport title is not correct");
        }
    }
}
