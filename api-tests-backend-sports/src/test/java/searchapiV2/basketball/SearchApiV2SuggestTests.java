package searchapiV2.basketball;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.EntityTypeEnum;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.basketball.BasketballTeamEnum;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import searchapiV2.BaseSearchApiV2Tests;

import java.util.List;

import static data.constants.MultiSportSportsEnum.BASKETBALL;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2SuggestTests extends BaseSearchApiV2Tests {

    @Test
    public void searchResultsReturned_when_getSuggestRequest() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setNameQueryParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.SPORT_STRING, BASKETBALL.getValue());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Name object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getName().contains(BasketballTeamEnum.LA_LAKERS.getName())),
                AssertMessages.responseNotContains("Name is not correct"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setEntityTypeParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.ENTITY_TYPE_STRING, StringConstants.TEAM_STRING);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertEquals(StringConstants.TEAM_STRING, suggestListResponse.getResult().get(0).getEntityType(),
                AssertMessages.responseNotContains("Entity type object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setIdsParameter() {
        var name = basketballTeamsRepo.getAll(defaultQueryParams).getResult().get(0).getName();
        var teamId = basketballTeamsRepo.getAll(defaultQueryParams).getResult().get(0).getId();

        queryParams.put(StringConstants.NAME_STRING, name);
        queryParams.put(StringConstants.IDS_STRING, teamId);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setSportParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.SPORT_STRING, BASKETBALL.getValue());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertEquals(BASKETBALL.getValue(), suggestListResponse.getResult().get(0).getSport(),
                AssertMessages.responseNotContains("Sport object"));
    }

//    @Issue("SBE-3261")
    @Test
    public void competitionIdsPresent_when_getSuggestRequest_and_checkEntityTypeTeam() {
        preQueryParams.put(StringConstants.SPORT_STRING, BASKETBALL.getValue());
        preQueryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        var basketballTeamResult =
                searchV2SuggestRepo.getAll(preQueryParams).getResult().stream().findAny().orElseThrow(() -> new RuntimeException("No competition found for team."));
        var basketballCompetitionId = basketballTeamResult.getCompetitionIds() != null ? basketballTeamResult.getCompetitionIds().stream().findFirst().orElse(null) : null;

        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, basketballCompetitionId);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<String> competitionIds = suggestListResponse.getResult().stream()
                .filter(e -> e.getEntityType().equals(EntityTypeEnum.TEAM.name().toLowerCase()))
                .findFirst()
                .orElseGet(() -> Assertions.fail("No team entity type found in the response"))
                .getCompetitionIds();

        Assertions.assertFalse(competitionIds.isEmpty(), AssertMessages.responseNotContains("Competition ids"));
        Assertions.assertTrue(competitionIds.contains(basketballCompetitionId), AssertMessages.responseNotContains("Competition id"));
    }

    @Test
    public void competitionIdPresent_when_getSuggestRequest_and_checkEntityTypeGame() {
        preQueryParams.put(StringConstants.SPORT_STRING, BASKETBALL.getValue());
        preQueryParams.put(StringConstants.NAME_STRING, BasketballCompetitionName.NBA.getName());
        var expectedCompetitionId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getId();

        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, expectedCompetitionId);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        String actualCompetitionId = suggestListResponse.getResult().stream()
                .filter(e -> e.getEntityType().equals(EntityTypeEnum.GAME.name().toLowerCase()))
                .findFirst()
                .orElseGet(() -> Assertions.fail("No game entity type found in the response"))
                .getCompetitionId();

        Assertions.assertEquals(expectedCompetitionId, actualCompetitionId, AssertMessages.responseNotContains("Sport object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setEventStartTimeParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.EVENT_START_TIME_STRING, "2024-01-18");
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getStartTime().contains("2024-01-18")),
                AssertMessages.responseNotContains("The start time is not correct"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setInputLanguageParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getNameBG());
        queryParams.put(StringConstants.INPUT_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Input language object"));
    }

    @Test
    public void searchResultsNotReturned_when_getSuggestRequest_with_enName_and_setInputLanguageBgParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.INPUT_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertTrue(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Input language object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setTranslationLanguageParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.SPORT_STRING, BASKETBALL.getValue());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getName().contains(BasketballTeamEnum.LA_LAKERS.getNameBG())),
                AssertMessages.responseNotContains("The name is not translated"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setLimitParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.LIMIT_STRING, String.valueOf(DEFAULT_LIMIT));
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Limit object"));
        Assertions.assertEquals(DEFAULT_LIMIT, suggestListResponse.getResult().size(),
                AssertMessages.responseNotContains("Number of elements are not correct"));
        Assertions.assertEquals(DEFAULT_LIMIT, searchV2SuggestRepo.getMetaData().getResult().getLimit(),
                AssertMessages.responseNotContains("Limit object"));
    }

    @Test
    //@Issue("SBE-3140")
    public void searchResultsReturned_when_getSuggestRequest_and_setOffsetParameter() {
        queryParams.put(StringConstants.NAME_STRING, BasketballTeamEnum.LA_LAKERS.getName());
        queryParams.put(StringConstants.OFFSET_STRING, String.valueOf(DEFAULT_OFFSET));
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, suggestListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Offset object"));
        Assertions.assertEquals(DEFAULT_OFFSET, searchV2SuggestRepo.getMetaData().getResult().getOffset(),
                AssertMessages.responseNotContains("Offset object"));
    }
}