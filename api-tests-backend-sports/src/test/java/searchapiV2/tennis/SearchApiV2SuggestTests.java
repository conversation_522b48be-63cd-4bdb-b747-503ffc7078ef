package searchapiV2.tennis;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.enums.tennis.TennisCompetitionName;
import data.constants.enums.tennis.TennisPlayerName;
import data.models.tennisapi.DetailsModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import searchapiV2.BaseSearchApiV2Tests;

import static data.constants.MultiSportSportsEnum.TENNIS;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2SuggestTests extends BaseSearchApiV2Tests {

    @Test
    public void searchResultsReturned_when_getSuggestRequest() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setNameQueryParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.SPORT_STRING, TENNIS.getValue());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Name object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getName().contains("Grigor Dimitrov")),
                AssertMessages.responseNotContains("Name is not correct"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setEntityTypeParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.SPORT_STRING, TENNIS.getValue());
        queryParams.put(StringConstants.ENTITY_TYPE_STRING, StringConstants.PLAYER_STRING);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertEquals(StringConstants.PLAYER_STRING, suggestListResponse.getResult().get(0).getEntityType(),
                AssertMessages.responseNotContains("Entity type object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setIdsParameter() {
        var matchesListResponse = tennisMatchesHttpRepo.getAll(defaultQueryParams);
        DetailsModel playerDetails = getPlayer(matchesListResponse.getResult());
        var playerResponse = tennisPlayersHttpRepo.getById(playerDetails.getId());
        var name = playerResponse.getResult().getName();
        var playerId = playerResponse.getResult().getId();

        queryParams.put(StringConstants.NAME_STRING, name);
        queryParams.put(StringConstants.IDS_STRING, playerId);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setSportParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.SPORT_STRING, TENNIS.getValue());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertEquals(TENNIS.getValue(), suggestListResponse.getResult().get(0).getSport(),
                AssertMessages.responseNotContains("Sport object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setCompetitionIdsParameter() {
        var tennisCompetitionId = tennisCompetitionsHttpRepo.getAll(defaultQueryParams).getResult().stream().filter(e ->
                e.getName().equals(TennisCompetitionName.ROLEX_PARIS_MASTERS.getName())).findFirst().orElseThrow().getId();

        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, tennisCompetitionId);
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertEquals(tennisCompetitionId, suggestListResponse.getResult().get(0).getCompetitionId(),
                AssertMessages.responseNotContains("Competition object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setEventStartTimeParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.EVENT_START_TIME_STRING, "2024-01-18");
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getStartTime().contains("2024-01-18")),
                AssertMessages.responseNotContains("The start time is not correct"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setInputLanguageParameter() {
        preQueryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        String translatedName = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getTranslations().stream()
                .filter(e -> e.getLanguage().equals(Language.BULGARIAN.getCode()))
                .findFirst()
                .orElseThrow()
                .getName();

        queryParams.put(StringConstants.NAME_STRING, translatedName);
        queryParams.put(StringConstants.INPUT_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Input language object"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setTranslationLanguageParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.SPORT_STRING, TENNIS.getValue());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.BULGARIAN.getCode());
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Search object"));
        Assertions.assertTrue(suggestListResponse.getResult().stream().anyMatch(e -> e.getName().contains("Григор Димитров")),
                AssertMessages.responseNotContains("The name is not translated"));
    }

    @Test
    public void searchResultsReturned_when_getSuggestRequest_and_setLimitParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.LIMIT_STRING, String.valueOf(DEFAULT_LIMIT));
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Limit object"));
        Assertions.assertEquals(DEFAULT_LIMIT, suggestListResponse.getResult().size(),
                AssertMessages.responseNotContains("Number of elements are not correct"));
        Assertions.assertEquals(DEFAULT_LIMIT, searchV2SuggestRepo.getMetaData().getResult().getLimit(),
                AssertMessages.responseNotContains("Limit object"));
    }

    @Test
//    @Issue("SBE-3140")
    public void searchResultsReturned_when_getSuggestRequest_and_setOffsetParameter() {
        queryParams.put(StringConstants.NAME_STRING, TennisPlayerName.GRIGOR_DIMITROV.getName());
        queryParams.put(StringConstants.OFFSET_STRING, String.valueOf(DEFAULT_OFFSET));
        var suggestListResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertFalse(suggestListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Offset object"));
        Assertions.assertEquals(DEFAULT_OFFSET, searchV2SuggestRepo.getMetaData().getResult().getOffset(),
                AssertMessages.responseNotContains("Offset object"));
    }
}