package searchapiV2.sportentities;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.SupportedSports;
import data.constants.enums.SportEntityEnum;
import data.models.searchapi.ResultModel;
import data.utils.StringUtils;
import factories.searchapiV2.SportEntityHttpFactory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;
import java.util.UUID;

import static data.constants.StringConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2HorsesTests extends BaseSearchApiV2SportEntitiesTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        sportEntityRequestBody = SportEntityHttpFactory.buildHorseMandatoryProperties(SupportedSports.HORSE_RACING);
    }

    @Test
    public void horseCreated_when_createHorse_and_setOnlyMandatoryProperties() {
        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_OK)
                .body(MESSAGE_STRING, equalTo(SUCCESS_STRING));

        ResultModel actualCreatedHorse = searchV2SuggestRepo.getEntityByName(sportEntityRequestBody.getName(), SportEntityEnum.HORSE);
        sportEntityRequestBody.setTranslations(List.of());

        assertSportEntity(sportEntityRequestBody, actualCreatedHorse, SportEntityEnum.HORSE);
    }

    @Test
    public void horseCreated_when_createHorse_and_setAllProperties() {
        sportEntityRequestBody = SportEntityHttpFactory.buildHorseAllProperties(SupportedSports.HORSE_RACING);

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_OK)
                .body(MESSAGE_STRING, equalTo(SUCCESS_STRING));

        ResultModel actualCreatedHorse = searchV2SuggestRepo.getEntityByName(sportEntityRequestBody.getName(), SportEntityEnum.HORSE);
        sportEntityRequestBody.setCountryId(actualCreatedHorse.getCountryId());
        sportEntityRequestBody.getCountry().setEntityType(actualCreatedHorse.getCountry().getEntityType());
        sportEntityRequestBody.getCountry().setSlug(actualCreatedHorse.getCountry().getSlug());

        assertSportEntity(sportEntityRequestBody, actualCreatedHorse, SportEntityEnum.HORSE);
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setEmptyRequestBody() {
        sportEntityRequestBody = SportEntityHttpFactory.buildEmptyRequestBody();

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldContainsInvalidUUID(ID_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setNoId() {
        sportEntityRequestBody.setId(null);

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldContainsInvalidUUID(ID_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setInvalidId() {
        sportEntityRequestBody.setId(sportEntityRequestBody.getId() + StringUtils.generateRandomString(3));

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldContainsInvalidUUID(ID_STRING)));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setNoName() {
        sportEntityRequestBody.setName(null);

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(NAME_STRING)));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setNoSport() {
        sportEntityRequestBody.setSport(null);

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("This entity cannot be submitted without a valid sport."));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void errorMessageReturned_when_createHorse_and_setInvalidSport() {
        sportEntityRequestBody.setSport(SupportedSports.INVALID.getValue());

        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(INVALID_VALUE_FOR_SPORT_SUBMITTED));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void horseDeleted_when_deleteHorse() {
        searchV2HorseRepo.create(sportEntityRequestBody).getResponse().then().statusCode(SC_OK);
        queryParams.put(ID_STRING, sportEntityRequestBody.getId());

        searchV2HorseRepo.delete(queryParams)
                .then()
                .statusCode(SC_OK)
                .body(MESSAGE_STRING, equalTo(SUCCESS_STRING));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void errorMessageReturned_when_deleteNotExistingHorse() {
        String randomUUID = UUID.randomUUID().toString();
        queryParams.put(ID_STRING, randomUUID);

        searchV2HorseRepo.delete(queryParams)
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING,
                        equalTo(AssertMessages.entityNotExistForProject(randomUUID)));

        sportEntityRequestBody.setId(null);
    }

    @Test
    public void horseUpdated_when_updateAllPropertiesWithMandatoryProperties() {
        sportEntityRequestBody = SportEntityHttpFactory.buildHorseAllProperties(SupportedSports.HORSE_RACING);
        searchV2HorseRepo.create(sportEntityRequestBody);

        sportEntityRequestBody = SportEntityHttpFactory.buildUpdateSportEntityRequestBodyMandatoryProperties(sportEntityRequestBody);
        searchV2HorseRepo.create(sportEntityRequestBody).getResponse()
                .then()
                .statusCode(SC_OK)
                .body(MESSAGE_STRING, equalTo(SUCCESS_STRING));

        ResultModel actualUpdatedHorse = searchV2SuggestRepo.getEntityByName(sportEntityRequestBody.getName(), SportEntityEnum.HORSE);
        sportEntityRequestBody.setTranslations(List.of());

        assertSportEntity(sportEntityRequestBody, actualUpdatedHorse, SportEntityEnum.HORSE);
    }
}
