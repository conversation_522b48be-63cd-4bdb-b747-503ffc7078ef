package searchapiV2.customentities;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.enums.CustomEntityEnum;
import data.models.searchapi.*;
import factories.searchapiV2.CustomEntityHttpFactory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

import java.util.List;
import java.util.UUID;

import static data.constants.StringConstants.*;
import static org.hamcrest.Matchers.equalTo;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2PeopleTests extends BaseSearchApiV2CustomEntitiesTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        customEntityRequestBody = CustomEntityHttpFactory.buildPersonMandatoryPropertiesRequestBody(DOMAIN_ID.get());
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setEmptyRequestBody() {
        customEntityRequestBody = CustomEntityHttpFactory.buildEmptyRequestBody();

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setNoName() {
        customEntityRequestBody.setName(null);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(NAME_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setNoDefaultLanguage() {
        customEntityRequestBody.setDefaultLanguage(null);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(DEFAULT_LANGUAGE_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setNoSlug() {
        customEntityRequestBody.setSlug(null);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setEmptySlug() {
        customEntityRequestBody.setSlug(EMPTY_STRING);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidSlugs")
    public void errorMessageReturned_when_createPerson_and_setInvalidSlug(String slug) {
        customEntityRequestBody.setSlug(slug);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldIsInvalid(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setNoContainedInDomain() {
        customEntityRequestBody.setContainedInDomain(null);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(CONTAINED_IN_DOMAIN_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setNotExistingDomain() {
        customEntityRequestBody.setContainedInDomain(UUID.randomUUID().toString());

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.domainDoesNotExistFor(CustomEntityEnum.PERSON)));
    }

    @ParameterizedTest
    @MethodSource("invalidDates")
    public void errorMessageReturned_when_createPerson_and_setInvalidBirthDate(String birthDate) {
        customEntityRequestBody.setBirthdate(birthDate);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldIsInvalid("birthdate")));
    }

    @ParameterizedTest
    @ValueSource(strings = {"M", "F", "O", "GENDER", "MAL", "FEM", "OTH", "UNKNOWN", "UNSPECIFIED", EMPTY_STRING})
    public void errorMessageReturned_when_createPerson_and_setInvalidGender(String gender) {
        customEntityRequestBody.setGender(gender);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo("Field `gender` is invalid. Valid values are `male`, `female` and `other`."));
    }

    @ParameterizedTest
    @MethodSource("invalidEmails")
    public void errorMessageReturned_when_createPerson_and_setInvalidEmail(String email) {
        customEntityRequestBody.setEmail(email);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldIsInvalid("email")));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPerson_and_setInvalidWebsite(String website) {
        customEntityRequestBody.setWebsite(website);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(WEBSITE_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidUrlsWithPlatforms")
    public void errorMessageReturned_when_createPerson_and_setInvalidSocialMediaLinksUrl(String socialMedia, String socialMediaLinkUrl) {
        customEntityRequestBody.setSocialMediaLinks(List.of(
                SocialMediaLinksModel.builder().
                        slug(socialMedia).
                        value(socialMediaLinkUrl).build()));

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(SOCIAL_MEDIA_LINKS_STRING.formatted(socialMedia))));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPerson_and_setInvalidDisplayAssetUrl(String displayAssetUrl) {
        customEntityRequestBody.setDisplayAsset(DisplayAsset.builder().url(displayAssetUrl).build());

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(DISPLAY_ASSET_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPerson_and_setInvalidIconUrl(String iconUrl) {
        customEntityRequestBody.setIcon(IconModel.builder().url(iconUrl).build());

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(ICON_STRING)));
    }

    @ParameterizedTest
    @ValueSource(strings = {"170", "3.1", "0.4", "0.49", "-0.5", "-3.0"})
    public void errorMessageReturned_when_createPerson_and_setInvalidHeight(String height) {
        customEntityRequestBody.setHeight(height);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Field `height` must be between 0.5 and 3.0. Example: 1.75."));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setEmptyHeight() {
        customEntityRequestBody.setHeight(EMPTY_STRING);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Field `height` must be a valid number. Example: 1.75."));
    }

    @ParameterizedTest
    @ValueSource(strings = {"19", "1001"})
    public void errorMessageReturned_when_createPerson_and_setInvalidWeight(String weight) {
        customEntityRequestBody.setWeight(weight);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Field `weight` must be between 20 and 1000. Example: 75."));
    }

    @Test
    public void errorMessageReturned_when_createPerson_and_setEmptyWeight() {
        customEntityRequestBody.setWeight(EMPTY_STRING);

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Field `weight` must be a valid integer. Example: 75."));
    }

    @Test
    //@Issue("SBE-3357")
    public void personCreated_when_createPerson_and_setOnlyMandatoryProperties() {
        var createPersonResponse = searchV2PersonRepo.create(customEntityRequestBody);
        createPersonResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CREATED_PERSON.set(createPersonResponse.getResult());
        ContainedInDomainModel containedInDomain = (ContainedInDomainModel)CREATED_PERSON.get().getContainedInDomain();

        Assertions.assertEquals(customEntityRequestBody.getName(), CREATED_PERSON.get().getName(), AssertMessages.entityNotExpected(NAME_STRING));
        Assertions.assertEquals(customEntityRequestBody.getDefaultLanguage(), CREATED_PERSON.get().getDefaultLanguage(),
                AssertMessages.entityNotExpected(DEFAULT_LANGUAGE_STRING));
        Assertions.assertEquals(customEntityRequestBody.getSlug(), CREATED_PERSON.get().getSlug(), AssertMessages.entityNotExpected(SLUG_STRING));
        Assertions.assertEquals(DOMAIN_ID.get(), containedInDomain.getId(), AssertMessages.entityNotExpected(CONTAINED_IN_DOMAIN_STRING));

        CustomEntityModel actualPerson = searchV2PersonRepo.getById(CREATED_PERSON.get().getId()).getResult();

        Assertions.assertEquals(CREATED_PERSON.get(), actualPerson, AssertMessages.entityNotExpected(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    public void personCreated_when_createPerson_and_setAllProperties() {
        CREATED_PLACE.set(searchV2PlaceRepo.create(CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get())).getResult());
        CREATED_ORGANIZATION.set(searchV2OrganizationRepo.create(CustomEntityHttpFactory.buildOrganizationMandatoryPropertiesRequestBody(DOMAIN_ID.get())).getResult());
        CREATED_ROLE.set(searchV2RoleRepo.create(CustomEntityHttpFactory.buildRoleMandatoryPropertiesRequestBody()).getResult());
        customEntityRequestBody = CustomEntityHttpFactory.buildPersonAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get(), CREATED_ORGANIZATION.get(), CREATED_ROLE.get());

        var createPersonResponse = searchV2PersonRepo.create(customEntityRequestBody);
        createPersonResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        CREATED_PERSON.set(createPersonResponse.getResult());

        CustomEntityModel actualPerson = searchV2PersonRepo.getById(CREATED_PERSON.get().getId()).getResult();
        Assertions.assertEquals(CREATED_PERSON.get(), actualPerson, AssertMessages.entityNotExpected(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    public void errorMessageReturned_when_createPersonWithAlreadyExistingSlug() {
        CREATED_PERSON.set(searchV2PersonRepo.create(customEntityRequestBody).getResult());

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(ENTITY_WITH_SLUG_ALREADY_EXIST.formatted(customEntityRequestBody.getSlug())));
    }

    @Test
    //@Issue("SBE-3357")
    public void personReturned_when_getPersonById() {
        CREATED_PERSON.set(searchV2PersonRepo.create(customEntityRequestBody).getResult());

        var personResponse = searchV2PersonRepo.getById(CREATED_PERSON.get().getId());
        personResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel actualCreatedPerson = personResponse.getResult();

        Assertions.assertEquals(CREATED_PERSON.get(), actualCreatedPerson,
                AssertMessages.responseNotContains(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    //@Issue("SBE-3357")
    public void personReturned_when_getPersonBySlug() {
        CREATED_PERSON.set(searchV2PersonRepo.create(customEntityRequestBody).getResult());

        var personResponse = searchV2PersonRepo.getById(CREATED_PERSON.get().getSlug());
        personResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel actualCreatedPerson = personResponse.getResult();
        Assertions.assertEquals(CREATED_PERSON.get(), actualCreatedPerson,
                AssertMessages.responseNotContains(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    public void errorMessageReturned_when_getPersonByNotExistingId() {
        String uuid = UUID.randomUUID().toString();
        searchV2PersonRepo.getById(uuid).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo(ENTITY_WITH_ID_OR_SLUG_NOT_EXIST.formatted(CustomEntityEnum.PERSON.getValue(), uuid)));
    }

    @Test
    public void personDeleted_when_deletePerson() {
        CREATED_PERSON.set(searchV2PersonRepo.create(customEntityRequestBody).getResult());

        searchV2PersonRepo.delete(CREATED_PERSON.get().getId()).then()
                .statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, equalTo("Person with an ID of %s is deleted!".formatted(CREATED_PERSON.get().getId())));

        searchV2PersonRepo.getById(CREATED_PERSON.get().getId()).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING,
                        equalTo(AssertMessages.entityWithIdOrSlugDoesNotExist(CustomEntityEnum.PERSON.getValue(), CREATED_PERSON.get().getId())));
    }

    @Test
    public void errorMessageReturned_when_deletePerson_and_setNotExistingId() {
        String uuid = UUID.randomUUID().toString();
        searchV2PersonRepo.delete(uuid).then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo(AssertMessages.entityNotExistForProject(uuid)));
    }

    @Test
    //@Issue("SBE-3357")
    public void personUpdated_when_updateAllPropertiesWithMandatoryProperties() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPersonAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE_PRECONDITION.get(),
                CREATED_ORGANIZATION_PRECONDITION.get(), CREATED_ROLE.get());
        CREATED_PERSON.set(searchV2PersonRepo.create(customEntityRequestBody).getResult());

        customEntityRequestBody = CustomEntityHttpFactory.buildPersonUpdateRequestBodyMandatoryProperties(CREATED_PERSON.get());
        var updatedPersonResponse = searchV2PersonRepo.create(customEntityRequestBody);
        updatedPersonResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        CustomEntityModel updatedPerson = updatedPersonResponse.getResult();

        CustomEntityModel actualUpdatedPerson = searchV2PersonRepo.getById(updatedPerson.getId()).getResult();
        Assertions.assertEquals(updatedPerson, actualUpdatedPerson, AssertMessages.entityNotExpected(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    public void errorMessageReturned_when_updatePerson_with_alreadyUsedSlug() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPersonMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        CustomEntityModel createdPerson = searchV2PersonRepo.create(customEntityRequestBody).getResult();

        customEntityRequestBody = CustomEntityHttpFactory.buildPersonUpdateRequestBodyMandatoryProperties(CREATED_PERSON.get());
        customEntityRequestBody.setSlug(createdPerson.getSlug());

        searchV2PersonRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(ENTITY_WITH_SLUG_ALREADY_EXIST.formatted(customEntityRequestBody.getSlug())));
    }

    @Test
    public void personCreated_when_createPerson_and_setUsedSlugForCreatingAnotherPersonButThenSlugUpdated() {
        String slug = CREATED_PERSON.get().getSlug();

        customEntityRequestBody = CustomEntityHttpFactory.buildPersonUpdateRequestBodyMandatoryProperties(CREATED_PERSON.get());
        customEntityRequestBody.setSlug("%s-%s".formatted(slug, faker.nation().language().toLowerCase()));
        searchV2PersonRepo.create(customEntityRequestBody).getResponse().then().statusCode(HttpStatus.SC_OK);

        customEntityRequestBody = CustomEntityHttpFactory.buildPersonMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        customEntityRequestBody.setSlug(slug);

        var createdPersonResponse = searchV2PersonRepo.create(customEntityRequestBody);
        createdPersonResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel expectedCreatedPerson = createdPersonResponse.getResult();
        ResultModel actualCreatedEntity = searchV2SuggestRepo.getEntityByName(expectedCreatedPerson.getName(), CustomEntityEnum.PERSON);

        EntitiesAsserter.assertAreEqual(expectedCreatedPerson, actualCreatedEntity, CLASS_STRING);
    }
}