package searchapiV2.customentities;

import categories.APITags;
import categories.SMPCategories;
import data.constants.Language;
import data.constants.enums.CustomEntityEnum;
import data.models.searchapi.CustomEntityModel;
import data.models.searchapi.ResultModel;
import data.models.searchapi.Translation;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.StringConstants.*;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2SuggestTests extends BaseSearchApiV2CustomEntitiesTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
    }

    @Test
    public void roleReturned_when_getSuggestRequest_and_setNameQueryParam() {
        queryParams.put(NAME_STRING, CREATED_ROLE.get().getName());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_ROLE.get(), CustomEntityEnum.ROLE, NAME_STRING);
    }

    @Test
    public void rolesReturned_when_getSuggestRequest_and_setRoleEntityTypeQueryParam() {
        queryParams.put(ENTITY_TYPE_STRING, CustomEntityEnum.ROLE.getValue().toLowerCase());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertEntityTypeOfReturnedResults(suggestResponse.getResult(), CustomEntityEnum.ROLE.getValue().toLowerCase());
    }

    @Test
    public void roleReturned_when_getSuggestRequest_and_setIdQueryParam() {
        queryParams.put(IDS_STRING, CREATED_ROLE.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_ROLE.get(), CustomEntityEnum.ROLE, ID_STRING);
    }

    @Test
    public void placeReturned_when_getSuggestRequest_and_setNameQueryParam() {
        queryParams.put(NAME_STRING, CREATED_PLACE.get().getName());
        queryParams.put(LIMIT_STRING, 300);

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_PLACE.get(), CustomEntityEnum.PLACE, NAME_STRING);
    }

    @Test
    public void placesReturned_when_getSuggestRequest_and_setRoleEntityTypeQueryParam() {
        queryParams.put(ENTITY_TYPE_STRING, CustomEntityEnum.PLACE.getValue().toLowerCase());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertEntityTypeOfReturnedResults(suggestResponse.getResult(), CustomEntityEnum.PLACE.getValue().toLowerCase());
    }

    @Test
    public void placeReturned_when_getSuggestRequest_and_setIdQueryParam() {
        queryParams.put(IDS_STRING, CREATED_PLACE.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_PLACE.get(), CustomEntityEnum.PLACE, ID_STRING);
    }

    @Test
    public void personReturned_when_getSuggestRequest_and_setNameQueryParam() {
        queryParams.put(NAME_STRING, CREATED_PERSON.get().getName());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_PERSON.get(), CustomEntityEnum.PERSON, NAME_STRING);
    }

    @Test
    public void personReturned_when_getSuggestRequest_and_setPersonEntityTypeQueryParam() {
        queryParams.put(ENTITY_TYPE_STRING, CustomEntityEnum.PERSON.getValue().toLowerCase());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertEntityTypeOfReturnedResults(suggestResponse.getResult(), CustomEntityEnum.PERSON.getValue().toLowerCase());
    }

    @Test
    public void personReturned_when_getSuggestRequest_and_setIdQueryParam() {
        queryParams.put(IDS_STRING, CREATED_PERSON.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_PERSON.get(), CustomEntityEnum.PERSON, ID_STRING);
    }

    @Test
    public void organizationReturned_when_getSuggestRequest_and_setNameQueryParam() {
        queryParams.put(NAME_STRING, CREATED_ORGANIZATION.get().getName());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_ORGANIZATION.get(), CustomEntityEnum.ORGANIZATION, NAME_STRING);
    }

    @Test
    public void organizationsReturned_when_getSuggestRequest_and_setPersonEntityTypeQueryParam() {
        queryParams.put(ENTITY_TYPE_STRING, CustomEntityEnum.ORGANIZATION.getValue().toLowerCase());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertEntityTypeOfReturnedResults(suggestResponse.getResult(), CustomEntityEnum.ORGANIZATION.getValue().toLowerCase());
    }

    @Test
    public void organizationReturned_when_getSuggestRequest_and_setIdQueryParam() {
        queryParams.put(IDS_STRING, CREATED_ORGANIZATION.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_ORGANIZATION.get(), CustomEntityEnum.ORGANIZATION, ID_STRING);
    }

    @Test
    public void domainReturned_when_getSuggestRequest_and_setNameQueryParam() {
        queryParams.put(NAME_STRING, CREATED_DOMAIN.get().getName());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<ResultModel> suggestResults = suggestResponse.getResult();
        assertCustomEntityFromSuggest(suggestResults, CREATED_DOMAIN.get(), CustomEntityEnum.DOMAIN, NAME_STRING);
    }

    @Test
    public void domainsReturned_when_getSuggestRequest_and_setPersonEntityTypeQueryParam() {
        queryParams.put(ENTITY_TYPE_STRING, CustomEntityEnum.DOMAIN.getValue().toLowerCase());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertEntityTypeOfReturnedResults(suggestResponse.getResult(), CustomEntityEnum.DOMAIN.getValue().toLowerCase());
    }

    @Test
    public void domainReturned_when_getSuggestRequest_and_setIdQueryParam() {
        queryParams.put(IDS_STRING, CREATED_DOMAIN.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        assertCustomEntityFromSuggest(suggestResponse.getResult(), CREATED_DOMAIN.get(), CustomEntityEnum.DOMAIN, ID_STRING);
    }

    @ParameterizedTest
    @MethodSource("customEntityModelProvider")
    public void suggestResultReturned_when_getSuggestRequest_and_setInputLanguageQueryParam(ThreadLocal<CustomEntityModel> customEntity) {
        queryParams.put(INPUT_LANGUAGE_STRING, Language.FRENCH.getCode());
        queryParams.put(IDS_STRING, customEntity.get().getId());

        var suggestResponse = searchV2SuggestRepo.getAll(queryParams);
        suggestResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<Translation> expectedTranslations = customEntity.get().getTranslations();
        List<Translation> actualTranslations = suggestResponse.getResult().get(0).getTranslations();

        assertTranslations(expectedTranslations, actualTranslations);
    }
}