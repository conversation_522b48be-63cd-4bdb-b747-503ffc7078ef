package searchapiV2.customentities;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.enums.CustomEntityEnum;
import data.models.searchapi.*;
import data.utils.StringUtils;
import factories.searchapiV2.CustomEntityHttpFactory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

import java.util.List;
import java.util.UUID;

import static data.constants.StringConstants.*;
import static org.hamcrest.Matchers.equalTo;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.SEARCH_API_V2)
public class SearchApiV2PlacesTests extends BaseSearchApiV2CustomEntitiesTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setEmptyRequestBody() {
        customEntityRequestBody = CustomEntityHttpFactory.buildEmptyRequestBody();

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setNoName() {
        customEntityRequestBody.setName(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(NAME_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setNoDefaultLanguage() {
        customEntityRequestBody.setDefaultLanguage(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(DEFAULT_LANGUAGE_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setNoSlug() {
        customEntityRequestBody.setSlug(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setEmptySlug() {
        customEntityRequestBody.setSlug(EMPTY_STRING);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(SLUG_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidSlugs")
    public void errorMessageReturned_when_createPlace_and_setInvalidSlug(String slug) {
        customEntityRequestBody.setSlug(slug);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldIsInvalid(SLUG_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setNoContainedInDomain() {
        customEntityRequestBody.setContainedInDomain(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(AssertMessages.fieldCannotBeEmpty(CONTAINED_IN_DOMAIN_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setNotExistingDomain() {
        String notExistingDomainId = DOMAIN_ID + StringUtils.generateRandomString(2);
        customEntityRequestBody.setContainedInDomain(notExistingDomainId);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo("The place's domain does not exist."));
    }

    @ParameterizedTest
    @CsvSource(value = {"-91:23", "-90:181", "91:179", "90:181"}, delimiter = ':')
//    @Issue("SBE-3387")
    public void errorMessageReturned_when_createPlace_and_setInvalidGeo(String latitude, String longitude) {
        GeoModel geo = CustomEntityHttpFactory.buildPlaceGeoProperties(latitude, longitude);
        customEntityRequestBody.setGeo(geo);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Latitude and longitude must be within the valid range. " +
                        "Latitude must be between -90 and 90, longitude must be between -180 and 180."));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setTelephone_without_anyFields() {
        customEntityRequestBody.setTelephone(TelephoneModel.builder().build());

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_IN_TELEPHONE_CANNOT_BE_EMPTY.formatted(COUNTRY_CODE_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidTelephoneCountryCodes")
    public void errorMessageReturned_when_createPlace_and_setInvalidCountryCodeForTelephone(String countryCode) {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setCountryCode(countryCode);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo("Field `country_code` in `telephone` is invalid. It should start with a `+`: e.g. +90."));
    }

    @Test
    public void errorMessageReturned_when_createPlace_without_countryCodeForTelephone() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setCountryCode(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_IN_TELEPHONE_CANNOT_BE_EMPTY.formatted("country_code")));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setInvalidNumberForTelephone() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setNumber(">aaa123");

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_NUMBER_IN_TELEPHONE_INVALID));
    }

    @Test
    public void errorMessageReturned_when_createPlace_and_setEmptyNumberForTelephone() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setNumber(EMPTY_STRING);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_IN_TELEPHONE_CANNOT_BE_EMPTY.formatted(NUMBER_STRING)));
    }

    @Test
    public void errorMessageReturned_when_createPlace_without_numberForTelephone() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setNumber(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_IN_TELEPHONE_CANNOT_BE_EMPTY.formatted(NUMBER_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidTelephoneCountryIsoCodes")
    public void errorMessageReturned_when_createPlace_and_setInvalidCountryIsoCodeForTelephone(String countryIsoCode) {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setCountryIsoCode(countryIsoCode);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_COUNTRY_ISO_CODE_IN_TELEPHONE_INVALID));
    }

    @Test
    public void errorMessageReturned_when_createPlace_without_countryIsoCodeForTelephone() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());
        customEntityRequestBody.getTelephone().setCountryIsoCode(null);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_IN_TELEPHONE_CANNOT_BE_EMPTY.formatted(COUNTRY_ISO_CODE_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPlace_and_setInvalidWebsite(String website) {
        customEntityRequestBody.setWebsite(website);

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(WEBSITE_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidUrlsWithPlatforms")
    public void errorMessageReturned_when_createPlace_and_setInvalidSocialMediaLinksUrl(String socialMedia, String socialMediaLinkUrl) {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        customEntityRequestBody.setSocialMediaLinks(List.of(
                SocialMediaLinksModel.builder().
                        slug(socialMedia).
                        value(socialMediaLinkUrl).build()));

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(SOCIAL_MEDIA_LINKS_STRING.formatted(socialMedia))));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPlace_and_setInvalidDisplayAssetUrl(String displayAssetUrl) {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        customEntityRequestBody.setDisplayAsset(DisplayAsset.builder().url(displayAssetUrl).build());

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(DISPLAY_ASSET_STRING)));
    }

    @ParameterizedTest
    @MethodSource("invalidUrls")
    public void errorMessageReturned_when_createPlace_and_setInvalidIconUrl(String iconUrl) {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        customEntityRequestBody.setIcon(IconModel.builder().url(iconUrl).build());

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(FIELD_HAS_AN_INVALID_URL.formatted(ICON_STRING)));
    }

    @Test
    public void placeCreated_when_createPlace_and_setOnlyMandatoryProperties() {
        var createPlaceResponse = searchV2PlaceRepo.create(customEntityRequestBody);
        createPlaceResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CREATED_PLACE.set(createPlaceResponse.getResult());
        ContainedInDomainModel containedInDomain = (ContainedInDomainModel)CREATED_PLACE.get().getContainedInDomain();

        Assertions.assertEquals(customEntityRequestBody.getName(), CREATED_PLACE.get().getName(), AssertMessages.entityNotExpected(NAME_STRING));
        Assertions.assertEquals(customEntityRequestBody.getDefaultLanguage(), CREATED_PLACE.get().getDefaultLanguage(),
                AssertMessages.entityNotExpected(DEFAULT_LANGUAGE_STRING));
        Assertions.assertEquals(customEntityRequestBody.getSlug(), CREATED_PLACE.get().getSlug(), AssertMessages.entityNotExpected(SLUG_STRING));
        Assertions.assertEquals(DOMAIN_ID.get(), containedInDomain.getId(), AssertMessages.entityNotExpected(CONTAINED_IN_DOMAIN_STRING));
    }

    @Test
    //@Issue("SBE-3357")
    public void placeCreated_when_createPlace_and_setAllProperties() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceAllPropertiesRequestBody(DOMAIN_ID.get(), CREATED_PLACE.get().getId());

        var createPlaceResponse = searchV2PlaceRepo.create(customEntityRequestBody);
        createPlaceResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        CREATED_PLACE.set(createPlaceResponse.getResult());

        var createdPlaceById = searchV2PlaceRepo.getById(CREATED_PLACE.get().getId());
        createdPlaceById.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertEquals(CREATED_PLACE.get(), createdPlaceById.getResult(), AssertMessages.entityNotExpected(PLACE_STRING));
    }

    @Test
    public void errorMessageReturned_when_createPlaceWithAlreadyExistingData() {
        CREATED_PLACE.set(searchV2PlaceRepo.create(customEntityRequestBody).getResult());

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(ENTITY_WITH_SLUG_ALREADY_EXIST.formatted(customEntityRequestBody.getSlug())));
    }

    @Test
    //@Issue("SBE-3357")
    public void placeReturned_when_getPlaceById() {
        var placeResponse = searchV2PlaceRepo.getById(CREATED_PLACE.get().getId());
        placeResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel actualCreatedPlace = placeResponse.getResult();
        Assertions.assertEquals(CREATED_PLACE.get(), actualCreatedPlace, AssertMessages.responseNotContains(PLACE_STRING));
    }

    @Test
    //@Issue("SBE-3357")
    public void placeReturned_when_getPlaceBySlug() {
        var placeResponse = searchV2PlaceRepo.getById(CREATED_PLACE.get().getSlug());
        placeResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel actualCreatedPlace = placeResponse.getResult();
        Assertions.assertEquals(CREATED_PLACE.get(), actualCreatedPlace, AssertMessages.responseNotContains(PLACE_STRING));
    }

    @Test
    public void errorMessageReturned_when_getPlaceByNotExistingId() {
        String uuid = UUID.randomUUID().toString();
        searchV2PlaceRepo.getById(uuid).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo(AssertMessages.entityWithIdOrSlugDoesNotExist(CustomEntityEnum.PLACE.getValue(), uuid)));
    }

    @Test
    public void placeDeleted_when_deletePlace() {
        CREATED_PLACE.set(searchV2PlaceRepo.create(customEntityRequestBody).getResult());

        searchV2PlaceRepo.delete(CREATED_PLACE.get().getId()).then()
                .statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, equalTo("Place with an ID of %s is deleted!".formatted(CREATED_PLACE.get().getId())));

        searchV2PlaceRepo.getById(CREATED_PLACE.get().getId()).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo(AssertMessages.entityWithIdOrSlugDoesNotExist(CustomEntityEnum.PLACE.getValue(), CREATED_PLACE.get().getId())));
    }

    @Test
    public void errorMessageReturned_when_deletePlace_and_setNotExistingId() {
        String uuid = UUID.randomUUID().toString();
        searchV2PlaceRepo.delete(uuid).then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, equalTo(AssertMessages.entityNotExistForProject(uuid)));
    }

    @Test
    public void placeUpdated_when_updateAllPropertiesWithMandatoryProperties() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceUpdateRequestBodyMandatoryProperties(CREATED_PLACE_PRECONDITION.get());
        var updatedPlaceResponse = searchV2PlaceRepo.create(customEntityRequestBody);
        updatedPlaceResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        CustomEntityModel updatedPlace = updatedPlaceResponse.getResult();

        CustomEntityModel actualUpdatedPerson = searchV2PlaceRepo.getById(updatedPlace.getId()).getResult();
        Assertions.assertEquals(updatedPlace, actualUpdatedPerson, AssertMessages.entityNotExpected(CustomEntityEnum.PERSON.getValue()));
    }

    @Test
    public void errorMessageReturned_when_updatePlace_with_alreadyUsedSlug() {
        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        CustomEntityModel createdPlace = searchV2PlaceRepo.create(customEntityRequestBody).getResult();

        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceUpdateRequestBodyMandatoryProperties(CREATED_PLACE_PRECONDITION.get());
        customEntityRequestBody.setSlug(createdPlace.getSlug());

        searchV2PlaceRepo.create(customEntityRequestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, equalTo(ENTITY_WITH_SLUG_ALREADY_EXIST.formatted(customEntityRequestBody.getSlug())));
    }

    @Test
    public void placeCreated_when_createPlace_and_setUsedSlugForCreatingAnotherPlaceButThenSlugUpdated() {
        String slug = CREATED_PLACE_PRECONDITION.get().getSlug();

        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceUpdateRequestBodyMandatoryProperties(CREATED_PLACE_PRECONDITION.get());
        customEntityRequestBody.setSlug("%s-%s".formatted(slug, faker.nation().language().toLowerCase()));
        searchV2PlaceRepo.create(customEntityRequestBody).getResponse().then().statusCode(HttpStatus.SC_OK);

        customEntityRequestBody = CustomEntityHttpFactory.buildPlaceMandatoryPropertiesRequestBody(DOMAIN_ID.get());
        customEntityRequestBody.setSlug(slug);

        var createdPlaceResponse = searchV2PlaceRepo.create(customEntityRequestBody);
        createdPlaceResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        CustomEntityModel expectedCreatedPlace = createdPlaceResponse.getResult();
        ResultModel actualCreatedEntity = searchV2SuggestRepo.getEntityByName(expectedCreatedPlace.getName(), CustomEntityEnum.PLACE);

        EntitiesAsserter.assertAreEqual(expectedCreatedPlace, actualCreatedEntity, CLASS_STRING);
    }
}
