package tennisapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.MethodParameterType;
import data.models.tennisapi.TournamentModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import static data.constants.StringConstants.*;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.TENNIS_API)
@Story(APITags.TENNIS_API)
public class TennisApiTournamentsTests extends BaseTennisApiTests {

    @Test
    public void tennisTournamentsReturned_when_getRequestAll() {
        var tournamentsListResponse = tennisTournamentsHttpRepo.getAll(defaultQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, tournamentsListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(tournamentsListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Tournaments objects list"));
    }

    @Test
    public void tennisTournamentReturned_when_getTournamentRequestById() {
        var tournamentsResponse = tennisTournamentsHttpRepo.getAll(defaultQueryParams);
        TournamentModel firstTournamentFromResponse = tournamentsResponse.getResult().get(0);

        var tournamentResponse = tennisTournamentsHttpRepo.getById(firstTournamentFromResponse.getId());

        Assertions.assertEquals(HttpStatus.SC_OK, tournamentResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertEquals(firstTournamentFromResponse, tournamentResponse.getResult(), AssertMessages.responseNotContains("First Tournament object"));
    }

    @Test
    public void errorMessageReturned_when_getTournamentsRequest_and_setNoQueryParameters() {
        tennisTournamentsHttpRepo.getAll().getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getTournamentsRequest_and_setOffsetParameterOnly() {
        defaultQueryParams.remove(LIMIT_STRING);

        tennisTournamentsHttpRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(LIMIT_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getTournamentsRequest_and_setLimitParameterOnly() {
        defaultQueryParams.remove(OFFSET_STRING);

        tennisTournamentsHttpRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(OFFSET_STRING, MethodParameterType.INTEGER)));
    }

    @Test
    public void errorMessageReturned_when_getTournamentRequestById_and_setInvalidId() {
        tennisTournamentsHttpRepo.getById(INVALID_ID).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Tournament with id: %s not found.".formatted(NOT_EXISTING_ID)));
    }
}
