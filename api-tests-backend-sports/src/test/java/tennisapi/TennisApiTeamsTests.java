package tennisapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.models.tennisapi.DetailsModel;
import data.models.tennisapi.MatchModel;
import data.models.tennisapi.TeamModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.TENNIS_API)
@Story(APITags.TENNIS_API)
public class TennisApiTeamsTests extends BaseTennisApiTests {

    @Test
    public void tennisTeamReturned_when_getTeamRequestById() {
        var matchesListResponse = tennisMatchesHttpRepo.getAll(defaultQueryParams);

        DetailsModel teamDetails = getTeam(matchesListResponse.getResult());
        TeamModel expectedTeamResponse = teamDetails.convertToModel(TeamModel.class);

        var teamResponse = tennisTeamsHttpRepo.getById(teamDetails.getId());
        TeamModel actualTeamResponse = teamResponse.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, teamResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertEquals(expectedTeamResponse, actualTeamResponse, AssertMessages.responseNotContains("First Team object"));
    }

    @Test
    public void errorMessageReturned_when_getTeamRequestById_and_setInvalidTeamId() {
        tennisTeamsHttpRepo.getById(INVALID_ID)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Team with ID %s does not exist!".formatted(NOT_EXISTING_ID)));
    }

    private DetailsModel getTeam(List<MatchModel> matches) {
        return matches.stream()
                .filter(match -> match.getParticipants().get(0).getEntityType().equals("team"))
                .findFirst()
                .orElseThrow()
                .getParticipants()
                .get(0)
                .getDetails()
                .get(0);
    }
}
