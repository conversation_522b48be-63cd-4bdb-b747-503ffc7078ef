package tennisapi.tenniswidgetsapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.Language;
import data.constants.MultiSportSportsEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.api.queryparamenums.StatusType;
import data.constants.enums.ScopeEnum;
import data.constants.enums.odds.OddClientCodeEnum;
import data.constants.enums.odds.OddFormatEnum;
import data.constants.enums.odds.OddTypeEnum;
import data.constants.enums.tennis.TennisCompetitionName;
import data.models.searchapi.ResultModel;
import data.models.standingapi.RankingsModel;
import data.models.tennisapi.CompetitionDetailsModel;
import data.models.tennisapi.TournamentsModel;
import data.utils.DateUtils;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.playoffs.PlayoffsHttpRepository;
import repositories.standings.StandingsParticipantRankingsHttpRepository;
import tennisapi.BaseTennisApiTests;

import java.time.Instant;
import java.time.LocalDate;

import static data.constants.StringConstants.COMPETITION_ID_UNDERSCORED_STRING;
import static data.constants.StringConstants.DESC_UPPERCASE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.TENNIS_API)
@Tag(APITags.WIDGETS_API)
@Story(APITags.TENNIS_API)
public class TennisWidgetsApiTests extends BaseTennisApiTests {

    private StandingsParticipantRankingsHttpRepository standingsParticipantRankingsHttpRepository =
            new StandingsParticipantRankingsHttpRepository(getCurrentTestProject(), SupportedSports.TENNIS.getValue());
    private PlayoffsHttpRepository playoffsHttpRepository =
            new PlayoffsHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.TENNIS);

    @Test
    public void tennisSingleEventWidgetDataForFinishedMatchReturned_when_getRequestByMatchId_forFinishedGame() {
        var finishedMatch = searchV2EventsHttpRepo.getEventsBySport(SupportedSports.TENNIS.getValue(), queryParams).stream()
                .findAny()
                .orElseThrow(() -> new RuntimeException("No finished tennis match found"));

        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.MARKET_TYPES_STRING, "12");
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.FRACTIONAL);

        var finishedMatchResponse = tennisMatchesHttpRepo.getById(finishedMatch.getId(), queryParams);
        finishedMatchResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        tennisMatchesHttpRepo.assertResponseSchema(finishedMatchResponse.getResponse());
    }

    @Test
    public void tennisLivescoreWidgetDataForMatchesReturned_when_getRequestMatchesLivescore() {
        ResultModel netStartedEvent = searchApiV2Facade.getNotStartedEventFor(SupportedSports.TENNIS);
        LocalDate date = DateUtils.getDateFromUTC(Instant.parse(netStartedEvent.getStartTime()));

        queryParams.put(StringConstants.DATE_STRING, date.toString());
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "2");
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.FRACTIONAL);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, "12");
        queryParams.put(StringConstants.LIMIT_STRING, "200");

        Response livescoreMatchesResponse = tennisMatchesHttpRepo.getLivescore(queryParams);
        livescoreMatchesResponse.then().statusCode(HttpStatus.SC_OK);

        tennisMatchesHttpRepo.assertResponseSchema(livescoreMatchesResponse);
    }

    @Test
    public void tennisRankingWidgetDataForPlayerReturned_when_getRequestRankingsAvailableForPlayer() {
        queryParams.put(StringConstants.SCOPE_STRING, ScopeEnum.CURRENT);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        var rankingsResponse = standingsParticipantRankingsHttpRepository.getAvailableRankings(queryParams);
        rankingsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        standingsParticipantRankingsHttpRepository.assertResponseSchema(rankingsResponse.getResponse());
    }

    @Test
    public void tennisRankingWidgetDataForPlayerReturned_when_getRequestRankingsById_ForPlayer() {
        queryParams.put(StringConstants.SCOPE_STRING, ScopeEnum.CURRENT);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        RankingsModel availableRankingsResponse =
                standingsParticipantRankingsHttpRepository.getAvailableRankings(queryParams).getResult().get(0);

        defaultQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        var rankingsResponseById = standingsParticipantRankingsHttpRepository.getById(availableRankingsResponse.getId(), defaultQueryParams);
        rankingsResponseById.getResponse().then().statusCode(HttpStatus.SC_OK);

        standingsParticipantRankingsHttpRepository.assertResponseSchema(rankingsResponseById.getResponse());
    }

    @Test
    public void tennisPlayoffWidgetDataReturned_when_getRequestCompetitionDetails_forCompetitionById() {
        var competitionId = searchV2SuggestRepo.getCompetitionsByName(TennisCompetitionName.US_OPEN.getName(),
                SupportedSports.TENNIS).get(0).getId();
        competitionDetailsQueryParams.put(COMPETITION_ID_UNDERSCORED_STRING, competitionId);
        competitionDetailsQueryParams.put(StringConstants.SEASON_YEAR_STRING, ScopeEnum.CURRENT);
        competitionDetailsQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        var competitionDetailsResponse = tennisCompetitionsHttpRepo.getCompetitionsDetails(competitionDetailsQueryParams);
        CompetitionDetailsModel competitionDetailsModel = competitionDetailsResponse.as(CompetitionDetailsModel.class);
        competitionDetailsResponse.then().statusCode(HttpStatus.SC_OK);

        tennisCompetitionsHttpRepo.assertResponseSchema(competitionDetailsResponse, competitionDetailsModel);
    }

    @Test
    public void tennisPlayoffWidgetDataReturned_when_getRequestPlayoffs_forStage() {
        var competitionId = searchV2SuggestRepo.getCompetitionsByName(TennisCompetitionName.US_OPEN.getName(),
                SupportedSports.TENNIS).get(0).getId();
        competitionDetailsQueryParams.put(COMPETITION_ID_UNDERSCORED_STRING, competitionId);
        competitionDetailsQueryParams.put(StringConstants.SEASON_YEAR_STRING, ScopeEnum.CURRENT);
        competitionDetailsQueryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        Response competitionDetailsResponse = tennisCompetitionsHttpRepo.getCompetitionsDetails(competitionDetailsQueryParams);
        CompetitionDetailsModel competitionDetailsModel = competitionDetailsResponse.as(CompetitionDetailsModel.class);

        queryParams.put(StringConstants.STAGE_ID_UNDERSCORED_STRING, competitionDetailsModel.getTournaments().get(0).getId());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        var playoffsResponse = playoffsHttpRepository.getAll(queryParams);
        playoffsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        playoffsHttpRepository.assertResponseSchema(playoffsResponse.getResponse());
    }

    @Test
    public void tennisAthleteProgrammeWidgetDataReturned_when_getRequestPlayers_forPlayerById() {
        var playerId = searchV2SuggestRepo.getPlayersByName("Grigor Dimitrov", SupportedSports.TENNIS).get(0).getId();
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        var playerResponse = tennisPlayersHttpRepo.getById(playerId, queryParams);
        playerResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        tennisPlayersHttpRepo.assertResponseSchema(playerResponse.getResponse());
    }

    @Test
    public void tennisAthleteProgrammeWidgetDataReturned_when_getRequestTournaments_forPlayerById() {
        var playerId = searchV2SuggestRepo.getPlayersByName("Grigor Dimitrov", SupportedSports.TENNIS).get(0).getId();

        var tournamentResponse = tennisTournamentsHttpRepo.getTournamentsByPlayerId(playerId).getResponse();
        TournamentsModel tournamentsModel = tournamentResponse.as(TournamentsModel.class);
        tournamentResponse.then().statusCode(HttpStatus.SC_OK);

        tennisPlayersHttpRepo.assertResponseSchema(tournamentResponse, tournamentsModel);
    }

    @Test
    public void tennisAthleteProgrammeWidgetDataReturned_when_getRequestMatches_forRoundFilter_and_playerById() {
        var playerId = searchV2SuggestRepo.getPlayersByName("Grigor Dimitrov", SupportedSports.TENNIS).get(0).getId();
        var tournamentId = tennisTournamentsHttpRepo.getTournamentsByPlayerId(playerId).getResult().stream()
                .filter(g -> g.getSeasonYear().equals("2023")).findFirst().get().getId();
        var roundId = tennisMatchesHttpRepo.getMatches(playerId, tournamentId).getResult().get(0).getRound().getId();

        queryParams.put(StringConstants.PLAYER_ID_UNDERSCORED, playerId);
        queryParams.put(StringConstants.ROUND_FILTER_STRING, "%s:%s".formatted(tournamentId, roundId));
        queryParams.put(StringConstants.SORT_ORDER_STRING, DESC_UPPERCASE_STRING);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, StatusType.FINISHED);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.FRACTIONAL);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, "12");
        queryParams.put(StringConstants.LIMIT_STRING, "200");
        queryParams.put(StringConstants.OFFSET_STRING, "0");

        var matchesResponse = tennisMatchesHttpRepo.getAll(queryParams);
            matchesResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        tennisMatchesHttpRepo.assertResponseSchema(matchesResponse.getResponse());
    }
}