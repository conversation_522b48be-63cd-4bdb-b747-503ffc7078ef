package tennisapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.models.tennisapi.DetailsModel;
import data.models.tennisapi.MatchModel;
import data.models.tennisapi.PlayerModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.TENNIS_API)
@Story(APITags.TENNIS_API)
public class TennisApiPlayersTests extends BaseTennisApiTests {

    @Test
    public void tennisPlayerReturned_when_getPlayerRequestById() {
        var matchesListResponse = tennisMatchesHttpRepo.getAll(defaultQueryParams);

        DetailsModel playerDetails = getPlayer(matchesListResponse.getResult());
        PlayerModel expectedPlayerResponse = playerDetails.convertToModel(PlayerModel.class);

        var playerResponse = tennisPlayersHttpRepo.getById(playerDetails.getId());
        PlayerModel actualPlayerResponse = playerResponse.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, playerResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertEquals(expectedPlayerResponse, actualPlayerResponse, AssertMessages.responseNotContains("First Player object"));
    }

    @Test
    public void errorMessageReturned_when_getPlayerRequestById_and_setInvalidPlayerId() {
        tennisTeamsHttpRepo.getById(INVALID_ID)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Team with ID %s does not exist!".formatted(NOT_EXISTING_ID)));
    }

    private DetailsModel getPlayer(List<MatchModel> matches) {
        return matches.stream()
                .filter(match -> match.getParticipants().get(0).getEntityType().equals("player"))
                .findFirst()
                .orElseThrow()
                .getParticipants()
                .get(0)
                .getDetails()
                .get(0);
    }
}