package tennisapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.models.tennisapi.CountryModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.TENNIS_API)
@Story(APITags.TENNIS_API)
public class TennisApiCountriesTests extends BaseTennisApiTests {

    @Test
    public void tennisCountryReturned_when_getCountryRequestById() {
        CountryModel expectedCountry = tennisMatchesHttpRepo.getAll(defaultQueryParams).getResult().get(0).getTournament().getCountry();

        var actualCountryResponse = tennisCountriesHttpRepo.getById(expectedCountry.getId());
        CountryModel actualCountry = actualCountryResponse.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, actualCountryResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertEquals(expectedCountry, actualCountry, AssertMessages.responseNotContains("First Country object"));
    }

    @Test
    public void errorMessageReturned_when_getCountryRequestById_and_setInvalidCountryId() {
        tennisCountriesHttpRepo.getById(INVALID_ID)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Country with id: %s does not exists".formatted(NOT_EXISTING_ID)));
    }
}