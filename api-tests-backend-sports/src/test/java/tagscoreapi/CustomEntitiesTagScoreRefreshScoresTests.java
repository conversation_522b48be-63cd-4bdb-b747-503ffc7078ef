package tagscoreapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.CleanupBehaviorEnum;
import data.constants.EntityTypeEnum;
import data.constants.StringConstants;
import data.models.related.RelatedModel;
import data.models.searchapi.ResultModel;
import factories.articles.ArticlesRelatedHttpFactory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import plugins.cleanupservice.DataCleanupBehavior;

import java.time.LocalDate;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@DataCleanupBehavior(cleanupBehavior = CleanupBehaviorEnum.AFTER_EACH)
@Tag(SMPCategories.API)
@Tag(APITags.TAG_SCORE_API)
@Tag(APITags.SEARCH_API_V2)
@Story(APITags.TAG_SCORE_API)
public class CustomEntitiesTagScoreRefreshScoresTests extends BaseTagScoreApiTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        ResultModel createdDomain = searchApiV2Facade.createDomainEntityRequiredFields();
        searchApiV2Facade.createPersonCustomEntityRequiredFields(createdDomain);
        searchApiV2Facade.createOrganizationCustomEntityRequiredFields(createdDomain);
        searchApiV2Facade.createPlaceCustomEntityRequiredFields(createdDomain);
        searchApiV2Facade.createRoleCustomEntityRequiredFields();
    }

    @Test
    public void errorMessageReturned_when_refreshScores_and_setInvalidFormatForStartTimeQueryParam() {
        queryParams.put(StringConstants.START_TIME_UNDERSCORED_STRING, LocalDate.now().toString());

        tagScoreRefreshScoresHttpRepo.get(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.invalidStartTimeFormatErrorMessage()));
    }

    //@Issue("PLT-41")
    @ParameterizedTest
    @EnumSource(value = EntityTypeEnum.class, names = {"ORGANIZATION", "PERSON", "PLACE"}, mode = EnumSource.Mode.INCLUDE)
    public void expectedTagScoreValueSet_when_tagCustomEntityInTwoDifferentArticles(EntityTypeEnum entityType) {
        int expectedTagScore = 2;

        ResultModel taggedEntity = addRelatedContentCustomEntity(entityType);
        waitUntilTagScoreIsUpdatedFor(taggedEntity.getId(), entityType, expectedTagScore, actualTagScore);

        Assertions.assertEquals(expectedTagScore, actualTagScore.get(),
                AssertMessages.tagScoreErrorMessage(taggedEntity.getName(), expectedTagScore, actualTagScore.get()));
    }

    //@Issue("PLT-41")
    @ParameterizedTest
    @EnumSource(value = EntityTypeEnum.class, names = {"ORGANIZATION", "PERSON", "PLACE"}, mode = EnumSource.Mode.INCLUDE)
    public void expectedTagScoreValueSet_when_tagCustomEntitiesInTwoDifferentArticles_and_deleteOneOfTheArticles(EntityTypeEnum entityType) {
        int expectedTagScore = 1;

        ResultModel taggedEntity = addRelatedContentCustomEntity(entityType);
        contentApiFacade.deleteArticle(secondCreatedArticle);
        createdArticles.remove(secondCreatedArticle);
        waitUntilTagScoreIsUpdatedFor(taggedEntity.getId(), entityType, expectedTagScore, actualTagScore);

        Assertions.assertEquals(expectedTagScore, actualTagScore.get(),
                AssertMessages.tagScoreErrorMessage(taggedEntity.getName(), expectedTagScore, actualTagScore.get()));
    }

    private ResultModel addRelatedContentCustomEntity(EntityTypeEnum entityType) {
        ResultModel entity = searchV2SuggestRepo.getEntityWithoutTagScoreByType(entityType);
        RelatedModel relatedModel = buildRelatedModelByCustomEntityType(entityType, entity);
        relatedHttpRepoFirstArticle.createAll(relatedModel.getData());
        relatedHttpRepoSecondArticle.createAll(relatedModel.getData());
        return entity;
    }

    private RelatedModel buildRelatedModelByCustomEntityType(EntityTypeEnum entityType, ResultModel entity) {
        return switch (entityType) {
            case ORGANIZATION -> ArticlesRelatedHttpFactory.buildOrganizationRelatedModel(entity);
            case PERSON -> ArticlesRelatedHttpFactory.buildPersonRelatedModel(entity);
            case PLACE -> ArticlesRelatedHttpFactory.buildPlaceRelatedModel(entity);
            default -> throw new IllegalStateException("Not supported entity type: " + entityType);
        };
    }
}