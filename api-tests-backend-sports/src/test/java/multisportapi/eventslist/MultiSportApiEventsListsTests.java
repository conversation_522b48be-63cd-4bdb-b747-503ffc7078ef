package multisportapi.eventslist;

import categories.APITags;
import categories.SMPCategories;
import data.constants.*;
import data.constants.enums.odds.*;
import data.models.multisportapi.*;
import data.models.oddsapi.OddsModel;
import data.models.searchapi.ResultModel;
import data.utils.DateUtils;
import io.qameta.allure.Description;
import io.qameta.allure.Story;
import multisportapi.BaseMultiSportApiTests;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import repositories.multisport.MultiSportEventsListHttpRepository;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiEventsListsTests extends BaseMultiSportApiTests {

    private final String filterDate = getFormattedDateForSport(SupportedSports.FOOTBALL);
    private final String selectionDate = LocalDate.now().toString();
    protected List<String> competitionIds;

    @Test
    public void multiSportEventsReturned_when_getEventsListRequest_and_setAllQueryParam() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, OddsMarketTypeEnum.ONE_X_TWO.getName());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.DECIMAL);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        CompetitionWithEventsModel firstCompetition = responseModel.getCompetitions().stream()
                .filter(comp -> comp.getCompetition() != null)
                .findFirst()
                .orElseThrow(() -> new AssertionError("No competitions found in response"));

        Assertions.assertAll("Competition data validation",
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getName(), "Competition name should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getName().isEmpty(), "Competition name should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getId(), "Competition ID should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getId().isEmpty(), "Competition ID should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getSlug(), "Competition slug should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getSlug().isEmpty(), "Competition slug should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getName(), "Event name should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getName().isEmpty(), "Event name should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getId(), "Event ID should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getId().isEmpty(), "Event ID should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getSlug(), "Event slug should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getSlug().isEmpty(), "Event slug should not be empty")
        );
    }

    @Test
    public void multiSportEventsReturned_when_getEventsListRequest_and_setMandatoryQueryParam() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        CompetitionWithEventsModel firstCompetition = responseModel.getCompetitions().stream()
                .filter(comp -> comp.getCompetition() != null)
                .findFirst()
                .orElseThrow(() -> new AssertionError("No competitions found in response"));

        Assertions.assertAll("Competition data validation",
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getName(), "Competition name should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getName().isEmpty(), "Competition name should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getId(), "Competition ID should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getId().isEmpty(), "Competition ID should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getCompetition().getSlug(), "Competition slug should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getCompetition().getSlug().isEmpty(), "Competition slug should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getName(), "Event name should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getName().isEmpty(), "Event name should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getId(), "Event ID should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getId().isEmpty(), "Event ID should not be empty"),
                () -> Assertions.assertNotNull(firstCompetition.getEvents().get(0).getSlug(), "Event slug should not be null"),
                () -> Assertions.assertFalse(firstCompetition.getEvents().get(0).getSlug().isEmpty(), "Event slug should not be empty")
        );
    }

    @Test
    public void multiSportEventsReturned_when_getEventsListRequest_and_setDifferentSelectionDateAndFilterDate() {
        LocalDate filterDate = LocalDate.now().plusDays(2);

        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate.toString());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        boolean allEventsMatchFilterDate = responseModel.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .allMatch(event -> {
                    LocalDate eventDate = Instant.parse(event.getStartTime())
                            .atZone(ZoneOffset.UTC)
                            .toLocalDate();
                    return eventDate.isEqual(filterDate);
                });

        assertExpectedApiResponse(allEventsMatchFilterDate, "All events should have start times on %s".formatted(filterDate));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void eventSelectionsReturnValues_when_getEventsBySelectionDate_and_setConfiguration(MultiSportSportsEnum sport) {
        CompetitionListModel competitionList = createCompetitionList(sport);
        createCompetitionListWithEventsByCurrentDate(sport, competitionList);
        competitionListCode = competitionList.getCode();
        ResultModel eventDetails = getEventWithStartTime(sport);
        String eventStartTime = DateUtils.getDateFromIsoDateTime(eventDetails.getStartTime());

        queryParams.put(StringConstants.SELECTION_DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionList.getCode());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.LIMIT_STRING, 300);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        List<String> selections = responseModel.getCompetitions().stream()
                .filter(competition -> competition.getCompetition() != null)
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getSelections() != null && !event.getSelections().isEmpty())
                .findFirst()
                .map(EventListModel::getSelections)
                .orElseThrow(() -> new AssertionError("No events found with selections"));

        Assertions.assertFalse(selections.isEmpty(), "Selections should not be empty");
        assertExpectedApiResponse(selections.size() >= 2, "There should be at least one selection, but found %s".formatted(selections.size()));
    }

    @Test
    public void eventSelectionsShouldBeEmpty_when_getEventsBySelectionDate_and_noSetConfiguration() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.POPULAR_SPORTS_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        boolean allSelectionsAreEmpty = responseModel.getCompetitions().stream()
                .filter(competition -> competition.getCompetition() != null)
                .flatMap(competition -> competition.getEvents().stream())
                .allMatch(event -> event.getSelections().isEmpty());

        assertExpectedApiResponse(allSelectionsAreEmpty,
                "All events should be empty selections when no configuration is provided but was not");
    }

    @ParameterizedTest
    @EnumSource(value = Language.class, names = {"ENGLISH", "BULGARIAN", "GERMAN", "FRENCH", "SPANISH"})
    public void multiSportEventsReturned_when_getEventsList_and_setEnTranslationLanguage(Language language) {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, language.getCode());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        CompetitionWithEventsModel competitionWithEventsModel = responseModel.getCompetitions()
                .stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations()
                        .stream().anyMatch(t -> t.getLanguage().equals(language.getCode()) && t.getName() != null))
                .findAny()
                .orElseThrow(() -> new AssertionError("No competitions found with %s translations for country name".formatted(language.getValue())));

        Assertions.assertNotNull(competitionWithEventsModel, "The competition with %s translation should not be null".formatted(language.getCode()));
    }

    @Test
    public void errorMessageReturned_when_getEventsList_and_setInvalidTranslationLanguage() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        boolean areCompetitionsEmpty = responseModel.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, "Expected competitions list to be empty, but it contained %s elements".formatted(responseModel.getCompetitions().size()));
    }

    @Test
    public void multiSportEventsReturned_when_when_getEventsListRequest_and_setOddClientQueryParam() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        List<OddsModel> odds = responseModel.getCompetitions()
                .stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .findFirst()
                .map(EventListModel::getOdds)
                .orElseThrow(() -> new AssertionError("No events with odds found in the response"));

        Assertions.assertFalse(odds.isEmpty(), "Odds list should not be empty but was %s".formatted(odds.toString()));
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidOddClient() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        boolean allEventsHaveNoOdds = responseModel.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .allMatch(event -> event.getOdds() == null || event.getOdds().isEmpty());

        assertExpectedApiResponse(allEventsHaveNoOdds,
                "All events should have null or empty odds when using invalid odd client");
    }

    @Test
    public void marketsWithSpecifiedOddTypeReturned_when_getEventsSearchRequest_and_setOddTypeQueryParam() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        List<OddsModel> oddsList = responseModel.getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allOddsHaveCorrectType = oddsList.stream()
                .allMatch(odds -> odds.getType().equals(OddTypeEnum.PRE_EVENT.name()));

        assertExpectedApiResponse(allOddsHaveCorrectType, "Not all odds have the expected type. Expected: " + OddTypeEnum.PRE_EVENT.name());
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidOddType() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_TYPE_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                "Invalid odd_type: invalid. Allowed values are: LIVE, PRE_EVENT, ALL");
    }

    @ParameterizedTest
    @EnumSource(value = OddsMarketTypeEnum.class, names = {"ONE_TWO"}, mode = EnumSource.Mode.EXCLUDE)
    public void specifiedMarketTypeReturned_when_getEventsSearchRequest_and_setMarketTypeQueryParam
            (OddsMarketTypeEnum marketType) {
        //Here, the UUID is hardcoded because the backend team set up one event with a past date containing all possible market types.
        //Since the date is dynamic, it's possible that sometimes one of the markets might be missing, making the test flaky.
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(FOOTBALL_COMPETITION_EVENT_UUID));
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId());
        String date = DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(0).getStartTime());
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, date);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.MARKET_TYPES_STRING, marketType.getName());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        List<EventListModel> eventsWithOdds = responseModel.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .toList();
        boolean marketTypeFound = eventsWithOdds.stream()
                .flatMap(event -> event.getOdds().stream())
                .flatMap(odds -> odds.getMarkets().stream())
                .anyMatch(market -> market.getType().getName().equalsIgnoreCase(marketType.getName()) ||
                        market.getType().getCode().equalsIgnoreCase(marketType.getName()));

        assertExpectedApiResponse(marketTypeFound, "Expected market type is not contained in odds, Expected " + marketType.getName());
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidMarketType() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.MARKET_TYPES_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                "Invalid market_type: invalid");
    }

    @Test
    public void eventsWithSpecifiedStatusReturned_when_setScopeTypeQueryParam() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        List<OddsModel> oddsList = responseModel.getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allMarketsHaveCorrectScopeType = oddsList.stream()
                .flatMap(odds -> odds.getMarkets().stream())
                .allMatch(market -> market.getScope().getType().equalsIgnoreCase(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));

        assertExpectedApiResponse(allMarketsHaveCorrectScopeType, "Not all markets have the expected scope type. Expected:%s".formatted(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));
    }

    @Test
    public void errorMessageReturned_when_getEventsSearchRequest_and_setInvalidScopeType() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsSearchHttpRepo, HttpStatus.SC_BAD_REQUEST, "Invalid scope_type: invalid. Allowed values are: ORDINARY_TIME, FULL_TIME");
    }

    @ParameterizedTest
    @EnumSource(OddFormatEnum.class)
    public void multiSportEventsWithOddsFormatReturned_when_getEventsListRequest_and_setOddFormatQueryParam
            (OddFormatEnum oddFormat) {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, oddFormat);
        EventsResponseModel responseModel = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        String actualOddsValue = responseModel.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .findAny()
                .map(event -> event.getOdds().get(0).getMarkets().get(0).getSelections().get(0).getOdds())
                .orElseThrow(() -> new RuntimeException("No events with odds found"));

        assertOddsFormat(oddFormat.name(), actualOddsValue);
    }

    @Test
    public void errorMessageReturned_when_setInvalidOddFormat() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, OddClientCodeEnum.SPORTAL365.getValue());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsSearchHttpRepo, HttpStatus.SC_BAD_REQUEST, "Invalid odd_format: invalid. Allowed values are: FRACTIONAL, DECIMAL, MONEYLINE");
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidSelectionDate() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, StringConstants.INVALID_STRING);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                "Invalid date format. Please use yyyy-MM-dd");
    }

    @Test
    @Description("Verifies that changing UTC offset affects the event count in the response." +
            "The data should be static for this case to ensure the competitions are the same when you set different offsets")
    public void eventCountDiffers_when_setUtcOffsetChanges() {
        preQueryParams.put(StringConstants.SELECTION_DATE_STRING, "2025-01-01");
        preQueryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        preQueryParams.put(StringConstants.FILTER_DATE_STRING, "2025-01-01");
        preQueryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        ApiResponse<EventsResponseModel> responseWithDefaultOffset = multiSportEventsListHttpRepository.get(preQueryParams);
        responseWithDefaultOffset.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);

        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+10");
        EventsResponseModel responseWithModifiedOffset = getResponseModel(multiSportEventsListHttpRepository, queryParams);

        int eventCountWithDefaultOffset = responseWithDefaultOffset.getResult().getCompetitions().size();
        int eventCountWithModifiedOffset = responseWithModifiedOffset.getCompetitions().size();

        Assertions.assertNotEquals(eventCountWithDefaultOffset, eventCountWithModifiedOffset,
                "Event counts should differ when using different UTC offsets, but they were identical");
    }

    @Test
    public void badRequestReturned_when_getEventsCompetitions_and_setInvalidUtcOffset() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, selectionDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST, "Invalid utc_offset.");
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidCompetitionList() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.INVALID_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, filterDate);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_NOT_FOUND,
                "There is no competition list with identifier invalid");
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidFilterDate() {
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.FILTER_DATE_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                "Invalid date format. Please use yyyy-MM-dd");
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setNoXProjectHeader() {
        multiSportEventsListHttpRepository.removeHeader(StringConstants.X_PROJECT_STRING);

        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                "There is no project named null, or the project does not support the Multi-Sport DB");
    }

    @Test
    public void errorMessageReturned_when_getEventsListRequest_and_setInvalidXProjectHeader() {
        multiSportEventsListHttpRepository = new MultiSportEventsListHttpRepository(Project.NONE);

        multiSportEventsListHttpRepository.get()
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNAUTHORIZED);
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEventsList_and_setNoQueryParameters() {
        validateErrorResponseWithMessage(multiSportEventsListHttpRepository, HttpStatus.SC_BAD_REQUEST,
                AssertMessages.requiredRequestParameter(StringConstants.SELECTION_DATE_STRING, MethodParameterType.LOCAL_DATE));
    }
}