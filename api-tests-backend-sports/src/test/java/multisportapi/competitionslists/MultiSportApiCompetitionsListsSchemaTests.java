package multisportapi.competitionslists;

import categories.APITags;
import categories.SMPCategories;
import data.constants.Language;
import data.constants.MultiSportSportsEnum;
import data.constants.StringConstants;
import data.models.multisportapi.CompetitionListModel;
import io.qameta.allure.Story;
import multisportapi.BaseMultiSportApiTests;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import repositories.multisport.MultiSportCompetitionListHttpRepository;

import java.util.List;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiCompetitionsListsSchemaTests extends BaseMultiSportApiTests {

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionsListsResponseSchemaIsValid_when_getMultiSportCompetitionsLists(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<List<CompetitionListModel>> competitionListByCode = multiSportCompetitionListHttpRepo.getAll(queryParams);

        multiSportCompetitionListHttpRepo.assertResponseSchema(competitionListByCode.getResponse());
    }
}