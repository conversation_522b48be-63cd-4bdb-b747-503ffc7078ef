package multisportapi.competitionslists;

import categories.APITags;
import categories.SMPCategories;
import data.constants.*;
import data.models.multisportapi.CompetitionListModel;
import data.models.multisportapi.CompetitionsRequestModel;
import data.models.multisportapi.CreateCompetitionsListModel;
import data.widgets.options.enums.DataPopularListEnum;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import multisportapi.BaseMultiSportApiTests;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import repositories.multisport.MultiSportCompetitionListHttpRepository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static factories.EntityFactory.getFaker;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiCompetitionListTests extends BaseMultiSportApiTests {

    private static final String THERE_IS_NO_PROJECT_ERROR_MESSAGE_STRING = "There is no project named null, or the project does not support the Multi-Sport DB";
    private long number;

    @Override
    protected void beforeAll() throws Exception {
        super.beforeAll();
        number = getFaker().date().birthday().getTime();
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getCompetitionsLists_and_setAllFields(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        CompetitionListModel responseModel = getResponseModel(multiSportCompetitionListHttpRepo, queryParams);

        Assertions.assertAll(
                () -> Assertions.assertNotNull(responseModel, "Response model should not be null"),
                () -> Assertions.assertNotNull(responseModel.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(responseModel.getCode().isEmpty(), "Code  should not be empty"),
                () -> Assertions.assertFalse(responseModel.getName().isEmpty(), "Name  should not be empty"),
                () -> Assertions.assertFalse(responseModel.getCompetitions().isEmpty(), "Competitions list should not be empty"));
    }

    @Test
    public void multiSportEventsReturned_when_getCompetitionsLists_and_setMandatoryFields() {
        CompetitionListModel responseModel = getResponseModel(multiSportCompetitionListHttpRepo, queryParams);

        Assertions.assertAll(
                () -> Assertions.assertNotNull(responseModel, "Response model should not be null"),
                () -> Assertions.assertNotNull(responseModel.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(responseModel.getCode().isEmpty(), "Code  should not be empty"),
                () -> Assertions.assertFalse(responseModel.getName().isEmpty(), "Name  should not be empty"),
                () -> Assertions.assertFalse(responseModel.getCompetitions().isEmpty(), "Competitions list should not be empty"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void unauthorizedReturned_when_getCompetitionsLists_and_setInvalidXProject(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(Project.NONE, sport.getValue());

        multiSportCompetitionListHttpRepo.getAll()
                .getResponse()
                .then().statusCode(HttpStatus.SC_UNAUTHORIZED);
    }

    @Test
    public void unauthorizedReturned_when_getCompetitionsLists_and_setInvalidSport() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.NOT_EXISTING.getValue());

        validateErrorResponseWithMessage(multiSportCompetitionListHttpRepo, HttpStatus.SC_BAD_REQUEST, AssertMessages.invalidSportSelection());
    }

    @Test
    public void errorMessageReturned_when_getEventsCompetitions_and_setNoXProjectHeader() {
        multiSportCompetitionListHttpRepo.removeHeader(StringConstants.X_PROJECT_STRING);

        validateErrorResponseWithMessage(multiSportCompetitionListHttpRepo, HttpStatus.SC_BAD_REQUEST, THERE_IS_NO_PROJECT_ERROR_MESSAGE_STRING);
    }

    @Test
    public void errorMessageReturned_when_getEventsCompetitions_and_setOnlyNotXProjectHeader() {
        multiSportCompetitionListHttpRepo.removeHeader(StringConstants.X_PROJECT_STRING);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        validateErrorResponseWithMessage(multiSportCompetitionListHttpRepo, HttpStatus.SC_BAD_REQUEST, THERE_IS_NO_PROJECT_ERROR_MESSAGE_STRING);
    }

    @Test
    public void errorMessageReturned_when_postCompetitionListRequest() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.NOT_EXISTING.getValue());
        List<String> competitionIds = getMultipleCompetitionIdFor(MultiSportSportsEnum.NOT_EXISTING);
        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildMultipleCompetitionList(MultiSportSportsEnum.NOT_EXISTING, competitionIds);

        ApiResponse<CompetitionListModel> createdCompetitionListResponse = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);

        createdCompetitionListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST).body(StringConstants.MESSAGE_STRING,
                        Matchers.equalTo("Invalid sport selection. The sport options are: basketball, multi-sport, tennis"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_invalidCompetitionId(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        String sport = multiSportSportsEnum.getValue();

        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(
                "Auto-MultiSport-%s".formatted(number),
                CompetitionsRequestModel.builder()
                        .id(null)
                        .order(1)
                        .build(),
                "Auto-%s-Competition-%s".formatted(sport, number));

        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);
        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[There is no id provided for the competition. (x1)]"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_invalidCode(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        String sport = multiSportSportsEnum.getValue();
        String competitionId = getCompetitionIdFor(multiSportSportsEnum);

        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(
                null,
                CompetitionsRequestModel.builder()
                        .id(competitionId)
                        .order(1)
                        .build(),
                "Auto-%s-Competition-%s".formatted(sport, number));

        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);
        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[There is no code provided for the competition list. (x1)]"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_invalidOrder(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        String sport = multiSportSportsEnum.getValue();
        String competitionId = getCompetitionIdFor(multiSportSportsEnum);

        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(
                "Auto-%s-%s".formatted(sport, number),
                CompetitionsRequestModel.builder()
                        .id(competitionId)
                        .order(null)
                        .build(),
                "Auto-%s-Competition-%s".formatted(sport, number));

        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);
        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[Invalid competition order (x1)] [There is no order provided for the competition. (x1)"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_invalidName(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        String sport = multiSportSportsEnum.getValue();
        String competitionId = getCompetitionIdFor(multiSportSportsEnum);

        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(
                "Auto-%s-%s".formatted(sport, number),
                CompetitionsRequestModel.builder()
                        .id(competitionId)
                        .order(1)
                        .build(),
                null);

        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);
        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[There is no name provided for the competition list. (x1)"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_emptyCompetitionsList(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        String sport = multiSportSportsEnum.getValue();

        CreateCompetitionsListModel emptyCompetitionsRequest = CreateCompetitionsListModel.builder()
                .code("Auto-%s-%s".formatted(sport, number))
                .competitions(List.of())
                .name("Auto-%s-Competition-%s".formatted(sport, number))
                .build();

        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(emptyCompetitionsRequest);
        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[There are no competitions provided for the competition list. (x1)]"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorReturned_when_postCompetitionListRequest_with_setNullToAllFields(MultiSportSportsEnum multiSportSportsEnum) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), multiSportSportsEnum.getValue());
        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(
                null,
                CompetitionsRequestModel.builder()
                        .id(null)
                        .order(null)
                        .build(),
                null);
        ApiResponse<CompetitionListModel> response = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);

        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("[Invalid competition order (x1)] [There is no code provided for the competition list. (x1)] [There is no order provided for the competition. (x1)] [There is no name provided for the competition list. (x1)] [There is no id provided for the competition. (x1)"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionsListsReturned_when_getCompetitionsListsRequestBySpecificSport(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());

        competitionListCode = createCompetitionListBySport(sport).getCode();

        ApiResponse<List<CompetitionListModel>> competitionsListsResponse = multiSportCompetitionListHttpRepo.getAll();

        Assertions.assertEquals(HttpStatus.SC_OK, competitionsListsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(competitionsListsResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Competitions Lists object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionListReturned_when_getCompetitionListRequestByCompetitionListCode(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());

        competitionListCode = createCompetitionListBySport(sport).getCode();

        CompetitionListModel expectedCompetitionListModel = multiSportCompetitionListHttpRepo.getAll().getResult().get(0);

        ApiResponse<CompetitionListModel> competitionListResponse = multiSportCompetitionListHttpRepo.getById(expectedCompetitionListModel.getCode());
        Assertions.assertEquals(HttpStatus.SC_OK, competitionListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        CompetitionListModel actualCompetitionListModel = competitionListResponse.getResult();
        Assertions.assertEquals(expectedCompetitionListModel, actualCompetitionListModel, AssertMessages.responseNotContains("Competition List object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorMessageReturned_when_getCompetitionListRequestByCompetitionListCode_and_setNotExistingCode(MultiSportSportsEnum sport) {
        final String notExistingCompetitionCode = "notExistingCompetitionCode";
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());

        multiSportCompetitionListHttpRepo.getById(notExistingCompetitionCode).getResponse().then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("There is no competition list with identifier %s".formatted(notExistingCompetitionCode)));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionListCreated_when_postCompetitionListRequest(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        String competitionId = getCompetitionIdFor(sport);
        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(sport, competitionId);

        ApiResponse<CompetitionListModel> createdCompetitionListResponse = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);

        Assertions.assertEquals(HttpStatus.SC_OK, createdCompetitionListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        CompetitionListModel expectedCreatedCompetitionListModel = createdCompetitionListResponse.getResult();
        competitionListCode = expectedCreatedCompetitionListModel.getCode();
        CompetitionListModel actualCreatedCompetitionListModel = multiSportCompetitionListHttpRepo.getById(competitionListCode).getResult();

        Assertions.assertEquals(expectedCreatedCompetitionListModel, actualCreatedCompetitionListModel, AssertMessages.responseNotContains("Created Competition List object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionListCreated_when_postCompetitionListRequest_with_multipleCompetitions(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        List<String> competitionIds = getMultipleCompetitionIdFor(sport);
        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildMultipleCompetitionList(sport, competitionIds);

        ApiResponse<CompetitionListModel> createdCompetitionListResponse = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);

        Assertions.assertEquals(HttpStatus.SC_OK, createdCompetitionListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        Collections.sort(createdCompetitionListResponse.getResult().getCompetitions());
        createdCompetitionListResponse.getResult().setCompetitions(createdCompetitionListResponse.getResult().getCompetitions());
        CompetitionListModel expectedCreatedCompetitionListModel = createdCompetitionListResponse.getResult();
        competitionListCode = expectedCreatedCompetitionListModel.getCode();
        CompetitionListModel actualCreatedCompetitionListModel = multiSportCompetitionListHttpRepo.getById(competitionListCode).getResult();

        Assertions.assertEquals(expectedCreatedCompetitionListModel, actualCreatedCompetitionListModel, AssertMessages.responseNotContains("Created Competition List object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionListUpdated_when_putCompetitionListRequest(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());

        CompetitionListModel createdCompetition = createCompetitionListBySport(sport);
        competitionListCode = createdCompetition.getCode();
        String competitionId = createdCompetition.getCompetitions().get(0).getCompetition().getId();

        CreateCompetitionsListModel updatedCompetitionListModel = multiSportHttpFactory.buildUpdatedCompetitionList(sport, competitionId, competitionListCode);
        ApiResponse<CompetitionListModel> updatedCompetitionListResponse = multiSportCompetitionListHttpRepo.updateCompetitionListCode(competitionListCode, updatedCompetitionListModel);

        Assertions.assertEquals(HttpStatus.SC_OK, updatedCompetitionListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        CompetitionListModel expectedUpdatedCompetitionListModel = updatedCompetitionListResponse.getResult();
        CompetitionListModel actualUpdatedCompetitionListModel = multiSportCompetitionListHttpRepo.getById(expectedUpdatedCompetitionListModel.getCode()).getResult();

        Assertions.assertEquals(expectedUpdatedCompetitionListModel, actualUpdatedCompetitionListModel, AssertMessages.responseNotContains("Updated Competition List object"));
    }

    @Test
    //@Issue("SBE-2963")
    public void errorMessageReturned_when_getCompetitionsListsRequest_and_setNotExistingSport() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.NOT_EXISTING.getValue());

        multiSportCompetitionListHttpRepo.getAll().getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.invalidSportSelection()));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionListDeleted_when_deleteCompetitionListRequest(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        String competitionId = getCompetitionIdFor(sport);
        CreateCompetitionsListModel createCompetitionsRequestBody = multiSportHttpFactory.buildDefaultCompetitionList(sport, competitionId);
        ApiResponse<CompetitionListModel> createdCompetitionListResponse = multiSportCompetitionListHttpRepo.createCompetitionList(createCompetitionsRequestBody);

        Response deletedCompetitionListResponse = multiSportCompetitionListHttpRepo.delete(createdCompetitionListResponse.getResult().getCode());

        Assertions.assertEquals(HttpStatus.SC_OK, deletedCompetitionListResponse.getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        ApiResponse<List<CompetitionListModel>> allCompetitionList = multiSportCompetitionListHttpRepo.getAll();
        Optional<CompetitionListModel> competitionListFound = allCompetitionList.getResult().stream().filter(CompetitionListModel -> CompetitionListModel.getCode().equals(createdCompetitionListResponse.getResult().getCode())).findFirst();
        Assertions.assertFalse(competitionListFound.isPresent(), "Created Competition List was returned int the list after deletion: \n" + allCompetitionList.getResponse().getBody().prettyPrint());
    }

    @Test
    public void errorMessageReturned_when_deleteCompetitionListRequest_and_setNotExistingCode() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.TENNIS.getValue());

        multiSportCompetitionListHttpRepo.delete("notExistingCompetitionListCode").then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.multiSportCompetitionListNotDeleted("notExistingCompetitionListCode")));
    }

    @Test
    public void errorMessageReturned_when_competitionListAll_and_noSportQueryParam() {
        new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.MULTI_SPORT.getValue())
                .get(DataPopularListEnum.ALL.getValue()).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid or missing sport for competition list search"));
    }

    @Test
    public void errorMessageReturned_when_competitionListAll_and_setMoreThanOneSportForSportQueryParam() {
        queryParams.put(StringConstants.SPORT_STRING, String.join(",", MultiSportSportsEnum.BASKETBALL.getValue(), MultiSportSportsEnum.TENNIS.getValue()));

        new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.MULTI_SPORT.getValue())
                .get(DataPopularListEnum.ALL.getValue(), queryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid or missing sport for competition list search"));
    }

    @Test
    public void errorMessageReturned_when_competitionListAll_and_setInvalidValueForSportQueryParam() {
        queryParams.put(StringConstants.SPORT_STRING, "soccer");

        new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), MultiSportSportsEnum.MULTI_SPORT.getValue())
                .get(DataPopularListEnum.ALL.getValue(), queryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid or missing sport for competition list search"));
    }

    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"FOOTBALL", "TENNIS", "BASKETBALL", "ICE_HOCKEY", "CYCLING", "HANDBALL"}, mode = EnumSource.Mode.INCLUDE)
    public void competitionsFromRespectiveSportReturned_when_getCompetitionListAll_and_setSportQueryParam(SupportedSports sport) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());

        ApiResponse<CompetitionListModel> competitionListResponse = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(),
                MultiSportSportsEnum.MULTI_SPORT.getValue())
                .get(DataPopularListEnum.ALL.getValue(), queryParams);
        competitionListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        competitionListResponse.getResult().getCompetitions()
                .forEach(competition -> Assertions.assertEquals(sport.getValue(), competition.getCompetition().getSport(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.SPORT_STRING, "competitions -> %s".formatted(StringConstants.COMPETITION_STRING))));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"BASKETBALL", "TENNIS"}, mode = EnumSource.Mode.INCLUDE)
    public void errorMessageReturned_when_getCompetitionListAll_and_setSportDifferentThanMultiSportInPath_and_setSportQueryParam(MultiSportSportsEnum sport) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());

        new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue())
                .get(DataPopularListEnum.ALL.getValue(), queryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("The sport request parameter is only allowed when the sport path parameter is multi-sport"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"BASKETBALL", "TENNIS"}, mode = EnumSource.Mode.INCLUDE)
    public void competitionsForRespectiveSportReturned_when_getCompetitionListAll_and_setSportDifferentThanMultiSPortInPath_and_noSportQueryParam(MultiSportSportsEnum sport) {
        ApiResponse<CompetitionListModel> competitionListResponse = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue()).get(DataPopularListEnum.ALL.getValue());
        competitionListResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        competitionListResponse.getResult().getCompetitions().forEach(competition ->
                Assertions.assertEquals(sport.getValue(), competition.getCompetition().getSport(),
                        AssertMessages.valueOfPropertyNotCorrect(StringConstants.SPORT_STRING, "competitions -> %s".formatted(StringConstants.COMPETITION_STRING))));
    }
}