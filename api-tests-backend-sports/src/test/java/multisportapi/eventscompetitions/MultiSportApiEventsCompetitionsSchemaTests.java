package multisportapi.eventscompetitions;

import categories.APITags;
import categories.SMPCategories;
import data.constants.DateSelectionEnum;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.multisportapi.EventsResponseModel;
import io.qameta.allure.Story;
import multisportapi.BaseMultiSportApiTests;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiEventsCompetitionsSchemaTests extends BaseMultiSportApiTests {

    @Test
    public void multiSportEventsCompetitionsResponseSchemaIsValid_when_getMultiSportEvent() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSelectionEnum.AUTOMATIC.name().toLowerCase());
        queryParams.put(StringConstants.DATE_STRING, getFormattedDateForSport(SupportedSports.FOOTBALL));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());

        ApiResponse<EventsResponseModel> eventsCompetitions = multiSportEventsCompetitionsListHttpRepo.get(queryParams);
        eventsCompetitions.getResponse().then().statusCode(HttpStatus.SC_OK);

        multiSportEventsCompetitionsListHttpRepo.assertResponseSchema(eventsCompetitions.getResponse());
    }
}