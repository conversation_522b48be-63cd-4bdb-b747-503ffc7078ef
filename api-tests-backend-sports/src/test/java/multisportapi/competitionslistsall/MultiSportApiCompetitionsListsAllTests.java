package multisportapi.competitionslistsall;

import categories.APITags;
import categories.SMPCategories;
import data.constants.Language;
import data.constants.MultiSportSportsEnum;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.multisportapi.CompetitionListModel;
import io.qameta.allure.Story;
import multisportapi.BaseMultiSportApiTests;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import repositories.multisport.MultiSportCompetitionListHttpRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static factories.EntityFactory.getFaker;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiCompetitionsListsAllTests extends BaseMultiSportApiTests {

    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"INVALID"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionsReturned_when_getCompetitionsListsAllRequest_and_setAllQueryParams(SupportedSports supportedSports) {
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.SPORT_STRING, supportedSports.getValue());
        CompetitionListModel eventListByDateModelApiResponse = getEventListByDateModelApiResponse(queryParams);

        Assertions.assertAll(
                () -> Assertions.assertNotNull(eventListByDateModelApiResponse, "Response model should not be null"),
                () -> Assertions.assertNotNull(eventListByDateModelApiResponse.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(eventListByDateModelApiResponse.getCompetitions().isEmpty(), "Competitions list should not be empty"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionsReturned_when_getCompetitionsListsAllRequest_and_setMandatoryQueryParams(MultiSportSportsEnum sport) {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), sport.getValue());
        setupPathParameterForSport(sport);

        CompetitionListModel eventListByDateModelApiResponse = getEventListByDateModelApiResponse(queryParams);

        Assertions.assertAll(
                () -> Assertions.assertNotNull(eventListByDateModelApiResponse, "Response model should not be null"),
                () -> Assertions.assertNotNull(eventListByDateModelApiResponse.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(eventListByDateModelApiResponse.getCompetitions().isEmpty(), "Competitions list should not be empty"));
    }

    @Test
    public void badRequestReturned_when_getCompetitionsListsAllRequest_and_setInvalidSportPath() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), StringConstants.INVALID_STRING);
        ApiResponse<CompetitionListModel> competitionListByAll = multiSportCompetitionListHttpRepo.getCompetitionListByAll(queryParams);

        validateBadRequestReturned(competitionListByAll, "Invalid sport selection. The sport options are: basketball, multi-sport, tennis");
    }

    @Test
    public void badRequestReturned_when_getCompetitionsListsAllRequest_and_setInvalidSportQuery() {
        queryParams.put(StringConstants.SPORT_STRING, StringConstants.INVALID_STRING);
        ApiResponse<CompetitionListModel> competitionListByAll = multiSportCompetitionListHttpRepo.getCompetitionListByAll(queryParams);

        validateBadRequestReturned(competitionListByAll, "Invalid or missing sport for competition list search");
    }

    @Test
    public void badRequestReturned_when_getCompetitionsListsAll_with_pathSportDifferentFromQuerySport() {
        multiSportCompetitionListHttpRepo = new MultiSportCompetitionListHttpRepository(getCurrentTestProject(), SupportedSports.TENNIS.getValue());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        ApiResponse<CompetitionListModel> competitionListByAll = multiSportCompetitionListHttpRepo.getCompetitionListByAll(queryParams);

        validateBadRequestReturned(competitionListByAll, "The sport request parameter is only allowed when the sport path parameter is multi-sport");
    }

    //@Issue("PLT-429")
    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"INVALID"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportCompetitionsReturned_when_getCompetitionsListsAllRequest_and_setInvalidTranslationLanguage(SupportedSports supportedSports) {
        queryParams.put(StringConstants.SPORT_STRING, supportedSports.getValue());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, StringConstants.INVALID_STRING);
        CompetitionListModel eventListByDateModelApiResponse = getEventListByDateModelApiResponse(queryParams);

        boolean areCompetitionsEmpty = eventListByDateModelApiResponse.getCompetitions().isEmpty();

        Assertions.assertTrue(areCompetitionsEmpty, "Expected competitions list to be empty, but it contained %s elements");
    }

    @Test
    public void badRequestReturned_when_getCompetitionsListsAllRequest_and_setNoXProjectByRequestBody() {
        multiSportCompetitionListHttpRepo.removeHeader(StringConstants.X_PROJECT_STRING);
        ApiResponse<CompetitionListModel> competitionListByAll = multiSportCompetitionListHttpRepo.getCompetitionListByAll(queryParams);

        validateBadRequestReturned(competitionListByAll, "There is no project named null, or the project does not support the Multi-Sport DB");
    }

    private CompetitionListModel getEventListByDateModelApiResponse(Map<String, Object> queryParams) {
        ApiResponse<CompetitionListModel> competitionListByAll = multiSportCompetitionListHttpRepo.getCompetitionListByAll(queryParams);

        competitionListByAll.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        return competitionListByAll.getResult();
    }

    private static void validateBadRequestReturned(ApiResponse<CompetitionListModel> competitionListByAll, String errorMessage) {
        competitionListByAll.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(errorMessage));
    }

    private void setupPathParameterForSport(MultiSportSportsEnum sport) {
        if (sport == MultiSportSportsEnum.MULTI_SPORT) {
            List<SupportedSports> validSports = Arrays.stream(SupportedSports.values())
                    .filter(s -> s != SupportedSports.INVALID)
                    .collect(Collectors.toList());

            SupportedSports randomSport = getFaker().options().nextElement(validSports);
            queryParams.put(StringConstants.SPORT_STRING, randomSport.getValue());
        }
    }
}