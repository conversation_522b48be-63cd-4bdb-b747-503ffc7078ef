package multisportapi.competitionlistcode;

import categories.APITags;
import categories.SMPCategories;
import data.constants.Language;
import data.constants.StringConstants;
import data.models.multisportapi.EventListByDateModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiCompetitionsListsCodeSchemaTests extends BaseMultiSportApiCompetitionsListsCodeTests {

    @Test
    public void multiSportCompetitionsListsCodeResponseSchemaIsValid_when_getMultiSportCompetitionsLists() {
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<EventListByDateModel> competitionListByCode = multiSportCompetitionListHttpRepo.getCompetitionListByCode(competitionList.getCode(), queryParams);

        multiSportCompetitionListHttpRepo.assertResponseSchema(competitionListByCode.getResponse());
    }
}