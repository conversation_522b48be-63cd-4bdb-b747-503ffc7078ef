package multisportapi.competitionevents;

import categories.APITags;
import categories.SMPCategories;
import data.constants.StringConstants;
import data.models.multisportapi.EventsResponseModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

import java.time.LocalDate;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportCompetitionEventsSchemaTests extends BaseMultiSportApiCompetitionEventsTests {

    @Test
    public void competitionEventsReturned_when_getCompetitionEvents_with_competitionIdsQueryParam_and_setMandatoryQueryParams() {
        queryParams.put(StringConstants.DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.UTC_OFFSET_STRING, UTC_OFFSET_VALUE);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionIds);
        ApiResponse<EventsResponseModel> eventsCompetitionResponseModelApiResponse = multiSportCompetitionEventsHttpRepo.get(queryParams);

        multiSportCompetitionEventsHttpRepo.assertResponseSchema(eventsCompetitionResponseModelApiResponse.getResponse());
    }
}