package multisportapi.competitionevents;

import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.footballapi.v2.MatchV2Model;
import data.models.searchapi.ResultModel;
import multisportapi.BaseMultiSportApiTests;

import java.util.List;

public class BaseMultiSportApiCompetitionEventsTests extends BaseMultiSportApiTests {

    protected static final String UTC_OFFSET_VALUE = "+2";
    protected List<MatchV2Model> eventsWithOdds;
    protected List<ResultModel> sportSearchApiEvents;
    protected List<String> competitionIds;

    @Override
    protected void beforeAll() {
        eventsWithOdds = footballApiFacade.getFootballEventsWithOdds(StringConstants.SPORTAL_365_STRING);
    }

    @Override
    protected void beforeEach() {
        super.beforeEach();
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(eventsWithOdds.get(0).getUuid(), eventsWithOdds.get(eventsWithOdds.size() - 1).getUuid()));
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId());
    }

    protected ResultModel getResultModel(List<ResultModel> searchV2EventsHttpRepo, SupportedSports sport) {
        return searchV2EventsHttpRepo
                .stream()
                .filter(event -> event.getCompetition() != null)
                .filter(event -> event.getCompetition().getId() != null && !event.getCompetition().getId().isEmpty())
                .findAny()
                .orElseThrow(() -> new AssertionError("No event found for sport %s".formatted(sport.getValue())));
    }
}