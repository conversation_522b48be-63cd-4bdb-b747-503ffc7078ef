package multisportapi.events;

import categories.APITags;
import categories.SMPCategories;
import data.constants.*;
import data.constants.api.queryparamenums.StatusType;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.constants.enums.multisport.DateSlection;
import data.constants.enums.odds.OddFormatEnum;
import data.constants.enums.odds.OddTypeEnum;
import data.constants.enums.odds.OddsMarketTypeEnum;
import data.constants.enums.odds.OddsScopeTypeEnum;
import data.models.multisportapi.*;
import data.models.oddsapi.OddsModel;
import data.models.searchapi.ResultModel;
import data.models.searchapi.SportEntityModel;
import data.utils.DateUtils;
import data.widgets.options.enums.DataOddsMarketValueTypeEnum;
import data.widgets.options.enums.DataPopularListEnum;
import io.qameta.allure.Description;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static data.utils.DateUtils.getDateFromIsoDateTime;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiEventsTests extends BaseMultiSportApiEventsTests {

    private SportEntityModel createdEvent;

    @Override
    protected void afterEach() {
        super.afterEach();
        if (createdEvent != null) {
            searchV2EventHttpRepo.delete(createdEvent.getId());
        }
    }

    @Test
    public void multiSportEventsReturned_when_getMultiSportEventsRequest_and_setAllQueryParams() {
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(eventsWithOdds.get(0).getUuid()));
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId());

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSlection.AUTOMATIC.getValue());
        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(0).getStartTime()));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, OddsMarketTypeEnum.ONE_X_TWO.getName());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.DECIMAL);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, EventStatusType.NOT_STARTED);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<EventsResponseModel> eventListByDateModelApiResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListByDateModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        EventsResponseModel eventsResult = eventListByDateModelApiResponse.getResult();

        List<OddsModel> oddsModel = eventsResult.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .findFirst()
                .map(EventListModel::getOdds)
                .orElseThrow(() -> new AssertionError("No events with odds found"));

        Assertions.assertAll("Event API response validation",
                () -> Assertions.assertNotNull(eventsResult.getSport(), "Sport should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(eventsResult.getCompetitions().isEmpty(), "Competitions list should not be empty"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition(), "Competition object should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getName(), "Competition name should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getId(), "Competition ID should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getSlug(), "Competition slug should not be null"),
                () -> Assertions.assertFalse(eventsResult.getCompetitions().get(0).getEvents().isEmpty(), "Competition events list should not be empty"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getEvents().get(0).getId(), "Event ID should not be null"),
                () -> Assertions.assertFalse(oddsModel.isEmpty(), "Event odds list should not be empty"),
                () -> Assertions.assertNotNull(oddsModel.get(0).getType(), "Odds type should not be null"),
                () -> Assertions.assertNotNull(oddsModel.get(0).getBookmaker(), "Odds bookmaker should not be null"),
                () -> Assertions.assertNotNull(oddsModel.get(0).getEntityId(), "Odds entity ID should not be null"));
    }

    @Test
    public void multiSportEventsReturned_when_getMultiSportEventsRequest_and_setMandatoryQueryParams() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        EventsResponseModel eventsResult = eventListResponse.getResult();

        Assertions.assertAll("Event API response validation",
                () -> Assertions.assertNotNull(eventsResult.getSport(), "Sport should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions(), "Competitions list should not be null"),
                () -> Assertions.assertFalse(eventsResult.getCompetitions().isEmpty(), "Competitions list should not be empty"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition(), "Competition object should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getName(), "Competition name should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getId(), "Competition ID should not be null"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getCompetition().getSlug(), "Competition slug should not be null"),
                () -> Assertions.assertFalse(eventsResult.getCompetitions().get(0).getEvents().isEmpty(), "Competition events list should not be empty"),
                () -> Assertions.assertNotNull(eventsResult.getCompetitions().get(0).getEvents().get(0).getId(), "Event ID should not be null"));
    }

    @Test
    public void multipleEventsReturned_when_getEventsRequest_and_setQueryCompetitionIdsWithMoreThanOneEvent() {
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId(), sportSearchApiEvents.get(1).getCompetitionId());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(1).getStartTime()));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        int competitionsCount = eventListResponse.getResult().getCompetitions().size();
        List<String> returnedCompetitionIds = eventListResponse.getResult()
                .getCompetitions().stream()
                .map(comp -> comp.getCompetition().getId())
                .toList();

        Assertions.assertTrue(competitionsCount > 1, "Expected more than one competition in response, but found only %s".formatted(competitionsCount));
        Assertions.assertTrue(returnedCompetitionIds.containsAll(competitionIds), "Response does not contain all competition IDs that were specified in query");
    }

    @Test
    @Description("Verifies that when both competition_ids and competition_list parameters are provided in the request, the API prioritizes the competition_ids parameter.")
    public void competitionIdsParameterTakesPriority_when_bothCompetitionIdsAndCompetitionListAreSet() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(0).getStartTime()));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        List<String> returnedCompetitionIds = eventListResponse.getResult().getCompetitions()
                .stream()
                .map(comp -> comp.getCompetition().getId())
                .toList();

        assertExpectedApiResponse(returnedCompetitionIds.containsAll(competitionIds),
                "Not all specified competitionIds were returned in the response");
        Assertions.assertEquals(competitionIds.size(), returnedCompetitionIds.size(),
                "Number of competitions in response doesn't match number of specified competitionIds");
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEventsRequest_and_setInvalidSportQuery() {
        queryParams.put(StringConstants.SPORT_STRING, StringConstants.INVALID_STRING);
        queryParams.put(StringConstants.DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid sport for event list selection"));
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEventsRequest_and_setInvalidDateQuery() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, StringConstants.INVALID_STRING);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid date format. Please use yyyy-MM-dd"));
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEventsRequest_and_setInvalidCompetitionListQuery() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("There is no competition list with identifier %s".formatted(StringConstants.INVALID_STRING)));
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEventsRequest_and_setWithoutMandatoryQueryParams() {
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSlection.AUTOMATIC.getValue());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, OddsMarketTypeEnum.ONE_X_TWO.getName());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.DECIMAL);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, EventStatusType.NOT_STARTED);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Required request parameter 'date' for method parameter type LocalDate is not present"));
    }

    @Test
    public void badRequestReturned_when_multiSportEvents_and_setNoXProjectByRequestBody() {
        multiSportEventsHttpRepo.removeHeader(StringConstants.X_PROJECT_STRING);
        ApiResponse<EventsResponseModel> eventListResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("There is no project named null, or the project does not support the Multi-Sport DB"));
    }

    @Test
    public void errorMessageReturned_when_putMultiSportEvents_and_setNoQueryParameters() {
        multiSportEventsHttpRepo.getAll().getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(StringConstants.DATE_STRING, MethodParameterType.LOCAL_DATE)));
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEvents_and_setNoQueryParameters() {
        multiSportEventsHttpRepo.getAll().getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(StringConstants.DATE_STRING, MethodParameterType.LOCAL_DATE)));
    }

    @Test
    public void errorMessageReturned_when_getMultiSportEvents_and_setOnlyDateQueryParameter() {
        queryParams.put(StringConstants.DATE_STRING, LocalDate.now().toString());

        multiSportEventsHttpRepo.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(StringConstants.COMPETITION_LIST_STRING, MethodParameterType.STRING)));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "ALL", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void errorMessageReturned_when_getMultiSportEvents_and_setOnlyCompetitionListQueryParameter(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);

        multiSportEventsHttpRepo.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(StringConstants.DATE_STRING, MethodParameterType.LOCAL_DATE)));
    }

    @ParameterizedTest
    @MethodSource("queryParams")
    public void errorMessageReturned_when_getMultiSportEvents_and_setQueryParametersWithoutDate(String queryParameter, Object queryParameterValue) {
        queryParams.put(queryParameter, queryParameterValue);

        multiSportEventsHttpRepo.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(StringConstants.DATE_STRING, MethodParameterType.LOCAL_DATE)));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsCreated_when_putEventsRequest(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventId = getEventWithStartTime(sport).getId();
        String date = LocalDate.now().toString();
        CreateEventsByDateModel createEventsRequestBody = multiSportHttpFactory.buildDefaultEventsByDateList(sport.getValue(), Collections.singleton(eventId));

        multiSportEventsHttpRepo.createCompetitionListWithEventsByDate(createEventsRequestBody, competitionListCode, date)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Curated list of events updated successfully."));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsByDateRequest(MultiSportSportsEnum sport) {
        CompetitionListModel competitionList = createCompetitionList(sport);
        String date = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());
        createCompetitionListWithEventsByCurrentDate(sport, competitionList);
        competitionListCode = competitionList.getCode();

        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsByDate(competitionListCode, date);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsRequest_with_requiredProperties(MultiSportSportsEnum sport) {
        CompetitionListModel competitionList = createCompetitionList(sport);
        createCompetitionListWithEventsByCurrentDate(sport, competitionList);
        String date = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());
        competitionListCode = competitionList.getCode();

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.DATE_STRING, date);
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @ParameterizedTest
    @EnumSource(DateSelectionEnum.class)
    public void multiSportEventsReturned_when_getEventsRequest_and_setDateSelectionProperties(DateSelectionEnum dataSelection) {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.BASKETBALL.getValue());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, dataSelection.name().toLowerCase());
        // The date should be static to ensure it consistently returns more than one event, preventing flakiness in the test, I've already discussed this with the BE team.
        queryParams.put(StringConstants.DATE_STRING, "2025-02-14");
        // Competition list should be "popular-sports" because this list is manually configured with selected competitions
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.POPULAR_SPORTS_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @Test
    public void errorMessageReturned_when_getEventsRequests_and_setDataSelectionQueryParamWithoutManuallySelectedCompetitions() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.BASKETBALL.getValue());
        //date_selection field should be 'enabled' – when no competition is manually selected for the list and 'enabled' is set, an error message should be returned
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSelectionEnum.ENABLED.name().toLowerCase());
        queryParams.put(StringConstants.DATE_STRING, formattedDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.POPULAR_ICE_HOCKEY_STRING);

        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        expectedCompetitionListWithEventsResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("No event selection available for the provided configurations."));
    }

    @Test
    public void errorMessagesReturned_when_getEventsRequest_with_setInvalidDateSelectionProperties() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.DATE_SELECTION_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        expectedCompetitionListWithEventsResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid date selection. It must be one of the following: automatic, enabled, disabled"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsRequest_with_competitionIdsProperty(MultiSportSportsEnum sport) {
        CompetitionListModel competitionList = createCompetitionList(sport);
        createCompetitionListWithEventsByCurrentDate(sport, competitionList);
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());
        competitionListCode = competitionList.getCode();

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionList.getCompetitions().get(0).getCompetition().getId());
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @Test
    public void multiSportEventsReturned_when_getEventsRequest_with_setInvalidCompetitionIdsProperty() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        boolean areCompetitionsEmpty = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty();

        Assertions.assertTrue(areCompetitionsEmpty, "Expected competitions list to be empty, but it contained %s elements");
    }

    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"INVALID"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsRequest_with_utcOffsetProperty(SupportedSports sport) {
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.DATE_STRING, getFormattedDateForSport(sport));
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @Test
    public void multiSportEventsReturned_when_getEventsRequest_with_setInvalidUtcOffsetProperty() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        expectedCompetitionListWithEventsResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid utc_offset."));
    }

    //@Issue("SBE-3410")
    @ParameterizedTest
    @EnumSource(value = Language.class, names = {"ENGLISH", "BULGARIAN", "GERMAN", "FRENCH", "SPANISH"})
    public void multiSportEventsReturned_when_getEventsRequest_with_setTranslationLanguageProperty(Language language) {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, language.getCode());
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        expectedCompetitionListWithEventsResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);

        CompetitionWithEventsModel competitionWithEventsModel = expectedCompetitionListWithEventsResponse.getResult().getCompetitions()
                .stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations()
                        .stream().anyMatch(t -> t.getLanguage().equals(language.getCode()) && t.getName() != null))
                .findAny()
                .orElseThrow(() -> new AssertionError("No competitions found with %s translations for country name".formatted(language.getValue())));

        Assertions.assertNotNull(competitionWithEventsModel, "The competition with %s translation should not be null".formatted(language.getCode()));
    }

    @ParameterizedTest
    @EnumSource(value = OddsMarketTypeEnum.class, names = {"ONE_TWO"}, mode = EnumSource.Mode.EXCLUDE)
    public void specifiedMarketTypeReturned_when_getEventsSearchRequest_and_setMarketTypeQueryParam(OddsMarketTypeEnum marketType) {
        //Here, the UUID is hardcoded because the backend team set up one event with a past date containing all possible market types.
        //Since the date is dynamic, it's possible that sometimes one of the markets might be missing, making the test flaky.
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(FOOTBALL_COMPETITION_EVENT_UUID));
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId());

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(0).getStartTime()));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, marketType.getName());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        List<OddsModel> oddsList = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null)
                .flatMap(event -> event.getOdds().stream())
                .toList();
        boolean marketTypeFound = oddsList.stream()
                .anyMatch(odds -> odds.getMarkets().stream()
                        .anyMatch(market -> market.getType().getCode().equalsIgnoreCase(marketType.getName())));

        assertExpectedApiResponse(marketTypeFound, "Expected market type is not contained in odds, Expected " + marketType.getName());
    }

    @ParameterizedTest
    @EnumSource(value = EventStatusType.class, names = {"UNKNOWN", "FINISHED", "CANCELLED", "INTERRUPTED", "POSTPONED"}, mode = EnumSource.Mode.EXCLUDE)
    public void eventsWithActiveStatusReturned_when_setStatusTypeQueryParam(EventStatusType statusType) {
        //The date should be static because if it's dynamic, the test may sometimes fail for status type LIVE.
        //This happens because the data from Enetpulse may not be updated on time, and some matches that are actually live
        //could still appear as NOT_STARTED in our system.
        eventsWithStatusType("2025-05-09", statusType);
    }

    @ParameterizedTest
    @EnumSource(value = EventStatusType.class, names = {"UNKNOWN", "LIVE", "NOT_STARTED"}, mode = EnumSource.Mode.EXCLUDE)
    public void eventsWithSpecifiedStatusReturned_when_setStatusTypeQueryParam(EventStatusType statusType) {
        //The date query here should be static to ensure that for this date we have matches with the following status
        eventsWithStatusType("2025-04-21", statusType);
    }

    @ParameterizedTest
    @EnumSource(DataOddsMarketValueTypeEnum.class)
    public void multiSportEventsWithOddsFormatReturned_when_getEventsRequest_and_setOddFormatQueryParam(DataOddsMarketValueTypeEnum oddFormat) {
        //Here, the UUID is hardcoded because the backend team set up one event with a past date containing all possible market types.
        //Since the date is dynamic, it's possible that sometimes one of the markets might be missing, making the test flaky.
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(FOOTBALL_COMPETITION_EVENT_UUID));
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId());
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromIsoDateTime(sportSearchApiEvents.get(0).getStartTime()));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);

        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, oddFormat.name());
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        String actualOddsValue = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .findAny()
                .map(event -> event.getOdds().get(0).getMarkets().get(0).getSelections().get(0).getOdds())
                .orElseThrow(() -> new RuntimeException("No events with odds found"));

        assertOddsFormat(oddFormat.name(), actualOddsValue);
    }

    @Test
    public void eventsWithSpecifiedStatusReturned_when_setScopeTypeQueryParam() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        List<OddsModel> oddsList = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allMarketsHaveCorrectScopeType = oddsList.stream()
                .flatMap(odds -> odds.getMarkets().stream())
                .allMatch(market -> market.getScope().getType().equalsIgnoreCase(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));

        assertExpectedApiResponse(allMarketsHaveCorrectScopeType, "Not all markets have the expected scope type. Expected:%s".formatted(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));
    }

    @Test
    public void marketsWithSpecifiedOddTypeReturned_when_getEventsSearchRequest_and_setOddTypeQueryParam() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        List<OddsModel> oddsList = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allOddsHaveCorrectType = oddsList.stream()
                .allMatch(odds -> odds.getType().equals(OddTypeEnum.PRE_EVENT.name()));

        assertExpectedApiResponse(allOddsHaveCorrectType, "Not all odds have the expected type. Expected: " + OddTypeEnum.PRE_EVENT.name());
    }

    @Test
    public void emptyCompetitionsReturned_when_invalidStatusTypeIsSet() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        expectedCompetitionListWithEventsResponse.getResponse().then().statusCode(HttpStatus.SC_BAD_REQUEST).body(StringConstants.MESSAGE_STRING, Matchers.equalTo("invalid is an illegal status name"));
    }

    @Test
    public void emptyMultiSportEventsReturned_when_getEventsRequest_with_setInvalidTranslationLanguageProperty() {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        boolean areCompetitionsEmpty = expectedCompetitionListWithEventsResponse.getResult().getCompetitions().isEmpty();

        Assertions.assertTrue(areCompetitionsEmpty, "Expected competitions list to be empty, but it contained %s elements");
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsListRequest_with_requiredProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsList(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @Test
    @Description("Test verifies that different competition lists return different sets of competitions")
    public void multiSportEventsReturned_when_getEventsCompetitions_and_setCompetitionListQueryParam() {
        String specificCompetitionList = StringConstants.MULTI_QA;
        String allCompetitionsList = StringConstants.ALL_STRING;

        preQueryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        preQueryParams.put(StringConstants.DATE_SELECTION_STRING, DateSelectionEnum.AUTOMATIC.name().toLowerCase());
        preQueryParams.put(StringConstants.DATE_STRING, formattedDate);
        preQueryParams.put(StringConstants.COMPETITION_LIST_STRING, specificCompetitionList);
        ApiResponse<EventsResponseModel> competitionListWithMultiQaCompetitionList = multiSportEventsHttpRepo.getCompetitionListEvents(preQueryParams);

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSelectionEnum.AUTOMATIC.name().toLowerCase());
        queryParams.put(StringConstants.DATE_STRING, formattedDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, allCompetitionsList);
        ApiResponse<EventsResponseModel> competitionListWithAllCompetitionList = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Assertions.assertNotEquals(competitionListWithMultiQaCompetitionList.getResult().getCompetitions(),
                competitionListWithAllCompetitionList.getResult().getCompetitions(),
                "Competition lists should be different when using %s versus %s competition_list filter".formatted(specificCompetitionList, allCompetitionsList));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsListRequest_without_utcOffsetProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsList(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    @DisplayName("Test Events List with UTC Offset and automatic retry for next day")
    @Description("Verifies that events list with UTC offset properties returns data, with retry logic for the next day if no events are found")
    public void multiSportEventsReturned_when_getEventsListRequest_with_utcOffsetProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsListWithRetry(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        List<EventListByDateModel> eventsList = expectedCompetitionListWithEventsResponse.getResult();
        Assertions.assertFalse(eventsList.isEmpty(),
                AssertMessages.responseNotContains("Events List"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEvents().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    //@Issue("SBE-3410")
    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT", "TENNIS"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsListRequest_with_translationLanguageProperties(MultiSportSportsEnum sport) {
        String expectedLanguage = Language.GERMAN.getCode();
        CompetitionListModel competitionList = createCompetitionList(sport);
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());
        competitionListCode = competitionList.getCode();

        String countryNameTranslated = competitionList.getCompetitions().stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations().stream().anyMatch(t -> t.getLanguage().equals(expectedLanguage) && t.getName() != null))
                .findFirst()
                .flatMap(c -> c.getCompetition().getCountry().getTranslations().stream().filter(t -> t.getLanguage().equals(expectedLanguage)).findFirst())
                .map(TranslationModel::getName)
                .orElseThrow(() -> new NoSuchElementException("No translation found for language: " + expectedLanguage));

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, expectedLanguage);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsList(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertTrue(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().stream().anyMatch(e -> e.getCompetition().getCountry().getName().contains(countryNameTranslated)),
                AssertMessages.responseNotContains("Country name is not translated"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsCompetitionsRequest_with_requiredProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        preQueryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        preQueryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        preQueryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        int expectedEventsCount = multiSportEventsHttpRepo.getCompetitionListWithEventsList(preQueryParams).getResult().get(0).getCompetitions().get(0).getEvents().size();

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.DATE_STRING, eventStartTime);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsCompetitions(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertEquals(expectedEventsCount, expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEventCount(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsCompetitionsRequest_with_dateSelectProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        preQueryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        preQueryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        preQueryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        int expectedEventsCount = multiSportEventsHttpRepo.getCompetitionListWithEventsList(preQueryParams).getResult().get(0).getCompetitions().get(0).getEvents().size();

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.DATE_STRING, eventStartTime);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsCompetitions(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertEquals(expectedEventsCount, expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEventCount(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsCompetitionsRequest_with_utcOffsetProperties(MultiSportSportsEnum sport) {
        competitionListCode = createCompetitionList(sport).getCode();
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());

        preQueryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        preQueryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        preQueryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        int expectedEventsCount = multiSportEventsHttpRepo.getCompetitionListWithEventsList(preQueryParams).getResult().get(0).getCompetitions().get(0).getEvents().size();

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSlection.AUTOMATIC.getValue());
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsCompetitions(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().isEmpty(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
        Assertions.assertEquals(expectedEventsCount, expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().get(0).getEventCount(),
                AssertMessages.responseNotContains("Competitions Lists With Events object"));
    }

    //@Issue("SBE-3410")
    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT", "TENNIS"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsCompetitionsRequest_with_translationLanguageProperties(MultiSportSportsEnum sport) {
        String expectedLanguage = Language.GERMAN.getCode();
        CompetitionListModel competitionList = createCompetitionList(sport);
        String eventStartTime = getDateFromIsoDateTime(getEventWithStartTime(sport).getStartTime());
        competitionListCode = competitionList.getCode();

        String countryNameTranslated = competitionList.getCompetitions().stream()
                .filter(a -> a.getCompetition().getCountry().getTranslations().stream().anyMatch(t -> t.getLanguage().equals(expectedLanguage) && t.getName() != null))
                .findFirst()
                .flatMap(c -> c.getCompetition().getCountry().getTranslations().stream().filter(t -> t.getLanguage().equals(expectedLanguage)).findFirst())
                .map(TranslationModel::getName)
                .orElseThrow(() -> new NoSuchElementException("No translation found for language: " + expectedLanguage));

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.SELECTION_DATE_STRING, LocalDate.now().toString());
        queryParams.put(StringConstants.FILTER_DATE_STRING, eventStartTime);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, expectedLanguage);
        ApiResponse<List<EventListByDateModel>> expectedCompetitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListWithEventsList(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, expectedCompetitionListWithEventsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertTrue(expectedCompetitionListWithEventsResponse.getResult().get(0).getCompetitions().stream().anyMatch(e -> e.getCompetition().getCountry().getName().contains(countryNameTranslated)),
                AssertMessages.responseNotContains("Country name is not translated"));
    }

    @ParameterizedTest
    @MethodSource("datesWithCompetitionListsProviderPerSport")
    public void multiSportEventsReturned_when_getMultiSportEvents_and_setSportQueryParamEqualToAll(DataPopularListEnum popularListEnum, Instant dateTimeUTC, SupportedSports sport) {
        String competitionList = popularListEnum.getValue();
        String competitionId = multiSportCompetitionListHttpRepo.getCompetitionFromCompetitionList(competitionList).getId();

        createMultipleEvents(dateTimeUTC, sport, competitionId);

        queryParams.put(StringConstants.DATE_STRING, DateUtils.getDateFromUTC(dateTimeUTC).toString());
        queryParams.put(StringConstants.SPORT_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionList);

        ApiResponse<EventsResponseModel> competitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        competitionListWithEventsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        EventsResponseModel eventListResults = competitionListWithEventsResponse.getResult();
        EventListModel eventFromCompetitionList = eventListResults.getCompetitions().stream()
                .filter(c -> c.getCompetition().getId().equals(competitionId))
                .findFirst()
                .orElseThrow()
                .getEvents()
                .stream()
                .filter(e -> e.getId().equals(createdEvent.getId()))
                .findFirst()
                .orElseThrow(() -> new AssertionError("Event with id [%s] not found in competition list".formatted(createdEvent.getId())));

        Assertions.assertEquals(StringConstants.ALL_STRING, eventListResults.getSport(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.SPORT_STRING));
        Assertions.assertAll(
                () -> Assertions.assertEquals(createdEvent.getId(), eventFromCompetitionList.getId(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.ID_STRING)),
                () -> Assertions.assertEquals(createdEvent.getCompetitionId(), eventFromCompetitionList.getCompetitionId(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.COMPETITION_ID_STRING)),
                () -> Assertions.assertEquals(createdEvent.getStartTime(), eventFromCompetitionList.getStartTime(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.START_TIME_STRING)),
                () -> Assertions.assertEquals(createdEvent.getSport(), eventFromCompetitionList.getSport(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.SPORT_STRING)),
                () -> Assertions.assertEquals(createdEvent.getParticipants(), eventFromCompetitionList.getParticipants(), AssertMessages.valueOfPropertyNotCorrect("participants"))
        );
    }

    @Test
    public void multiSportEventsFromAllSportsReturned_when_getMultiSportEvents_and_setSportParamEqualToAll_and_competitionListWithAllSports_and_dateWithEventsFromAllSports() {
        Set<String> expectedSports = Set.of(SupportedSports.FOOTBALL.getValue(), SupportedSports.BASKETBALL.getValue(),
                SupportedSports.TENNIS.getValue(), SupportedSports.ICE_HOCKEY.getValue());

        queryParams.put(StringConstants.DATE_STRING, "2024-01-28");
        queryParams.put(StringConstants.SPORT_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, DataPopularListEnum.POPULAR_SPORTS.getValue());

        ApiResponse<EventsResponseModel> competitionListWithEventsResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        competitionListWithEventsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        Set<String> actualSports = competitionListWithEventsResponse.getResult().getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .map(EventListModel::getSport)
                .collect(Collectors.toSet());

        Assertions.assertEquals(expectedSports, actualSports, AssertMessages.entityNotExpected("sports"));
    }

    @Test
    public void expectedEventsReturned_when_getEvents_and_setDateSelectionPropertyEqualToEnabled() {
        String competitionId = searchV2SuggestRepo.getTeamsByName(BasketballTeamEnum.LA_LAKERS.getName(), SupportedSports.BASKETBALL)
                .stream()
                .findFirst()
                .orElseThrow(() -> new AssertionError("Team with name [%s] not found".formatted(BasketballTeamEnum.LA_LAKERS.getName())))
                .getCompetitionIds()
                .stream()
                .findFirst()
                .orElseThrow(() -> new AssertionError("Competition id not found for team [%s]".formatted(BasketballTeamEnum.LA_LAKERS.getName())));

        CreateCompetitionsListModel requestBody = multiSportHttpFactory.buildDefaultCompetitionList(MultiSportSportsEnum.BASKETBALL, competitionId);
        CompetitionListModel createdCompetitionList = multiSportCompetitionListHttpRepo.createCompetitionList(requestBody).getResult();
        competitionListCode = createdCompetitionList.getCode();
        preQueryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionId);
        preQueryParams.put(StringConstants.STATUS_TYPE_STRING, StatusType.NOT_STARTED);
        List<ResultModel> basketballEvents = searchV2EventsHttpRepo.getEventsBySport(SupportedSports.BASKETBALL.getValue(), preQueryParams);

        String expectedEventId = basketballEvents.stream()
                .filter(test -> test.getCompetition().getId() != null)
                .findFirst()
                .map(ResultModel::getId)
                .orElseThrow(() -> new AssertionError("No basketball events found with a valid competition ID"));

        String expectedDate = basketballEvents.stream()
                .map(ResultModel::getStartTime)
                .findFirst()
                .orElseThrow();
        String dateTime = getDateFromIsoDateTime(expectedDate);

        CreateEventsByDateModel createEventsRequestBody = multiSportHttpFactory.buildDefaultEventsByDateList(SupportedSports.BASKETBALL.getValue(), Collections.singleton(expectedEventId));
        multiSportEventsHttpRepo.createCompetitionListWithEventsByDate(createEventsRequestBody, competitionListCode, dateTime);

        queryParams.put(StringConstants.COMPETITION_LIST_STRING, competitionListCode);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSelectionEnum.ENABLED.name().toLowerCase());
        queryParams.put(StringConstants.DATE_STRING, dateTime);
        ApiResponse<EventsResponseModel> competitionListWithEvents = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);
        competitionListWithEvents.getResponse().then().statusCode(HttpStatus.SC_OK);

        String actualEventIds = competitionListWithEvents.getResult().getCompetitions().stream()
                .flatMap(e -> e.getEvents().stream()
                        .map(EventListModel::getId))
                .findFirst()
                .orElseThrow(() -> new AssertionError("No events found in the competition list response"));

        Assertions.assertEquals(List.of(expectedEventId), List.of(actualEventIds), AssertMessages.entityNotExpected("events"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"BASKETBALL", "TENNIS"}, mode = EnumSource.Mode.INCLUDE)
    public void expectedEventReturned_when_getEventBySlug(MultiSportSportsEnum sport) {
        ResultModel expectedEvent = searchV2EventsHttpRepo.getEventsBySport(sport.getValue(), queryParams).stream()
                .findAny()
                .orElseThrow(() -> new AssertionError("No event found for sport [%s]".formatted(sport.getValue())));

        ApiResponse<EventListByDateModel> eventBySlugResponse = multiSportEventsHttpRepo.getById(expectedEvent.getSlug());
        eventBySlugResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        EventListByDateModel actualEvent = eventBySlugResponse.getResult();

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedEvent.getId(), actualEvent.getId(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.ID_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getName(), actualEvent.getName(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.NAME_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getName(), actualEvent.getName(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.NAME_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getSport(), actualEvent.getSport(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.SPORT_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getStatus(), actualEvent.getStatus(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.STATUS_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getParticipants(), actualEvent.getParticipants(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.PARTICIPANTS_STRING)),
                () -> Assertions.assertEquals(expectedEvent.getCompetition(), actualEvent.getCompetition(), AssertMessages.valueOfPropertyNotCorrect(StringConstants.COMPETITION_STRING))
        );
    }

    private static Stream<Arguments> queryParams() {
        return Stream.of(
                Arguments.of(MultiSportSportsEnum.BASKETBALL.getValue(), StringConstants.UTC_OFFSET_STRING, 2),
                Arguments.of(MultiSportSportsEnum.TENNIS.getValue(), StringConstants.UTC_OFFSET_STRING, 2),
                Arguments.of(MultiSportSportsEnum.MULTI_SPORT.getValue(), StringConstants.UTC_OFFSET_STRING, 2),

                Arguments.of(MultiSportSportsEnum.BASKETBALL.getValue(), StringConstants.COMPETITION_LIST_STRING, StringConstants.COMPETITION_LIST_STRING),
                Arguments.of(MultiSportSportsEnum.TENNIS.getValue(), StringConstants.COMPETITION_LIST_STRING, StringConstants.COMPETITION_LIST_STRING),
                Arguments.of(MultiSportSportsEnum.MULTI_SPORT.getValue(), StringConstants.COMPETITION_LIST_STRING, StringConstants.COMPETITION_LIST_STRING)
        );
    }

    private static Stream<Object[]> datesWithCompetitionListsProviderPerSport() {
        Instant yesterday = DateUtils.getYesterdayUTCFromNow();
        Instant today = DateUtils.getCurrentUTC();
        Instant tomorrow = DateUtils.getTomorrowUTCFromNow();

        List<Instant> dates = List.of(yesterday, today, tomorrow);

        Map<SupportedSports, DataPopularListEnum> sportsMap = Map.of(
                SupportedSports.ICE_HOCKEY, DataPopularListEnum.BASKET_ICE_HOCKEY_SPORTS,
                SupportedSports.BASKETBALL, DataPopularListEnum.BASKET_TENNIS_SPORTS,
                SupportedSports.TENNIS, DataPopularListEnum.TENNIS_BASKET_SPORTS
        );

        return sportsMap.entrySet().stream().flatMap(entry ->
                dates.stream().map(date -> new Object[]{entry.getValue(), date, entry.getKey()})
        );
    }

    private void requiredQueryParams(SupportedSports sport) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());
        queryParams.put(StringConstants.DATE_STRING, formattedDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
    }

    private void eventsWithStatusType(String date, EventStatusType statusType) {
        requiredQueryParams(SupportedSports.FOOTBALL);
        queryParams.put(StringConstants.DATE_STRING, date);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, statusType.name());

        ApiResponse<EventsResponseModel> response = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        Boolean hasEvents = response.getResult().getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .map(event -> event.getStatusType().equals(statusType.name()))
                .findFirst()
                .orElseThrow(() -> new AssertionError("No competitions found with status type %s".formatted(statusType.name())));

        Assertions.assertTrue(hasEvents, "No events were returned for status type: %s on date: %s"
                .formatted(statusType.name(), date));
    }

    private void createMultipleEvents(Instant dateTimeUTC, SupportedSports sport, String competitionId) {
        if (sport.equals(SupportedSports.ICE_HOCKEY)) {
            createdEvent = searchV2EventHttpRepo.createEvent(SupportedSports.ICE_HOCKEY, competitionId, dateTimeUTC);
            createdEvent = searchV2EventHttpRepo.createEvent(SupportedSports.BASKETBALL, competitionId, dateTimeUTC);
        } else {
            createdEvent = searchV2EventHttpRepo.createEvent(SupportedSports.BASKETBALL, competitionId, dateTimeUTC);
            createdEvent = searchV2EventHttpRepo.createEvent(SupportedSports.TENNIS, competitionId, dateTimeUTC);
        }
    }
}