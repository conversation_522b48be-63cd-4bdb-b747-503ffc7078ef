package multisportapi.events;

import categories.APITags;
import categories.SMPCategories;
import data.constants.EventStatusType;
import data.constants.Language;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.multisport.DateSlection;
import data.constants.enums.odds.OddFormatEnum;
import data.constants.enums.odds.OddTypeEnum;
import data.constants.enums.odds.OddsMarketTypeEnum;
import data.constants.enums.odds.OddsScopeTypeEnum;
import data.models.multisportapi.EventsResponseModel;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiEventsSchemaTests extends BaseMultiSportApiEventsTests {

    @Test
    public void multiSportEventsReturned_when_getMultiSportEventsRequest_and_setAllQueryParams() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.DATE_SELECTION_STRING, DateSlection.AUTOMATIC.getValue());
        queryParams.put(StringConstants.DATE_STRING, formattedDate);
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        queryParams.put(StringConstants.UTC_OFFSET_STRING, "+2");
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, OddsMarketTypeEnum.ONE_X_TWO.getName());
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.DECIMAL);
        queryParams.put(StringConstants.STATUS_TYPE_STRING, EventStatusType.NOT_STARTED);
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<EventsResponseModel> eventListByDateModelApiResponse = multiSportEventsHttpRepo.getCompetitionListEvents(queryParams);

        multiSportEventsHttpRepo.assertResponseSchema(eventListByDateModelApiResponse.getResponse());
    }
}