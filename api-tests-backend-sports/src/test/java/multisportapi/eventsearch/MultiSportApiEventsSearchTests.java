package multisportapi.eventsearch;

import categories.APITags;
import categories.SMPCategories;
import data.constants.*;
import data.constants.enums.odds.OddFormatEnum;
import data.constants.enums.odds.OddTypeEnum;
import data.constants.enums.odds.OddsMarketTypeEnum;
import data.constants.enums.odds.OddsScopeTypeEnum;
import data.constants.liveblog.MarketType;
import data.models.multisportapi.EventListModel;
import data.models.multisportapi.EventsResponseModel;
import data.models.oddsapi.OddsModel;
import data.models.searchapi.ResultModel;
import data.widgets.options.enums.DataOddsMarketValueTypeEnum;
import io.qameta.allure.Description;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Stream;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.MULTI_SPORT_API)
@Story(APITags.MULTI_SPORT_API)
public class MultiSportApiEventsSearchTests extends BaseMultiSportApiEventsSearchTests {

    private static final String EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING =
            "Expected competitions list to be empty, but it contained %s elements";

    @Test
    @Description("Validate that events returned by the Multi Sport API and Search API are identical when both APIs are queried with the same limit parameter")
    public void multiSportApiAndSearchApiEventsAreEqual_when_setLimitQueryParam() {
        queryParams.put(StringConstants.LIMIT_STRING, 50);
        EventsResponseModel eventSearchResponseModelApiResponse = getEventSearchResponseModelApiResponse();
        List<EventListModel> allMultiSportEvents = eventSearchResponseModelApiResponse.getCompetitions()
                .stream()
                .flatMap(competition -> competition.getEvents().stream())
                .toList();
        List<ResultModel> searchApiEvents = searchEventsHttpRepo.getAll().getResult();

        int searchApiEventsSize = searchApiEvents.size();
        int multiSportEventsSize = allMultiSportEvents.size();

        Assertions.assertEquals(searchApiEventsSize, multiSportEventsSize, "The size of the events is not equal, Expected %s but was %s".formatted(searchApiEventsSize, multiSportEventsSize));
        for (int i = 0; i < searchApiEventsSize; i++) {
            ResultModel searchApiEvent = searchApiEvents.get(i);
            EventListModel eventListModel = allMultiSportEvents.get(i);
            EntitiesAsserter.assertAreEqual(searchApiEvent, eventListModel, StringConstants.ODDS_STRING, StringConstants.RESULTS_STRING, StringConstants.CLASS_STRING);
        }
    }

    @Test
    public void multiSportEventsReturned_when_getEventsSearchRequest_and_setAllQueryParams() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.FROM_START_TIME, FROM_DATE);
        queryParams.put(StringConstants.TO_START_TIME, TO_DATE);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.SEASON_IDS_UNDERSCORED_STRING, String.join(",", seasonIds));
        queryParams.put(StringConstants.STAGE_IDS_UNDERSCORED_STRING, String.join(",", stageIds));
        queryParams.put(StringConstants.IDS_STRING, String.join(",", eventIds));
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, String.join(",", participantIds));
        queryParams.put(StringConstants.SORT_DIRECTION_STRING, SortDirectionEnum.ASCENDING.getStorybookValue().toUpperCase());
        queryParams.put(StringConstants.LIMIT_STRING, 100);
        queryParams.put(StringConstants.OFFSET_STRING, "0");
        queryParams.put(StringConstants.STATUS_TYPE_STRING, EventStatusType.NOT_STARTED);
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, MarketType.ONE_X_TWO.getValue());
        queryParams.put(StringConstants.ODD_FORMAT_STRING, OddFormatEnum.DECIMAL);
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);
        eventSearchResponseModelApiResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        EventsResponseModel result = eventSearchResponseModelApiResponse.getResult();
        ZonedDateTime fromDateTime = ZonedDateTime.parse(FROM_DATE);
        ZonedDateTime toDateTime = ZonedDateTime.parse(TO_DATE);
        boolean allEventsInRange = result.getCompetitions().stream()
                .filter(comp -> !comp.getEvents().isEmpty())
                .allMatch(comp -> comp.getEvents().stream()
                        .allMatch(event -> {
                            ZonedDateTime eventTime = ZonedDateTime.parse(event.getStartTime());
                            return !eventTime.isBefore(fromDateTime) && !eventTime.isAfter(toDateTime);
                        }));

        Assertions.assertAll(
                () -> Assertions.assertEquals(competitionIds.get(0), result.getCompetitions().get(0).getCompetition().getId(),
                        "Competition ID mismatch: Expected %s but got %s".formatted(competitionIds.get(0), result.getCompetitions().get(0).getCompetition().getId())),
                () -> Assertions.assertEquals(seasonIds.get(0), result.getCompetitions().get(0).getEvents().get(0).getSeason().getId(),
                        "Season ID mismatch: Expected %s but got %s".formatted(seasonIds.get(0), result.getCompetitions().get(0).getEvents().get(0).getSeason().getId())),
                () -> Assertions.assertEquals(stageIds.get(0), result.getCompetitions().get(0).getEvents().get(0).getStage().getId(),
                        "Stage ID mismatch: Expected %s but got %s".formatted(stageIds.get(0), result.getCompetitions().get(0).getEvents().get(0).getStage().getId())),
                () -> Assertions.assertEquals(roundId.get(0), result.getCompetitions().get(0).getEvents().get(0).getRound().getId(),
                        "Round ID mismatch: Expected %s but got %s".formatted(roundId.get(0), result.getCompetitions().get(0).getEvents().get(0).getRound().getId())),
                () -> Assertions.assertTrue(allEventsInRange, "All events should have start times between %s and %s".formatted(FROM_DATE, TO_DATE)),
                () -> Assertions.assertEquals(eventIds.size(), result.getCompetitions().get(0).getEvents().size(),
                        "Event count should be the same but wasn't, Expected %s, but was %s".formatted(eventIds.size(), result.getCompetitions().get(0).getEvents().size())),
                () -> Assertions.assertEquals(SupportedSports.FOOTBALL.getValue(), result.getSport(),
                        "Sport mismatch: Expected %s but got %s".formatted(SupportedSports.FOOTBALL.getValue(), result.getSport())),
                () -> Assertions.assertTrue(result.getCompetitions().get(0).getEvents().stream()
                                .anyMatch(event -> event.getParticipants().containsAll(participantIds)),
                        "Participant filter not applied correctly, expected participant %s not found".formatted(eventsWithOdds.get(0).getHomeTeam().getUuid())),
                () -> Assertions.assertTrue(result.getCompetitions().get(0).getEvents().stream()
                                .allMatch(event -> event.getStatusType().equals(EventStatusType.NOT_STARTED.toString())),
                        "Status type filter not applied correctly, expected all events to have status %s".formatted(EventStatusType.NOT_STARTED))
        );
    }

    //@Issue("PLT-250")
    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"INVALID"}, mode = EnumSource.Mode.EXCLUDE)
    public void multiSportEventsReturned_when_getEventsSearchRequest_and_requiredSportPropertyIsSet(SupportedSports sport) {
        queryParams.put(StringConstants.SPORT_STRING, sport.getValue());

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        Assertions.assertEquals(sport.getValue(), eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getSport(),
                "Sport in response should match the requested sport value; should be %s but was %s"
                        .formatted(sport.getValue(), eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getSport()));
    }

    @Test
    public void emptyCompetitionsReturned_when_invalidSportPropertyIsSet() {
        queryParams.put(StringConstants.SPORT_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING.formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    public void multiSportEventsReturned_when_getEventsSearchRequest_and_translationLanguageIsSet() {
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, Language.ENGLISH.getCode());
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        Assertions.assertAll(
                () -> Assertions.assertNotNull(eventSearchResponseModelApiResponse.getCompetitions(), "Competitions should not be null"),
                () -> Assertions.assertFalse(eventSearchResponseModelApiResponse.getCompetitions().isEmpty(), "Competitions list should not be empty"),
                () -> Assertions.assertNotNull(eventSearchResponseModelApiResponse.getCompetitions().get(0), "Events should not be null"),
                () -> Assertions.assertFalse(eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().isEmpty(), "Events list should not be empty"));
    }

    @Test
    public void emptyCompetitionsReturned_when_invalidLanguageProvided() {
        queryParams.put(StringConstants.TRANSLATION_LANGUAGE_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING.formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    @Description("Event count should be the same when using 'competition_list=all' compared to making a request without query parameters")
    public void sameEventsReturned_when_competitionListAllRequested_and_noParametersRequested() {
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);
        ApiResponse<EventsResponseModel> eventSearchResponseWithoutQueryParamsApiResponse = multiSportEventsSearchHttpRepo.get();
        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);

        int sizeWithCompetitionFilter = eventSearchResponseModelApiResponse.getResult().getCompetitions().get(0).getEvents().size();
        int sizeWithoutCompetitionFilter = eventSearchResponseWithoutQueryParamsApiResponse.getResult().getCompetitions().get(0).getEvents().size();

        Assertions.assertEquals(sizeWithCompetitionFilter, sizeWithoutCompetitionFilter,
                "Event count should be the same but wasn't, Expected %s, but was %s".formatted(sizeWithCompetitionFilter, sizeWithoutCompetitionFilter));
    }

    @Test
    public void errorMessageReturned_when_getEventsSearchRequest_and_invalidCompetitionListIsSet() {
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.INVALID_STRING);

        validateErrorResponseWithMessage(multiSportEventsSearchHttpRepo, HttpStatus.SC_NOT_FOUND, "There is no competition list with identifier %s".formatted(StringConstants.INVALID_STRING));
    }

    @Test
    @Description("Verifies that when both competition_ids and competition_list parameters are provided in the request, the API prioritizes the competition_ids parameter.")
    public void competitionIdsParameterTakesPriority_when_bothCompetitionIdsAndCompetitionListAreSet() {
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.ALL_STRING);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<String> returnedCompetitionIds = eventSearchResponseModelApiResponse.getCompetitions()
                .stream()
                .map(comp -> comp.getCompetition().getId())
                .toList();

        assertExpectedApiResponse(returnedCompetitionIds.containsAll(competitionIds),
                "Not all specified competitionIds were returned in the response");
        Assertions.assertEquals(competitionIds.size(), returnedCompetitionIds.size(),
                "Number of competitions in response doesn't match number of specified competitionIds");
    }

    @Test
    public void errorMessageReturn_when_invalidCompetitionListIsSet() {
        queryParams.put(StringConstants.COMPETITION_LIST_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);
        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("There is no competition list with identifier %s".formatted(StringConstants.INVALID_STRING)));
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 10, 100})
    public void multiSportEventsReturned_when_getEventsSearchRequest_and_setLimitQueryParam(int limit) {
        queryParams.put(StringConstants.LIMIT_STRING, limit);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);
        int size = eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().size();

        Assertions.assertEquals(limit, size, "Expected %s events when limit parameter is set to %s, but found %s events instead".formatted(limit, limit, size));
    }

    @Test
    public void emptyCompetitionsReturned_when_getEventsSearchRequest_and_setLimitQueryParamToZero() {
        queryParams.put(StringConstants.LIMIT_STRING, 0);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionIds);

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING
                .formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    public void multiSportEventsWithinDateRangeReturned_when_setDateRangeQueryParam() {
        String TO_START_DATE = "2025-06-01T13:59:59-00:00";
        queryParams.put(StringConstants.FROM_START_TIME, FROM_DATE);
        queryParams.put(StringConstants.TO_START_TIME, TO_START_DATE);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        ZonedDateTime fromDateTime = ZonedDateTime.parse(FROM_DATE);
        ZonedDateTime toDateTime = ZonedDateTime.parse(TO_START_DATE);

        boolean allEventsInRange = eventSearchResponseModelApiResponse.getCompetitions().stream()
                .filter(comp -> !comp.getEvents().isEmpty())
                .allMatch(comp -> comp.getEvents().stream()
                        .allMatch(event -> {
                            ZonedDateTime eventTime = ZonedDateTime.parse(event.getStartTime());
                            return !eventTime.isBefore(fromDateTime) && !eventTime.isAfter(toDateTime);
                        }));

        assertExpectedApiResponse(allEventsInRange, "All events should have start times between %s and %s".formatted(FROM_DATE, TO_START_DATE));
    }

    @ParameterizedTest
    @MethodSource("invalidDateScenarios")
    public void emptyCompetitionsReturned_when_setInvalidDateRangeQueryParam(String fromDate, String toDate) {
        queryParams.put(StringConstants.FROM_START_TIME, fromDate);
        queryParams.put(StringConstants.TO_START_TIME, toDate);

        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse =
                multiSportEventsSearchHttpRepo.get(queryParams);
        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getResult().getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING.formatted(eventSearchResponseModelApiResponse.getResult().getCompetitions().size()));
    }

    @Test
    public void multipleEventsReturned_when_getEventsSearchRequest_and_setQueryCompetitionIdsWithMoreThanOneEvent() {
        competitionIds = List.of(sportSearchApiEvents.get(0).getCompetitionId(), sportSearchApiEvents.get(1).getCompetitionId());
        queryParams.put(StringConstants.LIMIT_STRING, 500);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        int competitionsCount = eventSearchResponseModelApiResponse.getCompetitions().size();
        List<String> returnedCompetitionIds = eventSearchResponseModelApiResponse
                .getCompetitions().stream()
                .map(comp -> comp.getCompetition().getId())
                .toList();

        assertExpectedApiResponse(competitionsCount > 1, "Expected more than one competition in response, but found only %s".formatted(competitionsCount));
        assertExpectedApiResponse(returnedCompetitionIds.containsAll(competitionIds), "Response does not contain all competition IDs that were specified in query");
    }

    @Test
    public void multipleEventsReturned_when_getEventsSearchRequest_and_setSeasonsIdsWithMoreThanOneEvent() {
        seasonIds = List.of(sportSearchApiEvents.get(0).getSeason().getId(), sportSearchApiEvents.get(1).getSeason().getId());
        // The limit should be set to more than 600 because this query retrieves events from two season IDs, resulting in a large number of events.
        queryParams.put(StringConstants.LIMIT_STRING, 650);
        queryParams.put(StringConstants.SEASON_IDS_UNDERSCORED_STRING, String.join(",", seasonIds));

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<String> returnedSeasonIds = eventSearchResponseModelApiResponse.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .map(event -> event.getSeason().getId())
                .toList();

        assertExpectedApiResponse(returnedSeasonIds.size() > 1,
                "Expected more than one season in response, but found only %s".formatted(returnedSeasonIds));
        assertExpectedApiResponse(returnedSeasonIds.containsAll(seasonIds),
                "Response does not contain all the season IDs. Expected: %s, but was not in response".formatted(seasonIds));
    }

    @Test
    public void multipleEventsReturned_when_getEventsSearchRequest_and_setStageIdsWithMoreThanOneEvent() {
        stageIds = List.of(sportSearchApiEvents.get(0).getStage().getId(), sportSearchApiEvents.get(1).getStage().getId());
        queryParams.put(StringConstants.LIMIT_STRING, 300);
        queryParams.put(StringConstants.STAGE_IDS_UNDERSCORED_STRING, String.join(",", stageIds));
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<String> returnedStageIds = eventSearchResponseModelApiResponse.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .map(event -> event.getStage().getId())
                .toList();

        assertExpectedApiResponse(returnedStageIds.size() > 1, "Expected more than one stage in response, but found only %s".formatted(returnedStageIds));
        assertExpectedApiResponse(returnedStageIds.containsAll(stageIds), "Response does not contain all the season IDs. Expected: %s, but was not in response".formatted(seasonIds));
    }

    @Test
    public void multipleEventsReturned_when_getEventsSearchRequest_and_setEventIdsWithMoreThanOneEvent() {
        competitionIds = List.of(eventsWithOdds.get(0).getSeason().getTournament().getUuid(), eventsWithOdds.get(3).getSeason().getTournament().getUuid());
        eventIds = List.of(eventsWithOdds.get(0).getUuid(), eventsWithOdds.get(3).getUuid());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, String.join(",", competitionIds));
        queryParams.put(StringConstants.IDS_STRING, String.join(",", eventIds));
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<String> returnedEventIds = eventSearchResponseModelApiResponse.getCompetitions().stream()
                .flatMap(event -> event.getEvents().stream())
                .map(EventListModel::getId)
                .toList();

        assertExpectedApiResponse(returnedEventIds.size() > 1,
                "Expected more than one event in response, but found only %s".formatted(returnedEventIds.size()));
        assertExpectedApiResponse(returnedEventIds.containsAll(eventIds),
                "Response does not contain all event IDs that were specified in query:Expected: %s, but was not in response ".formatted(returnedEventIds));
    }

    @Test
    public void multipleEventsReturned_when_getEventsSearchRequest_and_setRoundFilterWithMoreThanOneEvent() {
        roundId = List.of(
                sportSearchApiEvents.get(0).getStage().getId() + ":" + sportSearchApiEvents.get(0).getRound().getId(),
                sportSearchApiEvents.get(1).getStage().getId() + ":" + sportSearchApiEvents.get(1).getRound().getId());
        queryParams.put(StringConstants.ROUND_FILTER_STRING, String.join(",", roundId));
        queryParams.put(StringConstants.LIMIT_STRING, 500);

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);
        int competitionsCount = eventSearchResponseModelApiResponse.getCompetitions().size();

        assertExpectedApiResponse(competitionsCount > 1,
                "Expected more than 1 competitions in response, but found " + competitionsCount);
    }

    @Test
    public void multiSportEventsWithSingleRoundReturned_getEventsSearchRequest_and_setRoundFilterWithOneEvent() {
        roundId = List.of(eventsWithOdds.get(0).getStage().getUuid() + ":" + eventsWithOdds.get(0).getRound().getUuid());
        queryParams.put(StringConstants.ROUND_FILTER_STRING, String.join(",", roundId));
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        int competitionsCount = eventSearchResponseModelApiResponse.getCompetitions().size();

        Assertions.assertEquals(1, competitionsCount,
                "Expected 1 competition in response, but found " + competitionsCount);
    }

    @Test
    public void emptyCompetitionsReturned_invalidRoundFilterIsSet() {
        queryParams.put(StringConstants.ROUND_FILTER_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING
                .formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    @Description("When both stage_ids and round_filter parameters are provided in the same request, the API should return empty competitions as these parameters cannot be used together.")
    public void emptyCompetitionsReturned_getEventsSearchRequest_and_setRoundFilterAndStageIdsQueryParams() {
        queryParams.put(StringConstants.ROUND_FILTER_STRING, String.join(",", roundId));
        queryParams.put(StringConstants.STAGE_IDS_UNDERSCORED_STRING, String.join(",", stageIds));

        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);
        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING
                .formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @ParameterizedTest
    @EnumSource(DataOddsMarketValueTypeEnum.class)
    public void multiSportEventsWithOddsFormatReturned_when_getEventsSearchRequest_and_setOddFormatQueryParam(DataOddsMarketValueTypeEnum oddFormat) {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, oddFormat.name());
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        String actualOddsValue = eventSearchResponseModelApiResponse.getCompetitions().stream()
                .flatMap(competition -> competition.getEvents().stream())
                .filter(event -> event.getOdds() != null && !event.getOdds().isEmpty())
                .findAny()
                .map(event -> event.getOdds().get(0).getMarkets().get(0).getSelections().get(0).getOdds())
                .orElseThrow(() -> new RuntimeException("No events with odds found"));

        assertOddsFormat(oddFormat.name(), actualOddsValue);
    }

    @Test
    //@Issue("PLT-275")
    public void errorMessageReturned_when_invalidOddFormatIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_FORMAT_STRING, StringConstants.INVALID_STRING);

        multiSportEventsSearchHttpRepo.get(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid odd_format: invalid. Allowed values are: FRACTIONAL, DECIMAL, MONEYLINE"));
    }

    @Test
    public void multiSportEventsWithOddsReturned_when_getEventsSearchRequest_and_setOddClientQueryParam() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<OddsModel> odds = eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getOdds();

        Assertions.assertFalse(odds.isEmpty(), "Odds list should not be empty but was %s".formatted(odds.toString()));
    }

    @Test
    public void emptyOddsReturned_when_invalidOddClientIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<OddsModel> odds = eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getOdds();

        assertExpectedApiResponse(odds.isEmpty(), "Odds list should be empty but had elements: " + odds);
    }

    @ParameterizedTest
    @EnumSource(value = OddsMarketTypeEnum.class, names = {"ONE_TWO"}, mode = EnumSource.Mode.EXCLUDE)
    public void specifiedMarketTypeReturned_when_getEventsSearchRequest_and_setMarketTypeQueryParam(OddsMarketTypeEnum marketType) {
        //Here, the UUID is hardcoded because the backend team set up one event with a past date containing all possible market types.
        //Since the date is dynamic, it's possible that sometimes one of the markets might be missing, making the test flaky.
        sportSearchApiEvents = searchV2EventsHttpRepo.getSportEvents(List.of(FOOTBALL_COMPETITION_EVENT_UUID));
        eventIds = List.of(sportSearchApiEvents.get(0).getId());

        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, marketType.getName());
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<OddsModel> oddsList = eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean marketTypeFound = oddsList.stream()
                .anyMatch(odds -> odds.getMarkets().stream()
                        .anyMatch(market -> market.getType().getCode().equalsIgnoreCase(marketType.getName())));

        assertExpectedApiResponse(marketTypeFound, "Expected market type is not contained in odds, Expected " + marketType.getName());
    }

    @Test
    public void errorMessageReturned_when_getEventsSearchRequest_and_invalidMarketIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.MARKET_TYPES_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);

        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid market_type: %s".formatted(StringConstants.INVALID_STRING)));
    }

    @Test
    public void marketsWithSpecifiedOddTypeReturned_when_getEventsSearchRequest_and_setOddTypeQueryParam() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, OddTypeEnum.PRE_EVENT);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        List<OddsModel> oddsList = eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allOddsHaveCorrectType = oddsList.stream()
                .allMatch(odds -> odds.getType().equals(OddTypeEnum.PRE_EVENT.name()));

        assertExpectedApiResponse(allOddsHaveCorrectType, "Not all odds have the expected type. Expected: " + OddTypeEnum.PRE_EVENT.name());
    }

    @Test
    //@Issue("PLT-275")
    public void errorMessageReturned_when_getEventsSearchRequest_and_invalidOddTypeIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.ODD_TYPE_STRING, StringConstants.INVALID_STRING);
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);

        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid odd_type: invalid. Allowed values are: LIVE, PRE_EVENT, ALL"));
    }

    @Test
    public void eventsWithSpecifiedStatusReturned_when_setScopeTypeQueryParam() {
        queryParams.put(StringConstants.IDS_STRING, String.join(",", eventIds));
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, OddsScopeTypeEnum.ORDINARY_TIME.name());
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);

        List<OddsModel> oddsList = eventSearchResponseModelApiResponse.getResult().getCompetitions().get(0).getEvents().get(0).getOdds();
        boolean allMarketsHaveCorrectScopeType = oddsList.stream()
                .flatMap(odds -> odds.getMarkets().stream())
                .allMatch(market -> market.getScope().getType().equalsIgnoreCase(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));

        assertExpectedApiResponse(allMarketsHaveCorrectScopeType, "Not all markets have the expected scope type. Expected:%s".formatted(OddsScopeTypeEnum.ORDINARY_TIME.name().toLowerCase()));
    }

    @Test
    //@Issue("PLT-275")
    public void errorMessageReturned_when_getEventsSearchRequest_and_invalidScopeTypeIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.ODD_CLIENT_STRING, StringConstants.SPORTAL_365_STRING);
        queryParams.put(StringConstants.SCOPE_TYPE_STRING, StringConstants.INVALID_STRING);

        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);
        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Invalid scope_type: invalid. Allowed values are: ORDINARY_TIME, FULL_TIME"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"NOT_STARTED", "FINISHED", "CANCELLED", "INTERRUPTED", "POSTPONED"})
    public void eventsWithSpecifiedStatusReturned_when_setStatusTypeQueryParam(String statusType) {
        queryParams.put(StringConstants.STATUS_TYPE_STRING, statusType);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        Assertions.assertEquals(statusType, eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getStatusType(),
                "Expected status type to be %s but was: %s".formatted(statusType, eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents().get(0).getStatusType()));
    }

    @Test
    public void emptyCompetitionsReturned_when_invalidStatusTypeIsSet() {
        queryParams.put(StringConstants.STATUS_TYPE_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING
                .formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    public void emptyCompetitionsReturned_when_invalidSortDirectionIsSet() {
        queryParams.put(StringConstants.SORT_DIRECTION_STRING, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);
        boolean areCompetitionsEmpty = eventSearchResponseModelApiResponse.getCompetitions().isEmpty();

        assertExpectedApiResponse(areCompetitionsEmpty, EXPECTED_COMPETITIONS_LIST_TO_BE_EMPTY_STRING
                .formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    @Test
    public void eventsWithSpecifiedParticipantsReturned_when_setParticipantsFilterQueryParam() {
        queryParams.put(StringConstants.IDS_STRING, String.join(",", eventIds));
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, String.join(",", participantIds));
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        Assertions.assertTrue(
                eventSearchResponseModelApiResponse.getCompetitions().get(0).getEvents()
                        .stream()
                        .anyMatch(event -> event.getParticipants().containsAll(participantIds)),
                "No events found containing all the specified participant IDs: %s".formatted(participantIds)
        );
    }

    @Test
    public void noEventsWithInvalidParticipantReturned_when_invalidParticipantsFilterIsSet() {
        queryParams.put(StringConstants.IDS_STRING, eventIds);
        queryParams.put(StringConstants.PARTICIPANTS_FILTER, StringConstants.INVALID_STRING);
        EventsResponseModel eventSearchResponseModelApiResponse = getResponseModel(multiSportEventsSearchHttpRepo, queryParams);

        assertExpectedApiResponse(eventSearchResponseModelApiResponse.getCompetitions().isEmpty(), "Competition list should be empty but had elements: %s".formatted(eventSearchResponseModelApiResponse.getCompetitions().size()));
    }

    private EventsResponseModel getEventSearchResponseModelApiResponse() {
        ApiResponse<EventsResponseModel> eventSearchResponseModelApiResponse = multiSportEventsSearchHttpRepo.get(queryParams);
        eventSearchResponseModelApiResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);

        return eventSearchResponseModelApiResponse.getResult();
    }

    private Stream<Arguments> invalidDateScenarios() {
        return Stream.of(
                Arguments.of(StringConstants.INVALID_DATA, TO_DATE),
                Arguments.of(FROM_DATE, StringConstants.INVALID_DATA),
                Arguments.of(StringConstants.INVALID_DATA, StringConstants.INVALID_DATA)
        );
    }
}