package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.liveblog.LiveBlogPostContentAlignment;
import data.constants.liveblog.LiveBlogPostContentWidth;
import data.models.videos.VideoResponseModel;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithVideoTests extends BaseContentApiLiveblogTests {

    private VideoResponseModel createdVideo;

    @Override
    protected void beforeAll() throws Exception {
        super.beforeAll();
        createdVideo = contentApiFacade.getVideo();
    }

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithVideo() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(createdVideo.getId());

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(createdVideo);

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0).toString(), liveBlogPost.getBlocks().get(0).toString(), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Issue("SBE-4136")
    @Test
    public void validationErrorReturned_when_postRequestWithNotExistingVideo() {
        String videoId = "0";
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(videoId);
        expectedLiveBlogPost.getBlocks().get(0).getData().setId(videoId);

        contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body("message", Matchers.containsString("Block data entity with id %s does not exist".formatted(videoId)));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogPostContentWidth.class)
    public void liveBlogPostReturned_when_postRequestWithVideo_and_changedImageWidth(LiveBlogPostContentWidth width) {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(createdVideo.getId());
        expectedLiveBlogPost.getBlocks().get(0).getData().getCustom().getPosition().setWidth(width.name);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(createdVideo);

        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0).toString(), liveBlogPost.getBlocks().get(0).toString(),
                AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithVideo_and_setNotListedImageWidth() {
        var notListedWidth = "61";
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(createdVideo.getId());
        expectedLiveBlogPost.getBlocks().get(0).getData().getCustom().getPosition().setWidth(notListedWidth);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(createdVideo);

        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0).toString(), liveBlogPost.getBlocks().get(0).toString(),
                AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogPostContentAlignment.class)
    public void liveBlogPostReturned_when_postRequestWithVideo_and_changedImageAlignment(LiveBlogPostContentAlignment alignment) {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(createdVideo.getId());
        expectedLiveBlogPost.getBlocks().get(0).getData().getCustom().getPosition().setAlignment(alignment.name);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(createdVideo);

        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0).toString(), liveBlogPost.getBlocks().get(0).toString(),
                AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithVideo_and_setNotListedImageAlignment() {
        var notListedAlignment = "bottom";
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithVideo(createdVideo.getId());
        expectedLiveBlogPost.getBlocks().get(0).getData().getCustom().getPosition().setAlignment(notListedAlignment);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(createdVideo);

        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0).toString(), liveBlogPost.getBlocks().get(0).toString(),
                AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }
}
