package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.liveblog.LiveBlogStatusEnum;
import data.constants.liveblog.LiveBlogType;
import data.models.categories.Category;
import data.models.liveblogapi.AdditionalInfo;
import data.models.liveblogapi.Collaborators;
import factories.liveblog.LiveBlogHttpFactory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDateTime;
import java.util.Collections;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogConfigurationTests extends BaseContentApiLiveblogTests {

    @Test
    @Disabled("Run On demand")
    public void deleteAllTestLiveBlogs() {
        deleteAllAutoGeneratedLiveBlogs();
    }

    @Override
    protected void beforeEach() {
        super.beforeEach();
        expectedLiveBlogConfig = LiveBlogHttpFactory.buildLiveBlogConfiguration(generateUnusedSportEvent());
    }

    @Test
    public void liveBlogReturned_when_getRequestAll() {
        createDefaultLiveBlog();
        var liveBlogId = actualLiveBlogConfig.getId();
        liveBlogApiFacade.waitUntilLiveBlogCreated(liveBlogId);

        var liveBlogs = contentLiveBlogRepo.getAllEntities();
        var blogFound = liveBlogs.getResult().stream().filter(blog -> blog.getId().equals(liveBlogId)).findFirst();

        Assertions.assertTrue(blogFound.isPresent(), "Created Liveblog was not returned from API. \n" + liveBlogs.getResponse().getBody().prettyPrint());
        Assertions.assertEquals(liveBlogId, blogFound.get().getId(), "Created Liveblog has incorrect Id. \n" + liveBlogs.getResponse().getBody().prettyPrint());
    }

    @Test
    public void liveBlogReturned_when_getRequestById() {
        createDefaultLiveBlog();
        var liveBlogId = actualLiveBlogConfig.getId();

        var blogFound = contentLiveBlogRepo.getById(liveBlogId);
        Assertions.assertEquals(liveBlogId, blogFound.getResult().getId(), "Created Liveblog has incorrect Id. \n" + blogFound.getResponse().getBody().prettyPrint());
    }

    @Test
    public void liveBlogCreated_when_postRequestWithRequiredData() {
        createDefaultLiveBlog();

        Assertions.assertNull(actualLiveBlogConfig.getMessage(), "Error validation was returned");
        Assertions.assertEquals(expectedLiveBlogConfig.getTitle(), actualLiveBlogConfig.getTitle(), "Expected Title is not equal to the actual one!");
        Assertions.assertEquals(expectedLiveBlogConfig.getDescription(), actualLiveBlogConfig.getDescription(), "Expected Description is not equal to the actual one!");
        Assertions.assertFalse(actualLiveBlogConfig.getId().isEmpty(), "The Live Blog Id is empty");
    }

    @Test
    public void liveBlogDeleted_when_deleteRequestWithId() {
        createDefaultLiveBlog();
        contentLiveBlogRepo.delete(actualLiveBlogConfig.getId());

        var allLiveBlogs = contentLiveBlogRepo.getAll();
        var blogFound = allLiveBlogs.getResult().stream().filter(blog -> blog.getId().equals(actualLiveBlogConfig.getId())).findFirst();

        Assertions.assertFalse(blogFound.isPresent(), "Created Liveblog was returned int the list after deletion: \n" + allLiveBlogs.getResponse().getBody().prettyPrint());
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyTitle() {
        expectedLiveBlogConfig.setTitle(null);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("title is required"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptyDescription() {
        expectedLiveBlogConfig.setDescription(null);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getDescription(), AssertMessages.entityNotExpected("LiveBlog Description"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptySlug() {
        expectedLiveBlogConfig.setSlug(null);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getSlug(), AssertMessages.entityNotExpected("LiveBlog Slug"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setCustomSlug() {
        String expectedSlug = String.format("Test slug - %s", LocalDateTime.now().format(dateTimeFormatter));

        expectedLiveBlogConfig.setSlug(expectedSlug);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(expectedSlug, actualLiveBlogConfig.getSlug(), AssertMessages.entityNotExpected("LiveBlog Slug"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptyStatus() {
        expectedLiveBlogConfig.setSlug(null);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getSlug(), AssertMessages.entityNotExpected("LiveBlog Status"));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogStatusEnum.class)
    public void liveBlogCreated_when_postRequest_and_setStatus(LiveBlogStatusEnum liveBlogStatus) {
        expectedLiveBlogConfig.setStatus(liveBlogStatus.name());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(liveBlogStatus.name(), actualLiveBlogConfig.getStatus(), AssertMessages.entityNotExpected("LiveBlog Status"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyType() {
        expectedLiveBlogConfig.setType(null);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("type is required"));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogType.class)
    public void liveBlogCreated_when_postRequest_and_setType(LiveBlogType liveBlogType) {
        expectedLiveBlogConfig.setType(liveBlogType.name());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(liveBlogType.name(), actualLiveBlogConfig.getType(), AssertMessages.entityNotExpected("LiveBlog Type"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptySport() {
        expectedLiveBlogConfig.setSport(null);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("sport is required"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyCategory() {
        var expectedCategory = Category.builder().build();
        expectedLiveBlogConfig.setCategory(expectedCategory);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptyAuthors() {
        expectedLiveBlogConfig.setAuthors(Collections.emptyList());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getAuthors(), AssertMessages.entityNotExpected("LiveBlog Authors list"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setAuthors() {
        var authorsList = authorsHttpRepo.getAll().getResult();

        expectedLiveBlogConfig.setAuthors(authorsList.subList(0, 1));
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(authorsList.subList(0, 1), actualLiveBlogConfig.getAuthors(), AssertMessages.entityNotExpected("LiveBlog Authors list"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptyCollaborators() {
        expectedLiveBlogConfig.setCollaborators(Collections.emptyList());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getCollaborators(), AssertMessages.entityNotExpected("LiveBlog Collaborators list"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setCollaborators() {
        var authorsList = authorsHttpRepo.getAll().getResult();
        var expectedCollaboratorsList = (Collections.singletonList(Collaborators
                .builder()
                .author(authorsList.get(0))
                .additionalInfo(AdditionalInfo
                        .builder()
                        .description("Test")
                        .build())
                .build()));

        expectedLiveBlogConfig.setCollaborators(expectedCollaboratorsList);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(expectedCollaboratorsList, actualLiveBlogConfig.getCollaborators(), AssertMessages.entityNotExpected("LiveBlog Collaborators list"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyMainMedia() {
        expectedLiveBlogConfig.setMainMedia(Collections.emptyList());

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("main_media must not be empty"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyStartTime() {
        expectedLiveBlogConfig.setStartTime(null);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("start_time is required"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setCreatedAt() {
        String expectedCreatedAt = LocalDateTime.now().format(dateTimeFormatter);

        expectedLiveBlogConfig.setCreatedAt(expectedCreatedAt);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertFalse(actualLiveBlogConfig.getCreateAt().isEmpty(), AssertMessages.entityNotExpected("LiveBlog Response Created At"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setUpdatedAt() {
        String expectedCreatedAt = LocalDateTime.now().format(dateTimeFormatter);

        expectedLiveBlogConfig.setUpdatedAt(expectedCreatedAt);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertFalse(actualLiveBlogConfig.getCreateAt().isEmpty(), AssertMessages.entityNotExpected("LiveBlog Response Updated At"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyCreatedBy() {
        expectedLiveBlogConfig.setCreatedBy(null);

        contentLiveBlogRepo.create(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("created_by is required"));
    }

    @Test
    public void validationErrorReceived_when_postRequest_and_setEmptyCompetitions() {
        expectedLiveBlogConfig.setCompetitions(Collections.emptyList());

        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getCompetitions(), AssertMessages.entityNotExpected("LiveBlog Competitions list"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptyTeams() {

        expectedLiveBlogConfig.setTeams(Collections.emptyList());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getTeams(), AssertMessages.entityNotExpected("LiveBlog Teams list"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptySportEvents() {

        expectedLiveBlogConfig.setSportEvents(Collections.emptyList());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getSportEvents(), AssertMessages.entityNotExpected("LiveBlog Sport Events list"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setMatchHeader() {

        expectedLiveBlogConfig.setMatchHeader(true);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(true, actualLiveBlogConfig.getMatchHeader(), AssertMessages.entityNotExpected("LiveBlog Match header"));
    }

    @Test
    public void liveBlogCreated_when_postRequestWithEmptySponsors() {

        expectedLiveBlogConfig.setSponsors(Collections.emptyList());
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNull(actualLiveBlogConfig.getSponsors(), AssertMessages.entityNotExpected("LiveBlog Sponsors"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setSponsors() {
        var expectedSponsors = footballOddsHttpRepo.getAll().getResult();

        expectedLiveBlogConfig.setSponsors(expectedSponsors);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(expectedSponsors, actualLiveBlogConfig.getSponsors(), AssertMessages.entityNotExpected("LiveBlog Sponsors"));
    }

    @Test
    public void liveBlogCreated_when_postRequest_and_setPagination() {
        var expectedPagination = 0;

        expectedLiveBlogConfig.setPagination(expectedPagination);
        var createdLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertEquals(expectedPagination, actualLiveBlogConfig.getPagination(), AssertMessages.entityNotExpected("LiveBlog Pagination"));
    }

    @Test
    public void liveBlogUpdated_when_createLiveBlog_and_postNewConfiguration() {
        var firstLiveBlogConfig = LiveBlogHttpFactory.buildLiveBlogConfiguration(generateUnusedSportEvent());
        var actualFirstLiveBlogConfig = contentLiveBlogRepo.create(firstLiveBlogConfig).getResult();

        expectedLiveBlogConfig.setId(actualFirstLiveBlogConfig.getId());
        var createdLiveBlogConfig = contentLiveBlogRepo.update(expectedLiveBlogConfig);
        actualLiveBlogConfig = createdLiveBlogConfig.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, createdLiveBlogConfig.getResponse().getStatusCode(), AssertMessages.entityNotExpected("LiveBlog Response status code"));
        Assertions.assertNotEquals(firstLiveBlogConfig.toString(), actualLiveBlogConfig.toString(), AssertMessages.entityNotExpected("LiveBlog Configuration"));
    }

    @Test
    public void validationErrorReceived_when_updateNotExistingRequest() {
        contentLiveBlogRepo.update(expectedLiveBlogConfig).getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body("message", Matchers.containsString("There is no configuration with identifier null"));
    }
}