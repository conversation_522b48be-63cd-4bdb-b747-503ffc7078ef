package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.liveblog.EmbedType;
import data.constants.liveblog.EmbedValidationType;
import data.constants.liveblog.LiveBlogPostPublishedType;
import data.models.liveblogapi.post.Object;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithEmbedCodeTests extends BaseContentApiLiveblogTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithEmbedCode() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithEmbedCode();

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Embed Type"));
    }

    @ParameterizedTest
    @EnumSource(EmbedType.class)
    public void liveBlogPostReturned_when_postRequestWithEmbedCode_and_setEmbedType(EmbedType embedType) {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithEmbedCode();
        ((Object)expectedLiveBlogPost.getBlocks().get(0).getData().getObject()).setEmbedType(embedType.name);
        ((Object)expectedLiveBlogPost.getBlocks().get(0).getData().getObject()).setType(embedType.name);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Embed Type"));
    }

    @ParameterizedTest
    @EnumSource(EmbedValidationType.class)
    public void liveBlogPostReturned_when_postRequestWithEmbedCode_and_setValidationType(EmbedValidationType embedValidationType) {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithEmbedCode();
        ((Object)expectedLiveBlogPost.getBlocks().get(0).getData().getObject()).getValidationType().setValue(embedValidationType.name);
        ((Object)expectedLiveBlogPost.getBlocks().get(0).getData().getObject()).getValidationType().setDefaultObject(false);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Validation Type"));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogPostPublishedType.class)
    public void liveBlogPostReturned_when_postRequestWithEmbedCode_and_setPublishedType(LiveBlogPostPublishedType liveBlogPostPublishedType) {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithEmbedCode();
        expectedLiveBlogPost.setPublishedType(liveBlogPostPublishedType.name());

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Published Type"));
    }

}
