package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import com.github.javafaker.Faker;
import data.constants.AssertMessages;
import data.constants.Language;
import data.constants.StringConstants;
import data.models.contentapi.liveblog.AuthorModel;
import data.models.footballapi.search.PersonResultModel;
import data.models.liveblogapi.Team;
import data.models.liveblogapi.post.SportTag;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsTests extends BaseContentApiLiveblogTests {

    @Override
    protected void beforeAll() {
        createDefaultLiveBlog();
    }

    @Override
    protected void afterEach() {
        contentLiveBlogPostsRepo.deletePost(createdLiveBlogPost.getId());
    }

    @Override
    protected void afterClass() {
        super.afterEach();
    }

    @Test
    public void liveBlogPostReturned_when_postRequest() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(createdLiveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogPostNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), createdLiveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogReturned_when_getRequestById() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        var actualLiveBlogPost = contentLiveBlogPostsRepo.getById(createdLiveBlogPost.getId()).getResult();

        Assertions.assertNotNull(actualLiveBlogPost.getId(), AssertMessages.liveBlogPostIncorrectProperty("Id"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), actualLiveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequest_and_setCustomDateTime() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.setDisplayTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'")));
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertEquals(expectedLiveBlogPost.getDisplayTimestamp(), createdLiveBlogPost.getDisplayTimestamp(), AssertMessages.liveBlogPostIncorrectProperty("Display Timestamp"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequest_and_setMinute() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.setMinute(String.valueOf(LocalDateTime.now().getMinute()));
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertEquals(expectedLiveBlogPost.getMinute(), createdLiveBlogPost.getMinute(), AssertMessages.liveBlogPostIncorrectProperty("Minute"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequest_and_setAuthorName() {
        var authorsList = authorsHttpRepo.getAll().getResult();
        var firstAuthor = authorsList.get(0);

        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.setAuthor(AuthorModel
                .builder()
                .id(firstAuthor.getId())
                .name(firstAuthor.getName())
                .build());

        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertEquals(expectedLiveBlogPost.getAuthor().toString(), createdLiveBlogPost.getAuthor().toString(), AssertMessages.liveBlogPostIncorrectProperty("Author"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequest_and_setSportTag() {
        String searchTag = actualLiveBlogConfig.getTeams().stream()
                .filter(name -> name.getName() != null && !name.getName().isEmpty())
                .findFirst()
                .map(Team::getName)
                .orElseThrow(() -> new RuntimeException("Team name not found"));

        queryParams.put(StringConstants.QUERY_STRING, searchTag);
        queryParams.put(StringConstants.ENTITY_TYPE_STRING, String.join(",",
                List.of(StringConstants.PLAYER_STRING, StringConstants.TEAM_STRING, StringConstants.COACH_STRING)));
        queryParams.put(StringConstants.INPUT_LANGUAGE_STRING, Language.ENGLISH.getCode());
        queryParams.put(StringConstants.SCOPE_STRING, "{\"tournament\":[%s]}".formatted(actualLiveBlogConfig.getCompetitions().get(0).getId()));
        List<PersonResultModel> sportTags = footballSearchPersonHttpRepo.getAll(queryParams).getResult();

        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.setSportTags(List.of(sportTags.get(0)));
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(createdLiveBlogPost.getSportTags().isEmpty(), AssertMessages.responseNotContains("Sport Tag"));
        Assertions.assertEquals(sportTags.get(0).getId(), ((SportTag)createdLiveBlogPost.getSportTags().get(0)).getId(), AssertMessages.liveBlogPostIncorrectProperty("Sport Tag Id"));
        Assertions.assertEquals(sportTags.get(0).getName(), ((SportTag)createdLiveBlogPost.getSportTags().get(0)).getName(), AssertMessages.liveBlogPostIncorrectProperty("Sport Tag Name"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequest_and_setSponsors() {
        var oddsList = footballOddsHttpRepo.getAll().getResult();

        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.setSponsors(List.of(oddsList.get(0)));
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(createdLiveBlogPost.getSponsors().isEmpty(), AssertMessages.responseNotContains("Sponsor"));
        Assertions.assertEquals(oddsList.get(0), createdLiveBlogPost.getSponsors().get(0), AssertMessages.liveBlogPostIncorrectProperty("Sponsor object"));
    }

    @Test
    public void liveBlogPostDeleted_when_deleteRequestById() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost);

        contentLiveBlogPostsRepo.delete(liveBlogPost.getResult().getId());
        var liveBlogPosts = contentLiveBlogPostsRepo.getAll().getResult();

        Assertions.assertFalse(liveBlogPosts.stream().anyMatch(e -> e.getId().contains(liveBlogPost.getResult().getId())), AssertMessages.liveBlogPostNotReturnedFromApi());
    }

    @Test
    public void liveBlogPostUpdated_when_updateRequestById() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        createdLiveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        var newParagraphContent = String.format("<p>%s</p>", new Faker().hitchhikersGuideToTheGalaxy().marvinQuote());
        createdLiveBlogPost.getBlocks().get(0).getData().setContent(newParagraphContent);
        var updateResponse = contentLiveBlogPostsRepo.update(createdLiveBlogPost);

        Assertions.assertEquals(HttpStatus.SC_OK, updateResponse.getResponse().getStatusCode(), "Created LiveBlog Post was not updated");
        Assertions.assertEquals(newParagraphContent, updateResponse.getResult().getBlocks().get(0).getData().getContent(), "The updated LiveBlog Post is not equal to the expected");
    }
}