package contentapi.liveblog;

import contentapi.BaseContentApiTests;
import data.configuration.SportalSettings;
import data.constants.AutomationProjectCommonStrings;
import data.models.footballapi.events.EventModel;
import data.models.footballapi.v2.MatchV2Model;
import data.models.liveblogapi.LiveBlogModel;
import data.models.liveblogapi.LiveBlogResponseModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import factories.liveblog.LiveBlogHttpFactory;
import repositories.content.ContentLiveBlogPostsHttpRepository;
import solutions.bellatrix.core.configuration.ConfigurationService;
import solutions.bellatrix.core.utilities.Log;

import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BaseContentApiLiveblogTests extends BaseContentApiTests {

    protected ContentLiveBlogPostsHttpRepository contentLiveBlogPostsRepo;
    protected final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ss'Z'");
    protected LiveBlogModel expectedLiveBlogConfig;
    protected LiveBlogResponseModel actualLiveBlogConfig;
    protected LiveBlogPostModel expectedLiveBlogPost;
    protected String baseSportEventId;
    protected LiveBlogPostModel createdLiveBlogPost;

    public MatchV2Model generateUnusedSportEvent() {
        var unusedSportEvent = footballApiFacade.getNotStartedMatch();
        baseSportEventId = unusedSportEvent.getId();
        return unusedSportEvent;
    }

    public LiveBlogResponseModel createDefaultLiveBlog() {
        MatchV2Model notStartedMatch = footballApiFacade.getNotStartedMatch();
        expectedLiveBlogConfig = LiveBlogHttpFactory.buildLiveBlogConfiguration(notStartedMatch);
        actualLiveBlogConfig = contentLiveBlogRepo.create(expectedLiveBlogConfig).getResult();

        contentLiveBlogPostsRepo = new ContentLiveBlogPostsHttpRepository(getCurrentTestProject(), actualLiveBlogConfig.getId());

        return actualLiveBlogConfig;
    }

    public java.util.Map<String, Object> getTeamComparisonDataForTeam(String footballTeam) {
        //Get Team
        var teamsList = footballTeamsHttpRepo.getAllByName(footballTeam);
        var firstTeam = teamsList.get(0);

        //Get Tournament and Season
        var tournamentsList = footballTournamentsHttpRepo.getAllTournaments();
        var firstTournament = footballTournamentsHttpRepo.getById(tournamentsList.get(0).getId().toString()).getResult();
        var firstSeason = firstTournament.getSeasons().get(0);

        java.util.Map<String, Object> map = new HashMap<>();
        map.put("teamId", firstTeam.getId());
        map.put("seasonId", firstSeason.getId());
        map.putAll(getMatchAndOddsIdList(footballTeam));

        return map;
    }

    public Map<String, Object> getMatchAndOddsIdList(String footballTeam) {
        var counter = 1;

        var teamsList = footballTeamsHttpRepo.getAllByName(footballTeam);
        var firstTeam = teamsList.get(0);
        var matchesList = footballMatchesHttpRepo.getAllMatchesForTeam(firstTeam.getId());

        var firstMatch = matchesList.get(0);
        var oddsList = footballEventsHttpRepo.getAllOddsForMatch(firstMatch.getId());

        while (oddsList.isEmpty() && matchesList.size() > counter) {
            firstMatch = matchesList.get(counter);
            oddsList = footballEventsHttpRepo.getAllOddsForMatch(firstMatch.getId());
            counter++;
        }

        ArrayList<String> oddProvidersIdList = new ArrayList<>();
        for (EventModel odd : oddsList) {
            oddProvidersIdList.add(new DecimalFormat("#.#").format(odd.getOddProvider().getId()));
        }

        java.util.Map<String, Object> map = new HashMap<>();
        map.put("match", firstMatch);
        map.put("oddProvidersIdList", oddProvidersIdList);

        return map;
    }

    public String generateTeamComparisonEmbedCode(String matchId,
                                                  List<Long> oddProviderIdsList,
                                                  List<String> statFields,
                                                  Long firstTeamId,
                                                  String firstTeamSeasons,
                                                  Long secondTeamId,
                                                  List<String> secondTeamSeasons) {
        return """
                <div data-widgetid='team-h2h'
                data-options='{
                    "lang":"bg",
                    "apiKey":"%1$s",
                    "apiURL":"%2$s",
                    "oddClient":"sportal",
                    "team1":{"id":"%3$s","seasonIds":%4$s},
                    "team2":{"id":"%5$s","seasonIds":%6$s},
                    "statFields":%7$s,
                    "displayOdds":true,
                    "eventId":%8$s,
                    "market":{"name":"1x2"},
                    "oddProviderIds":%9$s,
                    "mainOddProvider":null,
                    "canSelectMarkets":true,
                    "displayTeamShortNamesOnMobile":false,
                    "displayTeamShortNamesOnDesktop":false}'></div>
                """.formatted(
                ConfigurationService.get(SportalSettings.class).getApiKey(),
                ConfigurationService.get(SportalSettings.class).getFootballApiUrl(),
                firstTeamId,
                firstTeamSeasons,
                secondTeamId,
                secondTeamSeasons,
                statFields,
                matchId,
                oddProviderIdsList);
    }

    public String generatePlayerH2hEmbedCode(Long player1Id, String player1SeasonId, Long player2Id, String player2SeasonId) {
        return """
                <div data-widget-id='player-h2h'
                data-widget-sport='football'
                data-widget-type='tournament'
                data-header-display='true'
                data-sport-entity-one='{"id":%1$s,"seasonId":%2$s}'
                data-sport-entity-two='{"id":%3$s,"seasonId":%4$s}'
                data-elements='["played"]'></div>
                """.formatted(
                player1Id,
                player1SeasonId,
                player2Id,
                player2SeasonId);
    }

    public String generateOddsEmbedCode(Long matchId, ArrayList<Long> oddProvidersIdList) {
        return """
                <div data-widgetid='odds-listing' 
                data-options='{ 
                "lang":"bg",
                "apiKey":"%1$s",
                "apiURL":"%2$s",
                "oddClient":"sportal",
                "market":{"name":"1x2"},
                "eventId":%3$s,
                "oddProviderIds":%4$s,
                "canSelectMarkets":true,
                "displayMatchHeader":true,
                "displayTeamShortNamesOnMobile":false,
                "displayTeamShortNamesOnDesktop":false}'></div>
                """.formatted(
                ConfigurationService.get(SportalSettings.class).getApiKey(),
                ConfigurationService.get(SportalSettings.class).getFootballApiUrl(),
                matchId,
                oddProvidersIdList);
    }

    public String generateStandingsEmbedCode(Long competitionId, Long seasonId, String stageId) {
        return """
                <div data-widget-id='standings' 
                data-widget-sport='football' 
                data-widget-type='tournament'  
                data-competition='%1$s'  
                data-season='%2$s'  
                data-stage='%3$s' 
                data-header-display='false'></div>
                """.formatted(
                competitionId,
                seasonId,
                stageId);
    }

    @Override
    protected void afterEach() {
        if (actualLiveBlogConfig != null && actualLiveBlogConfig.getId() != null) {
            contentLiveBlogRepo.delete(actualLiveBlogConfig.getId());
        }
        if (expectedLiveBlogConfig != null && expectedLiveBlogConfig.getId() != null) {
            contentLiveBlogRepo.delete(expectedLiveBlogConfig.getId());
        }
    }

    protected void deleteAllAutoGeneratedLiveBlogs() {
        var result = contentLiveBlogRepo.getAll(new HashMap<>(Map.of("limit", "200"))).getResult();
        var count = 0;

        for (LiveBlogResponseModel liveBlog : result) {
            if (liveBlog.getSlug() == null || liveBlog.getSlug().contains(AutomationProjectCommonStrings.SLUG.string)) {
                contentLiveBlogRepo.delete(liveBlog.getId());
                count++;
            }
        }

        Log.info(String.format("All Auto Generated LiveBlogs are deleted! Total count: %s", count));
    }
}
