package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.models.articles.ArticleResponseModel;
import data.models.liveblogapi.post.LiveBlogPostModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithArticleTests extends BaseContentApiLiveblogTests {

    private ArticleResponseModel createdArticle;

    @Override
    protected void beforeAll() {
        createdArticle = contentApiFacade.createArticle();
    }

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
    }

    @Override
    protected void afterClass() {
        contentApiFacade.deleteArticle(createdArticle);
    }

    @Test
//    @Issue("SBE-4136")
    public void validationErrorReceived_when_createLiveBlogPosWithNotExistingArticle() {
        String notExistingArticleId = "1";
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithArticle(notExistingArticleId);

        contentLiveBlogPostsRepo.create(expectedLiveBlogPost)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Block data entity with id %s does not exist".formatted(notExistingArticleId)));
    }

    @Test
    public void correctArticlePublished_when_createLiveBlogPostWithExistingArticle() {
        ArticleResponseModel expectedArticle = articlesV2HttpRepo.search(createdArticle.getTitle()).get(0);
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithArticle(expectedArticle.getId());

        var result = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        //TODO navramov 15 Sep 2014: Possible bug. Needs to be isolated and reported
        expectedArticle.setIsSensitiveContent(null);

        Assertions.assertEquals(expectedArticle.toString(), result.getBlocks().get(0).getData().getObject().toString(),
                AssertMessages.liveBlogPostIncorrectProperty(StringConstants.OBJECT_STRING));
    }

    @Test
    public void correctEntityTypeOfPublishedArticle_when_createLiveBlogPostWithArticle() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithArticle(createdArticle.getId());

        var result = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();
        var actualEntityType = ((ArticleResponseModel)result.getBlocks().get(0).getData().getObject()).getEntityType();

        Assertions.assertEquals(createdArticle.getEntityType(), actualEntityType,
                AssertMessages.objectValueNotCorrect(StringConstants.ENTITY_TYPE_STRING, StringConstants.OBJECT_STRING));
    }

    @Test
    public void successfulDeletionOfPost_when_deletePublishedPostFromLiveBlog() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithArticle(createdArticle.getId());
        LiveBlogPostModel liveBlogPostModel = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        contentLiveBlogPostsRepo.delete(liveBlogPostModel.getId(), liveBlogPostModel)
                .then()
                .statusCode(HttpStatus.SC_OK)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Post with an ID of %s deleted!".formatted(liveBlogPostModel.getId())));
    }
}