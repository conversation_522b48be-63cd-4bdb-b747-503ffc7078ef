package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.models.articles.NestedMatch;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.ArrayList;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithMultipleBlocksTests extends BaseContentApiLiveblogTests {

    private Long firstPlayerId;
    private String firstPlayerSeasonId;
    private Long secondPlayerId;
    private String secondPlayerSeasonId;
    private String playerH2HEmbedCode;
    private String oddsEmbedCode;
    private NestedMatch match;
    private ArrayList<Long> oddProvidersIdList;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithFootballBlocks() {
        prepareTestDataForFootballBlocks();
        expectedLiveBlogPost = liveBlogPostFactory
                .buildLiveBlogPostWithCombinationOfFootballBlocks(match, oddProvidersIdList, oddsEmbedCode, firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, playerH2HEmbedCode);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraphAndOdds() {
        prepareTestDataForOdds();
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithCombinationOfParagraphAndOdds(match, oddProvidersIdList, oddsEmbedCode);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    private void prepareTestDataForFootballBlocks() {
        prepareTestDataForOdds();

        String firstPlayer = "Karim Benzema";
        var firstPlayerSearchList = footballPlayersHttpRepo.getAll("?name=" + firstPlayer).getResult();
        firstPlayerId = firstPlayerSearchList.get(0).getId();
        var firstPlayerStatistics = footballStatisticsPlayersHttpRepo.getAll(String.format("?player_ids=%s&language_code=en", firstPlayerId.toString())).getResult();
        firstPlayerSeasonId = firstPlayerStatistics.get(0).getSeason().getId();

        String secondPlayer = "Cristiano Ronaldo";
        var secondPlayerSearchList = footballPlayersHttpRepo.getAll("?name=" + secondPlayer).getResult();
        secondPlayerId = secondPlayerSearchList.get(0).getId();
        var secondPlayerStatistics = footballStatisticsPlayersHttpRepo.getAll(String.format("?player_ids=%s&language_code=en", secondPlayerId.toString())).getResult();
        secondPlayerSeasonId = secondPlayerStatistics.get(0).getSeason().getId();

        playerH2HEmbedCode = generatePlayerH2hEmbedCode(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId);
        oddsEmbedCode = generateOddsEmbedCode(match.getId(), oddProvidersIdList);
    }

    private void prepareTestDataForOdds() {
        var matchWithOdds = footballApiFacade.getFootballEventWithOdds();

        match = matchWithOdds.toNestedMatch();
        oddProvidersIdList = new ArrayList<>(matchWithOdds.getOdds().stream().map(odd -> Long.parseLong(odd.getBookmaker().getId())).toList());
    }
}
