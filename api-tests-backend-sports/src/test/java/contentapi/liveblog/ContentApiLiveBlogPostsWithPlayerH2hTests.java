package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithPlayerH2hTests extends BaseContentApiLiveblogTests {

    private static Long firstPlayerId;
    private static String firstPlayerSeasonId;
    private static Long secondPlayerId;
    private static String secondPlayerSeasonId;
    private static String embedCode;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
        prepareTestData();
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, embedCode);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyPlayersIds() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(null, firstPlayerSeasonId, null, secondPlayerSeasonId, embedCode);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptySeasonsIds() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, null, secondPlayerId, null, embedCode);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyEmbedCode() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, null);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyWidgetId() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, embedCode);
        expectedLiveBlogPost.getBlocks().get(0).getData().getConfig().setWidgetId(null);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyDataType() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, embedCode);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(null);

        contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResponse().then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body("message", Matchers.containsString("Invalid data sent"))
                .body("type", Matchers.equalTo("VALIDATION_ERROR"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyDataSport() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, embedCode);
        expectedLiveBlogPost.getBlocks().get(0).getData().setSport(null);
        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithPlayerH2h_and_setEmptyBlockType() {
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithPlayerH2h(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId, embedCode);
        expectedLiveBlogPost.getBlocks().get(0).setType(null);

        contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body("message", Matchers.containsString("An object in blocks does not have a type"));
    }

    private void prepareTestData() {
        if (embedCode == null) {
            String firstPlayer = "Karim Benzema";
            var firstPlayerSearchList = footballPlayersHttpRepo.getAll("?name=" + firstPlayer).getResult();
            firstPlayerId = firstPlayerSearchList.get(0).getId();
            var firstPlayerStatistics = footballStatisticsPlayersHttpRepo.getAll(String.format("?player_ids=%s&language_code=en", firstPlayerId.toString())).getResult();
            firstPlayerSeasonId = firstPlayerStatistics.get(0).getSeason().getId();

            String secondPlayer = "Cristiano Ronaldo";
            var secondPlayerSearchList = footballPlayersHttpRepo.getAll("?name=" + secondPlayer).getResult();
            secondPlayerId = secondPlayerSearchList.get(0).getId();
            var secondPlayerStatistics = footballStatisticsPlayersHttpRepo.getAll(String.format("?player_ids=%s&language_code=en", secondPlayerId.toString())).getResult();
            secondPlayerSeasonId = secondPlayerStatistics.get(0).getSeason().getId();

            embedCode = generatePlayerH2hEmbedCode(firstPlayerId, firstPlayerSeasonId, secondPlayerId, secondPlayerSeasonId);
        }
    }
}
