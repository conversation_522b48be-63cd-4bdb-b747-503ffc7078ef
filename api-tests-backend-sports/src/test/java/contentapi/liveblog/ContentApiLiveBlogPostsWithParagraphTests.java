package contentapi.liveblog;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.liveblog.LiveBlogPostBlockDataType;
import data.constants.liveblog.LiveBlogPostBlockType;
import data.constants.liveblog.LiveBlogPostContentAlignment;
import data.models.liveblogapi.post.Object;
import io.qameta.allure.Story;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.LIVE_BLOG_API)
@Story(APITags.LIVE_BLOG_API)
public class ContentApiLiveBlogPostsWithParagraphTests extends BaseContentApiLiveblogTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createDefaultLiveBlog();
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph() {
        var expectedType = LiveBlogPostBlockType.HTML_BLOCK.name;
        var expectedDataType = LiveBlogPostBlockDataType.PARAGRAPH.name;
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).setType(expectedType);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(expectedDataType);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), "Created LiveBlog post was not returned from API.");
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @ParameterizedTest
    @CsvSource(value = {"<strong>%s</strong>:bold", "<em>%s</em>:italic", "<u>%s</u>:underline", "<s>%s</s>:strikethrough"}, delimiter = ':')
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_formatParagraphText(String tag, String format) {
        String formattedTagWithText = String.format(tag, faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        var expectedParagraph = String.format("<p>%s</p>", formattedTagWithText);
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not " + format));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @ParameterizedTest
    @EnumSource(LiveBlogPostContentAlignment.class)
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_alignText(LiveBlogPostContentAlignment alignment) {
        var expectedParagraph = String.format("<p style=\"text-align: %1$s\">%2$s</p>", alignment.name, faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(Object.builder().textAlign(alignment.name).build());

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not align right"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_linkText() {
        var expectedParagraph = String.format("<p><a target=\"_blank\" rel=\"noopener noreferrer nofollow\" class=\"liveblog-editor-link liveblog-editor-link\" href=\"https://test.test/\">%s</a></p>",
                faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not link"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_tableText() {
        var expectedParagraph = "<table><tbody><tr><td colspan=\"1\" rowspan=\"1\"><p>t1</p></td><td colspan=\"1\" rowspan=\"1\"><p>t2</p></td></tr><tr><td colspan=\"1\" rowspan=\"1\"><p>t3</p></td><td colspan=\"1\" rowspan=\"1\"><p>t4</p></td></tr></tbody></table>";
        var expectedDataType = LiveBlogPostBlockDataType.TABLE.name;
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(expectedDataType);
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(null);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not table"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_headingText() {
        var expectedParagraph = String.format("<h3>%s</h3>", faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        var expectedDataType = LiveBlogPostBlockDataType.HEADING.name;
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(expectedDataType);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not heading"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_unorderedListText() {
        var expectedParagraph = "<ul><li><p>ul1</p></li><li><p>ul2</p></li></ul>";
        var expectedDataType = LiveBlogPostBlockDataType.UNORDERED_LIST.name;
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(expectedDataType);
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(null);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not unordered list"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_orderedListText() {
        var expectedParagraph = "<ol><li><p>ol1</p></li><li><p>ol2</p></li></ol>";
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithOrderedListParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not ordered list"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_blockquoteText() {
        var expectedParagraph = String.format("<blockquote><p>%s</p></blockquote>", faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        var expectedDataType = LiveBlogPostBlockDataType.BLOCKQUOTE.name;
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);
        expectedLiveBlogPost.getBlocks().get(0).getData().setType(expectedDataType);
        expectedLiveBlogPost.getBlocks().get(0).getData().setObject(null);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not blockquote"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }

    @Test
    public void liveBlogPostReturned_when_postRequestWithParagraph_and_codeBlockText() {
        var expectedParagraph = String.format("<pre><code>%s</code></pre>", faker.hitchhikersGuideToTheGalaxy().marvinQuote());
        expectedLiveBlogPost = liveBlogPostFactory.buildLiveBlogPostWithCodeBlockParagraph();
        expectedLiveBlogPost.getBlocks().get(0).getData().setContent(expectedParagraph);
        expectedLiveBlogPost.setBody(expectedParagraph);

        var liveBlogPost = contentLiveBlogPostsRepo.create(expectedLiveBlogPost).getResult();

        Assertions.assertFalse(liveBlogPost.getBlocks().isEmpty(), AssertMessages.liveBlogNotReturnedFromApi());
        Assertions.assertEquals(expectedLiveBlogPost.getBody(), liveBlogPost.getBody(), AssertMessages.liveBlogPostParagraphIncorrectlyFormatting("it is not codeblock"));
        Assertions.assertEquals(expectedLiveBlogPost.getBlocks().get(0), liveBlogPost.getBlocks().get(0), AssertMessages.liveBlogPostIncorrectProperty("Block object"));
    }
}
