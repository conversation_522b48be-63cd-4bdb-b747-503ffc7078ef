package contentapi.tags;

import contentapi.BaseContentApiTests;
import data.constants.AssertMessages;
import data.models.tags.TagModel;
import data.models.tags.TagResponseModel;
import factories.tags.TagHttpFactory;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import repositories.core.ApiResponse;
import solutions.bellatrix.core.assertions.DateTimeDeltaType;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

import static org.apache.http.HttpStatus.SC_OK;

public class BaseContentApiTagsTests extends BaseContentApiTests {

    protected TagModel requestBody;
    protected TagResponseModel actualTag;

    protected void createTag(TagModel requestBody) {
        ApiResponse<TagResponseModel> createdTagResponse = tagHttpRepo.create(requestBody);
        createdTagResponse.getResponse().then().statusCode(SC_OK);
        actualTag = createdTagResponse.getResult();
    }

    protected ApiResponse<TagResponseModel> createDefaultTagPage() {
        TagModel requestBody = TagHttpFactory.buildDefaultTag();
        ApiResponse<TagResponseModel> createTagResponse = tagHttpRepo.create(requestBody);

        createTagResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createTagResponse;
    }

    protected ApiResponse<TagResponseModel> createAllFieldsTagPage() {
        TagModel requestBody = TagHttpFactory.buildTagPageAllProperties();
        ApiResponse<TagResponseModel> createTagResponse = tagHttpRepo.create(requestBody);

        createTagResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
        return createTagResponse;
    }

    protected void assertTagResponse(TagResponseModel expectedTagResponse, TagResponseModel actualTagResponse) {
        Assertions.assertNotNull(actualTagResponse, AssertMessages.responseNotContains("Tag Response"));
        Assertions.assertNotNull(actualTagResponse.getId(), AssertMessages.responseNotContains("Tag Id"));
        EntitiesAsserter.assertAreEqual(expectedTagResponse, actualTagResponse,
                DateTimeDeltaType.SECONDS, DELTA_QUANTITY_SECOND, AssertMessages.entityNotExpected("Tag Response"));
    }
}