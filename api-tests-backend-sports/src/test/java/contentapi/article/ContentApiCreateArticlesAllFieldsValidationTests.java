package contentapi.article;

import categories.APITags;
import categories.SMPCategories;
import data.constants.ContentTypeEnum;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.football.FootballTeamEnum;
import data.models.articles.ArticleModel;
import data.models.articles.ArticleResponseModel;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.images.ImageModel;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDate;

import static org.apache.http.HttpStatus.SC_OK;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_ARTICLES)
public class ContentApiCreateArticlesAllFieldsValidationTests extends BaseContentApiArticleTests {

    private static final FootballTeamEnum FIRST_TEAM = FootballTeamEnum.BARCELONA;
    private static final LocalDate LOCAL_DATE = LocalDate.now();
    private static final FootballTeamEnum SECOND_TEAM = FootballTeamEnum.MANCHESTER_CITY;
    private static final BasketballCompetitionName BASKETBALL_COMPETITION_NAME = BasketballCompetitionName.NBA;
    private ArticleModel articleToBeCreated;
    private AuthorModel defaultAuthor;
    private Category activeCategory;
    private CommonModel defaultOrigin;
    private CommonModel defaultPublishChannel;
    private CommonModel defaultPublishRegion;
    private CommonModel defaultComment;
    private Category additionalCategories;
    private ImageModel defaultImage;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        activeCategory = contentApiFacade.getActiveCategory();
        defaultAuthor = contentApiFacade.getDefaultAuthor();
        defaultOrigin = contentApiFacade.getOrigin(ContentTypeEnum.ARTICLES);
        defaultPublishChannel = contentApiFacade.getDistributionChannel();
        defaultPublishRegion = contentApiFacade.getDistributionRegion();
        defaultComment = contentApiFacade.getComment(ContentTypeEnum.ARTICLES);
        additionalCategories = contentApiFacade.getActiveCategory();
        defaultImage = contentApiFacade.getImage(false);
        articleToBeCreated = articlesFactory.buildAllFieldsArticle(activeCategory, defaultAuthor, defaultComment, additionalCategories,
                defaultPublishChannel, defaultPublishRegion, defaultOrigin, defaultImage);
    }

    @Test
    public void articleCreatedSuccessfully_when_setAllFields() {
        // TODO: navramov 16/10/2024 figure out how to validate media
//        contentApiFacade.addAllMediaDataToArticle(articleToBeCreated);
        actualArticle = articlesHttpRepo.create(articleToBeCreated);
        actualArticle.getResponse()
                .then()
                .statusCode(SC_OK);

        ArticleResponseModel expectedArticleResponse = articlesFactory.buildExpectedArticleCreateResponse(
                articleToBeCreated,
                actualArticle.getResult().getId(),
                activeCategory,
                defaultAuthor,
                User.FULLSETUP,
                defaultOrigin,
                defaultPublishChannel,
                defaultPublishRegion,
                defaultComment,
                additionalCategories,
                defaultImage);

        assertArticleResponse(expectedArticleResponse, actualArticle.getResult());
    }

    @Test
    public void articleCreatedSuccessfully_when_setAllFieldsAndAddTeamComparisonBlocky() {
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(footballApiFacade.getTeamH2HBlockySportData(FIRST_TEAM, SECOND_TEAM)));
        actualArticle = articlesHttpRepo.create(articleToBeCreated);
        actualArticle.getResponse()
                .then()
                .statusCode(SC_OK);

        ArticleResponseModel expectedArticleResponse = articlesFactory.buildExpectedArticleCreateResponse(
                articleToBeCreated,
                actualArticle.getResult().getId(),
                activeCategory,
                defaultAuthor,
                User.FULLSETUP,
                defaultOrigin,
                defaultPublishChannel,
                defaultPublishRegion,
                defaultComment,
                additionalCategories,
                defaultImage);

        assertArticleResponse(expectedArticleResponse, actualArticle.getResult());
    }

    @Test
    public void articleCreatedSuccessfully_when_setAllFieldsAndAddLivescoreTennisBlocky() {
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithTennisLivescore(tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));
        actualArticle = articlesHttpRepo.create(articleToBeCreated);
        actualArticle.getResponse()
                .then()
                .statusCode(SC_OK);

        ArticleResponseModel expectedArticleResponse = articlesFactory.buildExpectedArticleCreateResponse(
                articleToBeCreated,
                actualArticle.getResult().getId(),
                activeCategory,
                defaultAuthor,
                User.FULLSETUP,
                defaultOrigin,
                defaultPublishChannel,
                defaultPublishRegion,
                defaultComment,
                additionalCategories,
                defaultImage);

        assertArticleResponse(expectedArticleResponse, actualArticle.getResult());
    }

    @Test
    public void articleCreatedSuccessfully_when_setAllFieldsAndAddBasketballStandingsBlocky() {
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithBasketballStandings(basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));
        actualArticle = articlesHttpRepo.create(articleToBeCreated);
        actualArticle.getResponse()
                .then()
                .statusCode(SC_OK);

        ArticleResponseModel expectedArticleResponse = articlesFactory.buildExpectedArticleCreateResponse(
                articleToBeCreated,
                actualArticle.getResult().getId(),
                activeCategory,
                defaultAuthor,
                User.FULLSETUP,
                defaultOrigin,
                defaultPublishChannel,
                defaultPublishRegion,
                defaultComment,
                additionalCategories,
                defaultImage);

        assertArticleResponse(expectedArticleResponse, actualArticle.getResult());
    }

    @Test
    public void articleCreatedSuccessfully_when_setAllFieldsAndAddAllSportBlocky() {
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(footballApiFacade.getTeamH2HBlockySportData(FIRST_TEAM, SECOND_TEAM)));
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithTennisLivescore(tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));
        articleToBeCreated.getBody().add(articlesFactory.buildBlockWithBasketballStandings(basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));

        actualArticle = articlesHttpRepo.create(articleToBeCreated);
        actualArticle.getResponse()
                .then()
                .statusCode(SC_OK);

        ArticleResponseModel expectedArticleResponse = articlesFactory.buildExpectedArticleCreateResponse(
                articleToBeCreated,
                actualArticle.getResult().getId(),
                activeCategory,
                defaultAuthor,
                User.FULLSETUP,
                defaultOrigin,
                defaultPublishChannel,
                defaultPublishRegion,
                defaultComment,
                additionalCategories,
                defaultImage);

        assertArticleResponse(expectedArticleResponse, actualArticle.getResult());
    }
}