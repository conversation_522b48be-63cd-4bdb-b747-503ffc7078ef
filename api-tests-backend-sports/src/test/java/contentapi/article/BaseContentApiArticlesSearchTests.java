package contentapi.article;

import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.enums.football.FootballEntity;
import data.models.MetaModel;
import data.models.articles.ArticleModel;
import data.models.articles.ArticleResponseModel;
import data.models.authors.AuthorModel;
import data.models.common.CommonModel;
import data.models.related.DataListObject;
import factories.articles.ArticlesHttpFactory;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import repositories.core.HttpRepository;
import solutions.bellatrix.core.utilities.Wait;

import java.util.List;

public class BaseContentApiArticlesSearchTests extends BaseContentApiArticleTests {

    protected static final int EXPECTED_ARTICLE_COUNT = 1;
    protected ArticleResponseModel createdArticleWithSpecificAuthor;
    protected ArticleResponseModel createdArticleWithPublishedChannel;
    protected AuthorModel author;
    protected CommonModel distributionChannel;
    protected DataListObject team;
    protected DataListObject player;
    protected List<ArticleResponseModel> foundArticles;

    @Override
    protected void beforeAll() {
        createdArticle = contentApiFacade.createArticle();
        author = contentApiFacade.createAuthor();
        distributionChannel = contentApiFacade.getDistributionChannel();

        createdArticleWithSpecificAuthor = articlesHttpRepo
                .create(ArticlesHttpFactory.buildArticleWithAuthor(author))
                .getResult();

        createdArticleWithPublishedChannel = articlesHttpRepo
                .create(ArticlesHttpFactory.buildArticleWithPublishedChannel(distributionChannel))
                .getResult();

        List<DataListObject> relatedData = contentApiFacade
                .createRelatedDataEntity(createdArticle, ENTITY_TYPE, FootballEntity.TEAM, FootballEntity.PLAYER)
                .getResult();

        team = relatedData.get(0);
        player = relatedData.get(1);
    }

    protected void waitForArticleSearchResponse(HttpRepository<ArticleModel, ArticleResponseModel, MetaModel> repository) {
        Wait.retry(() -> {
            var articleSearchResponse = repository.searchByString("*", queryParams);
            articleSearchResponse.getResponse().then().statusCode(HttpStatus.SC_OK);
            foundArticles = articleSearchResponse.getResult();

            if (foundArticles.size() != EXPECTED_ARTICLE_COUNT) {
                throw new RuntimeException(AssertMessages.entityCountNotCorrect(StringConstants.ARTICLE_STRING));
            }
        }, TIMES_TO_RETRY, SLEEP_INTERVAL, RuntimeException.class);
    }

    protected void assertFoundArticleAfterSearch(ArticleResponseModel expectedArticle) {
        Assertions.assertEquals(EXPECTED_ARTICLE_COUNT, foundArticles.size(), AssertMessages.entityCountNotCorrect(StringConstants.ARTICLE_STRING));
        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedArticle.getTitle(),
                        foundArticles.get(0).getTitle(), ARTICLE_TITLE_NOT_CORRECT),
                () -> Assertions.assertEquals(expectedArticle.getId(),
                        foundArticles.get(0).getId(), ARTICLE_ID_NOT_CORRECT)
        );
    }
}