package contentapi.article;

import categories.APITags;
import categories.SMPCategories;
import data.constants.enums.football.FootballEntity;
import io.qameta.allure.Story;
import org.assertj.core.api.CollectionAssert;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_ARTICLES)
@Tag(APITags.SCHEMA_TESTS_API)
@Story(APITags.SCHEMA_TESTS_API)
public class ContentApiArticlesSchemaTests extends BaseContentApiArticleTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        actualArticle = createDefaultArticle(FootballEntity.NONE);
    }

    @Test
    public void articleResponseSchemaIsValid_when_getArticleById() {
        var articleReturned = articlesHttpRepo.getById(actualArticle.getResult().getId());

        articlesHttpRepo.assertResponseSchema(articleReturned.getResponse());
    }

    @Test
    public void articleResponseSchemaIsValid_when_getAllArticles() {
        var getAllResponse = articlesV2HttpRepo.getAll();

        CollectionAssert.assertThatCollection(getAllResponse.getResult()).isNotEmpty();
        articlesV2HttpRepo.assertResponseSchema(getAllResponse.getResponse());
    }

    @Test
    public void articleResponseSchemaIsValid_when_createArticle() {
        articlesV2HttpRepo.assertResponseSchema(actualArticle.getResponse());
    }
}