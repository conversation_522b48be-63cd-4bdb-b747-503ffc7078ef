package contentapi.videos;

import categories.APITags;
import categories.SMPCategories;
import data.constants.enums.football.FootballEntity;
import data.models.articles.ArticleResponseModel;
import factories.video.VideoHttpFactory;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.content.RelatedHttpRepository;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_VIDEOS)
@Tag(APITags.CONTENT_API_RELATED_VIDEOS)
public class ContentApiVideosRelatedTests extends BaseContentApiVideosTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        requestBody = VideoHttpFactory.buildVideo(category);
        createVideo(requestBody);
        videoRelatedHttpRepo = new RelatedHttpRepository(getCurrentTestProject(), category.getId(), ENTITY_TYPE);
    }

    @Test
    public void videoCreatedSuccessfully_when_duplicateTagsAndRelatedEntities() {
        var expectedRelatedEntities = 3;

        var relatedVideoResult = contentApiFacade
                .createRelatedDataEntity(actualVideo.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.MATCH, FootballEntity.MATCH,
                        FootballEntity.TAG, FootballEntity.TAG);

        relatedVideoResult.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedVideoResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.MATCH.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TAG.name))));


        videoHttpRepo.assertResponseSchema(relatedVideoResult.getResponse());
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllTagsField() {
        var expectedRelatedEntities = 7;

        var relatedVideoResult = contentApiFacade
                .createRelatedDataEntity(actualVideo.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH,
                        FootballEntity.MATCH, FootballEntity.VENUE, FootballEntity.SEASON);

        relatedVideoResult.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedVideoResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.PLAYER.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TEAM.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TOURNAMENT.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.COACH.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.MATCH.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VENUE.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.SEASON.name)))
        );

        videoHttpRepo.assertResponseSchema(relatedVideoResult.getResponse());
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllRelatedContentField() {
        var expectedRelatedEntities = 4;

        var relatedVideoResult = contentApiFacade
                .createRelatedDataEntity(actualVideo.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.ARTICLE, FootballEntity.TAG,
                        FootballEntity.GALLERY, FootballEntity.VIDEO);

        relatedVideoResult.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedVideoResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.ARTICLE.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TAG.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.GALLERY.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VIDEO.name)))
        );

        videoHttpRepo.assertResponseSchema(relatedVideoResult.getResponse());
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllTagsAndRelatedContentField() {
        var expectedRelatedEntities = 11;

        var relatedVideoResult = contentApiFacade
                .createRelatedDataEntity(actualVideo.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH, FootballEntity.MATCH,
                        FootballEntity.VENUE, FootballEntity.SEASON, FootballEntity.ARTICLE,
                        FootballEntity.TAG, FootballEntity.GALLERY, FootballEntity.VIDEO);

        relatedVideoResult.getResponse().then().statusCode(HttpStatus.SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedVideoResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.PLAYER.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TEAM.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TOURNAMENT.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.COACH.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.MATCH.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VENUE.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.SEASON.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.ARTICLE.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TAG.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.GALLERY.name))),
                () -> Assertions.assertTrue(relatedVideoResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VIDEO.name)))
        );

        videoHttpRepo.assertResponseSchema(relatedVideoResult.getResponse());
    }

    @Test
    public void errorMessageReturned_when_getRelatedOptionalDataVideoIdNotExist() {
        var videoResult = ((ArticleResponseModel)actualVideo.convertToModel(ArticleResponseModel.class));
        videoResult.setId("invalidId");

        var relatedVideoResult = contentApiFacade
                .createRelatedDataEntity(
                        videoResult, ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH,
                        FootballEntity.MATCH, FootballEntity.ARTICLE,
                        FootballEntity.TAG, FootballEntity.VIDEO,
                        FootballEntity.GALLERY);

        relatedVideoResult.getResponse().then().statusCode(HttpStatus.SC_NOT_FOUND);
        videoHttpRepo.assertResponseSchema(relatedVideoResult.getResponse());
    }
}