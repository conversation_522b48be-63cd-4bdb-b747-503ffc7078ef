package contentapi.videos;

import categories.APITags;
import categories.SMPCategories;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.football.FootballTeamEnum;
import data.models.videos.VideoResponseModel;
import factories.video.VideoHttpFactory;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDate;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_VIDEOS)
public class ContentApiCreateVideosAllFieldsValidationTests extends BaseContentApiVideosTests {

    private static final FootballTeamEnum FIRST_TEAM = FootballTeamEnum.BARCELONA;
    private static final LocalDate LOCAL_DATE = LocalDate.now();
    private static final FootballTeamEnum SECOND_TEAM = FootballTeamEnum.MANCHESTER_CITY;
    private static final BasketballCompetitionName BASKETBALL_COMPETITION_NAME = BasketballCompetitionName.NBA;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        requestBody = VideoHttpFactory.buildVideoAllProperties(category, author, comment,
                additionalCategories, publishChannel, publishRegion, origin, image);
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllFields() {
        createVideo(requestBody);

        VideoResponseModel expectedVideoResponse = VideoHttpFactory.buildExpectedVideoCreateResponse(
                requestBody, actualVideo.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertVideoResponse(expectedVideoResponse, actualVideo);
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllFields_and_addTeamComparisonBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(
                footballApiFacade.getTeamH2HBlockySportData()));

        createVideo(requestBody);

        VideoResponseModel expectedVideoResponse = VideoHttpFactory.buildExpectedVideoCreateResponse(
                requestBody, actualVideo.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertVideoResponse(expectedVideoResponse, actualVideo);
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllFields_and_addLivescoreTennisBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTennisLivescore(
                tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));

        createVideo(requestBody);

        VideoResponseModel expectedVideoResponse = VideoHttpFactory.buildExpectedVideoCreateResponse(
                requestBody, actualVideo.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertVideoResponse(expectedVideoResponse, actualVideo);
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllFields_and_addBasketballStandingsBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithBasketballStandings(
                basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));

        createVideo(requestBody);

        VideoResponseModel expectedVideoResponse = VideoHttpFactory.buildExpectedVideoCreateResponse(
                requestBody, actualVideo.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertVideoResponse(expectedVideoResponse, actualVideo);
    }

    @Test
    public void videoCreatedSuccessfully_when_setAllFields_and_addAllSportBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(
                footballApiFacade.getTeamH2HBlockySportData()));
        requestBody.getBody().add(articlesFactory.buildBlockWithTennisLivescore(
                tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));
        requestBody.getBody().add(articlesFactory.buildBlockWithBasketballStandings(
                basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));

        createVideo(requestBody);

        VideoResponseModel expectedVideoResponse = VideoHttpFactory.buildExpectedVideoCreateResponse(
                requestBody, actualVideo.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertVideoResponse(expectedVideoResponse, actualVideo);
    }
}