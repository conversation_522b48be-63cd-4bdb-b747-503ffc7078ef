package contentapi.videos;

import categories.APITags;
import categories.SMPCategories;
import data.models.videos.VideoResponseModel;
import io.qameta.allure.Story;
import org.assertj.core.api.CollectionAssert;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_VIDEOS)
@Tag(APITags.SCHEMA_TESTS_API)
@Story(APITags.SCHEMA_TESTS_API)
public class ContentApiVideosSchemaTests extends BaseContentApiVideosTests {

    private ApiResponse<VideoResponseModel> createdVideoResponse;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        createdVideoResponse = createDefaultVideo();
    }

    @Test
    public void videoResponseSchemaIsValid_when_getVideoById() {
        var videoByIdResponse = articlesHttpRepo
                .getById(createdVideoResponse.getResult().getId());

        articlesHttpRepo.assertResponseSchema(videoByIdResponse.getResponse());
    }

    @Test
    public void videoResponseSchemaIsValid_when_getVideos() {
        var allVideosResponse = videoV2HttpRepo.getAll();

        CollectionAssert.assertThatCollection(allVideosResponse.getResult()).isNotEmpty();
        videoV2HttpRepo.assertResponseSchema(allVideosResponse.getResponse());
    }

    @Test
    public void videoResponseSchemaIsValid_when_createVideo() {
        videoHttpRepo.assertResponseSchema(createdVideoResponse.getResponse());
    }
}