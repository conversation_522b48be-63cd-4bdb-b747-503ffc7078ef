package contentapi.galleries;

import contentapi.BaseContentApiTests;
import data.constants.AssertMessages;
import data.constants.ContentTypeEnum;
import data.constants.EntityTypeEnum;
import data.models.authors.AuthorModel;
import data.models.categories.Category;
import data.models.common.CommonModel;
import data.models.galleries.GalleryRequestModel;
import data.models.galleries.GalleryResponseModel;
import data.models.images.ImageModel;
import factories.galleries.GalleryHttpFactory;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import repositories.content.RelatedHttpRepository;
import repositories.core.ApiResponse;
import solutions.bellatrix.core.assertions.DateTimeDeltaType;
import solutions.bellatrix.core.assertions.EntitiesAsserter;

import java.util.List;

import static org.apache.http.HttpStatus.SC_OK;

public class BaseContentApiGalleriesTests extends BaseContentApiTests {

    protected static final String ENTITY_TYPE = EntityTypeEnum.GALLERIES.name().toLowerCase();
    protected Category category;
    protected RelatedHttpRepository galleryRelatedHttpRepo;
    protected AuthorModel author;
    protected CommonModel origin;
    protected CommonModel publishChannel;
    protected CommonModel publishRegion;
    protected CommonModel comment;
    protected Category additionalCategories;
    protected ImageModel image;
    protected GalleryRequestModel requestBody;
    protected GalleryResponseModel actualGallery;

    @Override
    protected void beforeAll() {
        category = contentApiFacade.getActiveCategory();
        author = contentApiFacade.getDefaultAuthor();
        origin = contentApiFacade.getOrigin(ContentTypeEnum.GALLERIES);
        publishChannel = contentApiFacade.getDistributionChannel();
        publishRegion = contentApiFacade.getDistributionRegion();
        comment = contentApiFacade.getComment(ContentTypeEnum.GALLERIES);
        additionalCategories = contentApiFacade.getActiveCategory();
        image = contentApiFacade.getImage(false);
    }

    protected void createGallery(GalleryRequestModel requestBody) {
        ApiResponse<GalleryResponseModel> createdGalleryResponse = galleriesHttpRepo.create(requestBody);
        createdGalleryResponse.getResponse().then().statusCode(SC_OK);
        actualGallery = createdGalleryResponse.getResult();
    }

    protected ApiResponse<GalleryResponseModel> createDefaultGallery() {
        requestBody = GalleryHttpFactory.buildDefaultGallery(image.getId(), category.getId());
        requestBody.setAuthors(List.of(contentApiFacade.getDefaultAuthor().getId()));

        ApiResponse<GalleryResponseModel> createGalleryResponse = galleriesHttpRepo.create(requestBody);
        createGalleryResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        return createGalleryResponse;
    }

    protected void assertGalleryResponse(GalleryResponseModel expectedGalleryResponse, GalleryResponseModel actualGalleryResponse) {
        Assertions.assertNotNull(actualGalleryResponse, AssertMessages.responseNotContains("Gallery Response"));
        Assertions.assertNotNull(actualGalleryResponse.getId(), AssertMessages.responseNotContains("Gallery Id"));
        EntitiesAsserter.assertAreEqual(expectedGalleryResponse, actualGalleryResponse,
                DateTimeDeltaType.SECONDS, DELTA_QUANTITY_SECOND, AssertMessages.entityNotExpected("Gallery Response"));
    }
}