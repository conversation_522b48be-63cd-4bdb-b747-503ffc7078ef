package contentapi.galleries;

import categories.APITags;
import categories.SMPCategories;
import data.constants.enums.football.FootballEntity;
import data.models.articles.ArticleResponseModel;
import data.models.galleries.GalleryResponseModel;
import factories.galleries.GalleryHttpFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.content.RelatedHttpRepository;

import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_OK;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_ARTICLES)
@Tag(APITags.CONTENT_API_RELATED_ARTICLES)
public class ContentApiGalleriesRelatedTests extends BaseContentApiGalleriesTests {

    @Override
    protected void beforeEach() {
        super.beforeEach();
        requestBody = GalleryHttpFactory.buildDefaultGallery(image.getId(), category.getId());
        createGallery(requestBody);
        galleryRelatedHttpRepo = new RelatedHttpRepository(getCurrentTestProject(), category.getId(), ENTITY_TYPE);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllTagsField() {
        var expectedRelatedEntities = 7;

        var relatedGalleryResult = contentApiFacade
                .createRelatedDataEntity(actualGallery.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH,
                        FootballEntity.MATCH, FootballEntity.VENUE, FootballEntity.SEASON);

        relatedGalleryResult.getResponse().then().statusCode(SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedGalleryResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.PLAYER.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TEAM.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TOURNAMENT.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.COACH.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.MATCH.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VENUE.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.SEASON.name)))
        );

        galleryRelatedHttpRepo.assertResponseSchema(relatedGalleryResult.getResponse());
    }

    @Test
    public void galleryCreatedSuccessfully_when_duplicateTagsAndRelatedEntities() {
        var expectedRelatedEntities = 3;

        var relatedGalleryResult = contentApiFacade
                .createRelatedDataEntity(actualGallery.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.TEAM, FootballEntity.TEAM,
                        FootballEntity.GALLERY, FootballEntity.GALLERY);

        relatedGalleryResult.getResponse().then().statusCode(SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedGalleryResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TEAM.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.GALLERY.name))));

        galleryRelatedHttpRepo.assertResponseSchema(relatedGalleryResult.getResponse());
    }


    @Test
    public void galleryCreatedSuccessfully_when_setAllRelatedContentField() {
        var expectedRelatedEntities = 4;

        var relatedGalleryResult = contentApiFacade
                .createRelatedDataEntity(actualGallery.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.ARTICLE, FootballEntity.VIDEO,
                        FootballEntity.TAG, FootballEntity.GALLERY);

        relatedGalleryResult.getResponse().then().statusCode(SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedGalleryResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.ARTICLE.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VIDEO.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TAG.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.GALLERY.name)))
        );

        galleryRelatedHttpRepo.assertResponseSchema(relatedGalleryResult.getResponse());
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllTagsAndRelatedContentField() {
        var expectedRelatedEntities = 11;

        var relatedGalleryResult = contentApiFacade
                .createRelatedDataEntity(actualGallery.convertToModel(ArticleResponseModel.class), ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH, FootballEntity.MATCH,
                        FootballEntity.VENUE, FootballEntity.SEASON, FootballEntity.VIDEO,
                        FootballEntity.ARTICLE, FootballEntity.TAG, FootballEntity.GALLERY);

        relatedGalleryResult.getResponse().then().statusCode(SC_OK);

        Assertions.assertAll(
                () -> Assertions.assertEquals(expectedRelatedEntities, relatedGalleryResult.getResult().size(), "Not all Related Entities were added."),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.PLAYER.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TEAM.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TOURNAMENT.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.COACH.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.MATCH.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VENUE.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.SEASON.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.ARTICLE.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.TAG.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.GALLERY.name))),
                () -> Assertions.assertTrue(relatedGalleryResult.getResult().stream().anyMatch(x -> x.getType().equals(FootballEntity.VIDEO.name)))
        );

        galleryRelatedHttpRepo.assertResponseSchema(relatedGalleryResult.getResponse());
    }

    @Test
    public void errorMessageReturned_when_getGalleryIdNotExist() {
        var galleryResult = ((ArticleResponseModel)actualGallery.convertToModel(ArticleResponseModel.class));
        galleryResult.setId("invalidId");

        var relatedGalleryResult = contentApiFacade
                .createRelatedDataEntity(
                        galleryResult, ENTITY_TYPE,
                        FootballEntity.PLAYER, FootballEntity.TEAM,
                        FootballEntity.TOURNAMENT, FootballEntity.COACH,
                        FootballEntity.MATCH, FootballEntity.ARTICLE,
                        FootballEntity.TAG, FootballEntity.VIDEO,
                        FootballEntity.GALLERY);

        relatedGalleryResult.getResponse().then().statusCode(SC_NOT_FOUND);
        galleriesHttpRepo.assertResponseSchema(relatedGalleryResult.getResponse());
    }
}