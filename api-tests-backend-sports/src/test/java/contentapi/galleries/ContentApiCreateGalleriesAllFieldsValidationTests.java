package contentapi.galleries;

import categories.APITags;
import categories.SMPCategories;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.football.FootballTeamEnum;
import data.models.galleries.GalleryResponseModel;
import factories.galleries.GalleryHttpFactory;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDate;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.CONTENT_API)
@Tag(APITags.CONTENT_API_GALLERIES)
public class ContentApiCreateGalleriesAllFieldsValidationTests extends BaseContentApiGalleriesTests {

    private static final FootballTeamEnum FIRST_TEAM = FootballTeamEnum.BARCELONA;
    private static final LocalDate LOCAL_DATE = LocalDate.now();
    private static final FootballTeamEnum SECOND_TEAM = FootballTeamEnum.MANCHESTER_CITY;
    private static final BasketballCompetitionName BASKETBALL_COMPETITION_NAME = BasketballCompetitionName.NBA;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        requestBody = GalleryHttpFactory.buildGalleryAllProperties(category, author, comment,
                additionalCategories, publishChannel, publishRegion, origin, image);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllFields() {
        createGallery(requestBody);

        GalleryResponseModel expectedGalleryResponse = GalleryHttpFactory.buildExpectedGalleryCreateResponse(
                requestBody, actualGallery.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertGalleryResponse(expectedGalleryResponse, actualGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllFields_and_addTeamComparisonBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(
                footballApiFacade.getTeamH2HBlockySportData(FIRST_TEAM, SECOND_TEAM)));

        createGallery(requestBody);

        GalleryResponseModel expectedGalleryResponse = GalleryHttpFactory.buildExpectedGalleryCreateResponse(
                requestBody, actualGallery.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertGalleryResponse(expectedGalleryResponse, actualGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllFields_and_addLivescoreTennisBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTennisLivescore(
                tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));

        createGallery(requestBody);

        GalleryResponseModel expectedGalleryResponse = GalleryHttpFactory.buildExpectedGalleryCreateResponse(
                requestBody, actualGallery.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertGalleryResponse(expectedGalleryResponse, actualGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllFields_and_addBasketballStandingsBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithBasketballStandings(
                basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));

        createGallery(requestBody);

        GalleryResponseModel expectedGalleryResponse = GalleryHttpFactory.buildExpectedGalleryCreateResponse(
                requestBody, actualGallery.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertGalleryResponse(expectedGalleryResponse, actualGallery);
    }

    @Test
    public void galleryCreatedSuccessfully_when_setAllFields_and_addAllSportBlocky() {
        requestBody.getBody().add(articlesFactory.buildBlockWithTeamComparisonForArticle(
                footballApiFacade.getTeamH2HBlockySportData(FIRST_TEAM, SECOND_TEAM)));
        requestBody.getBody().add(articlesFactory.buildBlockWithTennisLivescore(
                tennisApiFacade.getLivescoreBlockySportData(LOCAL_DATE)));
        requestBody.getBody().add(articlesFactory.buildBlockWithBasketballStandings(
                basketballApiFacade.getStandingsSportData(BASKETBALL_COMPETITION_NAME)));

        createGallery(requestBody);

        GalleryResponseModel expectedGalleryResponse = GalleryHttpFactory.buildExpectedGalleryCreateResponse(
                requestBody, actualGallery.getId(), category, author,
                User.FULLSETUP, origin, publishChannel, publishRegion,
                comment, additionalCategories, image);

        assertGalleryResponse(expectedGalleryResponse, actualGallery);
    }
}