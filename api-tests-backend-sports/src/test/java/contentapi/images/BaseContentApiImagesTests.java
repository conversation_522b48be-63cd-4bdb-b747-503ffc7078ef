package contentapi.images;

import contentapi.BaseContentApiTests;
import data.constants.EntityTypeEnum;
import data.models.categories.Category;
import data.models.images.ImageModel;
import repositories.content.RelatedHttpRepository;

public class BaseContentApiImagesTests extends BaseContentApiTests {

    protected ImageModel actualImage;
    protected Category activeCategory;
    protected RelatedHttpRepository imageRelatedHttpRepo;
    protected final static String ENTITY_TYPE = EntityTypeEnum.IMAGES.name().toLowerCase();

    //ToDo hhristov 09/25/2024 - Uncomment when you can delete the image
    //  @Override
    //  protected void afterEach() {
    //      if (actualImage != null) {
    //          contentApiFacade.deleteImage(actualImage);
    //      }
    //  }
}