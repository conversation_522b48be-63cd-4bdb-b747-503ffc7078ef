package sportentitycustomizationapi;

import basepi.BaseApiTest;
import data.constants.EntityTypeEnum;
import data.constants.StringConstants;
import data.models.MetaModel;
import data.models.searchapi.ResultModel;
import data.models.searchapi.Translation;
import data.models.sportentitycustomization.TranslationModel;
import data.models.sportentitycustomization.TranslationsModel;
import data.models.tennisapi.TournamentModel;
import factories.sportentitycustomization.SportEntityCustomizationTranslationsHttpFactory;
import org.apache.http.HttpStatus;
import org.openqa.selenium.NotFoundException;
import repositories.basketball.BasketballTeamsHttpRepository;
import repositories.core.ApiEntity;
import repositories.core.SportApiHttpRepository;
import repositories.sportentitycustomization.SportEntityCustomizationAssetsUploadRepository;
import repositories.sportentitycustomization.SportEntityCustomizationTranslationsHttpRepository;

import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class BaseSportEntityCustomizationApiTests extends BaseApiTest {

    protected SportEntityCustomizationTranslationsHttpRepository sportEntityCustomizationTranslationsHttpRepo;
    protected SportEntityCustomizationAssetsUploadRepository sportEntityCustomizationAssetsUploadRepo;
    protected BasketballTeamsHttpRepository basketballTeamsHttpRepo;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        sportEntityCustomizationTranslationsHttpRepo = new SportEntityCustomizationTranslationsHttpRepository(getCurrentTestProject());
        sportEntityCustomizationAssetsUploadRepo = new SportEntityCustomizationAssetsUploadRepository(getCurrentTestProject());
        basketballTeamsHttpRepo = new BasketballTeamsHttpRepository(getCurrentTestProject());
    }
}