package standingsapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StandingsSportEnum;
import data.constants.StringConstants;
import data.models.searchapi.ResultModel;
import data.models.standingapi.AvailableSeasonModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.standings.StandingsAvailableSeasonsHttpRepository;

import static org.hamcrest.Matchers.equalTo;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.STANDINGS_API)
@Story(APITags.STANDINGS_API)
public class StandingsApiAvailableSeasonsTests extends BaseStandingsApiTests {

    @Test
    public void errorMessageReturned_when_getAvailableSeasonsRequest_and_setNotSupportedSport() {
        standingsAvailableSeasonsHttpRepo = new StandingsAvailableSeasonsHttpRepository(getCurrentTestProject(), StandingsSportEnum.FOOTBALL.name().toLowerCase());
        queryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, "123");

        standingsAvailableSeasonsHttpRepo.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        equalTo("Filters are invalid. /sport is a path parameter, must be one of the following: [%s, %s]".formatted(
                                StandingsSportEnum.BASKETBALL.name().toLowerCase(),
                                StandingsSportEnum.ICE_HOCKEY.name().toLowerCase()
                        )));
    }

    @ParameterizedTest
    @EnumSource(value = StandingsSportEnum.class, names = {"BASKETBALL", "ICE_HOCKEY"}, mode = EnumSource.Mode.INCLUDE)
    public void errorMessageReturned_when_getAvailableSeasonsRequest_and_setInvalidValueCompetitionIdQueryParam(StandingsSportEnum sport) {
        standingsAvailableSeasonsHttpRepo = new StandingsAvailableSeasonsHttpRepository(getCurrentTestProject(), sport.name().toLowerCase());
        queryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, "123");

        standingsAvailableSeasonsHttpRepo.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, equalTo(AssertMessages.fieldContainsInvalidUUID(StringConstants.COMPETITION_ID_UNDERSCORED_STRING)));
    }

    @ParameterizedTest
    @EnumSource(value = StandingsSportEnum.class, names = {"BASKETBALL", "ICE_HOCKEY"}, mode = EnumSource.Mode.INCLUDE)
    public void availableSeasonsReturned_when_getRequest_and_setCompetitionIdQueryParam(StandingsSportEnum sport) {
        preQueryParams.put(StringConstants.SPORT_STRING, sport.name().toLowerCase());
        ResultModel competition = searchV2SuggestRepo.getCompetitions(preQueryParams).get(0);

        queryParams.put(StringConstants.COMPETITION_ID_UNDERSCORED_STRING, competition.getId());
        standingsAvailableSeasonsHttpRepo = new StandingsAvailableSeasonsHttpRepository(getCurrentTestProject(), sport.name().toLowerCase());
        var availableSeasonsApiResponse = standingsAvailableSeasonsHttpRepo.get(queryParams);
        availableSeasonsApiResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        AvailableSeasonModel responseBody = availableSeasonsApiResponse.getResult();
        Assertions.assertNotNull(responseBody, AssertMessages.responseNotContains("Available seasons"));
        Assertions.assertNotNull(responseBody.getCompetition(), AssertMessages.responseNotContains("Competition"));
        Assertions.assertFalse(responseBody.getAvailableSeasons().isEmpty(), AssertMessages.responseNotContains("Available seasons"));
    }
}