package standingsapi;

import basepi.BaseApiTest;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.constants.enums.basketball.BasketballCompetitionName;
import data.constants.enums.basketball.BasketballTeamEnum;
import data.constants.enums.enetpulseproxy.IceHockeyCompetition;
import data.constants.enums.football.FootballTeamEnum;
import data.constants.enums.football.FootballTournamentEnum;
import data.constants.enums.icehockey.IceHockeyTeamName;
import data.models.searchapi.EventStageModel;
import data.models.searchapi.ResultModel;
import data.models.searchapi.events.SeasonModel;
import repositories.football.v2.FootballSeasonsV2HttpRepository;
import repositories.standings.StandingsAvailableSeasonsHttpRepository;
import repositories.standings.StandingsStandingTypesHttpRepository;
import repositories.standings.StandingsStandingsHttpRepository;
import repositories.standings.availablestandings.StandingsAvailableStandingsHttpRepository;

public class BaseStandingsApiTests extends BaseApiTest {

    protected String sport;
    protected StandingsAvailableSeasonsHttpRepository standingsAvailableSeasonsHttpRepo;
    protected StandingsAvailableStandingsHttpRepository standingsAvailableStandingsHttpRepo;
    protected StandingsStandingsHttpRepository standingsStandingsHttpRepo;
    protected StandingsStandingTypesHttpRepository standingsStandingTypesHttpRepo;
    protected FootballSeasonsV2HttpRepository footballSeasonsV2HttpRepo;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        standingsStandingTypesHttpRepo = new StandingsStandingTypesHttpRepository(getCurrentTestProject());
        footballSeasonsV2HttpRepo = new FootballSeasonsV2HttpRepository(getCurrentTestProject());
    }

    protected SeasonModel getSeason(String sport) {
        return getEventFor(sport).getSeason();
    }

    protected EventStageModel getTournamentStage(String sport) {
        return getEventFor(sport).getStage();
    }

    protected ResultModel getEventFor(String sport) {
        String teamName;
        String competition;
        String entityType = StringConstants.GAME_STRING;

        if (sport.equalsIgnoreCase(SupportedSports.FOOTBALL.getValue())) {
            teamName = FootballTeamEnum.BARCELONA.getName();
            competition = FootballTournamentEnum.LA_LIGA.getName();
            entityType = StringConstants.MATCH_STRING;
        } else if (sport.equalsIgnoreCase(SupportedSports.BASKETBALL.getValue())) {
            teamName = BasketballTeamEnum.LA_LAKERS.getName();
            competition = BasketballCompetitionName.NBA.getName();
        } else if (sport.equalsIgnoreCase(SupportedSports.ICE_HOCKEY.getValue())) {
            teamName = IceHockeyTeamName.NEW_YORK_RANGERS.getName();
            competition = IceHockeyCompetition.NHL.getName();
        } else {
            throw new IllegalArgumentException("Unsupported sport: %s".formatted(sport));
        }

        preQueryParams.put(StringConstants.NAME_STRING, teamName);
        preQueryParams.put(StringConstants.ENTITY_TYPE_STRING, entityType);
        preQueryParams.put(StringConstants.SPORT_STRING, sport);

        ResultModel event = searchV2SuggestRepo.getAll(preQueryParams).getResult().stream()
                .filter(game -> game.getSeason() != null && game.getStage().getName().equals(competition))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Event not found"));

        preQueryParams.clear();
        return event;
    }
}