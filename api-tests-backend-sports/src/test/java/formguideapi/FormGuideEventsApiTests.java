package formguideapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.MethodParameterType;
import data.constants.StringConstants;
import data.constants.SupportedSports;
import data.models.formguideapi.EventModel;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.StringConstants.MESSAGE_STRING;
import static data.constants.StringConstants.SPORT_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FORM_GUIDE_API)
@Story(APITags.FORM_GUIDE_API)
public class FormGuideEventsApiTests extends BaseFormGuideEventsApiTests {

    @Test
    public void formGuideEventsReturned_when_getRequestAll() {
        setupPreQueryParams();
        var seasonId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getSeason().getId();

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId);
        var eventListResponse = formGuideEventsHttpRepository.getAll(queryParams);

        eventListResponse.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        Assertions.assertFalse(eventListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Event objects list"));
    }

    @ParameterizedTest
    @EnumSource(value = SupportedSports.class, names = {"FOOTBALL", "BASKETBALL", "TENNIS", "ICE_HOCKEY"}, mode = EnumSource.Mode.INCLUDE)
    public void errorMessageReturned_when_getRequestAll_and_removeQueryParameters(SupportedSports sport) {
        formGuideEventsHttpRepository.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(SPORT_STRING, MethodParameterType.STRING)));
    }

    @Test
    public void errorMessageReturned_when_getCompetitionsRequest_and_setSportQueryParameterOnly() {
        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());

        formGuideEventsHttpRepository.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(SPORT_STRING, MethodParameterType.STRING)));
    }

    @Test
    public void errorMessageReturned_when_getCompetitionsRequest_and_setSeasonIdQueryParameterOnly() {
        setupPreQueryParams();
        var seasonId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getSeason().getId();
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId);

        formGuideEventsHttpRepository.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredRequestParameter(SPORT_STRING, MethodParameterType.STRING)));
    }

    @Test
    public void formGuideParticipantsReturned_when_getParticipantsRequest() {
        setupPreQueryParams();
        var seasonId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getSeason().getId();

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId);

        var response = formGuideEventsHttpRepository.getAll(queryParams);
        var eventListResponse = response.getResult().get(0).getParticipant();

        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        Assertions.assertNotNull(eventListResponse, AssertMessages.responseNotContains("Participants details object"));
    }

    @Test
    public void formGuideApi_returnsFirstEventWithValidOutcome() {
        setupPreQueryParams();
        var seasonId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getSeason().getId();

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId);
        var response = formGuideEventsHttpRepository.getAll(queryParams);

        var eventListResponse = response.getResult().get(0);
        List<EventModel> events = eventListResponse.getEvents();

        var outcome = events.get(0).getOutcome();

        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        Assertions.assertNotNull(outcome, AssertMessages.responseNotContains("Outcome should not be null"));
    }

    @Test
    public void formGuideApi_returnsFirstEventWithValidEventName() {
        setupPreQueryParams();
        var seasonId = searchV2SuggestRepo.getAll(preQueryParams).getResult().get(0).getSeason().getId();

        queryParams.put(StringConstants.SPORT_STRING, SupportedSports.FOOTBALL.getValue());
        queryParams.put(StringConstants.SEASON_ID_UNDERSCORED_STRING, seasonId);
        var response = formGuideEventsHttpRepository.getAll(queryParams);

        var eventListResponse = response.getResult().get(0);
        List<EventModel> events = eventListResponse.getEvents();
        var eventName = events.get(0).getStatus().getName();

        response.getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK);
        Assertions.assertNotNull(eventName, AssertMessages.responseNotContains("Event name should not be null"));
    }
}