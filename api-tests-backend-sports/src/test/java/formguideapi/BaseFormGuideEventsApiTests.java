package formguideapi;

import basepi.BaseApiTest;
import data.constants.StringConstants;
import data.widgets.options.enums.DataTeamEnum;
import repositories.formguideapi.FormGuideEventsHttpRepository;

public class BaseFormGuideEventsApiTests extends BaseApiTest {

    protected FormGuideEventsHttpRepository formGuideEventsHttpRepository;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        formGuideEventsHttpRepository = new FormGuideEventsHttpRepository(getCurrentTestProject());
    }

    protected void setupPreQueryParams() {
        preQueryParams.put(StringConstants.NAME_STRING, DataTeamEnum.BARCELONA.getFullName());
        preQueryParams.put(StringConstants.ENTITY_TYPE_STRING, StringConstants.MATCH_STRING);
        preQueryParams.put(StringConstants.EVENT_START_TIME_STRING, "2024-03-03");
    }
}