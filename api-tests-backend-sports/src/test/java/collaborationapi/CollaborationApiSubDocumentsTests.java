package collaborationapi;

import categories.APITags;
import categories.SMPCategories;
import data.models.collaborationapi.subdocument.Meta;
import data.models.collaborationapi.subdocument.SubDocumentModel;
import data.models.collaborationapi.subdocument.UpdatedBy;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import static data.constants.StringConstants.ID_STRING;
import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.COLLABORATION_API)
@Story(APITags.COLLABORATION_API)
public class CollaborationApiSubDocumentsTests extends BaseCollaborationApiTests {

    private static final String SUB_DOCUMENT_ID = "499fb5a0-6694-40e9-9eb5-0ad8940f023a";
    private static final String ADMIN_NAME = "Ivan Ivanov";

    @Test
    public void subDocumentCreated_when_postRequest() {
        UpdatedBy updatedBy = subDocumentsFactory.buildUpdatedByModel(ADMIN_ID, ADMIN_NAME);
        Meta meta = subDocumentsFactory.buildMetaModel(updatedBy);
        SubDocumentModel subDocument = subDocumentsFactory.buildPostSubDocumentBodyModel(SUB_DOCUMENT_ID, meta);

        collaborationSubDocumentsHttpRepository.create(subDocument)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, Matchers.containsString("Successful field creation"))
                .body(ID_STRING, Matchers.equalTo(DOCUMENT_ID));
    }

    @Test
    public void subDocumentCreated_when_updateRequest() {
        UpdatedBy updatedBy = subDocumentsFactory.buildUpdatedByModel(ADMIN_ID, ADMIN_NAME);
        Meta meta = subDocumentsFactory.buildMetaModel(updatedBy);
        SubDocumentModel subDocumentUpdateRequestModel = subDocumentsFactory.buildUpdateSubDocumentBodyModel(meta);

        collaborationSubDocumentsHttpRepository.update(subDocumentUpdateRequestModel)
                .getResponse().then().statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, Matchers.containsString("Successful field update"))
                .body(ID_STRING, Matchers.equalTo(DOCUMENT_ID));
    }

    @Test
    public void documentDeleted_when_deleteRequest() {
        SubDocumentModel deleteRequestModel = subDocumentsFactory.buildDeleteSubDocumentBodyModel(ADMIN_ID, ADMIN_NAME);

        collaborationSubDocumentsHttpRepository.delete(SUB_DOCUMENT_ID, deleteRequestModel)
                .then().statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, Matchers.containsString("Successful field deletion"))
                .body(ID_STRING, Matchers.equalTo(DOCUMENT_ID));
    }
}
