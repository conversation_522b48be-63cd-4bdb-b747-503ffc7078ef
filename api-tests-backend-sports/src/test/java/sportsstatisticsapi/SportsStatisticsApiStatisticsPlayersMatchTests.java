package sportsstatisticsapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.StringConstants;
import data.constants.enums.FootballPlayerStatisticEnum;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.footballapi.v2.LineupModel;
import data.models.footballapi.v2.MatchV2Model;
import data.models.footballapi.v2.PlayerV2Model;
import data.models.searchapi.ResultModel;
import data.models.sportsstatistics.StatisticModel;
import data.models.sportsstatistics.StatisticsPlayersMatchResponseModel;
import factories.sportsstatistics.SportsStatisticsHttpFactory;
import io.qameta.allure.Story;
import jdk.jfr.Description;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

import java.util.List;

@Description("These tests will be executed against Static project in order to avoid data pollution in other projects")
@Authenticate(user = User.FULLSETUP, project = Project.STATIC)
@Tag(SMPCategories.API)
@Tag(APITags.SPORTS_STATISTICS_API)
@Story(APITags.SPORTS_STATISTICS_API)
public class SportsStatisticsApiStatisticsPlayersMatchTests extends BaseSportsStatisticsApiTests {

    private ResultModel event;
    private ResultModel team;
    private ResultModel player;

    @Override
    protected void beforeAll() throws Exception {
        super.beforeAll();
        MatchV2Model match = footballApiFacade.getFinishedMatchFromTournament(FootballTournamentEnum.CHAMPIONS_LEAGUE);
        event = searchV2EventsHttpRepo.getEventById(match.getUuid());

        String homeTeam = event.getParticipantDetails().get(0).getParticipant().getId();
        LineupModel lineup = footballMatchesV2HttpRepository.getMatchLineupsResponse(event.getLegacyId()).getResult();
        team = searchV2SuggestRepo.getEntityById(homeTeam);

        PlayerV2Model playerFromLineup = lineup.getHomeTeam().getPlayers().get(0).getPlayer();
        player = searchV2SuggestRepo.getEntityById(playerFromLineup.getUuid());
    }

    @Test
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNoXProjectHeader() {
        requestBody = SportsStatisticsHttpFactory.buildEmptyStatisticsPlayersMatchRequestBody();
        sportsStatisticsStatisticsHttpRepository.removeHeader(StringConstants.X_PROJECT_STRING);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        Matchers.equalTo("400 BAD_REQUEST \"%s must be set and not empty\""
                                .formatted(StringConstants.X_PROJECT_STRING)));
    }

    @Test
    public void unauthorizedStatusCodeReturned_when_putStatisticsPlayersMatch_and_setNoAuthorizationHeader() {
        requestBody = SportsStatisticsHttpFactory.buildEmptyStatisticsPlayersMatchRequestBody();
        sportsStatisticsStatisticsHttpRepository.removeHeader(StringConstants.AUTHORIZATION_STRING);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNAUTHORIZED);
    }

    @Test
    public void badRequestReturned_when_putStatisticsPlayersMatch_and_setEmptyRequestBody() {
        requestBody = SportsStatisticsHttpFactory.buildEmptyStatisticsPlayersMatchRequestBody();

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `season` is mandatory"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `id` cannot be blank"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `name` cannot be blank"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `origin` cannot be blank"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `competition` is mandatory"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `start_time` cannot be blank"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `stage` is mandatory"))
                .body(StringConstants.MESSAGE_STRING, Matchers.containsString("Event field `player` is mandatory"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setIdPropertyEmptyOrNull(String id) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setId(id);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNamePropertyEmptyOrNull(String name) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setName(name);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `name` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setStartTimePropertyEmptyOrNull(String startTime) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setStartTime(startTime);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `start_time` cannot be blank]"));
    }

    @Test
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNoStageProperty() {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setStage(null);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `stage` is mandatory]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setStage_idPropertyEmptyOrNull(String stageId) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getStage().setId(stageId);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setStage_namePropertyEmptyOrNull(String stageName) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getStage().setName(stageName);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `name` cannot be blank]"));
    }

    @Test
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNoSeasonProperty() {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setSeason(null);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `season` is mandatory]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setSeason_idPropertyEmptyOrNull(String seasonId) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getSeason().setId(seasonId);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setSeason_namePropertyEmptyOrNull(String seasonName) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getSeason().setName(seasonName);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `name` cannot be blank]"));
    }

    @Test
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNoCompetitionProperty() {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setCompetition(null);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `competition` is mandatory]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setCompetition_idPropertyEmptyOrNull(String competitionId) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getCompetition().setId(competitionId);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setCompetition_namePropertyEmptyOrNull(String competitionName) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getCompetition().setName(competitionName);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Competition/Season/Stage field `name` cannot be blank]"));
    }

    @Test
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setNoPlayerProperty() {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setPlayer(null);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `player` is mandatory]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setPlayer_idPropertyEmptyOrNull(String playerId) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getPlayer().setId(playerId);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Player field `id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setPlayer_namePropertyEmptyOrNull(String playerName) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getPlayer().setName(playerName);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Player field `name` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setPlayer_teamIdPropertyEmptyOrNull(String playerTeamId) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getPlayer().setTeamId(playerTeamId);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Player field `team_id` cannot be blank]"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setOriginPropertyEmptyOrNull(String origin) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setOrigin(origin);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("[Event field `origin` cannot be blank]"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"AUTOMATIC", "automatic_calculation", "AutomaticCalculation"})
    public void errorMessageReturned_when_putStatisticsPlayersMatch_and_setOriginPropertyValueDifferentThanAutomaticCalculation(String origin) {
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.setOrigin(origin);

        sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING,
                        Matchers.equalTo("Field event.origin can only be %s".formatted(StringConstants.AUTOMATIC_CALCULATION_STRING)));
    }

    @Test
    public void expectedCountOfStatisticsReturned_when_putStatisticsPlayersMatch_and_setStatisticsPropertyEmpty() {
        // Get count of available statistics for player in respective event
        int statisticsCountBeforeUpdateStatistics = getStatisticsForPlayerFromEvent(player, event).size();

        // Update request of statistics for the same player but without any statistics
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        ApiResponse<List<StatisticsPlayersMatchResponseModel>> updateStatisticsResponse =
                sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody);
        updateStatisticsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        // Assert that statistics array after PUT request is empty because no statistics were provided
        Assertions.assertTrue(updateStatisticsResponse.getResult().get(0).getStatistics().isEmpty(), "Statistics array should be empty");

        // Assert that statistics count after PUT request for player in respective event is the same as before update request was made
        int statisticsCountAfterUpdateStatistics = getStatisticsForPlayerFromEvent(player, event).size();
        Assertions.assertEquals(statisticsCountBeforeUpdateStatistics, statisticsCountAfterUpdateStatistics, "Statistics count should be the same");
    }

    @Test
    public void expectedStatisticsUpdated_putStatisticsPlayersMatch_and_updateAllAvailableStatisticsWhichCanBeUpdated() {
        List<StatisticModel> expectedPlayerStatistic = SportsStatisticsHttpFactory.buildFootballPlayerStatistics(FootballPlayerStatisticEnum.getUpdatableStatistics());
        requestBody = SportsStatisticsHttpFactory.buildAllFieldsStatisticsPlayersMatchRequestBody(event, player, team);
        requestBody.getPlayer().setStatistics(expectedPlayerStatistic);

        ApiResponse<List<StatisticsPlayersMatchResponseModel>> updateStatisticsResponse =
                sportsStatisticsStatisticsHttpRepository.putStatisticsPlayersMatch(requestBody);
        updateStatisticsResponse.getResponse().then().statusCode(HttpStatus.SC_OK);

        List<StatisticModel> actualUpdatedPlayerStatistics = updateStatisticsResponse.getResult().get(0).getStatistics();

        Assertions.assertEquals(expectedPlayerStatistic.size(), actualUpdatedPlayerStatistics.size(),
                "Count of updated statistics is not correct");

        for (int i = 0; i < expectedPlayerStatistic.size(); i++) {
            StatisticModel expectedStatistic = expectedPlayerStatistic.get(i);
            StatisticModel actualStatistic = actualUpdatedPlayerStatistics.get(i);

            Assertions.assertEquals(expectedStatistic.getName(), actualStatistic.getName(),
                    "Statistic name is not correct");
            Assertions.assertEquals(expectedStatistic.getValue(), actualStatistic.getValue(),
                    "Statistic value is not correct");
        }

        List<StatisticModel> statisticsForPlayerFromEvent = getStatisticsForPlayerFromEvent(player, event);
        assertPlayerStatisticsFromGetStatisticsRequest(expectedPlayerStatistic, statisticsForPlayerFromEvent);
    }
}