package sportsstatisticsapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.StringConstants;
import data.constants.enums.FootballPlayerStatisticAggregateEnum;
import data.constants.enums.FootballPlayerStatisticEnum;
import data.constants.enums.FootballTeamStatisticAggregateEnum;
import data.constants.enums.FootballTeamStatisticEnum;
import data.models.footballapi.v2.MatchV2Model;
import data.models.searchapi.ResultModel;
import data.models.sportsstatistics.StatisticModel;
import data.widgets.options.enums.FootballPlayerEnum;
import io.qameta.allure.Story;
import jdk.jfr.Description;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.Arrays;
import java.util.List;

@Description("These tests will be executed against Static project in order to avoid data pollution in other projects")
@Authenticate(user = User.FULLSETUP, project = Project.STATIC)
@Tag(SMPCategories.API)
@Tag(APITags.SPORTS_STATISTICS_API)
@Story(APITags.SPORTS_STATISTICS_API)
public class SportsStatisticsApiAggregateTests extends BaseSportsStatisticsApiTests {

    private ResultModel event;
    private String competitionId;
    private String seasonId;
    private String stageId;
    private String teamId;
    private static final String CLUB = StringConstants.CLUB_STRING;
    private static final String NATIONAL = StringConstants.NATIONAL_TEAM_STRING;
    private static final String SEASON = StringConstants.SEASON_STRING;
    private static final String COMPETITION = StringConstants.COMPETITION_STRING;
    private static final String AGGREGATE = "aggregate";

    @Override
    protected void beforeAll() throws Exception {
        super.beforeAll();
        MatchV2Model match = getFirstFinishedMatchFromTournamentLaLiga();
        event = searchV2EventsHttpRepo.getEventById(match.getUuid());
        competitionId = event.getCompetition().getId();
        seasonId = event.getSeason().getId();
        stageId = event.getStage().getId();
        teamId = event.getParticipants().get(1);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setParticipantIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, AGGREGATE);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setCompetitionIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.PLAYER_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, COMPETITION);
    }

    @Test
    public void expectedStatisticsReturned_when_getTeamsStatistics_and_setCompetitionIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, teamId);
        queryParams.put(StringConstants.COMPETITION_IDS_STRING, competitionId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.TEAM_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertTeamStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballTeamStatisticAggregateEnum.values()), statistics, COMPETITION);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setSeasonIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.SEASON_IDS_UNDERSCORED_STRING, seasonId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.PLAYER_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, SEASON);
    }

    @Test
    public void expectedStatisticsReturned_when_getTeamsStatistics_and_setSeasonIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, teamId);
        queryParams.put(StringConstants.SEASON_IDS_UNDERSCORED_STRING, seasonId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.TEAM_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertTeamStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballTeamStatisticAggregateEnum.values()), statistics, SEASON);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setStageIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.STAGE_IDS_UNDERSCORED_STRING, stageId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.PLAYER_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, SEASON);
    }

    @Test
    public void expectedStatisticsReturned_when_getTeamsStatistics_and_setStageIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, teamId);
        queryParams.put(StringConstants.STAGE_IDS_UNDERSCORED_STRING, stageId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.TEAM_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertTeamStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballTeamStatisticAggregateEnum.values()), statistics, SEASON);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setEventIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.EVENT_IDS_STRING, event.getId());
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.PLAYER_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticEnum.values()), statistics, CLUB);
    }

    @Test
    public void expectedStatisticsReturned_when_getTeamsStatistics_and_setEventIdsQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, teamId);
        queryParams.put(StringConstants.EVENT_IDS_STRING, event.getId());
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.TEAM_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertTeamStatisticsNamesAndValuesCorrect(Arrays.asList(FootballTeamStatisticEnum.values()), statistics);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setParticipantTypePlayerQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.PLAYER_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, AGGREGATE);
    }

    @Test
    public void expectedStatisticsReturned_when_getTeamsStatistics_and_setParticipantTypeTeamQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, teamId);
        queryParams.put(StringConstants.PARTICIPAN_TYPE_STRING, StringConstants.TEAM_STRING.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertTeamStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballTeamStatisticAggregateEnum.values()), statistics, AGGREGATE);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setTeamTypeClubQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.TEAM_TYPE_STRING, CLUB.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, CLUB);
    }

    @Test
    public void expectedStatisticsReturned_when_getPlayersStatistics_and_setTeamTypeNationalQueryParam() {
        queryParams.put(StringConstants.PARTICIPANT_IDS_STRING, FootballPlayerEnum.ADRIA_PEDROSA.getId());
        queryParams.put(StringConstants.TEAM_TYPE_STRING, NATIONAL.toUpperCase());

        List<StatisticModel> statistics = sportsStatisticsAggregateHttpRepository.getAll(queryParams)
                .getResult().get(0).getStatistics();

        assertPlayerStatisticsAggregateNamesAndValuesCorrect(Arrays.asList(FootballPlayerStatisticAggregateEnum.values()), statistics, NATIONAL);
    }
}