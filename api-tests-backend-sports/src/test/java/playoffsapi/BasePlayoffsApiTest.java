package playoffsapi;

import basepi.BaseApiTest;
import data.constants.MultiSportSportsEnum;
import data.constants.StatusEnum;
import data.models.basketball.competition.CompetitionModel;
import data.models.tennisapi.TournamentModel;
import repositories.basketball.BasketballCompetitionsHttpRepository;
import repositories.basketball.BasketballSeasonsHttpRepository;
import repositories.playoffs.PlayoffsHttpRepository;
import repositories.tennis.TennisCompetitionsHttpRepository;
import repositories.tennis.TennisTournamentsHttpRepository;

import static data.constants.StringConstants.*;

public class BasePlayoffsApiTest extends BaseApiTest {

    protected PlayoffsHttpRepository playoffsHttpRepository;
    private BasketballCompetitionsHttpRepository basketballCompetitionsHttpRepo;
    private BasketballSeasonsHttpRepository basketballSeasonsRepo;
    private TennisCompetitionsHttpRepository tennisCompetitionsHttpRepo;
    protected TennisTournamentsHttpRepository tennisTournamentsHttpRepo;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        basketballCompetitionsHttpRepo = new BasketballCompetitionsHttpRepository(getCurrentTestProject());
        basketballSeasonsRepo = new BasketballSeasonsHttpRepository(getCurrentTestProject());
        tennisCompetitionsHttpRepo = new TennisCompetitionsHttpRepository(getCurrentTestProject());
        tennisTournamentsHttpRepo = new TennisTournamentsHttpRepository(getCurrentTestProject());
    }

    protected String getCurrentSeasonStageId(MultiSportSportsEnum sport) {
        String stageId;
        switch (sport) {
            case TENNIS -> {
                TournamentModel firstTennisTournamentFromResponse = tennisTournamentsHttpRepo.getAll(defaultQueryParams).getResult().get(0);
                stageId = tennisTournamentsHttpRepo.getById(firstTennisTournamentFromResponse.getId()).getResult().getId();
            }
            case BASKETBALL -> {
                CompetitionModel firstBasketballCompetitionFromResponse = basketballCompetitionsHttpRepo.getAll(defaultQueryParams).getResult().get(0);
                queryParams.put(COMPETITION_ID_UNDERSCORED_STRING, firstBasketballCompetitionFromResponse.getId());
                queryParams.put(SEASON_STRING, StatusEnum.CURRENT);
                stageId = basketballSeasonsRepo.getById(DETAILS_STRING, queryParams).getResult().getStages().get(0).getId();
                queryParams.clear();
            }
            default -> throw new IllegalArgumentException("Unexpected value [%s] for sport".formatted(sport));
        }
        return stageId;
    }

    protected String getFirstCompetitionId(MultiSportSportsEnum sport) {
        return switch (sport) {
            case TENNIS -> tennisCompetitionsHttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();
            case BASKETBALL -> basketballCompetitionsHttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();
            default -> throw new IllegalArgumentException("Unexpected value [%s] for sport".formatted(sport));
        };
    }
}
