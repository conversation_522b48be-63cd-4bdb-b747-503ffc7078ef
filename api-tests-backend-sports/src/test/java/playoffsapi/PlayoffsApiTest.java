package playoffsapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.MultiSportSportsEnum;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.playoffs.PlayoffsHttpRepository;

import static data.constants.PlayoffsType.MALE_SINGLES;
import static data.constants.PlayoffsType.MALE_TEAM;
import static data.constants.StatusEnum.CURRENT;
import static data.constants.StringConstants.*;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.PLAYOFFS_API)
@Story(APITags.PLAYOFFS_API)
public class PlayoffsApiTest extends BasePlayoffsApiTest {
    //TODO - Only basic test that verify status code. We need to add more verifications! (Ivan Radnev - 2023-12-28)

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void playoffsReturned_when_setQueryParametersWithoutDate(MultiSportSportsEnum sport) {
        playoffsHttpRepository = new PlayoffsHttpRepository(getCurrentTestProject(), sport);

        playoffsHttpRepository.getAll().getResponse().then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.requiredStageOrCompetitionParameter()));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "MULTI_SPORT", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void playoffsReturned_when_getStageIdRequestBySpecificSport(MultiSportSportsEnum sport) {
        String stageId = getCurrentSeasonStageId(sport);

        playoffsHttpRepository = new PlayoffsHttpRepository(getCurrentTestProject(), sport);

        queryParams.put(STAGE_ID_UNDERSCORED_STRING, stageId);
        playoffsHttpRepository.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_OK);
//        var response = playoffsHttpRepository.getAll(queryParams);
//        Assertions.assertEquals(HttpStatus.SC_OK, response.getResponse().getStatusCode(),
//                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
//        var responseResult = response.getResult();
//        Assertions.assertFalse(responseResult.isEmpty(), AssertMessages.responseNotContains("First Playoffs object"));
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "TENNIS", "MULTI_SPORT", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void playoffsReturned_when_getCompetitionIdRequestBySpecificSport_and_setStatus(MultiSportSportsEnum sport) {
        String competitionId = getFirstCompetitionId(sport);

        playoffsHttpRepository = new PlayoffsHttpRepository(getCurrentTestProject(), sport);

        queryParams.put(COMPETITION_ID_UNDERSCORED_STRING, competitionId);
        queryParams.put(STATUS_STRING, CURRENT);
        playoffsHttpRepository.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_OK);
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "BASKETBALL", "MULTI_SPORT", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void playoffsReturned_when_getCompetitionIdRequestBySpecificSport_and_setType(MultiSportSportsEnum sport) {
        String stageId = getCurrentSeasonStageId(sport);

        playoffsHttpRepository = new PlayoffsHttpRepository(getCurrentTestProject(), sport);

        queryParams.put(STAGE_ID_UNDERSCORED_STRING, stageId);
        queryParams.put(TYPE_STRING, MALE_SINGLES);
        playoffsHttpRepository.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_OK);
    }

    @ParameterizedTest
    @EnumSource(value = MultiSportSportsEnum.class, names = {"NOT_EXISTING", "TENNIS", "MULTI_SPORT", "ALL"}, mode = EnumSource.Mode.EXCLUDE)
    public void playoffsReturned_when_getCompetitionIdRequestBySpecificSport_and_setStatus_and_setType(MultiSportSportsEnum sport) {
        String competitionId = getFirstCompetitionId(sport);

        playoffsHttpRepository = new PlayoffsHttpRepository(getCurrentTestProject(), sport);

        queryParams.put(COMPETITION_ID_UNDERSCORED_STRING, competitionId);
        queryParams.put(STATUS_STRING, CURRENT);
        queryParams.put(TYPE_STRING, MALE_TEAM);
        playoffsHttpRepository.getAll(queryParams).getResponse().then()
                .statusCode(HttpStatus.SC_OK);
    }
}
