package imageapi;

import basepi.BaseApiTest;
import repositories.content.ImagesHttpRepository;
import repositories.content.ImagesV2HttpRepository;
import repositories.image.ImageUploadHttpRepository;
import repositories.image.ImageWatermarksHttpRepository;

public class BaseImageApiTests extends BaseApiTest {

    protected static final String ERROR_CODE_1006 = "1006";
    protected static final String ERROR_CODE_1009 = "1009";
    protected static final String ERROR_CODE_0000 = "0000";
    protected static final String ERROR_CODE_1017 = "1017";
    protected static final String ERROR_CODE_1018 = "1018";
    protected static final String LOG_REFERENCE = "IA-";
    protected ImageUploadHttpRepository imageUploadHttpRepo;
    protected ImageWatermarksHttpRepository imageWatermarksHttpRepo;
    protected ImagesV2HttpRepository imagesV2HttpRepo;
    protected ImagesHttpRepository imagesHttpRepo;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        imageUploadHttpRepo = new ImageUploadHttpRepository(getCurrentTestProject());
        imageWatermarksHttpRepo = new ImageWatermarksHttpRepository(getCurrentTestProject());
        imagesV2HttpRepo = new ImagesV2HttpRepository(getCurrentTestProject());
        imagesHttpRepo = new ImagesHttpRepository(getCurrentTestProject());
    }
}