package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.models.footballapi.v2.LineupModel;
import data.models.footballapi.v2.PlayerV2Model;
import factories.footballapi.LineupV2Factory;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.enums.football.FootballTournamentEnum.SERIE_A;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiLineupsV2Tests extends BaseFootballApiV2Tests {

    @Test
    public void footballMatchLineupsReturned_when_getV2MatchLineupRequestByMatchId() {
        var matchLineupsResponse = footballMatchesV2HttpRepo.getMatchLineupsResponse(getMatchId());

        Assertions.assertEquals(HttpStatus.SC_OK, matchLineupsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertNotNull(matchLineupsResponse.getResult(), AssertMessages.responseNotContains("Matches objects list"));
    }

    @Test
    @Issue("SBE-519")
    public void footballMatchLineupsUpdated_when_putV2MatchLineupRequestByMatchId() {
        String matchId = getMatchId();
        List<String> idsOf22Players = getIdsOf22Players();

        LineupModel lineupModel = LineupV2Factory.buildMatchLineups(idsOf22Players);

        var updateMatchLineupsResponse = footballMatchesV2HttpRepo.updateMatchLineupsResponse(matchId, lineupModel);
        Assertions.assertEquals(HttpStatus.SC_OK, updateMatchLineupsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        LineupModel expectedMatchLineups = updateMatchLineupsResponse.getResult();
        LineupModel actualMatchLineups = footballMatchesV2HttpRepo.getMatchLineupsResponse(matchId).getResult();

        Assertions.assertEquals(expectedMatchLineups, actualMatchLineups, AssertMessages.responseNotContains("Match lineups"));
    }

    private String getMatchId() {
        defaultQueryParams.put(StringConstants.TOURNAMENT_IDS_STRING, SERIE_A.getId());
        return footballMatchesV2HttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();
    }

    private List<String> getIdsOf22Players() {
        return footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult()
                .stream()
                .filter(e -> e.getName().length() < 20)
                .map(PlayerV2Model::getId).limit(22).toList();
    }
}