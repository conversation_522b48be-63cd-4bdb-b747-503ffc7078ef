package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.ImageApiUrl;
import data.models.footballapi.v2.AssetsPlayer;
import data.models.footballapi.v2.assets.AssetModel;
import factories.footballapi.AssetsV2Factory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;

import java.util.List;

import static data.constants.StringConstants.MESSAGE_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiAssetsV2Tests extends BaseFootballApiV2Tests {

    private final String expectedAssetUrl = ImageApiUrl.PROCESS_INTEGRATION.getUrl() + "/assets/%s";
    private final AssetsV2Factory assetsV2Factory = new AssetsV2Factory();
    private AssetModel expectedAssetForCreation;
    private String playerId;

    @Test
    public void footballAssetsForPlayerCreated_when_postV2AssetsRequestForPlayerEntity() {
        var createdAssetResponse = createAsset();

        Assertions.assertEquals(HttpStatus.SC_OK, createdAssetResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        AssetModel actualCreatedAsset = createdAssetResponse.getResult().get(0);
        Assertions.assertEquals(expectedAssetForCreation, actualCreatedAsset, AssertMessages.responseNotContains("Created Asset object"));

        String actualPlayerAsset = footballPlayersV2HttpRepo.getById(playerId).getResult().getAssets().getImage().getUrl();
        String expectedPlayerAsset = expectedAssetUrl.formatted(expectedAssetForCreation.getPath());
        Assertions.assertEquals(expectedPlayerAsset, actualPlayerAsset, AssertMessages.responseNotContains("Created Asset object"));

        //delete asset
        actualCreatedAsset.setPath(null);
        footballAssetsV2HttpRepo.delete(actualCreatedAsset);
    }

    @Test
    public void footballAssetsForPlayerDeleted_when_deleteV2AssetsRequestForPlayerEntity() {
        var createdAssetResponse = createAsset();
        AssetModel assetForDelete = createdAssetResponse.getResult().get(0);
        assetForDelete.setPath(null);

        footballAssetsV2HttpRepo.delete(assetForDelete).then()
                .statusCode(HttpStatus.SC_OK)
                .body(MESSAGE_STRING, Matchers.equalTo("Assets successfully deleted."));

        AssetsPlayer actualPlayerAsset = footballPlayersV2HttpRepo.getById(playerId).getResult().getAssets();
        Assertions.assertNull(actualPlayerAsset, AssertMessages.responseNotContains("Deleted Asset object"));
    }

    private ApiResponse<List<AssetModel>> createAsset() {
        playerId = footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();
        expectedAssetForCreation = assetsV2Factory.buildCreateAssetRequestBodyForPlayerEntity(playerId);

        return footballAssetsV2HttpRepo.createAsset(expectedAssetForCreation);
    }
}