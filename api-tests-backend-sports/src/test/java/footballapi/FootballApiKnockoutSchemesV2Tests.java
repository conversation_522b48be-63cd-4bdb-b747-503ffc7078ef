package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.enums.football.FootballTournamentEnum;
import io.qameta.allure.Issue;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import static data.constants.StringConstants.*;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiKnockoutSchemesV2Tests extends BaseFootballApiV2Tests {

    @Test
    public void footballKnockoutSchemesReturned_when_getV2KnockoutSchemesRequestByStageId() {
        // Get stage id from tournament with knockout scheme (World Cup)
        queryParams.put(TOURNAMENT_IDS_STRING, FootballTournamentEnum.WORLD_CUP.getId());
        String stageId = footballStagesHttpRepo.getAll(queryParams).getResult().get(0).getEntityId();

        var listKnockoutSchemesResponse = footballKnockoutSchemesV2HttpRepo.getAll(stageId);

        Assertions.assertEquals(HttpStatus.SC_OK, listKnockoutSchemesResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(listKnockoutSchemesResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Knockout schemes objects list"));
    }

    @Test
    public void errorMessageReturned_when_getV2KnockoutSchemesRequestByStageId_and_setStageIdAsString() {
        String stageIdAsString = "12O3";

        footballKnockoutSchemesV2HttpRepo.getAll(stageIdAsString).getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body(MESSAGE_STRING, Matchers.equalTo("id should be a number"));
    }

    @Test
    public void errorMessageReturned_when_getV2KnockoutSchemesRequestByStageId_and_stageIdValueGreaterThanMaxValue() {
        footballKnockoutSchemesV2HttpRepo.getById(String.valueOf(ID_MAX_VALUE + 1))
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body(MESSAGE_STRING, Matchers.equalTo("id parameter should be valid number less than %s".formatted(ID_MAX_VALUE)));
    }

    @Test
    @Issue("SBE-2923")
    public void errorMessageReturned_when_getV2KnockoutSchemesRequestByStageId_and_setNotExistingStageId() {
        long stageId = ID_MAX_VALUE;

        footballKnockoutSchemesV2HttpRepo.getById(String.valueOf(stageId))
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Resource with identifier %s %s not found.".formatted(stageId, STAGE_ID_UNDERSCORED_STRING)));
    }
}