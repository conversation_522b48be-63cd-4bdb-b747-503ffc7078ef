package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.util.List;

import static data.constants.StringConstants.*;
import static data.constants.enums.football.FootballTournamentEnum.SERIE_A;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiRoundsV2Tests extends BaseFootballApiV2Tests {

    @Test
    public void footballRoundsReturned_when_getV2RoundsRequest_and_setRequiredQueryParamSeasonId() {
        defaultQueryParams.put(TOURNAMENT_ID_STRING, SERIE_A.getId());
        String seasonId = footballSeasonsV2HttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();

        queryParams.put(SEASON_ID_UNDERSCORED_STRING, seasonId);
        var listRoundsResponse = footballRoundsV2HttpRepo.getAll(queryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, listRoundsResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertNotNull(listRoundsResponse.getResult(), AssertMessages.responseNotContains("Rounds objects list"));
    }

    @Test
    public void errorMessageReturned_when_getV2RoundsRequest_and_setNoQueryParams() {
        footballRoundsV2HttpRepo.getAll().getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(AssertMessages.missingFilter(SEASON_ID_UNDERSCORED_STRING)));
    }

    @Test
    public void errorMessageReturned_when_getV2RoundsRequest_and_setNotExistingSeasonId() {
        final int notExistingSeasonId = 9999999;
        queryParams.put(SEASON_ID_UNDERSCORED_STRING, notExistingSeasonId);

        footballRoundsV2HttpRepo.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo("Season with id %s does not exist.".formatted(notExistingSeasonId)));
    }

    @Test
    public void errorMessageReturned_when_getV2RoundsRequest_and_setStringValueForSeasonId() {
        final String seasonIdAsString = "season123";
        final int expectedSizeMethodAsString = 1;
        queryParams.put(SEASON_ID_UNDERSCORED_STRING, seasonIdAsString);

        footballRoundsV2HttpRepo.getAll(queryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body(SIZE_METHOD_AS_STRING, is(expectedSizeMethodAsString))
                .body(SEASON_ID_UNDERSCORED_STRING, equalTo(List.of(VALIDATION_REQUIRED_INTEGER_STRING)));
    }
}