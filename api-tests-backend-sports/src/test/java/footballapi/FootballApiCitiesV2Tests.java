package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.models.footballapi.v2.CityModel;
import factories.footballapi.CitiesV2Factory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import static data.constants.StringConstants.ID_STRING;
import static data.constants.StringConstants.NAME_STRING;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiCitiesV2Tests extends BaseFootballApiV2Tests {

    private final CitiesV2Factory citiesV2Factory = new CitiesV2Factory();

    @Test
    public void cityCreated_when_postV2CitiesRequest() {
        CityModel cityForCreation = citiesV2Factory.buildDefaultCreateCityRequestBody();

        var createdCityResponse = footballCitiesV2HttpRepo.create(cityForCreation);
        Assertions.assertEquals(HttpStatus.SC_OK, createdCityResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        CityModel actualCreatedCity = createdCityResponse.getResult();
        Assertions.assertNotNull(actualCreatedCity, AssertMessages.responseNotContains("Created City object"));
        Assertions.assertEquals(cityForCreation.getName(), actualCreatedCity.getName(), AssertMessages.responseNotContains(NAME_STRING));
    }

    @Test
    public void cityUpdated_when_putV2CitiesRequest() {
        CityModel cityForCreation = citiesV2Factory.buildDefaultUpdateCityRequestBody();
        String cityId = footballCitiesV2HttpRepo.create(cityForCreation).getResult().getId();

        CityModel cityForUpdate = citiesV2Factory.buildDefaultUpdateCityRequestBody();
        var updatedPresidentResponse = footballCitiesV2HttpRepo.update(cityId, cityForUpdate);
        Assertions.assertEquals(HttpStatus.SC_OK, updatedPresidentResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        CityModel actualUpdatedCity = updatedPresidentResponse.getResult();

        Assertions.assertNotNull(actualUpdatedCity, AssertMessages.responseNotContains("Updated City object"));
        Assertions.assertEquals(cityId, actualUpdatedCity.getId(), AssertMessages.responseNotContains(ID_STRING));
        Assertions.assertEquals(cityForUpdate.getName(), actualUpdatedCity.getName(), AssertMessages.responseNotContains(NAME_STRING));
    }
}