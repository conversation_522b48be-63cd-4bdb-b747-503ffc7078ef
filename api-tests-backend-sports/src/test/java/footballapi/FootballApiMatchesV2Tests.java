package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.StringConstants;
import data.constants.enums.football.FootballMatchStatus;
import data.constants.enums.football.FootballTournamentEnum;
import data.models.footballapi.v2.CreateMatchV2Model;
import data.models.footballapi.v2.MatchV2Model;
import factories.footballapi.MatchV2Factory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;

import java.time.LocalDate;

import static data.constants.enums.football.FootballTeamEnum.AC_MILAN;
import static data.constants.enums.football.FootballTeamEnum.JUVENTUS;
import static data.constants.enums.football.FootballTournamentEnum.SERIE_A;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiMatchesV2Tests extends BaseFootballApiV2Tests {

    @Test
    public void footballMatchesReturned_when_getV2MatchesRequest() {
        defaultQueryParams.put(StringConstants.TOURNAMENT_IDS_STRING, SERIE_A.getId());

        var matchesListResponse = footballMatchesV2HttpRepo.getAll(defaultQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, matchesListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(matchesListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Matches objects list"));
    }

    @Test
    public void errorMessageReturned_when_getV2MatchesRequest_and_notExistingTournamentId() {
        int notExistingTournamentId = 999999;
        defaultQueryParams.put(StringConstants.TOURNAMENT_IDS_STRING, notExistingTournamentId);

        footballMatchesV2HttpRepo.getAll(defaultQueryParams).getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Tournament with id %s does not exist.".formatted(notExistingTournamentId)));
    }

    @Test
    public void footballMatchReturned_when_getV2MatchRequestById() {
        defaultQueryParams.put(StringConstants.TOURNAMENT_IDS_STRING, SERIE_A.getId());
        var matchesListResponse = footballMatchesV2HttpRepo.getAll(defaultQueryParams);

        MatchV2Model expectedMatch = matchesListResponse.getResult().get(0);

        var matchResponse = footballMatchesV2HttpRepo.getById(expectedMatch.getId());
        MatchV2Model actualMatch = matchResponse.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, matchResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertEquals(expectedMatch, actualMatch, AssertMessages.responseNotContains("Match object"));
    }

    @Test
    public void errorMessageReturned_when_getV2MatchRequestById_and_notExistingMatchId() {
        String notExistingMatchId = "999999";

        footballMatchesV2HttpRepo.getById(notExistingMatchId).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(StringConstants.MESSAGE_STRING, Matchers.equalTo("Match not found:%s".formatted(notExistingMatchId)));
    }

    @Test
    public void footballMatchCreated_when_postMatchesV2Request() {
        var createdNotStartedMatchResponse = footballApiFacade.createNotStartedMatch(AC_MILAN, JUVENTUS, SERIE_A, "2023/2024");

        Assertions.assertEquals(HttpStatus.SC_OK, createdNotStartedMatchResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        MatchV2Model expectedCreatedMatch = createdNotStartedMatchResponse.getResult();
        MatchV2Model actualCreatedMatch = footballMatchesV2HttpRepo.getById(expectedCreatedMatch.getId()).getResult();
        Assertions.assertEquals(expectedCreatedMatch, actualCreatedMatch, AssertMessages.responseNotContains("Created match object"));
    }

    @Test
    public void footballMatchUpdated_when_putMatchesV2ByIdRequest() {
        String matchId = footballApiFacade.createNotStartedMatch(AC_MILAN, JUVENTUS, SERIE_A, "2023/2024").getResult().getId();

        String homeTeamId = footballTeamsHttpRepo.getAllByName("Barcelona").get(0).getId();
        String awayTeamId = footballTeamsHttpRepo.getAllByName("Real Madrid").get(0).getId();

        CreateMatchV2Model matchForUpdate = MatchV2Factory.buildNotStartedMatchRequestBody(homeTeamId, awayTeamId, FootballTournamentEnum.LA_LIGA.getId());
        matchForUpdate.setRoundKey("2");
        matchForUpdate.setStatusId(FootballMatchStatus.POSTPONED.getId());

        var updateMatchResponse = footballMatchesV2HttpRepo.updateMatch(matchForUpdate, matchId);

        Assertions.assertEquals(HttpStatus.SC_OK, updateMatchResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        MatchV2Model expectedUpdatedMatch = updateMatchResponse.getResult();
        MatchV2Model actualUpdatedMatch = footballMatchesV2HttpRepo.getById(expectedUpdatedMatch.getId()).getResult();
        Assertions.assertEquals(expectedUpdatedMatch, actualUpdatedMatch, AssertMessages.responseNotContains("Updated match object"));
    }

    @Test
    public void footballMatchesLivescoreReturned_when_getV2MatchesLivescoreRequest() {
        defaultQueryParams.put(StringConstants.DATE_STRING, String.valueOf(LocalDate.now()));
        defaultQueryParams.put(StringConstants.UTC_OFFSET_STRING, 2);

        var livescoreResponse = footballMatchesV2HttpRepo.getAll("livescore", defaultQueryParams);
        Assertions.assertEquals(HttpStatus.SC_OK, livescoreResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(livescoreResponse.getResult().isEmpty(), AssertMessages.responseNotContains("Livescore matches objects list"));
    }
}