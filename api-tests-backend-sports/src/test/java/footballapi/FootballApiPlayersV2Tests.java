package footballapi;

import categories.APITags;
import categories.SMPCategories;
import data.constants.AssertMessages;
import data.constants.CountryEnum;
import data.constants.StringConstants;
import data.constants.enums.SportEntityEnum;
import data.constants.enums.football.FootballTeamType;
import data.models.footballapi.common.Season;
import data.models.footballapi.tournamentseason.TournamentSeasonEventModel;
import data.models.footballapi.v2.ActiveClubsRequestModel;
import data.models.footballapi.v2.PlayerActiveClubsModel;
import data.models.footballapi.v2.PlayerV2Model;
import data.models.footballapi.v2.TeamV2Model;
import data.models.searchapi.ResultModel;
import data.widgets.options.enums.FootballPlayerEnum;
import data.widgets.options.enums.StatusTypeEnum;
import factories.footballapi.ActiveClubsFactory;
import factories.footballapi.PlayerV2Factory;
import io.qameta.allure.Story;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import plugins.authentication.Authenticate;
import plugins.authentication.Project;
import plugins.authentication.User;
import repositories.core.ApiResponse;
import repositories.football.FootballTournamentSeasonEventsHttpRepository;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static data.constants.StringConstants.*;

@Authenticate(user = User.FULLSETUP, project = Project.FULLSETUP_API)
@Tag(SMPCategories.API)
@Tag(APITags.FOOTBALL_API)
@Story(APITags.FOOTBALL_API)
public class FootballApiPlayersV2Tests extends BaseFootballApiV2Tests {

    private final FootballTournamentSeasonEventsHttpRepository footballTournamentSeasonEventsHttpRepo = new FootballTournamentSeasonEventsHttpRepository(getCurrentTestProject());
    private final PlayerV2Factory playerV2ModelFactory = new PlayerV2Factory();
    private HashSet<String> seasonIds;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        seasonIds = new HashSet<>();
    }

    @Test
    public void footballPlayersReturned_when_getV2PlayersRequest() {
        var playersListResponse = footballPlayersV2HttpRepo.getAll(defaultQueryParams);

        Assertions.assertEquals(HttpStatus.SC_OK, playersListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));
        Assertions.assertFalse(playersListResponse.getResult().isEmpty(), AssertMessages.responseNotContains("First Player object"));
    }

    @Test
    public void errorMessageReturned_when_getV2PlayersRequest_and_setNoQueryParameters() {
        footballPlayersV2HttpRepo.getAll()
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(INVALID_OFFSET_OR_LIMIT_ERROR));
    }

    @Test
    public void errorMessageReturned_when_getV2PlayersRequest_and_setOffsetParameterOnly() {
        defaultQueryParams.remove(LIMIT_STRING);

        footballPlayersV2HttpRepo.getAll(defaultQueryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(INVALID_OFFSET_OR_LIMIT_ERROR));
    }

    @Test
    public void errorMessageReturned_when_getV2PlayersRequest_and_setLimitParameterOnly() {
        defaultQueryParams.remove(OFFSET_STRING);

        footballPlayersV2HttpRepo.getAll(defaultQueryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(INVALID_OFFSET_OR_LIMIT_ERROR));
    }

    @Test
    public void errorMessageReturned_when_getV2PlayersRequest_and_setLimitGreaterThanMaxValue() {
        int limitMaxValue = 1000;
        defaultQueryParams.put(LIMIT_STRING, limitMaxValue + 1);

        footballPlayersV2HttpRepo.getAll(defaultQueryParams)
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_BAD_REQUEST)
                .body(MESSAGE_STRING, Matchers.equalTo(INVALID_OFFSET_OR_LIMIT_ERROR));
    }

    @Test
    //@Issue("SBE-2939")
    public void footballPlayerReturned_when_getV2PlayersRequestById() {
        PlayerV2Model firstPlayerInResponse = footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult().get(0);

        var playerResponse = footballPlayersV2HttpRepo.getById(firstPlayerInResponse.getId());
        PlayerV2Model actualPlayer = playerResponse.getResult();

        Assertions.assertEquals(HttpStatus.SC_OK, playerResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        firstPlayerInResponse.setTeams(actualPlayer.getTeams());
        firstPlayerInResponse.setActiveSeasons(actualPlayer.getActiveSeasons());
        firstPlayerInResponse.setForm(actualPlayer.getForm());

        Assertions.assertEquals(firstPlayerInResponse, playerResponse.getResult(), AssertMessages.responseNotContains("First Player object"));
    }

    @Test
    public void footballPlayerTeamsObjectExist_when_getV2PlayersRequestById() {
        PlayerV2Model firstPlayerInResponse = footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult().get(0);

        var playerResponse = footballPlayersV2HttpRepo.getById(firstPlayerInResponse.getId());
        Assertions.assertNotNull(playerResponse.getResult().getTeams(), AssertMessages.entityNotExpected("Teams object"));
    }

    @Test
    public void errorMessageReturned_when_getV2PlayersRequestById_and_idValueGreaterThanMaxValue() {
        footballTeamsV2HttpRepo.getById(String.valueOf(ID_MAX_VALUE + 1))
                .getResponse()
                .then()
                .statusCode(HttpStatus.SC_UNPROCESSABLE_ENTITY)
                .body(MESSAGE_STRING, Matchers.equalTo("id parameter should be valid number less than %s".formatted(ID_MAX_VALUE)));
    }

    @Test
    //@Issue("SBE-2939")
    public void footballPlayerCreated_when_postV2PlayersRequest() {
        PlayerV2Model playerV2Model = playerV2ModelFactory.buildCreatePlayerPostRequestBody();
        var createPlayerResponse = footballPlayersV2HttpRepo.create(playerV2Model);

        Assertions.assertEquals(HttpStatus.SC_OK, createPlayerResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        PlayerV2Model expectedCreatedPlayer = createPlayerResponse.getResult();
        PlayerV2Model actualCreatedPlayer = footballPlayersV2HttpRepo.getById(createPlayerResponse.getResult().getId()).getResult();

        expectedCreatedPlayer.setTeams(actualCreatedPlayer.getTeams());
        expectedCreatedPlayer.setActiveSeasons(actualCreatedPlayer.getActiveSeasons());
        expectedCreatedPlayer.setForm(actualCreatedPlayer.getForm());

        Assertions.assertEquals(expectedCreatedPlayer, actualCreatedPlayer, AssertMessages.responseNotContains("Created Player object"));
    }

    @Test
    //@Issue("SBE-2939")
    public void footballPlayerUpdated_when_putV2PlayersRequestById() {
        PlayerV2Model playerForUpdate = footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult().get(0);
        playerForUpdate.setName("%s Auto Player Updated".formatted(faker.harryPotter().character()));
        playerForUpdate.setCountryId(CountryEnum.ITALY.getId());

        var updatedPlayerResponse = footballPlayersV2HttpRepo.update(playerForUpdate);

        Assertions.assertEquals(HttpStatus.SC_OK, updatedPlayerResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        PlayerV2Model actualUpdatedPlayer = updatedPlayerResponse.getResult();
        PlayerV2Model expectedUpdatedPlayer = footballPlayersV2HttpRepo.getById(actualUpdatedPlayer.getId()).getResult();

        expectedUpdatedPlayer.setTeams(actualUpdatedPlayer.getTeams());
        expectedUpdatedPlayer.setActiveSeasons(actualUpdatedPlayer.getActiveSeasons());
        expectedUpdatedPlayer.setForm(actualUpdatedPlayer.getForm());
        expectedUpdatedPlayer.setCurrentSeason(null); // Set to null in order to check both objects are equal

        Assertions.assertEquals(expectedUpdatedPlayer, actualUpdatedPlayer, AssertMessages.responseNotContains("Updated Player object"));
    }

    @Test
    public void footballPlayerUpdateActiveClubs_when_putV2PlayersActiveClubsRequest() {
        String teamId = footballTeamsV2HttpRepo.getAll(defaultQueryParams).getResult()
                .stream()
                .filter(e -> e.getType().equals(FootballTeamType.CLUB.toString().toLowerCase()))
                .findFirst()
                .orElseThrow()
                .getId();

        String playerId = footballPlayersV2HttpRepo.getAll(defaultQueryParams).getResult().get(0).getId();

        ActiveClubsRequestModel activeClubsModel = new ActiveClubsFactory().buildActiveClubRequestBody(teamId);

        var activeClubsListResponse = footballPlayersV2HttpRepo.updateActiveClubs(playerId, activeClubsModel);

        Assertions.assertEquals(HttpStatus.SC_OK, activeClubsListResponse.getResponse().getStatusCode(),
                AssertMessages.statusCodeNotExpected(String.valueOf(HttpStatus.SC_OK)));

        TeamV2Model expectedActiveClub = activeClubsListResponse.getResult().get(0).getTeam();
        TeamV2Model actualActiveClub = footballPlayersV2HttpRepo.getById(playerId).getResult().getTeams().
                stream()
                .filter(e -> e.getTeam().getId().equals(teamId))
                .findFirst()
                .orElseThrow().
                getTeam();
        actualActiveClub.getCountry().setAssets(null); // Set to null in order to check both objects are equal
        actualActiveClub.setAssets(null);

        Assertions.assertEquals(expectedActiveClub, actualActiveClub, AssertMessages.responseNotContains("Active Club object"));
    }

    @Test
    public void footballPlayerReturn_when_getPlayerByIdWithActiveClubAndNationalTeam_and_validOrderedSeasonsReturn() {
        var player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.LAMINE_YAMAL.getNameEn(), SportEntityEnum.PLAYER);
        var playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerActiveSeason = playerResponse.getResult().getActiveSeasons();
        String playerClubTeam = getPLayerByTeam(playerResponse, StringConstants.CLUB_STRING);
        String playerNationalTeam = getPLayerByTeam(playerResponse, StringConstants.NATIONAL_TEAM_STRING);
        List<TournamentSeasonEventModel> allTournaments = getTournamentSeasonEventModels(playerActiveSeason);

        List<TournamentSeasonEventModel> clubSeasons = allTournaments.stream()
                .filter(season -> season.getType().equals(StringConstants.CLUB_STRING))
                .filter(season -> season.getName().equals(playerClubTeam))
                .toList();
        List<TournamentSeasonEventModel> nationalSeasons = allTournaments.stream()
                .filter(season -> season.getType().equals(StringConstants.NATIONAL_TEAM_STRING))
                .filter(season -> season.getName().equals(playerNationalTeam))
                .toList();

        Assertions.assertFalse(playerActiveSeason.isEmpty(), "Active seasons should not be empty");
        Assertions.assertFalse(clubSeasons.isEmpty(), "Should have club seasons for %s".formatted(FootballPlayerEnum.LAMINE_YAMAL.getNameEn()));
        Assertions.assertFalse(nationalSeasons.isEmpty(), "Should have national team seasons for %s".formatted(FootballPlayerEnum.LAMINE_YAMAL.getNameEn()));
        playerActiveSeason.forEach(season -> Assertions.assertTrue(seasonIds.add(season.getId()), "Duplicate season found with ID:%s ".formatted(season.getId())));

        boolean allSeasonsActive = playerActiveSeason.stream().allMatch(season -> season.getStatus().equals(StringConstants.ACTIVE_STRING.toUpperCase()));

        Assertions.assertTrue(allSeasonsActive, "All seasons should have ACTIVE status");
        validateAllRequiredSeasonMetadata(playerActiveSeason);
    }

    @Test
    public void footballPlayerReturn_when_getPlayerByIdWithActiveClubTeam_and_validOrderedSeasonsReturn() {
        var player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.RAHEEM_STERLING.getNameEn(), SportEntityEnum.PLAYER);
        var playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerActiveSeasons = playerResponse.getResult().getActiveSeasons();
        String playerClubTeam = getPLayerByTeam(playerResponse, StringConstants.CLUB_STRING);
        String playerNationalTeam = getPLayerByTeam(playerResponse, StringConstants.NATIONAL_TEAM_STRING);
        List<TournamentSeasonEventModel> allTournaments = getTournamentSeasonEventModels(playerActiveSeasons);

        List<TournamentSeasonEventModel> clubSeasons = allTournaments.stream()
                .filter(season -> season.getType().equals(CLUB_STRING))
                .filter(season -> season.getName().equals(playerClubTeam))
                .toList();

        List<TournamentSeasonEventModel> nationalSeasons = allTournaments.stream()
                .filter(season -> season.getType().equals(StringConstants.NATIONAL_TEAM_STRING))
                .filter(season -> season.getName().equals(playerNationalTeam))
                .toList();

        Assertions.assertFalse(clubSeasons.isEmpty(), "Should have club seasons for %s".formatted(FootballPlayerEnum.RAHEEM_STERLING.getNameEn()));
        Assertions.assertTrue(nationalSeasons.isEmpty(), "Should have national team seasons for %s".formatted(FootballPlayerEnum.RAHEEM_STERLING.getNameEn()));
        playerActiveSeasons.forEach(season -> Assertions.assertTrue(seasonIds.add(season.getId()), "Duplicate season found with ID:%s ".formatted(season.getId())));

        boolean allSeasonsActive = playerActiveSeasons.stream().allMatch(season -> season.getStatus().equals(StringConstants.ACTIVE_STRING.toUpperCase()));

        Assertions.assertTrue(allSeasonsActive, "All seasons should have ACTIVE status");
        validateAllRequiredSeasonMetadata(playerActiveSeasons);
    }

    @Test
    public void footballPlayerReturn_when_getPlayerByIdWithActiveNationalTeam_and_validOrderedSeasonsReturn() {
        var player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.MANUAL_CREATED_PLAYER_WITH_NATIONAL_TEAM_ONLY.getNameEn(), SportEntityEnum.PLAYER);
        var playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerActiveSeasons = playerResponse.getResult().getActiveSeasons();
        List<PlayerActiveClubsModel> teams = playerResponse.getResult().getTeams();
        List<TournamentSeasonEventModel> allTournaments = getTournamentSeasonEventModels(playerActiveSeasons);

        List<TournamentSeasonEventModel> clubSeasons = allTournaments.stream()
                .filter(season -> season.getType().equals(StringConstants.CLUB_STRING))
                .toList();
        List<TournamentSeasonEventModel> nationalTeam = allTournaments.stream()
                .filter(season -> season.getType().equals(StringConstants.NATIONAL_TEAM_STRING))
                .toList();

        Assertions.assertTrue(clubSeasons.isEmpty(), "Player should have active national team seasons");
        Assertions.assertFalse(nationalTeam.isEmpty(), "Player should have active national team seasons");

        String playerNationalTeam = playerResponse.getResult().getCountry().getName();

        Assertions.assertFalse(teams.isEmpty(), "Player should have active national team seasons");
        Assertions.assertNotNull(playerNationalTeam, "Player's national team should be set");
        playerActiveSeasons.forEach(season -> Assertions.assertTrue(seasonIds.add(season.getId()), "Duplicate season found with ID:%s ".formatted(season.getId())));

        boolean allSeasonsActive = playerActiveSeasons.stream().allMatch(season -> season.getStatus().equals(StringConstants.ACTIVE_STRING.toUpperCase()));

        Assertions.assertTrue(allSeasonsActive, "All seasons should have ACTIVE status");
        validateAllRequiredSeasonMetadata(playerActiveSeasons);
    }

    @Test
    public void getPlayerById_returnValidResponse_when_playerHasNoActiveSeasons() {
        var player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.MANUAL_CREATED_PLAYER_WITHOUT_ACTIVE_SEASON.getNameEn(), SportEntityEnum.PLAYER);
        var playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerCurrentSeason = playerResponse.getResult().getActiveSeasons();
        List<PlayerActiveClubsModel> teams = playerResponse.getResult().getTeams();
        List<TournamentSeasonEventModel> allTournaments = getTournamentSeasonEventModels(playerCurrentSeason);

        Assertions.assertNotNull(playerResponse.getResult(), "Player result should not be null");
        Assertions.assertTrue(playerCurrentSeason.isEmpty(), "Active seasons array should be empty");
        Assertions.assertTrue(allTournaments.isEmpty(), "Tournaments list should be empty");
        Assertions.assertNull(playerResponse.getResult().getCurrentSeason(), "Current season should be null");
        Assertions.assertNotNull(teams, "Teams array should not be null even if player has no active seasons");
        Assertions.assertNotNull(playerResponse.getResult().getId(), "Player ID should be present");
        Assertions.assertNotNull(playerResponse.getResult().getName(), "Player name should be present");
    }

    @Test
    public void getPlayerById_when_playerTeamParticipateInMultipleActiveSeasonCompetitions() {
        int multipleSeasons = 2;
        var player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.LAMINE_YAMAL.getNameEn(), SportEntityEnum.PLAYER);
        var playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerActiveSeason = playerResponse.getResult().getActiveSeasons();

        Set<String> uniqueTournaments = playerActiveSeason.stream()
                .map(season -> season.getTournament().getName())
                .collect(Collectors.toSet());

        Assertions.assertTrue(uniqueTournaments.size() > multipleSeasons, "Player should participate in multiple active tournaments");

        boolean allSeasonsActive = playerActiveSeason.stream().allMatch(season -> season.getStatus().equals(StatusTypeEnum.ACTIVE.name()));

        Assertions.assertTrue(allSeasonsActive, "All seasons should have ACTIVE status");
        playerActiveSeason.forEach(season -> Assertions.assertTrue(seasonIds.add(season.getId()), "Duplicate season found with ID:%s ".formatted(season.getId())));
    }

    @Test
    public void getPlayerById_when_haveMultipleClubTeams_and_multipleActiveSeasons() {
        int minimumRequiredTeams = 2;
        ResultModel player = searchV2SuggestRepo.getEntityByName(FootballPlayerEnum.MANUAL_CREATED_PLAYER_WITH_TWO_TEAMS.getNameEn(), SportEntityEnum.PLAYER);
        ApiResponse<PlayerV2Model> playerResponse = footballPlayersV2HttpRepo.getById(player.getId());
        List<Season> playerActiveSeasons = playerResponse.getResult().getActiveSeasons();
        List<PlayerActiveClubsModel> teams = playerResponse.getResult().getTeams();
        Season playerCurrentSeason = playerResponse.getResult().getCurrentSeason();

        List<PlayerActiveClubsModel> clubTeams = teams.stream()
                .filter(team -> team.getTeam().getType().equals(CLUB_STRING))
                .filter(team -> team.getStatus().equals(ACTIVE_STRING.toUpperCase()))
                .toList();

        Assertions.assertTrue(clubTeams.size() >= minimumRequiredTeams, "Player should have multiple club teams");
        playerActiveSeasons.forEach(season -> Assertions.assertTrue(seasonIds.add(season.getId()), "Duplicate season found with ID:%s ".formatted(season.getId())));
        Assertions.assertNotNull(playerCurrentSeason, "Current season should be set");
        Assertions.assertFalse(playerActiveSeasons.isEmpty(), "Active seasons should not be empty");
        Assertions.assertTrue(playerActiveSeasons.stream().allMatch(season -> season.getStatus().equals(StringConstants.ACTIVE_STRING.toUpperCase())), "All seasons should be active");
    }

    @Test
    public void errorMessageReturned_when_givenNonExistentPlayerId() {
        String invalidIdPlayer = "99999999";

        footballPlayersV2HttpRepo.getById(invalidIdPlayer).getResponse()
                .then()
                .statusCode(HttpStatus.SC_NOT_FOUND)
                .body(MESSAGE_STRING, Matchers.equalTo("Resource with identifier %s not found.".formatted(invalidIdPlayer)));
    }

    private static String getPLayerByTeam(ApiResponse<PlayerV2Model> playerResponse, String clubTeamString) {
        return playerResponse.getResult().getTeams().stream()
                .filter(team -> team.getTeam().getType().equals(clubTeamString))
                .map(team -> team.getTeam().getName())
                .findFirst()
                .orElse(null);
    }

    private static void validateAllRequiredSeasonMetadata(List<Season> activeSeasons) {
        activeSeasons.forEach(season -> {
            Assertions.assertNotNull(season.getId(), "Season ID should not be null");
            Assertions.assertNotNull(season.getName(), "Season name should not be null");
            Assertions.assertNotNull(season.getSlug(), "Season slug should not be null");
            Assertions.assertNotNull(season.getStatus(), "Season status should not be null");
            Assertions.assertNotNull(season.getUuid(), "Season UUID should not be null");
            Assertions.assertNotNull(season.getTournament(), "Tournament should not be null");
            Assertions.assertNotNull(season.getTournament().getId(), "Tournament ID should not be null");
            Assertions.assertNotNull(season.getTournament().getName(), "Tournament name should not be null");
        });
    }

    private List<TournamentSeasonEventModel> getTournamentSeasonEventModels(List<Season> activeSeasons) {
        List<TournamentSeasonEventModel> allTournaments = new ArrayList<>();
        for (Season season : activeSeasons) {
            List<TournamentSeasonEventModel> result = footballTournamentSeasonEventsHttpRepo
                    .getTournamentSeasonByTeamsId(season.getId())
                    .getResult();
            allTournaments.addAll(result);
        }
        return allTournaments;
    }
}