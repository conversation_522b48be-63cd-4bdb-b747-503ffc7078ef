package customizationapi;

import basepi.BaseApiTest;
import repositories.customization.CustomizationCustomBlocksHttpRepository;
import repositories.customization.CustomizationSidebarResourcesHttpRepository;

import java.util.stream.Stream;

import static data.constants.StringConstants.EMPTY_STRING;

public class BaseCustomizationApiTest extends BaseApiTest {

    protected CustomizationCustomBlocksHttpRepository customizationCustomBlocksHttpRepo;
    protected CustomizationSidebarResourcesHttpRepository customizationSidebarResourcesHttpRepo;

    @Override
    protected void beforeEach() {
        super.beforeEach();
        customizationCustomBlocksHttpRepo = new CustomizationCustomBlocksHttpRepository(getCurrentTestProject());
        customizationSidebarResourcesHttpRepo = new CustomizationSidebarResourcesHttpRepository(getCurrentTestProject());
    }

    protected static Stream<String> invalidUrls() {
        return Stream.of("www.website.com", "website.com", "http//", "htt://", "https//", EMPTY_STRING);
    }
}