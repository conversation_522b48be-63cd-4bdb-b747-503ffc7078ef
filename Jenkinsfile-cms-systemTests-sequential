pipeline {
    agent  {
        node {
            label 'qa-win'
        }
    }
    environment {
        // Define COMMIT_HASH
        COMMIT_HASH=''
        CRON_SETTING=''
    }
    parameters {
        // Define a string parameter for COMMIT_HASH
        booleanParam(
                name: 'EXECUTE_TESTS',
                defaultValue: true,
                description: 'Controls test execution mode for this pipeline run.'
        )
        choice(
                name: 'EXECUTION_ENVIRONMENT',
                choices: ['staging', 'integration'],
                description: '''Target environment for test execution.
                Note: Requires EXECUTE_TESTS set to TRUE'''
        )
        string(
                name: 'CMS_BASE_URL',
                defaultValue: 'https://cms.staging.sportal365.com/',
                description: '''Base URL for the CMS environment under test.
                - Default points to main staging environment
                - For feature branches use format: https://${feature-branch}.cms.staging.sportal365.com/
                - Example for feature branch: https://sfe-4204.cms.staging.sportal365.com/'''
        )
        string(
                name: 'TESTS_FILTER',
                defaultValue: '',
                description: '''Additional test filters to combine with the default "cms" filter.
                Use & for AND, | for OR operations.
                Example: "&crud" or "|e2e"'''
        )
        choice(
                name: 'TESTS_GROUP',
                choices: [
                        '', 'cms', 'cms&blockies', 'cms&sidebar',
                        'cms&liveblog', 'cms&navigation', 'cms&configuration', 'cms&crud',
                        'cms&e2e', 'crud|e2e|clipboard', 'cms&customEntities', 'cms&customEntitiesCreate'
                ],
                description: '''Specific test group to execute.
                Common groups: &blockies, &liveblog, &sidebar, &navigation
                Example: "&blockies" for CMS blocky tests only'''
        )
        choice(
                name: 'DEFAULT_BROWSER',
                choices: [
                        'chrome_headless',
                        'firefox_headless',
                        'edge_headless',
                        'chrome',
                        'firefox',
                        'edge'
                ],
                description: '''Browser configuration for test execution.
                Headless Modes (Recommended for CI/CD):
                - chrome_headless: Fastest execution, best for CI pipelines
                - firefox_headless: Good for cross-browser validation
                - edge_headless: Suitable for Windows-specific testing
                
                Regular Modes (Better for Debug):
                - chrome: Full DevTools access, best for local debugging
                - firefox: Excellent for cross-browser compatibility
                - edge: Good for Windows-specific features
                
                Note: Headless modes recommended for automated runs'''
        )
        choice(
                name: 'SLACK_CHANNEL_NOTIFICATION',
                choices: [
                        '#platform-reports',
                        '#qa-automation',
                        '#test-executions'

                ],
                description: '''Slack channel for test results:
                 - #platform-reports: Platform test reports
                 - #qa-automation: Automation framework updates
                 - #test-executions: General test execution updates'''
        )
        string(
                name: 'COMMIT_HASH',
                defaultValue: 'No Commit Hash Provided',
                description: '''Commit hash from the previous job in the pipeline.
                - When provided, triggers a 25-minute wait period for deployment completion
                - Format example: 2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
                - Leave as default if running tests independently
                - Required for deployment verification testing'''
        )
    }
    stages {
        stage('Print Commit Hash') {
            when {
                expression {
                    return params.COMMIT_HASH != 'No Commit Hash Provided'
                }
            }
            steps {
                // Waiting for the CMS to be deployed and display the commit hash
                script {
                    echo "The commit hash is: ${params.COMMIT_HASH}"
                    echo 'Waiting for 25 minutes before triggering the pipeline...'
                    sleep(time: 25, unit: 'MINUTES')
                }
            }
        }
        stage('Build') {
            steps {
                bat 'mvn -T 8 clean package -Dmaven.test.skip -pl system-tests-cms -am'
            }
        }
        stage('Parallel Test Execution') {
            when {
                expression {
                    return (params.EXECUTE_TESTS || params.COMMIT_HASH != 'No Commit Hash Provided')
                }
            }
            steps {
                slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#FFA500', message: "Testing has started :rocket: \n Url: ${params.CMS_BASE_URL} \n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                bat """mvn test -pl system-tests-cms -am -Dsurefire.failIfNoSpecifiedTests=false -Dgroups="${TESTS_FILTER}${TESTS_GROUP}" -Denvironment=${EXECUTION_ENVIRONMENT}"""
            }
            post {
                always {
                    script {
                        try {
                            archiveArtifacts artifacts: 'TestData/**/*', fingerprint: true
                        }
                        catch (exc) {
                            echo 'No Test Artefacts Found!' + exc.getMessage()
                        }
                        env.TEST_SUMMARY = processTestResults()
                    }
                    allure([
                            includeProperties: false,
                            jdk: '',
                            properties: [],
                            reportBuildPolicy: 'ALWAYS',
                            results: [[path: '**/allure-results']]
                    ])
                    zip zipFile: './system-tests-cms/target/junit_tests.zip', archive: true, glob:'**/target/surefire-reports/*.xml'
                    sh(script: '''
                    curl -H "Content-Type: multipart/form-data" -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oc5oL-Z-VlkTAO_L2f_YjpRfKml5R6dDSEOaOwrB99k" -F "file=@./system-tests-cms/target/junit_tests.zip" "https://api.zephyrscale.smartbear.com/v2/automations/executions/junit?projectKey=SMP"
                    ''')
                }
                success {
                    script {
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#3EB991', message: "SUCCESSFUL :aaw_yeah: \n Url: ${params.CMS_BASE_URL} \n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${env.TEST_SUMMARY}\n More info at: ${env.BUILD_URL}")
                    }
                }
                failure {
                    script {
                        slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#E01563', message: "FAILED :upside_down_face: \n Url: ${params.CMS_BASE_URL} \n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n ${env.TEST_SUMMARY}\nMore info at: ${env.BUILD_URL}")
                    }
                }
                aborted {
                    slackSend (channel: "${params.SLACK_CHANNEL_NOTIFICATION}", color: '#8B4513', message: "ABORTED :airplane_departure: \n Url: ${params.CMS_BASE_URL} \n ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}")
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}

def processTestResults() {
    def testResults = junit '**/target/surefire-reports/*.xml'
    def testSummary = "Test Results:\n"
    testResults?.each { suite ->
        testSummary += "${suite.totalCount} tests were run\n"
        testSummary += "  Passed: ${suite.passCount}\n"
        testSummary += "  Failed: ${suite.failCount}\n"
        testSummary += "  Skipped: ${suite.skipCount}\n"
    }
    return testSummary
}
