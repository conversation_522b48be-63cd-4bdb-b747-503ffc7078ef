/*
 * Copyright 2022 Automate The Planet Ltd.
 * Author: <PERSON>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * You may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package solutions.bellatrix.core.plugins;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Consumer;

public class EventListener<TArgs> {
    private final Set<Consumer<TArgs>> listeners = new HashSet<>();

    public void addListener(Consumer<TArgs> listener) {
        listeners.add(listener);
    }

    public void removeListener(Consumer<TArgs> listener) {
        listeners.remove(listener);
    }

    public void broadcast(TArgs args) {
        if (listeners.size() > 0) {
            listeners.forEach(x -> x.accept(args));
        }
    }
}
